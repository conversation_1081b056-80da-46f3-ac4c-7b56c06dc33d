package com.cet.pq.common.model;

import com.cet.pq.common.annotation.FieldAnnotation;
import lombok.Data;

/**
 * @Title: PageChangeDataParams
 * @Package: com.cet.pq.pqservice.model.upload
 * @Description:
 * @Author: zhangyifu
 * @Date: 2024/3/13 15:21
 * @Version:1.0
 */
@Data
public class PageChangeDataParams {

    @FieldAnnotation(name = "时间")
    private Long logtime;

    @FieldAnnotation(name = "地市")
    private String regionName; // 地/市

    @FieldAnnotation(name = "在运监测终端数量")
    private Integer runTerminalNum; //在运监测终端数量

    @FieldAnnotation(name = "在线监测点个数")
    private Integer onlineMonitorNum; //在线监测点个数

    @FieldAnnotation(name = "监测点在线率")
    private Double onlineMonitorRate; //监测点在线率

    @FieldAnnotation(name = "完整率")
    private Double dataFullRate; // 完整率

    @FieldAnnotation(name = "老在运监测终端数量")
    private Integer oldRunTerminalNum; //在运监测终端数量 老值

    @FieldAnnotation(name = "老在线监测点个数")
    private Integer oldOnlineMonitorNum; //在线监测点个数 老值

    @FieldAnnotation(name = "老监测点在线率")
    private Double oldOnlineMonitorRate; //监测点在线率 老值

    @FieldAnnotation(name = "老完整率")
    private Double oldDataFullRate; // 完整率 老值

    @FieldAnnotation(name = "统计地市层级")
    private String statisticalLevel; // 统计地市层级

    @FieldAnnotation(name = "时间统计层级")
    private String statisticalType;  // 时间统计层级
}

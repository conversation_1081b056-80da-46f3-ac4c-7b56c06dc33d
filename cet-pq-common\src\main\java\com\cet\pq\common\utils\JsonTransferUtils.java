package com.cet.pq.common.utils;

import com.cet.pq.common.exception.CommonManagerException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019年11月1日 Json转换工具类
 */
@SuppressWarnings("rawtypes")
public class JsonTransferUtils {

	private static final Logger logger = LoggerFactory.getLogger(JsonTransferUtils.class);

	private static ObjectMapper objectMapper = new ObjectMapper();

	static {
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		// 如果是空对象的时候,不抛异常
		objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
	}

	public static <T> List<T> transferList(List list, Class<T> clazz) {
		if (CollectionUtils.isEmpty(list)) {
			return new ArrayList<T>();
		}
		try {
			JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, clazz);
			return objectMapper.readValue(objectMapper.writeValueAsString(list), javaType);
		} catch (IOException ex) {
			logger.error("JsonTransferUtils.transferList", ex);
			throw new CommonManagerException("JSON转换集合失败！",ex);
		}
	}

	public static <T> List<T> transferJsonString(String jsonString, Class<T> clazz) {
		if (StringUtils.isBlank(jsonString)) {
			return new ArrayList<T>();
		}
		try {
			JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, clazz);
			return objectMapper.readValue(jsonString, javaType);
		} catch (IOException ex) {
			logger.error("JsonTransferUtils.transferJsonString", ex);
			throw new CommonManagerException("JSON转换集合失败！",ex);
		}
	}

	public static <T> T parseObject(String data, Class<T> clazz) {
		try {
			return objectMapper.readValue(data, clazz);
		} catch (IOException e) {
			logger.error("JsonTransferUtils.parseObject", e);
			throw new CommonManagerException("JSON转换对象失败！", e);
		}
	}

	public static <T> T parseObjectNotLog(String data, Class<T> clazz) {
		try {
			return objectMapper.readValue(data, clazz);
		} catch (IOException e) {
			throw new CommonManagerException("JSON转换对象失败！", e);
		}
	}

	public static <T> String toJsonString(List<T> data) {
		try {
			return objectMapper.writeValueAsString(data);
		} catch (JsonProcessingException e) {
			logger.error("JsonTransferUtils.toJSONString", e);
			throw new CommonManagerException("JSON转换对象失败！", e);
		}
	}

	public static <T> String toJsonString(T data) {
		try {
			return objectMapper.writeValueAsString(data);
		} catch (JsonProcessingException e) {
			logger.error("JsonTransferUtils.toJSONString", e);
			throw new CommonManagerException("JSON转换对象失败！", e);
		}
	}

	public static <T> List<List<T>> transferNestingJson(String jsonString, Class<T> clazz) {
		List<List<T>> result = new ArrayList<>();
		List<List> list = transferJsonString(jsonString, List.class);
		list.forEach(e -> {
			result.add(transferList(e, clazz));
		});
		return result;
	}

	public static <T> JSONObject transBean2JsonObject(T data) {
		JSONObject json;
		try {
			json = new JSONObject(toJsonString(data));
		} catch (JSONException e) {
			logger.error("JsonTransferUtils.transBean2JsonObject", e);
			throw new CommonManagerException("Bean转JSONObject失败！",e);
		}
		return json;
	}

	public static <T> List<T> transArray2BeanList(JSONArray array, Class<T> clazz) {
		List<T> list = new ArrayList<>();
		for (int i = 0; i < array.length(); i++) {
			String json = null;
			try {
				json = array.getString(i);
			} catch (JSONException e) {
				logger.debug("context", e);
			}
			list.add(parseObject(json, clazz));
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	public static Map<String, Object> transJosn2Map(JSONObject json) {
		Map<String, Object> map = parseObject(json.toString(), HashMap.class);
		return map;
	}

	public static List<Map<String, Object>> transArray2MapList(List<JSONObject> list) {
		List<Map<String, Object>> mapList = new ArrayList<>();
		for (JSONObject json : list) {
			Map<String, Object> map = transJosn2Map(json);
			mapList.add(map);
		}
		return mapList;
	}

	public static <K, V> List<Map<K, V>> castListMap(Object obj, Class<K> kClazz, Class<V> vClazz){
		List<Map<K, V>> result = new ArrayList<>();
		if(obj instanceof List<?>){
			for(Object mapObj: (List<?>) obj){
				if(mapObj instanceof Map<?,?>){
					Map<K, V> map = new HashMap<>(16);
					for(Map.Entry<?,?> entry: ((Map<?,?>) mapObj).entrySet()){
						map.put(kClazz.cast(entry.getKey()), vClazz.cast(entry.getValue()));
					}
					result.add(map);
				}
			}
			return result;
		}
		return null;
	}

}
2025-08-05 10:27:30.033 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-05 10:27:30.140 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-05 10:27:30.142 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:732] : 暂降结束检测: 额定电压=5773.6720554272515V, 90%阈值=5196.304849884526V, 触发RMS=5039.138883751789V
2025-08-05 10:27:30.144 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:760] : 暂降结束: 索引5恢复到5517.422905126656V(≥90%额定电压), 事件结束索引=4
2025-08-05 10:27:30.145 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:786] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-05 10:27:30.146 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1097] : 持续时间计算: RMS索引4到5, 采样点2048到3071, 持续时间40ms
2025-08-05 10:27:30.147 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 10:27:53.595 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 10:28:20.936 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 10:28:22.760 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 10:28:37.154 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 46.199 seconds (JVM running for 47.847)
2025-08-05 10:28:37.173 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 10:28:48.556 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 10:28:59.012 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 10:28:59.349 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:970] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-05 10:28:59.350 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:970] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-05 10:28:59.351 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-05 10:28:59.352 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:688] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-05 10:28:59.353 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:737] : 暂降结束: 索引5恢复到5517.422905126656V(≥90%额定电压), 事件结束索引=4
2025-08-05 10:28:59.354 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:762] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-05 10:28:59.354 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1066] : 持续时间计算: RMS索引4到5, 采样点2048到3071, 持续时间40ms
2025-08-05 10:28:59.355 [33m- INFO[0;39m - [192.168.0.2] - [34m4964[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 10:30:08.241 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 10:30:17.897 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 10:30:19.581 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 10:30:33.547 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 27.809 seconds (JVM running for 29.228)
2025-08-05 10:30:33.564 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 10:30:38.461 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 10:31:54.609 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 10:31:54.970 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:970] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-05 10:31:54.971 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:970] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-05 10:31:54.972 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-05 10:31:54.974 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:688] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-05 10:31:54.974 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:737] : 暂降结束: 索引7恢复到5463.8810723335255V(≥90%额定电压), 事件结束索引=6
2025-08-05 10:31:54.975 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1066] : 持续时间计算: RMS索引5到6, 采样点2560到3583, 持续时间40ms
2025-08-05 10:31:54.976 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 61.124935%
2025-08-05 10:36:36.214 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 10:47:25.896 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:970] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-05 10:47:25.899 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:970] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-05 10:47:25.900 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-05 10:47:25.901 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:688] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-05 10:47:25.903 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:737] : 暂降结束: 索引7恢复到5463.8810723335255V(≥90%额定电压), 事件结束索引=6
2025-08-05 10:47:25.904 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1066] : 持续时间计算: RMS索引5到6, 采样点2560到3583, 持续时间40ms
2025-08-05 10:47:25.905 [33m- INFO[0;39m - [192.168.0.2] - [34m23328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 61.124935%
2025-08-05 10:47:43.485 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 10:47:53.330 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 10:47:55.305 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 10:48:09.832 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.842 seconds (JVM running for 30.62)
2025-08-05 10:48:09.851 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 10:48:15.578 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 10:48:27.746 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 10:48:28.010 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:738] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-05 10:48:28.011 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 10:49:07.670 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 10:52:09.966 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:738] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-05 10:52:09.969 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 10:52:17.449 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 11:36:18.517 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:738] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-05 11:36:18.527 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 11:36:32.468 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 11:51:48.798 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:738] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-05 11:51:48.802 [33m- INFO[0;39m - [192.168.0.2] - [34m29464[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 11:52:08.609 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 11:52:19.848 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 11:52:21.786 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 11:52:37.108 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 31.704 seconds (JVM running for 33.658)
2025-08-05 11:52:37.124 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 11:52:42.290 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 11:52:53.549 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 11:52:53.854 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 11:53:15.948 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 12:00:45.044 [33m- INFO[0;39m - [192.168.0.2] - [34m29712[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-05 12:01:04.048 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 12:01:13.645 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 12:01:15.290 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 12:01:29.812 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.307 seconds (JVM running for 30.197)
2025-08-05 12:01:29.829 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 12:01:35.770 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 12:01:36.702 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 12:01:37.062 [33m- INFO[0;39m - [192.168.0.2] - [34m20684[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 60ms, 幅值为: 61.124935%
2025-08-05 12:02:24.503 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 12:02:35.102 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 12:02:36.750 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 12:02:50.501 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.375 seconds (JVM running for 30.04)
2025-08-05 12:02:50.517 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 12:02:55.146 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 12:03:05.279 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 12:03:05.463 [33m- WARN[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 12:03:05.464 [33m- WARN[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 12:03:05.465 [33m- INFO[0;39m - [192.168.0.2] - [34m22424[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 12:12:18.955 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 12:12:28.738 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 12:12:30.419 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 12:12:44.271 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 27.707 seconds (JVM running for 29.332)
2025-08-05 12:12:44.289 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 12:12:48.921 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 12:13:10.215 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 12:13:10.298 [33m- INFO[0;39m - [192.168.0.2] - [34m30568[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 200ms, 幅值为: 0.7038257%
2025-08-05 13:31:48.831 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 13:31:59.323 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 13:32:01.024 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 13:32:16.690 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 30.642 seconds (JVM running for 33.344)
2025-08-05 13:32:16.707 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 13:32:22.449 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 13:32:45.465 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:33:17.560 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 200ms, 幅值为: 0.031992078%
2025-08-05 13:36:02.507 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:39:47.273 [33m- INFO[0;39m - [192.168.0.2] - [34m34760[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 200ms, 幅值为: 0.031992078%
2025-08-05 13:40:05.541 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 13:40:15.539 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 13:40:17.291 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 13:40:31.374 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.67 seconds (JVM running for 30.233)
2025-08-05 13:40:31.392 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 13:40:41.075 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 13:41:10.145 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:41:10.225 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:738] :   - 事件至少持续1个RMS点，结束索引调整为: 3
2025-08-05 13:41:10.226 [33m- INFO[0;39m - [192.168.0.2] - [34m27272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 99.911354%
2025-08-05 13:43:48.306 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 13:43:58.273 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 13:44:00.030 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 13:44:14.519 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.728 seconds (JVM running for 30.227)
2025-08-05 13:44:14.537 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 13:44:19.589 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 13:46:02.947 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:46:03.243 [33m- INFO[0;39m - [192.168.0.2] - [34m34604[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 85.37657%
2025-08-05 13:47:56.335 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 13:48:06.444 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 13:48:08.220 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 13:48:22.995 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 29.296 seconds (JVM running for 30.922)
2025-08-05 13:48:23.012 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 13:48:31.859 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 13:48:33.582 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:48:33.760 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 13:48:33.761 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 13:48:33.762 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 13:48:49.604 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:48:49.732 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 13:48:49.733 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 13:48:49.734 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 13:49:06.077 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:55:33.784 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 13:55:33.787 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 13:55:39.063 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 13:55:49.703 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:56:21.893 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 13:56:21.896 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 13:56:23.435 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 13:56:56.714 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:56:56.930 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 13:56:56.932 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 13:56:56.933 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 13:57:07.503 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:57:07.649 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 13:57:07.650 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 13:57:07.651 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 13:57:52.585 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-7][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:58:11.878 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-7][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 13:58:11.881 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-7][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 13:58:28.393 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-7][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 13:58:37.597 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 13:59:51.191 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:00:13.696 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:00:13.697 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 14:00:23.561 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:00:26.852 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:00:26.855 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:00:43.803 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 14:00:57.744 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:07:31.863 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:07:31.871 [33m- WARN[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:07:31.873 [33m- INFO[0;39m - [192.168.0.2] - [34m26776[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-05 14:07:49.552 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:07:59.968 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:08:01.905 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:08:16.779 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 30.129 seconds (JVM running for 31.838)
2025-08-05 14:08:16.797 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:08:21.938 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:08:39.618 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:08:45.850 [33m- WARN[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:08:45.852 [33m- WARN[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:08:45.853 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 14:09:10.511 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:11:33.946 [33m- WARN[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:11:33.947 [33m- WARN[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:11:33.949 [33m- INFO[0;39m - [192.168.0.2] - [34m14744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 14:11:49.716 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:11:59.621 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:12:01.325 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:12:15.732 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.415 seconds (JVM running for 29.841)
2025-08-05 14:12:15.748 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:12:20.537 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:12:40.731 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:12:41.388 [33m- INFO[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1064] : 未找到通道:[U1, UA, 保护电压A相]的数据
2025-08-05 14:12:41.417 [33m-ERROR[0;39m - [192.168.0.2] - [34m45328[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:266] : 解析波形持续时间失败,device is 103,eventtime is 1730437741983
com.cet.pq.common.exception.CommonManagerException: 未找到通道数据
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getChannelData(PqEventServiceImpl.java:1065)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.calculateWaveDuration(PqEventServiceImpl.java:290)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.lambda$parseFaultWaveEvent$5(PqEventServiceImpl.java:263)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.parseFaultWaveEvent(PqEventServiceImpl.java:254)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.processFaultWaveEvent(PqEventServiceImpl.java:197)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getPqVariationEvent(PqEventServiceImpl.java:100)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$FastClassBySpringCGLIB$$fb6ddef3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.cet.pq.common.aspect.LogAspect.doAround(LogAspect.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$EnhancerBySpringCGLIB$$d0ff7bb3.getPqVariationEvent(<generated>)
	at com.cet.pq.pqservice.controller.PqEventController.getEventList(PqEventController.java:41)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.cet.electric.log.filter.MvpFilter.doFilterInternal(MvpFilter.java:44)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:164)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:237)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:223)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:887)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1684)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-08-05 14:15:14.631 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:15:24.421 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:15:26.147 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:15:40.304 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.19 seconds (JVM running for 29.504)
2025-08-05 14:15:40.327 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:15:45.286 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:15:56.250 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:15:58.820 [33m- INFO[0;39m - [192.168.0.2] - [34m42540[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 61.37537%
2025-08-05 14:21:20.045 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:21:30.104 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:21:31.755 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:21:45.903 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.284 seconds (JVM running for 29.624)
2025-08-05 14:21:45.921 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:21:51.375 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:22:05.794 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:22:07.127 [33m- WARN[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:22:07.128 [33m- WARN[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:22:07.129 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 14:25:38.854 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:26:53.223 [33m- WARN[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:26:53.225 [33m- WARN[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:26:53.226 [33m- INFO[0;39m - [192.168.0.2] - [34m36784[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 14:27:35.436 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:27:45.626 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:27:47.276 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:28:02.617 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 29.789 seconds (JVM running for 31.396)
2025-08-05 14:28:02.636 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:28:08.126 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:28:17.775 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:28:20.374 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 61.38%
2025-08-05 14:30:14.536 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:35:21.377 [33m- INFO[0;39m - [192.168.0.2] - [34m22120[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 61.38%
2025-08-05 14:42:33.835 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:42:44.604 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:42:46.373 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:43:01.726 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 30.597 seconds (JVM running for 32.35)
2025-08-05 14:43:01.741 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:43:29.156 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:43:29.259 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:44:01.264 [33m- INFO[0;39m - [192.168.0.2] - [34m4412[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 61.38%
2025-08-05 14:45:05.207 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:45:16.528 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:45:18.373 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:45:33.963 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 31.903 seconds (JVM running for 33.462)
2025-08-05 14:45:33.979 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:45:49.394 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:45:50.202 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:45:53.266 [33m- WARN[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:45:53.267 [33m- WARN[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:45:53.268 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 14:53:03.744 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:53:47.715 [33m- WARN[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:53:47.717 [33m- WARN[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:53:47.719 [33m- INFO[0;39m - [192.168.0.2] - [34m22876[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 14:54:03.700 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:54:13.251 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:54:14.944 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:54:28.529 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 27.332 seconds (JVM running for 28.887)
2025-08-05 14:54:28.545 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:54:37.328 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:54:41.874 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:55:27.538 [33m- WARN[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 触发索引超出范围: 9
2025-08-05 14:55:27.541 [33m- WARN[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:954] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 14:55:27.545 [33m- INFO[0;39m - [192.168.0.2] - [34m45276[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 14:55:41.830 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 14:55:51.155 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 14:55:52.813 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 14:56:06.405 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 26.874 seconds (JVM running for 28.082)
2025-08-05 14:56:06.421 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 14:56:13.165 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 14:56:22.502 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 14:56:23.128 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1064] : 未找到通道:[U1, UA, 保护电压A相]的数据
2025-08-05 14:56:23.140 [33m-ERROR[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:266] : 解析波形持续时间失败,device is 103,eventtime is 1730437741983
com.cet.pq.common.exception.CommonManagerException: 未找到通道数据
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getChannelData(PqEventServiceImpl.java:1065)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.calculateWaveDuration(PqEventServiceImpl.java:290)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.lambda$parseFaultWaveEvent$5(PqEventServiceImpl.java:263)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.parseFaultWaveEvent(PqEventServiceImpl.java:254)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.processFaultWaveEvent(PqEventServiceImpl.java:197)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getPqVariationEvent(PqEventServiceImpl.java:100)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$FastClassBySpringCGLIB$$fb6ddef3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.cet.pq.common.aspect.LogAspect.doAround(LogAspect.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$EnhancerBySpringCGLIB$$628818f2.getPqVariationEvent(<generated>)
	at com.cet.pq.pqservice.controller.PqEventController.getEventList(PqEventController.java:41)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.cet.electric.log.filter.MvpFilter.doFilterInternal(MvpFilter.java:44)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:164)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:237)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:223)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:887)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1684)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-08-05 14:56:44.309 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 15:33:41.240 [33m- INFO[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1064] : 未找到通道:[U1, UA, 保护电压A相]的数据
2025-08-05 15:33:41.255 [33m-ERROR[0;39m - [192.168.0.2] - [34m38148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:266] : 解析波形持续时间失败,device is 103,eventtime is 1730437741983
com.cet.pq.common.exception.CommonManagerException: 未找到通道数据
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getChannelData(PqEventServiceImpl.java:1065)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.calculateWaveDuration(PqEventServiceImpl.java:290)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.lambda$parseFaultWaveEvent$5(PqEventServiceImpl.java:263)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.parseFaultWaveEvent(PqEventServiceImpl.java:254)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.processFaultWaveEvent(PqEventServiceImpl.java:197)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getPqVariationEvent(PqEventServiceImpl.java:100)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$FastClassBySpringCGLIB$$fb6ddef3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.cet.pq.common.aspect.LogAspect.doAround(LogAspect.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$EnhancerBySpringCGLIB$$628818f2.getPqVariationEvent(<generated>)
	at com.cet.pq.pqservice.controller.PqEventController.getEventList(PqEventController.java:41)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.cet.electric.log.filter.MvpFilter.doFilterInternal(MvpFilter.java:44)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:164)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:237)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:223)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:887)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1684)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-08-05 15:33:55.436 [33m- INFO[0;39m - [192.168.0.2] - [34m42632[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 15:34:04.476 [33m- INFO[0;39m - [192.168.0.2] - [34m42632[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 15:34:06.046 [33m- INFO[0;39m - [192.168.0.2] - [34m42632[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 15:34:19.224 [33m- INFO[0;39m - [192.168.0.2] - [34m42632[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 26.058 seconds (JVM running for 27.423)
2025-08-05 15:34:19.239 [33m- INFO[0;39m - [192.168.0.2] - [34m42632[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 15:34:25.360 [33m- INFO[0;39m - [192.168.0.2] - [34m42632[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 15:34:51.821 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 15:35:02.230 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 15:35:03.907 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 15:35:16.981 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 27.462 seconds (JVM running for 28.959)
2025-08-05 15:35:16.996 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 15:35:22.034 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 15:35:55.969 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 15:36:10.050 [33m- WARN[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:500] : 触发索引超出范围: 9
2025-08-05 15:36:10.051 [33m- WARN[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:617] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 15:36:10.052 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 15:39:00.526 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 15:39:00.687 [33m- WARN[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:500] : 触发索引超出范围: 9
2025-08-05 15:39:00.688 [33m- WARN[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:617] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 15:39:00.689 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 15:39:06.400 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:37:06.092 [33m- WARN[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:500] : 触发索引超出范围: 9
2025-08-05 16:37:06.114 [33m- WARN[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:617] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 16:37:06.115 [33m- INFO[0;39m - [192.168.0.2] - [34m26112[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 16:38:39.916 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 16:39:07.173 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 16:39:08.958 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 16:39:23.498 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 46.474 seconds (JVM running for 48.232)
2025-08-05 16:39:23.514 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 16:39:27.834 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 16:47:17.864 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:47:18.025 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:548] : 事件至少持续1个RMS点，结束索引调整为: 50
2025-08-05 16:47:18.032 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 100.0%
2025-08-05 16:47:29.685 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:47:29.800 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:548] : 事件至少持续1个RMS点，结束索引调整为: 50
2025-08-05 16:47:29.801 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-8][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 100.0%
2025-08-05 16:48:13.713 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:48:13.855 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:548] : 事件至少持续1个RMS点，结束索引调整为: 50
2025-08-05 16:48:13.856 [33m- INFO[0;39m - [192.168.0.2] - [34m43696[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40ms, 幅值为: 100.0%
2025-08-05 16:49:47.848 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 16:49:57.306 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 16:49:58.923 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 16:50:12.501 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 27.274 seconds (JVM running for 28.758)
2025-08-05 16:50:12.517 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 16:50:17.644 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 16:50:31.923 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 3ms
2025-08-05 16:50:31.929 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:50:32.115 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:551] : 事件至少持续1个RMS点，结束索引调整为: 50
2025-08-05 16:50:32.123 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 40ms, 幅值为: 100.0%
2025-08-05 16:50:54.608 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 3ms
2025-08-05 16:50:54.609 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:50:54.754 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:551] : 事件至少持续1个RMS点，结束索引调整为: 50
2025-08-05 16:50:54.755 [33m- INFO[0;39m - [192.168.0.2] - [34m40856[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 40ms, 幅值为: 100.0%
2025-08-05 16:51:55.002 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 16:52:04.372 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 16:52:06.005 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 16:52:19.449 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 26.831 seconds (JVM running for 28.259)
2025-08-05 16:52:19.464 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 16:52:33.934 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:52:34.469 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:551] : 事件至少持续1个RMS点，结束索引调整为: 50
2025-08-05 16:52:34.476 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 40ms, 幅值为: 100.0%
2025-08-05 16:52:34.477 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:200] : 0ms
2025-08-05 16:52:36.998 [33m- INFO[0;39m - [192.168.0.2] - [34m41044[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 16:54:01.080 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 16:54:10.220 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 16:54:11.811 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 16:54:24.893 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 26.312 seconds (JVM running for 27.479)
2025-08-05 16:54:24.909 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 16:54:29.325 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 16:54:34.817 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 3514ms
2025-08-05 16:54:34.823 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 16:54:34.991 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:551] : 事件至少持续1个RMS点，结束索引调整为: 50
2025-08-05 16:54:34.998 [33m- INFO[0;39m - [192.168.0.2] - [34m22264[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 40ms, 幅值为: 100.0%
2025-08-05 17:25:56.170 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 17:26:06.017 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 17:26:07.787 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 17:26:22.326 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.863 seconds (JVM running for 30.473)
2025-08-05 17:26:22.343 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 17:26:27.045 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-05 18:19:44.872 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 8983ms
2025-08-05 18:19:44.883 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 18:20:53.424 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 3502ms
2025-08-05 18:20:53.425 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 18:20:56.443 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 18:20:56.444 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 18:20:56.445 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 18:20:57.724 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 2786ms
2025-08-05 18:20:57.725 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 18:21:00.791 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 18:21:00.793 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 18:21:00.793 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 18:21:23.551 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 2884ms
2025-08-05 18:21:23.553 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 18:21:26.553 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 18:21:26.554 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 18:21:26.555 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 18:22:19.038 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 3169ms
2025-08-05 18:22:19.039 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 18:22:21.805 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 18:22:21.806 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 18:22:21.806 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 18:22:26.777 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 18:22:26.779 [33m- WARN[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 18:22:26.780 [33m- INFO[0;39m - [192.168.0.2] - [34m45080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 18:22:37.737 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-05 18:22:47.134 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-05 18:22:48.706 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-05 18:23:02.022 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 26.839 seconds (JVM running for 28.092)
2025-08-05 18:23:02.041 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-05 18:23:10.012 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 3565ms
2025-08-05 18:23:10.017 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 18:34:00.092 [33m- WARN[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 18:34:00.095 [33m- WARN[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 18:34:00.096 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 18:34:00.168 [33m-ERROR[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.aspect.LogAspect      [0;39m - [LogAspect.java:42] : +++++around execution(ResultWithTotal com.cet.pq.common.feign.ModelDataService.query(QueryCondition))Use time :654200 ms with exception : Error while extracting response for type [com.cet.pq.common.model.ResultWithTotal<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>>] and content type [application/json]; nested exception is org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Connection reset; nested exception is com.fasterxml.jackson.databind.JsonMappingException: Connection reset (through reference chain: com.cet.pq.common.model.ResultWithTotal["data"]->java.util.ArrayList[2982])
2025-08-05 18:34:00.169 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:55] : The ledger cache init fail:Error while extracting response for type [com.cet.pq.common.model.ResultWithTotal<java.util.List<java.util.Map<java.lang.String, java.lang.Object>>>] and content type [application/json]; nested exception is org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Connection reset; nested exception is com.fasterxml.jackson.databind.JsonMappingException: Connection reset (through reference chain: com.cet.pq.common.model.ResultWithTotal["data"]->java.util.ArrayList[2982])
2025-08-05 18:34:14.128 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 3519ms
2025-08-05 18:34:14.129 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 18:37:22.602 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:199] : 186196ms
2025-08-05 18:37:22.604 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:262] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-05 19:12:04.576 [33m- WARN[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 19:12:04.579 [33m- WARN[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 19:12:04.580 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 19:12:08.236 [33m- WARN[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发索引超出范围: 9
2025-08-05 19:12:08.237 [33m- WARN[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 无效的索引范围: 触发点=9, 结束点=-1
2025-08-05 19:12:08.238 [33m- INFO[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:267] : 持续时间为: 0ms, 幅值为: 0.0%
2025-08-05 19:12:08.282 [33m-ERROR[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.c.e.h.ControllerExceptionHandler  [0;39m - [ControllerExceptionHandler.java:43] : 业务处理异常
java.lang.NullPointerException: null
	at org.springframework.web.util.UrlPathHelper.getSanitizedPath(UrlPathHelper.java:408)
	at org.springframework.web.util.UrlPathHelper.decodeAndCleanUriString(UrlPathHelper.java:551)
	at org.springframework.web.util.UrlPathHelper.getOriginatingRequestUri(UrlPathHelper.java:496)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.addContentDispositionHeader(AbstractMessageConverterMethodProcessor.java:431)
	at org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:288)
	at org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:183)
	at org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:135)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.cet.electric.log.filter.MvpFilter.doFilterInternal(MvpFilter.java:44)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:164)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:237)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:223)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:887)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1684)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-08-05 19:12:08.304 [33m-ERROR[0;39m - [192.168.0.2] - [34m45916[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mo.s.web.servlet.HandlerExecutionChain   [0;39m - [HandlerExecutionChain.java:181] : HandlerInterceptor.afterCompletion threw exception
java.lang.NullPointerException: null
	at org.springframework.boot.actuate.metrics.web.servlet.LongTaskTimingHandlerInterceptor.stopLongTaskTimers(LongTaskTimingHandlerInterceptor.java:134)
	at org.springframework.boot.actuate.metrics.web.servlet.LongTaskTimingHandlerInterceptor.afterCompletion(LongTaskTimingHandlerInterceptor.java:83)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:178)
	at org.springframework.web.servlet.DispatcherServlet.triggerAfterCompletion(DispatcherServlet.java:1460)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1092)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.cet.electric.log.filter.MvpFilter.doFilterInternal(MvpFilter.java:44)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:164)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:237)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:223)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:887)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1684)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)

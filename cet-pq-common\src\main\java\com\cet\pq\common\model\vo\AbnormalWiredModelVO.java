package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 11:43
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AbnormalWiredModelVO {

    /**
     * 监测点id
     */
    private Long monitorid;

    /**
     * 监测点编号
     */
    private String monitorCode;

    /**
     * 监测点名称
     */
    private String monitorName;

    /**
     * 装置状态：0:接线正常，1：接线异常
     */
    private String deviceStatus;

    /**
     * 状态总览：0：---，1：电压断相，2：电压相序错误，3：电流相序错误，4：电流极性错误，5：混合接线错误
     */
    private String overviewStatus;

    /**
     * 校正功能：0：无，1：有
     */
    private String correctiveFunction;

    /**
     * 校正状态：0：未校正，1：已校正，2：---
     */
    private String correctiveStatus;

    /**
     * 监测时间
     */
    private Long logtime;


}

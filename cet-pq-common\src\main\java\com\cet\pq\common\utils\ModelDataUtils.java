package com.cet.pq.common.utils;

import com.cet.pq.common.model.ConditionBlock;
import com.cet.pq.common.model.ConditionBlockCompose;
import com.cet.pq.common.model.FlatQueryConditionDTO;
import com.cet.pq.common.model.SingleModelConditionDTO;
import com.cet.pq.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.pq.common.time.TimeFormat;
import org.joda.time.DateTime;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 公共类
 * 
 * <AUTHOR>
 */
public final class ModelDataUtils {

	public static final int SMS_START_INDEX = 0;

	public static final int SMS_MAX_INDEX = 20;

	/**
	 * 获取单个模型条件
	 * 
	 * @param modelLables 模型label
	 * @return 条件
	 */
	public static List<SingleModelConditionDTO> getSingleModelConditionsByLabels(List<String> modelLables) {
		List<SingleModelConditionDTO> result = new ArrayList<>();
		modelLables.forEach(label -> result.add(getSingleModelConditionByLabel(label)));

		return result;
	}

	/**
	 * 根据模型获取单个模型条件
	 * 
	 * @param modelLable 模型label
	 * @return 条件
	 */
	public static SingleModelConditionDTO getSingleModelConditionByLabel(String modelLable) {
		SingleModelConditionDTO conditionDTO = new SingleModelConditionDTO();
		conditionDTO.setModelLabel(modelLable);
		return conditionDTO;
	}

	/**
	 * 从map中获取数据
	 * 
	 * @param key   键
	 * @param datas 数据列表
	 * @param <T>   类型
	 * @return 目标数据列表
	 */
	@SuppressWarnings("unchecked")
	public static <T> T getValueFromMap(String key, Map<String, Object> datas) {
		Object value = datas.get(key);
		return value == null ? null : (T) value;
	}

	/**
	 * 从数据字典中获取时间
	 * 
	 * @param key   键
	 * @param datas 数据字典
	 * @return 时间
	 */
	public static Date getDateBy(String key, Map<String, Object> datas) {
		Object value = datas.get(key);
		if (value instanceof Integer) {
			return null;
		}
		return value == null ? null : getDateFrom((Long) value);
	}

	/**
	 * 获取按照id查询的条件
	 * 
	 * @param modelLabel 模型label
	 * @param ids        id列表
	 * @return 条件
	 */
	public static SingleModelConditionDTO getSingleModelConditionDtoForIds(
			String modelLabel, List<Integer> ids) {
		SingleModelConditionDTO idCondition = new SingleModelConditionDTO();
		List<ConditionBlock> expressions = new ArrayList<>();
		ConditionBlock conditionBlock = new ConditionBlock();
		conditionBlock.setProp("id");
		conditionBlock.setLimit(ids);
		conditionBlock.setOperator("IN");
		expressions.add(conditionBlock);
		idCondition.setFilter(new ConditionBlockCompose());
		idCondition.getFilter().setExpressions(expressions);
		idCondition.setModelLabel(modelLabel);
		return idCondition;
	}

	/**
	 * 根据id获取扁平查询条件
	 * 
	 * @param ids 模型id
	 * @return 条件
	 */
	public static FlatQueryConditionDTO getFlatQueryConditionDtoForRoot(List<Integer> ids) {
		FlatQueryConditionDTO rootCondition = new FlatQueryConditionDTO();
		rootCondition.setFilter(new ConditionBlockCompose());
		List<ConditionBlock> expressions = new ArrayList<>();
		ConditionBlock conditionBlock = new ConditionBlock();
		conditionBlock.setProp("id");
		conditionBlock.setLimit(ids);
		conditionBlock.setOperator("IN");
		expressions.add(conditionBlock);
		rootCondition.getFilter().setExpressions(expressions);
		return rootCondition;
	}

	/**
	 * 将时间转换成字符串
	 * 
	 * @param format 格式
	 * @param time   时间
	 * @return 字符串
	 */
	public static String toDateString(String format, Date time) {
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		return sdf.format(time);
	}

	/**
	 * 为短信获取子串
	 * 
	 * @param str 字符串
	 * @return 子串
	 */
	public static String getSubStrForSms(String str) {
		if (str == null || str.length() <= SMS_MAX_INDEX) {
			return str;
		} else {
			return str.substring(SMS_START_INDEX, SMS_MAX_INDEX);
		}
	}

	/**
	 * 获取日期
	 * 
	 * @param time 时间
	 * @return 日期
	 */
	public static Date getDateFrom(Date time) {
		DateTime dt = new DateTime(time);
		dt = new DateTime(dt.getYear(), dt.getMonthOfYear(), dt.getDayOfMonth(), 0, 0, 0);

		return dt.toDate();
	}

	/**
	 * 将时间戳转换成时间
	 * 
	 * @param timestamp 时间戳
	 * @return 时间
	 */
	public static Date getDateFrom(Long timestamp) {
		return new Date(timestamp);
	}

	public static long getLongValue(String key, Map<String, Object> datas) {
		Object value = ModelDataUtils.getValueFromMap(key, datas);
		if (value instanceof Integer) {
			return (int) value;
		} else {
			return (long) value;
		}
	}

	public static boolean objectIsNotNull(Object obj) {
		return obj == null ? false : true;
	}

	/**
	 * 按周期分隔成一个装有指定对象的map
	 * 
	 * @param startTime    开始时间
	 * @param endTime      结束时间
	 * @param cycle        周期
	 * @param defaultValue 默认对象
	 * @param <T>          指定对象
	 * @return 返回一个时间key的map
	 */
	public static <T> Map<Integer, T> getPeriods(long startTime, long endTime, int cycle, T defaultValue) {
		DateTime startDateTime = new DateTime(startTime);
		DateTime endDateTime = new DateTime(endTime);
		Map<Integer, T> map = new ConcurrentHashMap<>(0);
		while (startDateTime.isBefore(endDateTime)) {
			if (cycle == AggregationCycle.ONE_MONTH) {
				// 月
				int key = Integer.parseInt(startDateTime.toString(TimeFormat.MONTHTIMEFORMAT));
				map.put(key, defaultValue);
				startDateTime = startDateTime.plusMonths(1);
			} else if (cycle == AggregationCycle.ONE_DAY) {
				// 天
				int key = Integer.parseInt(startDateTime.toString(TimeFormat.DATETIMEFORMAT));
				map.put(key, defaultValue);
				startDateTime = startDateTime.plusDays(1);
			} else if (cycle == AggregationCycle.ONE_HOUR) {
				// 小时
				int key = Integer.parseInt(startDateTime.toString(TimeFormat.HOURTIMEFORMAT));
				map.put(key, defaultValue);
				startDateTime = startDateTime.plusHours(1);
			}
		}
		return map;
	}

	/**
	 * 根据传入周期得到一个查询数据库的真实周期
	 * 
	 * @param cycle 周期
	 * @return 返回周期
	 */
	public static int getTimeDivisionCycle(int cycle) {
		switch (cycle) {
		case AggregationCycle.ONE_YEAR:
			return AggregationCycle.ONE_MONTH;
		case AggregationCycle.ONE_MONTH:
			return AggregationCycle.ONE_DAY;
		case AggregationCycle.ONE_DAY:
			return AggregationCycle.ONE_HOUR;
		default:
			return cycle;
		}
	}
}

package com.cet.pq.common.enums;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum FieldType {
    //类型
    DATE, BOOLEAN, STRING, TERMINALSTATUSENUM, VOLTAGELEVELENUM, MONITORSTATUSENUM, NEUTRALP<PERSON><PERSON><PERSON><PERSON><PERSON>UND<PERSON><PERSON><PERSON>NU<PERSON>,
    WIR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, POW<PERSON>ST<PERSON><PERSON><PERSON>PEENUM, I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NU<PERSON>, COVERTERTYPEENUM, R<PERSON><PERSON><PERSON>PEENUM, TRACTIONTYPEENUM,
    TRACTIONVARPRIMARYVOLT, TRACTIONVARSECONDARYVOLT, RA<PERSON><PERSON>Y<PERSON><PERSON>EENU<PERSON>, <PERSON><PERSON><PERSON><PERSON>NU<PERSON>, INVERTERNETWORKWAY, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>CE<PERSON>P<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>EENU<PERSON>
}

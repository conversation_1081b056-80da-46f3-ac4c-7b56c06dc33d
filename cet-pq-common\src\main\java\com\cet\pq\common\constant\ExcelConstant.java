package com.cet.pq.common.constant;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public class ExcelConstant {

	// xls的excel文件类型
	public static final String EXCEL_TYPE_XLS = "xls";

	// xlsx的excel文件类型
	public static final String EXCEL_TYPE_XLSX = "xlsx";

	// 表头文本：合计
	public static final String HEAD_COUNT = "合计";

	// 表头文本：序号
	public static final String HEAD_ROWID = "序号";

	// 表头文本：单位
	public static final String HEAD_COMPANY = "单位";

	// 表头文本：厂家
	public static final String HEAD_VENDOR = "厂家";

  	// 表头文本：监测终端数
	public static final String HEAD_TERMINAL_NUM = "监测终端数（个）";

	// 表头文本：监测终端占比
	public static final String HEAD_TERMINAL_RATE = "监测终端占比（%）";

  	// 表头文本：监测点数
	public static final String HEAD_MONITOR_NUM = "监测点数（个）";

	// 表头文本：监测点占比
	public static final String HEAD_MONITOR_RATE = "监测点占比（%）";

	// 表头文本：监测对象类型
	public static final String HEAD_OBJECT_TYPE = "监测对象类型";

	// 表头文本：监测点类型
	public static final String HEAD_MONITOR_TYPE = "监测点类型";

	// 表头文本：电压等级
	public static final String HEAD_VOLTAGE_LEVEL = "电压等级（kV）";

	// 表头文本：监测点标题前缀
	public static final String HEAD_MONITOR_PREFIX = "监测点台账 - ";

	// 表头文本：监测终端标题前缀
	public static final String HEAD_TERMINAL_PREFIX = "监测终端台账 - ";

	// 表头文本：全网
	public static final String HEAD_UPLOAD_FALSE = "全网";

	// 表头文本：上送
	public static final String HEAD_UPLOAD_TRUE = "上送";

	// 表头文本：间隔符
	public static final String HEAD_DELIMITER = " - ";

	public static final String HEAD_PROVINCE = "所属省份";
	public static final String HEAD_CITY = "所属地市";

	public static final String HEAD_COUNTRY = "所属区县";
	public static final String HEAD_MONITOR_NAME = "监测点名称";
	public static final String HEAD_MONITOR_OBJECT_TYPE = "监测对象类型";
	public static final String HEAD_MONITOR_SORT = "监测点类别";
	public static final String HEAD_MONITOR_OBJECT_NAME = "监测对象名称";
	public static final String HEAD_MONITOR_VOLTAGE_LEVEL = "监测点电压等级";
	public static final String HEAD_SUBSTATION_NAME = "所属变电站";
	public static final String HEAD_SUBSTATION_VOLTAGE_LEVEL = "变电站电压等级";

	public static final String HEAD_MIN_BASECAPACITY = "基准短路容量";
	public static final String HEAD_MIN_CAPACITY = "最小短路容量";
	public static final String HEAD_USER_CAPACITY = "用户协议容量";
	public static final String HEAD_SUPPLY_CAPACITY = "供电设备容量";
	public static final String HEAD_DATA = "统计时段";
	public static final String HEAD_TIME = "报表制作时间";
	public static final String HEAD_STATUS = "监测点历史状态";
	public static final String HEAD_PT_RATIO = "PT变比";
	public static final String HEAD_CT_RATIO = "CT变比";
	public static final String HEAD_BUS_NAME = "母线名称";
	public static final String HEAD_BUS_VOLTAGE_LEVEL = "母线电压等级";
	public static final String HEAD_POWERDISTRIBUTIONAREA_LINENAME = "所属线路";

	public static final String HEAD_POWERDISTRIBUTIONAREA_NAME = "所属台区";

	public static final String IS_OVER_LIMIT = "不合格";
	public static final String NOT_OVER_LIMIT = "合格";
	public static final String MAX = "最大值";
	public static final String AVG = "平均值";
	public static final String MIN = "最小值";
	public static final String PRO_95 = "CP95值";
	public static final String LIMIT = "限值";
	public static final String IS_PASS = "结论";

	public static final String BEARING_CAPACITY_EVALUATION = "承载力评估";

	public static final String SINGLEPOINTREPORTNAME = "配网单点数据报表";

	public static final String SINGLEPOINTDETAILREPORTNAME = "配网数据详情报表";

	public static final String SINGLEPOINTREPORT = "singlePointReport";

	public static final String SINGLEPOINTDETAILREPORT = "singlePointDetailReport";

	public static final Integer SINGLEPOINTREPORT1 = 1;
	public static final Integer SINGLEPOINTREPORT2 = 2;

	//前端要区分类型，改成了5，6
	public static final Integer SINGLEPOINTREPORT3 = 5;
	public static final Integer SINGLEPOINTREPORT4 = 6;

	public static final Integer SINGLEPOINTDETAILREPORT1 = 1;
	public static final Integer SINGLEPOINTDETAILREPORT2 = 2;

	//前端要区分类型，改成了5，6
	public static final Integer SINGLEPOINTDETAILREPORT3 = 5;
	public static final Integer SINGLEPOINTDETAILREPORT4 = 6;

	public static final String NOW_YEAR = "当年";
	public static final String LAST_YEAR = "上一年";
	public static final String ONLINE_LINE_NUM_YEAR = "当年在线监测点数(个)";
	public static final String NOW_QUARTER = "当季";
	public static final String NOW_YEAR_ACCUMULATION = "当年累计";
	public static final String ONLINE_LINE_NUM_QUARTER = "当季在线监测点数(个)";
	public static final String STRING_EMPTY = "";
	public static final String STRING_COL = "col";

	// 单点详细报表表头
	public static final String HEAD_LINE_NAME = "监测点名称";
	public static final String HEAD_LINE_OBJECT_TYPE = "监测对象类型";
	public static final String HEAD_LINE_OBJECT_NAME = "监测对象名称";
	public static final String HEAD_LINE_VOLTAGE_LEVEL = "监测点电压等级";
}

package com.cet.pq.common.model.auth;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserGroup {

	private Long tenantId;
	private Long id;
    private String name;
    private Long parentId;
    private String customConfig;
    private List<Long> auths;
    private List<PecstarNode> pecstarNodes;
    private List<GraphNode> graphNodes;
    private List<ModelNode> modelNodes;
    private List<PageNode> pageNodes;
    private List<Role> roles;
    private List<User> users;
    private List<UserGroup> userGroups;
	
}

package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.common.DataReportParam;
import com.cet.pq.anlysis.model.common.ReportParam;
import com.cet.pq.anlysis.model.datareport.*;
import com.cet.pq.anlysis.model.excel.ExportCustomReportParam;
import com.cet.pq.anlysis.model.excel.ExportReportParam;
import com.cet.pq.anlysis.model.report.ParaSetResult;
import com.cet.pq.anlysis.model.report.PointReportParam;
import com.cet.pq.anlysis.service.DataReportService;
import com.cet.pq.anlysis.service.SinglePointDetailReportService;
import com.cet.pq.common.model.PageResult;
import com.cet.pq.common.model.Result;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName DataReportController
 * @Description TODO
 * @Date 2020/10/13
 */
@Api(value="DataReportController",tags={"报表管理 - 稳态指标数据报表/暂态事件分类统计报表"})
@RestController
@RequestMapping(value = "/pq/v1/report")
public class DataReportController {

    @Autowired
    private DataReportService dataReportService;

    @Autowired
    SinglePointDetailReportService singlePointDetailReportService;

    @ApiOperation(value = "主网指标统计分析 - 单点数据报表")
    @PostMapping(value = "/getPointReportDataId",produces = "application/json")
    public Result<Object> getPointReportDataId(
            @RequestBody PointReportParam dataReportParam) {
        return singlePointDetailReportService.getPointReportDataId(dataReportParam);
    }

    @ApiOperation("根据监测点id查询指标参数")
    @GetMapping("/getParaSetById")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lineId",value = "监测点id", required = true, dataType = "Long"),
            @ApiImplicitParam(name = "type",value = "报表类型,1是单点数据报表，2是数据详情报表", required = true, dataType = "int")
    })
    public Result<ParaSetResult> getParaSetById(@RequestParam Long lineId,@RequestParam Integer type) {
        return Result.success(dataReportService.getParaSetById(lineId,type));
    }

    @ApiOperation(value = "主网指标统计分析 - 数据详情报表")
    @PostMapping(value = "/steady/singlePointDetailReport",produces = "application/json")
    public Result<SingleDetailReport> getSinglePointDetailReport(
            @RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true)DataReportParam dataReportParam) {
        SingleDetailReport singleDetailReport = singlePointDetailReportService.getSinglePointDetailReport(dataReportParam);
        return Result.success(singleDetailReport);
    }

    @ApiOperation(value = "单点详细月报表")
    @PostMapping(value = "/getPointMonthReport",produces = "application/json")
    public Result<Object> getPointMonthReport(
            @RequestBody PointReportParam dataReportParam) {
        return singlePointDetailReportService.getPointMonthReport(dataReportParam);
    }

    @ApiOperation(value = "主网指标统计分析 - 单点数据报表")
    @PostMapping(value = "/steady/singlePointReport",produces = "application/json")
    public Result<SingleReport> getSinglePointReport(
            @RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true) DataReportParam dataReportParam) {
        SingleReport singleReport = singlePointDetailReportService.getSinglePointReport(dataReportParam);
        return Result.success(singleReport);
    }

    @ApiOperation(value = "暂态事件分类统计报表 - 暂态事件综合统计")
    @PostMapping(value = "/transient/eventsReport",produces = "application/json")
    public PageResult<List<TransientEvent>> getTransientEvents(
            @RequestBody @Valid @ApiParam(name = "reportParam", value = "参数", required = true)ReportParam reportParam) {
        return dataReportService.getTransientEvents(reportParam);
    }

    @ApiOperation(value = "暂态事件分类统计报表 - 按管理单位统计查询")
    @PostMapping(value = "/transient/managementUnit",produces = "application/json")
    public PageResult<List<ManagementUnit>> getManagementUnit(
            @RequestBody @Valid @ApiParam(name = "reportParam", value = "参数", required = true)ReportParam reportParam) {
        return dataReportService.getManagementUnit(reportParam);
    }

    @ApiOperation(value = "暂态事件分类统计报表 - 按变电站统计查询")
    @PostMapping(value = "/transient/substationReport",produces = "application/json")
    public PageResult<List<SubstationReport>> getSubstationReport(
            @RequestBody @Valid @ApiParam(name = "reportParam", value = "参数", required = true)ReportParam reportParam) {
        return dataReportService.getSubstationReport(reportParam);
    }

    @ApiOperation(value = "暂态事件分类统计报表 - 按监测点统计查询")
    @PostMapping(value = "/transient/monitorReport",produces = "application/json")
    public PageResult<List<MonitorReport>> getMonitorReport(
            @RequestBody @Valid @ApiParam(name = "reportParam", value = "参数", required = true)ReportParam reportParam) {
        return dataReportService.getMonitorReport(reportParam);
    }

    @ApiOperation(value = "导出主网指标统计分析 - 单点详细月报表")
    @PostMapping(value = "/steady/exportSinglePointDetailReport")
    public void exportSinglePointDetailReport(@RequestBody @Valid @ApiParam(name = "ExportDataReportDetailParam", value = "参数", required = true) PointReportParam pointReportParam, HttpServletResponse response) {
        singlePointDetailReportService.exportSinglePointDetailReport(pointReportParam, response);
    }

    @ApiOperation(value = "导出主网指标统计分析 - 单点数据报表")
    @PostMapping(value = "/steady/exportSinglePointReport")
    public void exportSinglePointReport(@RequestBody @Valid @ApiParam(name = "exportDataReportParam", value = "参数", required = true)PointReportParam dataReportParam, HttpServletResponse response) {
        singlePointDetailReportService.exportSinglePointReport(dataReportParam, response);
    }

    @ApiOperation(value = "导出暂态事件分类统计报表 - 暂态事件综合统计")
    @PostMapping(value = "/transient/exportEventsReport")
    public void exportEventsReport(@RequestBody @Valid @ApiParam(name = "exportReportParam", value = "参数", required = true)ExportReportParam exportReportParam, HttpServletResponse response) {
        dataReportService.exportEventsReport(exportReportParam, response);
    }

    @ApiOperation(value = "导出暂态事件分类统计报表 - 按管理单位统计")
    @PostMapping(value = "/transient/exportManagementUnit")
    public void exportManagementUnit(@RequestBody @Valid @ApiParam(name = "exportReportParam", value = "参数", required = true)ExportReportParam exportReportParam, HttpServletResponse response) {
        dataReportService.exportManagementUnit(exportReportParam, response);
    }

    @ApiOperation(value = "导出暂态事件分类统计报表 - 按变电站统计")
    @PostMapping(value = "/transient/exportSubstationReport")
    public void exportSubstationReport(@RequestBody @Valid @ApiParam(name = "exportReportParam", value = "参数", required = true)ExportReportParam exportReportParam, HttpServletResponse response) {
        dataReportService.exportSubstationReport(exportReportParam, response);
    }

    @ApiOperation(value = "导出暂态事件分类统计报表 - 按监测点统计")
    @PostMapping(value = "/transient/exportMonitorReport")
    public void exportMonitorReport(@RequestBody @Valid @ApiParam(name = "exportReportParam", value = "参数", required = true)ExportReportParam exportReportParam, HttpServletResponse response) {
        dataReportService.exportMonitorReport(exportReportParam, response);
    }


    @ApiOperation(value = "提交稳态自定义报表计算")
    @PostMapping(value = "/steady/submitCustomPlan")
    public Result<String> submitCustomPlan(@RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true)DataReportParam dataReportParam) {
        dataReportService.submitCustomPlan(dataReportParam);
        return Result.success("后台开始计算，请到自定义统计报表中查看进度");
    }

    @ApiOperation(value = "稳态自定义报表记录查询")
    @PostMapping(value = "/steady/customPlan")
    public PageResult<List<SteadyCustomPlanVo>> getCustomPlans(@RequestBody @Valid CustomReportParam customReportParam) {
        return dataReportService.getCustomPlans(customReportParam);

    }

    @ApiOperation(value = "导出稳态自定义报表记录")
    @PostMapping(value = "/steady/exportCustomPlan")
    public void exportCustomPlan(HttpServletResponse response, @RequestBody @Valid ExportCustomReportParam exportCustomReportParam) throws IOException {
        dataReportService.exportCustomPlan(response, exportCustomReportParam);

    }

    @ApiOperation(value = "稳态自定义报表文件下载")
    @PostMapping(value = "/steady/customFile/download")
    public void downloadCustomFile(HttpServletResponse response, Long id) {
        dataReportService.downloadCustomFile(response, id);

    }

    @ApiOperation(value = "删除稳态自定义报表文件")
    @DeleteMapping(value = "/steady/deleteCustomPlan")
    public Result deleteCustomPlan(
            @NotNull(message = "自定义报表不存在") @RequestParam("id") Long id) {
        dataReportService.deleteCustomPlan(id);
        return Result.success("删除成功");
    }


    @ApiOperation(value = "预览报表数据")
    @PostMapping(value = "/steady/previewReport")
    public Result<CustomSingleReport> previewReport(@RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true)CustomReportOverViewParam param) {
        return Result.success(dataReportService.previewReport(param));
    }


    @ApiOperation(value = "稳态分析-稳态指标数据报表-监测点数据报表")
    @PostMapping(value = "/steady/indicatorDataReport/line")
    public PageResult<List<LineDataReport>> indicatorDataReportLine(@RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true) LineReportParam lineReportParam) {
        return dataReportService.indicatorDataReportLine(lineReportParam);
    }

    @ApiOperation(value = "导出 稳态分析-稳态指标数据报表-监测点数据报表")
    @PostMapping(value = "/steady/exportIndicatorDataReport/line")
    public void exportSinglePointReport(@RequestBody @Valid @ApiParam(name = "exportDataReportParam", value = "参数", required = true)LineReportParam lineReportParam, HttpServletResponse response) {
        dataReportService.exportIndicatorDataReportLine(lineReportParam, response);
    }
}

package com.cet.pq.common.model;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlarmPoints {

	private String alarmKey;
	private String alias;
	private String memo;
	private int reserved1;
	private int reserved2;
	private int schemeId;
	private List<ActionsBean> actions;

	public String getAlarmKey() {
		return alarmKey;
	}

	public void setAlarmKey(String alarmKey) {
		this.alarmKey = alarmKey;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public int getReserved1() {
		return reserved1;
	}

	public void setReserved1(int reserved1) {
		this.reserved1 = reserved1;
	}

	public int getReserved2() {
		return reserved2;
	}

	public void setReserved2(int reserved2) {
		this.reserved2 = reserved2;
	}

	public int getSchemeId() {
		return schemeId;
	}

	public void setSchemeId(int schemeId) {
		this.schemeId = schemeId;
	}

	public List<ActionsBean> getActions() {
		return actions;
	}

	public void setActions(List<ActionsBean> actions) {
		this.actions = actions;
	}

	public static class ActionsBean {
		/**
		 * actionParams : string actionType : 0 repeatDay : 0 retries : 0
		 * retriesInterval : 0 schemeId : 0 schemeItemId : 0 timer : string triggerType
		 * : 0
		 */

		private String actionParams;
		private int actionType;
		private int repeatDay;
		private int retries;
		private int retriesInterval;
		private int schemeId;
		private int schemeItemId;
		private String timer;
		private int triggerType;

		public String getActionParams() {
			return actionParams;
		}

		public void setActionParams(String actionParams) {
			this.actionParams = actionParams;
		}

		public int getActionType() {
			return actionType;
		}

		public void setActionType(int actionType) {
			this.actionType = actionType;
		}

		public int getRepeatDay() {
			return repeatDay;
		}

		public void setRepeatDay(int repeatDay) {
			this.repeatDay = repeatDay;
		}

		public int getRetries() {
			return retries;
		}

		public void setRetries(int retries) {
			this.retries = retries;
		}

		public int getRetriesInterval() {
			return retriesInterval;
		}

		public void setRetriesInterval(int retriesInterval) {
			this.retriesInterval = retriesInterval;
		}

		public int getSchemeId() {
			return schemeId;
		}

		public void setSchemeId(int schemeId) {
			this.schemeId = schemeId;
		}

		public int getSchemeItemId() {
			return schemeItemId;
		}

		public void setSchemeItemId(int schemeItemId) {
			this.schemeItemId = schemeItemId;
		}

		public String getTimer() {
			return timer;
		}

		public void setTimer(String timer) {
			this.timer = timer;
		}

		public int getTriggerType() {
			return triggerType;
		}

		public void setTriggerType(int triggerType) {
			this.triggerType = triggerType;
		}
	}
}

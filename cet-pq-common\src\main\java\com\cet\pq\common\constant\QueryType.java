package com.cet.pq.common.constant;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class QueryType {
	/**
	 * 无
	 */
	public static final int CURRENT = 0;
	/**
	 * 同比
	 */
	public static final int YEAR_ON_YEAR = 1;
	/**
	 * 环比
	 */
	public static final int MONTH_ON_MONTH = 2;
	/**
	 * 同环比
	 */
	public static final int YEAR_AND_MONTH = 3;

	/**
	 * 自定义查询
	 */
	public static final int CUSTOM_QUERY = 20;

	/**
	 * 年
	 */
	public static final String YEAR = "year";
	/**
	 * 月
	 */
	public static final String MONTH = "month";
	/**
	 * 今年
	 */
	public static final String CURRENT_YEAR = "current_year";
	/**
	 * 明年
	 */
	public static final String NEXT_YEAR = "next_year";
	/**
	 * 去年
	 */
	public static final String LAST_YEAR = "last_year";
	/**
	 * 分析类型-同环比
	 */
	public static final int THB = 1;
	/**
	 * 分析类型-分时
	 */
	public static final int TIME_SHARED = 2;
	/**
	 * 分析类型-节点对比
	 */
	public static final int NODES_COMPARE = 3;

}

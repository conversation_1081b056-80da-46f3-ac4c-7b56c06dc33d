package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.common.BaseParam;
import com.cet.pq.anlysis.model.complianceevaluation.*;
import com.cet.pq.anlysis.model.excel.ExportEvaluationParam;
import com.cet.pq.anlysis.model.excel.ExportSteadyLineEvaluationParam;
import com.cet.pq.anlysis.model.qualtityevaluation.*;
import com.cet.pq.anlysis.service.PowerQualityEvaluationIndexService;
import com.cet.pq.common.model.Result;
import com.cet.pq.anlysis.model.complianceevaluation.*;
import com.cet.pq.pqservice.model.qualityevaluation.EvaluationType;
import com.cet.pq.pqservice.service.PlatformAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Description 电能质量评价指标
 * <AUTHOR>
 * @Date 2020/8/6 15:33
 */
@Api(value="PowerQualityEvaluationIndexController",tags={"指标管理 - 电能质量评价指标"})
@RestController
@RequestMapping(value = "/pq/v1/powerQualityEvaluationIndex")
public class PowerQualityEvaluationIndexController {
    @Autowired
    private PowerQualityEvaluationIndexService powerQualityEvaluationIndexService;

    /**
     * 各管理单位符合性评价
     * @return
     */
    @ApiOperation(value = "1.各管理单位符合性评价")
    @PostMapping(value = "/getComplianceEvaluation",produces = "application/json")
    public Result<List<ComplianceEvaluationResult>> getComplianceEvaluation(@RequestBody ComprehensiveEvaluationTableParam complianceEvaluationParam){
        List<ComplianceEvaluationResult> resultList=powerQualityEvaluationIndexService.getComplianceEvaluation(complianceEvaluationParam);
        return Result.success(resultList);
    }
    /**
     * 2.各电压等级符合性评价
     * @return
     */
    @ApiOperation(value = "2.各电压等级符合性评价")
    @PostMapping(value = "/getVoltageLevelEvaluation",produces = "application/json")
    public Result<List<ComplianceEvaluationResult>> getVoltageLevelEvaluation(@RequestBody ComprehensiveEvaluationTableParam complianceEvaluationParam){
        List<ComplianceEvaluationResult> resultList=powerQualityEvaluationIndexService.getVoltageLevelEvaluation(complianceEvaluationParam);
        return Result.success(resultList);
    }
    /**
     * 3.管理单位-电压等级
     * @return
     */
    @ApiOperation(value = "3.管理单位-电压等级")
    @PostMapping(value = "/getManagementUnitVoltage",produces = "application/json")
    public Result<List<ManageEvaluationResult>> getManagementUnitVoltage(@RequestBody ComprehensiveEvaluationTableParam complianceEvaluationParam){
        List<ManageEvaluationResult> resultList= powerQualityEvaluationIndexService.getManagementUnitVoltage(complianceEvaluationParam);
        return Result.success(resultList);
    }
    /**
     * 4.符合性明细表-区域符合性评价
     * @return
     */
    @ApiOperation(value = "4.符合性明细表-区域符合性评价")
    @PostMapping(value = "/comprehensiveEvaluation",produces = "application/json")
    public Result comprehensiveEvaluation(@RequestBody ComprehensiveEvaluationTableParam param){
        List<RegionalEvaluationResult> resultList=powerQualityEvaluationIndexService.comprehensiveEvaluation(param);
        return Result.success(resultList);
    }
    /**
     * 符合性明细表-监测点符合性评价
     * @return
     */
    @ApiOperation(value = "5.符合性明细表-监测点符合性评价")
    @PostMapping(value = "/monitorFitEvalueTable",produces = "application/json")
    public Result monitorFitEvalueTable(@RequestBody ComprehensiveEvaluationTableParam param){
        List<MonitorFitEvaluationResult> resultList=powerQualityEvaluationIndexService.monitorFitEvalueTable(param);
        return Result.success(resultList);
    }

    @ApiOperation(value = "6.导出符合性评价指标")
    @PostMapping(value = "/exportEvaluation")
    public void exportEvaluation(@RequestBody ExportEvaluationParam exportEvaluationParam, HttpServletResponse response) {
        powerQualityEvaluationIndexService.exportEvaluation(exportEvaluationParam, response);
    }

    /**
     * 水平评价指标-稳态电能质量水平评价-区域电能质量水平评价
     * @return
     */
    @ApiOperation(value = "水平评价指标-稳态电能质量水平评价-区域电能质量水平评价")
    @PostMapping(value = "/getRoSteadyQualityEvaluation",produces = "application/json")
    public Result<List<QualityEvaluation<RoQualityEvaluation>>> getRoSteadyQualityEvaluation(
            @RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true) BaseParam baseParam){
        return Result.success(powerQualityEvaluationIndexService.getRoSteadyQualityEvaluation(baseParam));
    }

    /**
     * 水平评价指标-稳态电能质量水平评价-监测点电能质量水平评价
     * @return
     */
    @ApiOperation(value = "水平评价指标-稳态电能质量水平评价-监测点电能质量水平评价")
    @PostMapping(value = "/getLineSteadyQualityEvaluation",produces = "application/json")
    public Result<List<QualityEvaluation<LineQualityEvaluation>>> getLineSteadyQualityEvaluation(
            @RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true) BaseParam baseParam){
        return Result.success(powerQualityEvaluationIndexService.getLineSteadyQualityEvaluation(baseParam));
    }

    /**
     * 电压暂降和短时中断统计
     * @return
     */
    @ApiOperation(value = "电压暂降和短时中断统计")
    @PostMapping(value = "/getTimeAndVoltage",produces = "application/json")
    public Result<List<TransientVoltageEvaluationResult>> getDropTimeAndVoltage(
            @RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true) BaseParam baseParam){
        return Result.success(powerQualityEvaluationIndexService.getDropTimeAndVoltage(baseParam));
    }

    @ApiOperation(value = "导出水平评价指标-稳态电能质量水平评价")
    @PostMapping("/exportSteadyLineEvaluation")
    public void exportSteadyLineEvaluation(@RequestBody ExportSteadyLineEvaluationParam exportSteadyLineEvaluationParam, HttpServletResponse response) {
        powerQualityEvaluationIndexService.exportSteadyLineEvaluation(exportSteadyLineEvaluationParam, response);
    }

    @ApiOperation(value = "导出水平评价指标-暂态电能质量水平评价")
    @PostMapping("/exportTimeAndVoltage")
    public void exportTimeAndVoltage(@RequestBody ExportSteadyLineEvaluationParam exportSteadyLineEvaluationParam, HttpServletResponse response) {
        powerQualityEvaluationIndexService.exportTimeAndVoltage(exportSteadyLineEvaluationParam, response);
    }
}

package com.cet.pq.common.model.offlinetest;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OffLineVariationEvent {

	private String modelLabel = "offlinevariationevent";
	
	private Long id;
	
	private Integer confirmeventstatus;
	private String description;
	private Long duration;
	private Long eventtime;
	private Long importtime;
	private Boolean isminuteextreme;
	private Double magnitude;
	private Double nominalvoltage;
	private Long offlinetest_id;
	private String operator;
	//电压暂态事件类型
	private Integer pqvariationeventtype;
	private String remark;
	//容忍区事件类型
	private Integer toleranceband;
	//故障方向
	private Integer transientfaultdirection;
	private Long updatetime;
	private Long v1duration;
	private Double v1magnitude;
	private Boolean v1trigger;
	private Long v2duration;
	private Double v2magnitude;
	private Boolean v2trigger;
	private Long v3duration;
	private Double v3magnitude;
	private Boolean v3trigger;
	private Boolean valid;
	private Long waveformlogtime;

}
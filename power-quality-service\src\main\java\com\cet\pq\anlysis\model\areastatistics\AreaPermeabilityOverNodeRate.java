package com.cet.pq.anlysis.model.areastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/6 13:57
 * @description
 **/
@Data
@NoArgsConstructor
public class AreaPermeabilityOverNodeRate {
    @ApiModelProperty("渗透率范围")
    private String permeabilityrange;
    @ApiModelProperty("闪变超标占比")
    private Double flinkOverRate;
    @ApiModelProperty("频率超标占比")
    private Double FrequencyOverRate;
    @ApiModelProperty("电压超标占比")
    private Double VoltageOverRate;
    @ApiModelProperty("三相超标占比")
    private Double ThreePhaseOverRate;
    @ApiModelProperty("谐波超标占比")
    private Double ThdOverRate;

    public AreaPermeabilityOverNodeRate(String permeabilityrange) {
        this.permeabilityrange = permeabilityrange;
    }
}

package com.cet.pq.common.enums;

import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 监测对象类型
 *
 * <AUTHOR>
 */
public enum ObjectTypeEnum {
	//监测对象类型
	// 一、其他
	acrossprovince(1, "跨省计量关口点", 1100, "跨省计量关口点", 1100),
	firstsubstation(1, "各类重要变电站", 2100, "一类变电站", 2101),
	secondsubstation(1, "各类重要变电站", 2100, "二类变电站", 2102),
	thirdsubstation(1, "各类重要变电站", 2100, "三类变电站", 2103),
	fourthsubstation(1, "各类重要变电站", 2100, "四类变电站", 2104),
	converterstation(1, "受换流站影响的变电站", 2200, "受换流站影响的变电站", 2200),
	exceedsubstation(1, "超标较严重或用户投诉较多的变电站", 2501, "超标较严重或用户投诉较多的变电站", 2501),
	transformerside(1, "换流站", 1200, "换流变网侧出线", 1201),
	filterline(1, "换流站", 1200, "滤波大组出线", 1202),
	acconverter(1, "换流站", 1200, "换流站交流出线", 1203),
	other_power_plants(1, "其他发电厂", 1403, "其他发电厂", 1403),
	// 二、干扰源 所属干扰源台账
	electrifiedrailway(2, "电气化铁路//牵引站", 1300, "电气化铁路", 1300),
	wind_farm(2, "风电场", 1401, "风电场", 1401),
	power_station(2, "光伏电站", 1402, "光伏电站", 1402),
	storage_station(2, "储能站", 1404, "储能站", 1404),//干扰源用户台账-储能站台账（4.0新增）energystoragestation
	charging_station(2, "充电站", 1405, "充电站", 1405),//干扰源用户台账-充电站台账（4.0新增）chargingstation
	Electricheatingload(2, "其他非线性负荷", 2300, "电加热负荷", 2301),
	roll(2, "其他非线性负荷", 2300, "轧机", 2303),
	rail_transit(2, "其他非线性负荷", 2300, "轨道交通", 2304),
	electric_station(2, "其他非线性负荷", 2300, "电动汽车充电站", 2305),
	weldload(2, "其他非线性负荷", 2300, "电焊负荷", 2306),
	liftload(2, "其他非线性负荷", 2300, "起重负荷", 2308),
	electrolyticload(2, "其他非线性负荷", 2300, "电解负荷", 2309),
	frequencycontrolload(2, "其他非线性负荷", 2300, "变频调速负荷", 2312),
	commermunicCivilelec(2, "其他非线性负荷", 2300, "商业/市政/民用/电子通讯负荷", 2315),
	facts_substation(2, "其他非线性负荷",2300, "装设FACTS设备的变电站", 1501),
	facts_converter(2, "其他非线性负荷", 2300, "装设FACTS设备的换流站", 1502),
	capacitor(2, "其他非线性负荷", 2300, "电容器", 2503),
	// 三、敏感重要用户 所属敏感重要用户台账
	semiconductor(3, "敏感/重要/高危用户", 2400, "半导体制造", 2401),
	precision(3, "敏感/重要/高危用户", 2400, "精密加工", 2402),
	party_government(3, "敏感/重要/高危用户", 2400, "党政机关", 2403),
	hospital(3, "敏感/重要/高危用户", 2400, "医院", 2404),
	transportation_hub(3, "敏感/重要/高危用户", 2400, "交通枢纽", 2405),
	airport(3, "敏感/重要/高危用户", 2400, "机场", 2406),
	finance(3, "敏感/重要/高危用户", 2400, "金融", 2407),
	datacentor(3, "敏感/重要/高危用户", 2400, "数据中心", 2408),
	hazardous_chemicals(3, "敏感/重要/高危用户", 2400, "危险化学品", 2409),
	manufacture_and_explosive(3, "敏感/重要/高危用户", 2400, "易燃易爆品制造", 2410),
	large_venues(3, "敏感/重要/高危用户", 2400, "大型场馆", 2411),
	high_manufacture(3, "敏感/重要/高危用户", 2400, "高端制造", 2412),
	public_service(3, "敏感/重要/高危用户", 2400, "公共服务", 2413),
	genetic_testing(3, "敏感/重要/高危用户", 2400, "基因检测", 2414),
	hotel(3, "敏感/重要/高危用户", 2400, "酒店", 2415),
	commercial_centre(3, "敏感/重要/高危用户", 2400, "商业中心", 2416),
	biotechnology(3, "敏感/重要/高危用户", 2400, "生物技术", 2417),
	communication(3, "敏感/重要/高危用户", 2400, "通信", 2418),
	critical_infrastructure(3, "敏感/重要/高危用户", 2400, "重要基础设施", 2419),
	jewelry_manufacturing(3, "敏感/重要/高危用户", 2400, "珠宝制造", 2420),
	other(3, "敏感/重要/高危用户", 2400, "其它", 2421),
	;

	//类别
	private Integer category;
	// 小组
	private Integer group;
	private String groupname;
	// 名称
	private String name;
	// 值
	private Integer value;

	private ObjectTypeEnum(Integer category, String groupname, Integer group, String name, Integer value) {
		this.category = category;
		this.group = group;
		this.groupname = groupname;
		this.name = name;
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public Integer getValue() {
		return value;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public Integer getGroup() {
		return group;
	}

	public void setGroup(Integer group) {
		this.group = group;
	}

	public String getGroupname() {
		return groupname;
	}

	public void setGroupname(String groupname) {
		this.groupname = groupname;
	}
	/**
	 * 获取其他类型枚举
	 * @return
	 */
	public static List<ObjectTypeEnum> OtherObjectTypeValues() {
		List<ObjectTypeEnum> values = new ArrayList<>();
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if(objectTypeEnum.getCategory().equals(1)){
				values.add(objectTypeEnum);
			}
		}
		return values;
	}
	/**
	 * 获取其他干扰源枚举
	 * @return
	 */
	public static List<ObjectTypeEnum> OtherInterferenceValues() {
		// 电铁风电光伏冶炼
		List<Integer> besides = Arrays.asList(1300,1401,1402,2301);
		List<ObjectTypeEnum> values = new ArrayList<>();
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if((objectTypeEnum.getCategory().equals(2) || objectTypeEnum.getCategory().equals(3))
					&& !besides.contains(objectTypeEnum.getValue())){
				values.add(objectTypeEnum);
			}
		}
		return values;
	}
	/**
	 * 获取干扰源枚举
	 * @return
	 */
	public static List<ObjectTypeEnum> InterferenceValues() {
		List<ObjectTypeEnum> values = new ArrayList<>();
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if(objectTypeEnum.getCategory().equals(2)){
				values.add(objectTypeEnum);
			}
		}
		return values;
	}
	/**
	 * 获取敏感用户枚举
	 * @return
	 */
	public static List<ObjectTypeEnum> SensitiveUserValues() {
		List<ObjectTypeEnum> values = new ArrayList<>();
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if(objectTypeEnum.getCategory().equals(3)){
				values.add(objectTypeEnum);
			}
		}
		return values;
	}

	public static Integer getValueByName(String name) {
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if (name.equals(objectTypeEnum.getName())) {
				return objectTypeEnum.getValue();
			}
		}
		throw new CommonManagerException("检测对象类型值无效");
	}

	/**
	 * 判断监测对象类型获取分组
	 * @param name
	 * @return
	 */
	public static Integer getCategoryByName(String name) {
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if (name.equals(objectTypeEnum.getName())) {
				return objectTypeEnum.getCategory();
			}
		}
		throw new CommonManagerException("检测对象类型值无效");
	}

	public static Integer getValueByNameNoThrows(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if (objectTypeEnum.getName().equals(name)) {
				return objectTypeEnum.getValue();
			}
		}
		return 0;
	}

	public static String getOtherInterferenceTextByIdNoThrows(Integer value) {
		if(Objects.isNull(value)){
			return StringUtils.EMPTY;
		}
		// 电铁风电光伏冶炼
		List<Integer> values = Arrays.asList(1300,1401,1402,2301);
		// 其他干扰源，不包括电铁风电光伏冶炼
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if (objectTypeEnum.getCategory().equals(2) && !values.contains(value) && Objects.equals(objectTypeEnum.getValue(),value)) {
				return objectTypeEnum.getName();
			}
		}
		return StringUtils.EMPTY;
	}
	public static String getNameByValue(Integer value) {
		if(Objects.isNull(value)){
			return StringUtils.EMPTY;
		}
		for (ObjectTypeEnum objectTypeEnum : ObjectTypeEnum.values()) {
			if (objectTypeEnum.getValue().equals(value)) {
				return objectTypeEnum.getName();
			}
		}
		return StringUtils.EMPTY;
	}

	/**
	 * 敏感重要用户类型id
	 * @param name
	 * @return
	 */
	public static Integer getSensitiveUserId(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (ObjectTypeEnum objectTypeEnum : values()) {
			if (objectTypeEnum.category.equals(3) && objectTypeEnum.getName().equals(name)) {
				return objectTypeEnum.getValue();
			}
		}
		return 0;
	}
	/**
	 * 敏感重要用户类型id 不抛null
	 * @param name
	 * @return
	 */
	public static Integer getSensitiveUserIdNoThrows(String name) {
		if(StringUtils.isBlank(name)){
			return 0;
		}
		for (ObjectTypeEnum objectTypeEnum : values()) {
			if (objectTypeEnum.category.equals(3) && objectTypeEnum.getName().equals(name)) {
				return objectTypeEnum.getValue();
			}
		}
		return 0;
	}

	/**
	 * 获取敏感重要用户名称
	 * @param id
	 * @return
	 */
	public static String getSensitiveUserName(Integer id) {
		if(Objects.isNull(id)){
			return StringUtils.EMPTY;
		}
		for (ObjectTypeEnum objectTypeEnum  : values()) {
			if (objectTypeEnum.category.equals(3) && objectTypeEnum.getValue().equals(id)) {
				return objectTypeEnum.getName();
			}
		}
		return StringUtils.EMPTY;
	}

	/**
	 * 根据监测对象值获取存储模型
	 *
	 * @param value
	 * @return
	 */
	public static String getObjectLabel(Integer value) {
		String modelLabel = null;
		for (ObjectTypeEnum objectTypeEnum  : values()) {
			if (objectTypeEnum.getValue().equals(value) && !objectTypeEnum.category.equals(1)) {
				switch (value) {
					case 1300:
						modelLabel = TableName.ELECTRICRAILWAY;
						break;
					case 1402:
						modelLabel = TableName.PHOTOVOLTAICSTATION;
						break;
					case 1401:
						modelLabel = TableName.WINDPOWERSTATION;
						break;
					case 2301:
						modelLabel = TableName.SMELTLOAD;
						break;
					case 1404:
						modelLabel = TableName.ENERGY_STORAGE_STATION;
						break;
					case 1405:
						modelLabel = TableName.CHARGING_STATION;
						break;
					default:
						modelLabel = TableName.INTERFERENCESOURCE;
						break;
				}
			}
		}
		return modelLabel;
	}

	public static Integer getIdByNameImport(String name) {
		if (StringUtils.isBlank(name)) {
			return null;
		}
		for (ObjectTypeEnum objectTypeEnum : values()) {
			if (name.equals(objectTypeEnum.getName())) {
				return objectTypeEnum.getValue();
			}
		}
		return -1;
	}

	public static Integer getIdByTextNoThrows(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (ObjectTypeEnum typeEnum : ObjectTypeEnum.values()) {
			if (typeEnum.getName().trim().equalsIgnoreCase(name)) {
				return typeEnum.getValue();
			}
		}
		return null;
	}

	public static String getNameByIdNoThrows(Integer value) {
		if(Objects.isNull(value)){
			return StringUtils.EMPTY;
		}
		for (ObjectTypeEnum objectTypeEnum  : values()) {
			if (objectTypeEnum.category.equals(2) && objectTypeEnum.getValue().equals(value)) {
				return objectTypeEnum.getName();
			}
		}
		return StringUtils.EMPTY;
	}

	public static String getObjectNameByLabel(String objectType) {
		if (TableName.ELECTRICRAILWAY.equals(objectType)) {
			return "电气化铁路";
		} else if (TableName.PHOTOVOLTAICSTATION.equals(objectType)) {
			return "光伏电站";
		} else if (TableName.WINDPOWERSTATION.equals(objectType)) {
			return "风电场";
		}  else if (TableName.SMELTLOAD.equals(objectType)) {
			return "冶炼负荷";
		}  else if (TableName.ENERGY_STORAGE_STATION.equals(objectType)) {
			return "储能站";
		}  else if (TableName.CHARGING_STATION.equals(objectType)) {
			return "充电站";
		} else {
			return "其他干扰源";
		}
	}

}

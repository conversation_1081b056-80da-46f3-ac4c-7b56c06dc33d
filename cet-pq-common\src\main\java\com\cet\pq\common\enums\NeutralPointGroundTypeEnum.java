package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 中性点接地方式枚举类
 *
 * <AUTHOR>
 */
public enum NeutralPointGroundTypeEnum {
    //中性点接地方式枚举类

    directground("直接接地-3PT", 1),
    nonlinearground("经非线性电阻接地-消谐器", 2),
    mutualinductor("经互感器接地-4PT", 3);

    private String name;
    private Integer value;

    private NeutralPointGroundTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }


    public Integer getValue() {
        return value;
    }


    public static Integer getValueByName(String name) {
        for (NeutralPointGroundTypeEnum neutralPointGroundTypeEnum : NeutralPointGroundTypeEnum.values()) {
            if (name.equals(neutralPointGroundTypeEnum.getName())) {
                return neutralPointGroundTypeEnum.getValue();
            }
        }
        throw new CommonManagerException("中性点接地方式值异常");
    }

    public static Integer getValueByNameNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (NeutralPointGroundTypeEnum neutralPointGroundTypeEnum : NeutralPointGroundTypeEnum.values()) {
            if (name.equals(neutralPointGroundTypeEnum.getName())) {
                return neutralPointGroundTypeEnum.getValue();
            }
        }
        return 0;
    }

    public static String getNameByValueNoThrows(Integer value) {
        for (NeutralPointGroundTypeEnum neutralPointGroundTypeEnum : NeutralPointGroundTypeEnum.values()) {
            if (neutralPointGroundTypeEnum.getValue().equals(value)) {
                return neutralPointGroundTypeEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer getNameByValueImport(String name) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (NeutralPointGroundTypeEnum neutralPointGroundTypeEnum : NeutralPointGroundTypeEnum.values()) {
            if (name.equals(neutralPointGroundTypeEnum.getName())) {
                return neutralPointGroundTypeEnum.getValue();
            }
        }
        return -1;
    }
}

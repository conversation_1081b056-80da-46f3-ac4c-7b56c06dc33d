package com.cet.pq.common.utils;

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.model.CustomSSLSocketFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.*;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpClientParams;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.httpclient.protocol.Protocol;
import org.apache.commons.lang3.StringUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLEncoder;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName ：HttpUtils
 * @date ：Created in 2021/2/18 8:47
 * @description：http帮助类
 */
@Slf4j
public class HttpUtils {
    /**
     * http超时时间
     */
    private static int timeOut = 10000;

    /**
     * http请求返回信息
     */
    private static String message;

    public static String getMessage() {
        return message;
    }

    public static void setMessage(String message) {
        HttpUtils.message = message;
    }

    /**
     * description:  get请求
     * create time: 2021/2/18 8:56
     */
    public static String get(String url, String encoding, Map<String, String> params, Map<String, String> headers) {
        HttpClient httClient = new HttpClient(new HttpClientParams(), new SimpleHttpConnectionManager(true));
        StringBuffer content = new StringBuffer();
        HttpMethod method = initHttpMethod(url, encoding, params, headers, httClient);
        try {
            httClient.executeMethod(method);
            String respString = method!=null? method.getResponseBodyAsString():null;
            content.append(respString);
        } catch (ConnectTimeoutException e) {
            message = "Connect Timeout:" + url;
            log.error(message);
        } catch (SocketTimeoutException e) {
            message = "Socket Timeout:" + url;
            log.error(message);
        } catch (IOException e) {
            message = e.toString();
            log.error(message);
        } finally {
            if(method != null){
                method.releaseConnection();
            }
        }
        return content.toString();
    }

    /**
     * description:  post请求
     * create time: 2021/2/18 8:56
     */
    public static String post(String url, String contentType, String sendData, String encoding, Map<String, String> params, Map<String, String> headers) {
        HttpClient httClient = new HttpClient(new HttpClientParams(), new SimpleHttpConnectionManager(true));
        StringBuffer content = new StringBuffer();
        try {
            url = setParams(url, params);
//            log.info("PostUrl"+url);
        } catch (Exception e) {
            log.error("http init method error.", e);
        }


        PostMethod method = new PostMethod(url);
        // 设置url连接超时
        httClient.getHttpConnectionManager().getParams().setConnectionTimeout(
                timeOut);
        // 设置读取数据超时
        httClient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
        // 这两行解决 too many open files问题
        httClient.getParams().setBooleanParameter(
                "http.protocol.expect-continue", false);
        // 解决Httpclient远程请求所造成Socket没有释放
        method.addRequestHeader("Connection", "close");
        // 添加额外的Header
        if (headers != null && headers.size() > 0) {
            Set<String> keys = headers.keySet();
            for (String key : keys) {
                method.addRequestHeader(key, headers.get(key));
            }
        }
        method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,
                encoding);
        InputStream in = null;
        BufferedReader buffer = null;
        //失败重试3次
        postRetry(url, contentType, sendData, encoding, httClient, content, method, in, buffer);
        return content.toString();
    }

    private static void postRetry(String url, String contentType, String sendData, String encoding, HttpClient httClient, StringBuffer content, PostMethod method, InputStream in, BufferedReader buffer) {
        for (int i = 0; i < 1; i++) {
            try {
                RequestEntity entity = new StringRequestEntity(sendData,
                        contentType, encoding);
                log.info("PostRequestEntity:"+ entity);
                method.setRequestEntity(entity);
                httClient.executeMethod(method);
                int code = method.getStatusCode();
                log.info("httpClient method statusLine:{},  ", method.getStatusLine());
                if (code == HttpStatus.SC_OK) {
                    in = method.getResponseBodyAsStream();
                    buffer = new BufferedReader(new InputStreamReader(in, encoding));
                    for (String tempstr = ""; (tempstr = buffer.readLine()) != null; ) {
                        content.append(tempstr);
                    }
                    break;
                }
            } catch (Exception e) {
                log.error("http post error. url {}.", url, e);
            } finally {
                try {
                    if (buffer != null) {
                        buffer.close();
                    }
                    if (in != null) {
                        in.close();
                    }
                } catch (IOException e) {
                    log.error("http post error", e);
                }
                method.releaseConnection();
            }
        }
    }

    /**
     * post请求，支持https绕过证书检查
     * @param url
     * @param contentType
     * @param sendData
     * @param encoding
     * @param params
     * @param headers
     * @return
     */
    public static String postUnsecure(String url, String contentType, String sendData, String encoding, Map<String, String> params, Map<String, String> headers) {
        HttpClient httpClient = new HttpClient(new HttpClientParams(), new SimpleHttpConnectionManager(true));
        StringBuffer content = new StringBuffer();

        try {
            url = setParams(url, params);
        } catch (Exception e) {
            log.error("http init method error.", e);
        }

        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };
        try {
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
            Protocol.registerProtocol("https", new Protocol("https", new CustomSSLSocketFactory(sslContext), 443));
        } catch (Exception e) {
            log.error("Error while setting SSL context", e);
        }

        PostMethod method = new PostMethod(url);

        httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(timeOut);
        httpClient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
        httpClient.getParams().setBooleanParameter("http.protocol.expect-continue", false);
        method.addRequestHeader("Connection", "close");

        if (headers != null && headers.size() > 0) {
            Set<String> keys = headers.keySet();
            for (String key : keys) {
                method.addRequestHeader(key, headers.get(key));
            }
        }

        method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, encoding);

        InputStream in = null;
        BufferedReader buffer = null;

        postRetry(url, contentType, sendData, encoding, httpClient, content, method, in, buffer);

        return content.toString();
    }

    public static String doGet(String httpUrl) {
        HttpURLConnection connection = null;
        InputStream is = null;
        BufferedReader br = null;
        StringBuffer result = new StringBuffer();
        try {
            URL url = new URL(httpUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setReadTimeout(timeOut);
            connection.connect();
            if (connection.getResponseCode() == CommonConstant.NUMBER_TWOHUNDRED) {
                is = connection.getInputStream();
                if (null != is) {
                    br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                    String temp = null;
                    while (null != (temp = br.readLine())) {
                        result.append(temp);
                    }
                }
            }
        } catch (Exception e) {
            log.error("http post error.", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (br != null) {
                    br.close();
                }
                if (connection != null) {
                    connection.disconnect();
                }
            } catch (IOException e) {
                log.error("http get error", e);
            }
        }
        return result.toString();
    }

    /**
     * description: 初始化http
     * create time: 2021/2/18 8:57
     */
    private static HttpMethod initHttpMethod(String url, String encoding,
                                             Map<String, String> params, Map<String, String> headers, HttpClient httClient) {
        try {
            url = setParams(url, params);

            HttpMethod method = new GetMethod(url);
            // 设置url连接超时
            httClient.getHttpConnectionManager().getParams().setConnectionTimeout(
                    timeOut);
            // 设置读取数据超时
            httClient.getHttpConnectionManager().getParams().setSoTimeout(timeOut);
            // 这两行解决 too many open files问题
            httClient.getParams().setBooleanParameter(
                    "http.protocol.expect-continue", false);
            // 解决Httpclient远程请求所造成Socket没有释放
            method.addRequestHeader("Connection", "close");
            // 添加额外的Header
            if (headers != null && headers.size() > 0) {
                Set<String> keys = headers.keySet();
                for (String key : keys) {
                    method.addRequestHeader(key, headers.get(key));
                }
            }
            method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,
                    encoding);
            return method;
        } catch (Exception e) {
            log.error("http init method error.", e);
            return null;
        }
    }

    private static String setParams(String url, Map<String, String> params) throws UnsupportedEncodingException {
        // 添加param
        if (params != null && params.size() > 0) {
            StringBuilder sbQuery = new StringBuilder();
            for (Map.Entry<String, String> query : params.entrySet()) {
                if (0 < params.size()) {
                    sbQuery.append("&");
                }
                if (StringUtils.isBlank(query.getKey()) && !StringUtils.isBlank(query.getValue())) {
                    sbQuery.append(query.getValue());
                }
                if (!StringUtils.isBlank(query.getKey())) {
                    sbQuery.append(query.getKey());
                    if (!StringUtils.isBlank(query.getValue())) {
                        sbQuery.append("=");
                        sbQuery.append(URLEncoder.encode(query.getValue(), "utf-8"));
                    }
                }
            }
            if (0 < sbQuery.length()) {
                url = url + "?" + sbQuery;
            }
        }
        return url;
    }
}

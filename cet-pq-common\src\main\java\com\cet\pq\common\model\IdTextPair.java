package com.cet.pq.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019年10月23日
 * 
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdTextPair implements Comparable<IdTextPair> {
	
	private Integer id;
	
	private String text;

	@Override
	public int compareTo(IdTextPair o) {
		if (this.id >= o.id) {
			return 1;
		}
		return -1;
	}
}

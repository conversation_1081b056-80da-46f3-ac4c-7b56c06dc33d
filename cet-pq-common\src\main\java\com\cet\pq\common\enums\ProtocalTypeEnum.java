package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;

/**
 * 规约类型枚举类
 * <AUTHOR>
 */
public enum ProtocalTypeEnum {
	//规约类型枚举类

	dlt645(1),modbustcp(2),modbus<PERSON>e(3),iec104(4),iec101(5),iec61850(6);
	
	private Integer value;

	private ProtocalTypeEnum(Integer value) {
		this.value = value;
	}

	public Integer getValue() {
		return value;
	}


	public static Integer getProtocal(String name) {
		for (ProtocalTypeEnum protocalTypeEnum : ProtocalTypeEnum.values()) {
			if(name.equals(protocalTypeEnum.name())){
				return protocalTypeEnum.getValue();
			}
		}
		throw new CommonManagerException("规约类型值无效");
	}
}

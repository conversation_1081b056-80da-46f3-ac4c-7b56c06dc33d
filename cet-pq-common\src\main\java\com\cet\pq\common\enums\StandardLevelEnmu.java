package com.cet.pq.common.enums;


import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description: 电能质量标准库标准级别
 * @date 2023/9/14
 */
public enum StandardLevelEnmu {
    NationalStandard(1,"国家标准"),
    IndustryStandard(2,"行业标准"),
    EnterpriseStandard(3,"企业标准"),
    OtherStandard(4,"其他标准"),
    ;

    private Integer id;
    private String text;

    StandardLevelEnmu(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (StandardLevelEnmu enmu : StandardLevelEnmu.values()) {
            if (enmu.id.equals(id)) {
                return enmu.text;
            }
        }
        return StringUtils.EMPTY;
    }
}

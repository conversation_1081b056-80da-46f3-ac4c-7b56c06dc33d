# 定时任务处理规则

#### 1，定时任务使用@EnableScheduling，@Scheduled设置定时记录周期cron

// 主配置类启用定时任务
@EnableScheduling  // 必须显式启用
public class SchedulerConfig { 
}

// 任务类配置
@Component
public class OrderCleanupTask {
    @Scheduled(cron = "${order.cleanup.cron:0 0 2 * * ?}")  // 必须使用配置化cron
    public void cleanExpiredOrders() {
        // ...
    }
}

- **必须** 使用 `@EnableScheduling` 显式启用定时任务
- **必须** 通过 `@Scheduled(cron = "${property}")` 从配置文件读取 cron 表达式



#### 2，定时记录添加开关控制

//application.yml

schedule:
  enabled: true  # 全局总开关
  tasks:
    orderCleanup: true  # 子任务开关



- **必须** 在配置文件中设置全局开关 `schedule.enabled`
- **必须** 为每个任务设置独立开关（如 `schedule.tasks.orderCleanup`）
- 任务执行前校验开关状态：

@Scheduled(cron = "...")
public void task() {
  if (!scheduleConfig.isOrderCleanupEnabled()) return; // 开关检查
  // 业务逻辑
}



#### 3, 异常处理与日志

@Scheduled(cron = "...")
public void scheduledTask() {
    try {
        // 业务逻辑
    } catch (Exception ex) {
        log.error("定时任务执行失败: {}", ex.getMessage());  // 必须记录完整错误
    }
}

- **必须** 捕获所有异常避免任务终止
- **必须** 记录错误日志并包含任务标识



# 异常处理规则

#### 1，异常处理强制统一使用CommonManagerException，不要使用RuntimeException



// 自定义业务异常
public class CommonManagerException extends RuntimeException {
    private final String errorCode;  // 业务错误码
    

    public CommonManagerException(String code, String message) {
        super(message);
        this.errorCode = code;
    }
}

// 使用示例
throw new CommonManagerException("ORDER_404", "订单不存在");



#### 2，**异常传递原则**

@Service
public class OrderService {
    public Order getOrder(Long id) {
        Order order = repository.findById(id);
        if (order == null) {
            // 必须使用自定义异常
            throw new CommonManagerException("ORDER_404", "订单不存在");
        }
        return order;
    }
}

- **禁止** 抛出原生异常（如 `NullPointerException`）
- **必须** 使用 `CommonManagerException` 封装业务错误
- **推荐** 在数据异常，违反核心业务规则，一些验证操作异常判断等场景时，抛出业务异常


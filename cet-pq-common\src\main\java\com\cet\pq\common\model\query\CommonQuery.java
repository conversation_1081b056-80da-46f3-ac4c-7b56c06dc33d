package com.cet.pq.common.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/05/18 13:41
 */
@ApiModel(value = "通用查询参数")
@Data
public class CommonQuery {

    @ApiModelProperty(required = true, value = "模型id", example = "1")
    private Long modelId;

    @ApiModelProperty(required = true, value = "模型名称", example = "line")
    private String modelLabel;

    @ApiModelProperty(required = true, value = "开始时间", example = "11111")
    private Long startTime;

    @ApiModelProperty(required = true, value = "结束时间", example = "22222")
    private Long endTime;

    @ApiModelProperty(value = "起始页", example = "1")
    private Integer pageNum;

    @ApiModelProperty(value = "每页行数", example = "10")
    private Integer pageSize;

    @ApiModelProperty(value = "时间周期", example = "14")
    private Integer aggregationCycle;

    @ApiModelProperty(required = true, value = "监测点编号", example = "1")
    private Long monitorId;

}

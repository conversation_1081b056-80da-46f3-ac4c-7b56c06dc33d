package com.cet.pq.common.model.peccore;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/27 14:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PecDeviceBatchVo {
    /**
     * 场站
     */
    private Long stationId;
    /**
     * 通道
     */
    private Long channelId;
    /**
     * 设备id集合
     */
    private List<Long> deviceIds;
    /**
     * 设备类型
     */
    private Integer metertype;
    /**
     * 指示是否进行覆盖
     */
    private Boolean isOverride;
}

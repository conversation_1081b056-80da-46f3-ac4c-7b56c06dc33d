package com.cet.pq.anlysis.model.excel;

import com.cet.pq.anlysis.model.common.CommonParam;
import com.cet.pq.common.model.excel.ExportParameter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/3/8 14:16
 */
@Data
public class ExportInterferenceByCompanyParam extends CommonParam {
    @ApiModelProperty(value="voltagerLevel",name="voltagerLevel")
    private Integer voltagerLevel;
    @ApiModelProperty(value="interferenceSourseType",name="interferenceSourseType")
    private String interferenceSourseType;

    private ExportParameter exportParameter;
}

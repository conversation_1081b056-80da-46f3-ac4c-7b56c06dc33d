package com.cet.pq.common.cca;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月25日 10:03
 */
public class TimeLine {
    /**
     * 时间间隔3分钟
     */
    public static int INTERVAL_3 = 3;
    /**
     * 时间间隔5分钟
     */
    public static int INTERVAL_5 = 5;

    /**
     * 时间间隔15分钟
     */
    public static int INTERVAL_15 = 15;

    /**
     * 时间间隔3分钟的时间西安
     */
    public static List<LocalTime> TIME_LINE_INTERVAL_3;

    /**
     * 时间间隔5分钟的时间西安
     */
    public static List<LocalTime> TIME_LINE_INTERVAL_5;

    /**
     * 时间间隔15分钟的时间
     */
    public static List<LocalTime> TIME_LINE_INTERVAL_15;

    static {
        TIME_LINE_INTERVAL_3 = create(INTERVAL_3);
        TIME_LINE_INTERVAL_5 = create(INTERVAL_5);
        TIME_LINE_INTERVAL_15 = create(INTERVAL_15);
    }


    /**
     * 创建时间线
     * @param interval 时间间隔 分钟
     * @return
     */
    public static List<LocalTime> create(int interval) {
        //创建时间线 一天
        LocalTime startTime = LocalTime.of(0, 0);
        LocalTime endTime = startTime.plusMinutes(-interval);
        List<LocalTime> timeLine = new ArrayList<>();
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        while (startTime.compareTo(endTime) < 0) {
            timeLine.add(startTime);
            startTime = startTime.plusMinutes(interval);
        }
        timeLine.add(endTime);
        return timeLine;
    }
}

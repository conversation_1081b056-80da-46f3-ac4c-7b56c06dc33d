# 项目规则

1，后端开发代码生成时，**必须** 严格遵守代码规则 .lingma/rules/backend-coding.md

2，请学习模型服务，设备数据服务的代码，以便在后续使用这些依赖生成代码时，生成正确的代码内容  .lingma/rules/database-access_pq.md

3，**必须** 严格遵守项目层级结构，按照项目层级结构输出相关的文件 .lingma/rules/project-structure.md

4，在日志和对象定义生成注释方面，**必须** 严格遵守注释全部规范 .lingma/rules/project-define.md

5，如果涉及定时任务处理和异常处理，**必须**严格遵守 .lingma/rules/project-exception_task.md 全部规范

6，在生成单元测试方面，**必须** 严格遵守定时记录单元测试全部规则 .lingma/rules/project-unit-test.md

7，在生成国际化方面，**必须** 严格遵守国际化的全部规则 .lingma/rules/i18n-development-standards.md

8，业务在生成代码时，**必须** 严格遵守 .cursor\rules\project_special_pq.md 全部规范


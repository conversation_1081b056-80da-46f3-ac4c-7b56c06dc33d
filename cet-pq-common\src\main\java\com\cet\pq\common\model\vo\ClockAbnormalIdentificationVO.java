package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 14:21
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ClockAbnormalIdentificationVO {

    /**
     * 序号
     */
    private Integer orderNum;

    /**
     * 监测点id
     */
    private Long monitorId;

    /**
     * 监测点编号
     */
    private String monitorCode;

    /**
     * 监测点名称
     */
    private String monitorName;

    /**
     * 时钟状态: 0：时钟正常，1：时钟异常
     */
    private String clockStatus;

    /**
     * 异常原因: 0：其他，1：对时偏差，2：---
     */
    private String errorReason;


}

package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/06/08 16:29
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LongTermFlickerPart1VO {

    /**
     * 监测时间
     */
    private Long logtime;

    /**
     * 长时闪变值
     */
    private Double value;

    /**
     * 相别类型: 0：A，1：B，2：C
     */
    private Integer phaseType;


}

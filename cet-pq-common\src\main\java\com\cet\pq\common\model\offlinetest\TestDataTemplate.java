package com.cet.pq.common.model.offlinetest;

import com.cet.pq.common.constant.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/6/7 14:33
 * @Description 测试数据导入模板记录表
 */
@Data
public class TestDataTemplate {

    private String modelLabel = TableName.TEST_DATA_TEMPLATE;
    private Long id;
    private String name;
    @JsonProperty("logtime")
    private Long logTime;

}

package com.cet.pq.common.encode.sm4;

/**
 * <AUTHOR>
 * @ClassName CodeUtil
 * @Description TODO
 * @Date 2020/10/14
 */

import java.util.*;

public class CodeUtil {

    public static byte[] StringToSmBuffer(String strInput) {
        return StringToSmBuffer(strInput, 16);
    }

    //C# TO JAVA CONVERTER NOTE: Java does not support optional parameters. Overloaded method(s) are created above:
//ORIGINAL LINE: public static byte[] StringToSmBuffer(string strInput, int length = 16)
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
    public static byte[] StringToSmBuffer(String strInput, int length) {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: byte[] buffer = new byte[length];
        byte[] buffer = new byte[length];
        for (int i = 0; i < buffer.length; i++) {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: byte b = 0;
            byte b = 0;
            if (i < strInput.length()) {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: b = (byte)strInput[i];
                b = (byte) strInput.charAt(i);
            }
            buffer[i] = b;
        }
        return buffer;
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: public static string SmBufferToString(byte[] buffer)
    public static String SmBufferToString(byte[] buffer) {
        StringBuilder sb = new StringBuilder();
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: foreach (var b in buffer)
        for (byte b : buffer) {
            sb.append((char) b);
        }
        String strResult = sb.toString();
        return strResult;
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: public static string BufferToHexString(byte[] buffer)
    public static String BufferToHexString(byte[] buffer) {
        StringBuilder sbHex = new StringBuilder();
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: foreach (var b in buffer)
        for (byte b : buffer) {
            String strHex = NumberTo0xString(b, 2);
            sbHex.append(strHex);
        }
        String strResult = sbHex.toString();
        return strResult;
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: public static byte[] HexStringToBuffer(string strHexValues)
    public static byte[] HexStringToBuffer(String strHexValues) {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: List<byte> byteList = new List<byte>();
        ArrayList<Byte> byteList = new ArrayList<Byte>();
        int index = 0;
        while (index < strHexValues.length()) {
            int subCount = 2;
            int remainCount = strHexValues.length() - index;
            if (remainCount < 2) {
                subCount = remainCount;
            }

            String strHex = strHexValues.substring(index, index + subCount);
            index += subCount;
            byteList.add(HexStringToByte(strHex));
        }
        return ByteLists.toArray(byteList);
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: public static byte HexStringToByte(string str0xValue)
    public static byte HexStringToByte(String str0xValue) {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: return Convert.ToByte(str0xValue.Trim(), 16);
        return Byte.parseByte(str0xValue.trim(), 16);
    }

    /**
     * 格式化为16进制的字符串
     *
     * <typeparam name="T">整数类型</typeparam>
     *
     * @param value
     * @return
     */

    public static <T> String NumberTo0xString(T value) {
        return NumberTo0xString(value, 0);
    }

    //C# TO JAVA CONVERTER NOTE: Java does not support optional parameters. Overloaded method(s) are created above:
//ORIGINAL LINE: public static string NumberTo0xString<T>(T value, int totalWidth = 0)
    public static <T> String NumberTo0xString(T value, int totalWidth) {
        String strValue = String.format("%1$X", value);
        String result = FormatToTotalWidth(totalWidth, strValue, '0');
        return result;
    }

    /**
     * 格式化为指定的数字位数
     *
     * @param totalWidth
     * @param strValue
     * @return
     */
    private static String FormatToTotalWidth(int totalWidth, String strValue, char paddingChar) {
        String result = "";
        if (totalWidth == 0) {
            result = strValue;
        } else {
            if (strValue.length() >= totalWidth) //从最后一位开始向前totalWidth个字符
            {
                result = StringHelper.substring(strValue, strValue.length() - totalWidth, totalWidth);
            } else {
                result = StringHelper.padLeft(strValue, totalWidth, paddingChar);
            }
        }
        return result;
    }
}







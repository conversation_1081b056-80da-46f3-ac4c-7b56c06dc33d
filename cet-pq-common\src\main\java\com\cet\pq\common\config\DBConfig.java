package com.cet.pq.common.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Description: mybatis sqlSessionFactory和sqlSessionTemplate 配置 如果数据源没配置，则该项也不会配置
 * @date 2021/3/26 14:22
 */
@Configuration
@MapperScan(basePackages = {"com.cet.pq.common.mapper"},sqlSessionFactoryRef = "sqlSessionFactory")
@ConditionalOnBean(name = "dataSourceConfig")
public class DBConfig {

    @Autowired
    @Qualifier("aggdata")
    private DataSource dataSource;

    @Bean
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(dataSource);
        return factoryBean.getObject();
    }

    @Bean
    public SqlSessionTemplate sqlSessionTemplate() throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory());
    }


}

package com.cet.pq.common.feign;

import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.auth.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "cloud-auth-service")
public interface AuthService {



	@PostMapping("/cloud/api/auth/v1/user")
	Result<Long> saveUser(@RequestBody UserAddParam user);

	/**
	 * 获取用户信息
	 *
	 * @param userId
	 * @return
	 */
	@GetMapping("/cloud/api/auth/v1/user")
	Result<User> getUser(@RequestHeader(value = "User-ID") Long userId);

	/**
	 * 获取用户关联的用户组
	 *
	 * @param id
	 * @param tenantId
	 * @return
	 */
	@GetMapping("/cloud/api/auth/v1/relativeUserGroups")
	Result<List<UserGroup>> getRelativeUserGroups(@RequestParam("id") Long id, @RequestParam("tenantId") Long tenantId);

	/**
	 * 根据用户名查询用户
	 * @param name
	 * @return
	 */
	@GetMapping("/cloud/api/auth/v1/user/queryByName")
	Result<User> queryUserByName(@RequestParam("name") String name);

	/**
	 * 安全模式的登陆接口(双token)
	 * @param loginInfo
	 * @return
	 */
	@PostMapping("/cloud/api/auth/v1/loginSecurityDoubleToken")
	Result<LoginRes> loginSecurityDoubleToken(@RequestBody LoginInfo loginInfo);

	/**
	 * 用户登录表接口
	 * @param loginInfo
	 * @return
	 */
	@PostMapping("/cloud/api/auth/v1/login")
	Result<LoginRes> login(@RequestBody LoginInfo loginInfo);

	/**
	 * 查询用户登录状态
	 * @param userId
	 * @return
	 */
	@GetMapping("/cloud/api/auth/v1/userLoginState")
	Result<UserLoginState> getUserLoginState(@RequestParam("userId") Long userId);

	/**
	 * 刷新用户的token
	 * @param loginInfo
	 * @return
	 */
	@PostMapping("/cloud/api/auth/v1/refreshToken")
	Result<String> refreshToken(@RequestBody LoginInfo loginInfo);


	@GetMapping("/cloud/api/auth/v1/role/{id}")
	Result<Role> getRoleById(@PathVariable("id")Long id);

	@GetMapping("/cloud/api/auth/v1/usergroups")
	Result<List<UserGroup>> getAllUserGroups(@RequestParam("tenantId") Long tenantId);

	@GetMapping("/cloud/api/auth/v1/usergroup/queryById")
	Result<UserGroup> getUserGroupById(@RequestParam("userGroupId") Long userGroupId);

	@GetMapping("/cloud/api/auth/v1/user/password/info/queryByUserId")
	Result<PasswordInfo> queryByUserId(@RequestParam("userId") Long userId);

	@GetMapping("/cloud/api/auth/v1/user/queryByName")
	Result<User> getUserByName(@RequestParam("name") String name);

	@PutMapping("/cloud/api/auth/v1/usergroup")
	Result<Long> updateUserGroup(@RequestBody UserGroup userGroup);

	@GetMapping("/cloud/api/auth/v1/users")
	Result<List<User>> listUsers(@RequestParam("tenantId") Long var1,
										  @RequestParam(value = "includeRole",required = false,defaultValue = "true") Boolean var2);

}

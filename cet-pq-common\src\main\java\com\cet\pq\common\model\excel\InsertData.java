package com.cet.pq.common.model.excel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.CellStyle;

import java.util.LinkedList;

/**
 * 从坐标单元格开始横向插入数据
 * <AUTHOR>
 * @date 2021/3/3 14:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InsertData {
    /**
     * 行号，从0起，第一个单元格坐标：（0，0）
     */
    private int row;
    /**
     * 列号
     */
    private int col;
    /**
     * 数据列表, 在单元格坐标：（row，col）按data填数据
     */
    private LinkedList<Object> data;
    /**
     * 单元格格式，扩展为data都采用该格式
     */
    CellStyle style;

    public InsertData(int row, int col, LinkedList<Object> data) {
        this.row = row;
        this.col = col;
        this.data = data;
    }

    public InsertData(int row, int col) {
        this.row = row;
        this.col = col;
    }
}

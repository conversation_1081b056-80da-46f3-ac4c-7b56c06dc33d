package com.cet.pq.common.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName BaseVo
 * @Description 基础模型，只包括这三个字段，请勿新增字段
 * <AUTHOR>
 * @Date 2020/2/24 14:17
 */
@Data
@NoArgsConstructor
public class BaseVo {
    private long id;
    private String modelLabel;
    private String name;

    public BaseVo(long id) {
        this.id = id;
    }

    public BaseVo(long id, String modelLabel) {
        this.id = id;
        this.modelLabel = modelLabel;
    }

    public BaseVo(long id, String modelLabel, String name) {
        this.id = id;
        this.modelLabel = modelLabel;
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof BaseVo)) {
            return false;
        }
        BaseVo baseVo = (BaseVo) o;
        return getId() == baseVo.getId() &&
                Objects.equals(getModelLabel(), baseVo.getModelLabel());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getModelLabel());
    }
}

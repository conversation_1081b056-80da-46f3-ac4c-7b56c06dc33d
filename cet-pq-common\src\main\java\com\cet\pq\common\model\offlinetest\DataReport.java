package com.cet.pq.common.model.offlinetest;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataReport {

    private Long dataId;
    private String unit;

    @JsonProperty("offlinetest_id")
    private Long offlinetestId;

    private Long teststarttime;

    private Long testendtime;

    private Double maxValue;

    private Double minValue;

    private Double avgValue;

    private Double cpValue;

    private Boolean isoverlimit;

    private Double overlimitdegree;

    private Double lowlimit;

    private Double uplimit;

    private List<Integer> overValues;

}

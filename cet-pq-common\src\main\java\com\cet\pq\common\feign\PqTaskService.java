package com.cet.pq.common.feign;

import com.cet.pq.common.model.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Description:
 * @Author: Wisdom
 * @Since 2024/6/20 14:01
 **/
@FeignClient(value = "pq-task-service")
public interface PqTaskService {
    /**
     * 巡检
     * @return
     */
    @GetMapping("/pq/daily-inspection/line-inspection")
    Result doLineInspection();

}

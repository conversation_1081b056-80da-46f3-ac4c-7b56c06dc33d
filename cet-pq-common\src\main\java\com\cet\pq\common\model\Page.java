package com.cet.pq.common.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页
 * 
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Page {
	private Integer index;
	private Integer limit;

	@Override
	public String toString() {
		return "Page{" +
				"index=" + index +
				", limit=" + limit +
				'}';
	}

	public Page(Long index, Long limit) {
		this.index = new BigDecimal(index).intValue();
		this.limit = new BigDecimal(limit).intValue();
	}
}

package com.cet.pq.common.model.objective.oilfield;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 水井
 */
@Data
public class WaterWell {
	private long id;
	/**
	 * 井编码
	 */
	@JsonProperty("wellcode")
	private String wellCode;
	/**
	 * 名称
	 */
	private String name;

	private Double ia;

	private Double ib;

	private Double ic;

	private Double kwh;

	private Long pf;

	private Long state;

	private Long vab;

	private Long van;

	private Long vbc;

	private Long vbn;

	private Long vca;

	private Long vcn;

	private long cumulativeFlow;

	private long instantaneousFlow;

	private Double oilPressure;

	private Double pupinputPressure;

	private Double pupoutPressure;

	private Double sleevePressure;
}

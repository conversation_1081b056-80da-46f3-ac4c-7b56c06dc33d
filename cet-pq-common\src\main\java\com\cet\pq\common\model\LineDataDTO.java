package com.cet.pq.common.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 线性数据
 * <AUTHOR>
 * @date 2022年10月18日 14:48
 */
@Getter
@Setter
@Data
@EqualsAndHashCode
public class LineDataDTO {
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @ExcelProperty(index = 0)
    private LocalDateTime time;

    @ExcelProperty(index = 1)
    private Double value;

    public LineDataDTO() {
    }

    public LineDataDTO(LocalDateTime time, Double value) {
       this.time = time;
       this.value = value;
    }


}
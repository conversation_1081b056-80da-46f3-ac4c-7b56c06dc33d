package com.cet.pq.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Slf4j
@Configuration
@EnableAsync
public class ExecutorConfig {

    @Value("${task.queue.corePoolSize}")
    private int corePoolSize;
    //线程池维护线程的最大数量
    @Value("${task.queue.maxPoolSize}")
    private int maxPoolSize;
    //缓存队列
    @Value("${task.queue.queueCapacity}")
    private int queueCapacity;
    //允许的空闲时间
    @Value("${task.queue.keepAlive}")
    private int keepAlive;

    @Bean(name = "threadPoolExecutor")
    public ThreadPoolTaskExecutor taskQueueExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(corePoolSize);
        //配置最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //配置队列大小
        executor.setQueueCapacity(queueCapacity);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("pq-");
        //允许空闲时间
        executor.setKeepAliveSeconds(keepAlive);

        // 设置拒绝策略：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }


    /*@Bean
    public Executor asyncServiceExecutor() {
        log.info("start asyncServiceExecutor");
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(corePoolSize);
        //配置最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        //配置队列大小
        executor.setQueueCapacity(queueCapacity);
        //允许空闲时间
        executor.setKeepAliveSeconds(keepAlive);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("pq-");
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        //执行初始化
        executor.initialize();
        return executor;
    }*/

}

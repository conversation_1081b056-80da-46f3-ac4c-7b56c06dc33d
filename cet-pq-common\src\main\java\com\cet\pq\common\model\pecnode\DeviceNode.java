/**
  * Copyright 2020 bejson.com 
  */
package com.cet.pq.common.model.pecnode;
import lombok.Data;

import java.util.List;

/**
 * 实现对设备节点属性的复刻
 *
 * <AUTHOR>
 */
@Data
public class DeviceNode {

    private int channelId;
    private int circuitId;
    private ComParam comParam;
    private int communicationId;
    private CommunicationParam communicationParam;
    private List<CustomInfParamList> customInfParamList;
    private DeviceAccessParam deviceAccessParam;
    private String nodeName;
    private String portType;
    private Long deviceId;


}
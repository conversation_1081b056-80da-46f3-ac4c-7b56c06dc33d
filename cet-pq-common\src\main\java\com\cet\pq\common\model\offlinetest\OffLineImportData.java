package com.cet.pq.common.model.offlinetest;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
@NoArgsConstructor
public class OffLineImportData {

	private String modelLabel = "offlineimportdata";

	private Long id;

	private Long firsttime;
	private String importer;
	private String importfilepath;
	private Integer importfiletype;
	private Long importtime;
	private Long lasttime;
	@JsonProperty("offlinetest_id")
	private Long offlinetestId;
	//普测计划ID
	@JsonProperty("generalplan_id")
	private Long generalplanId;
	//工作证明
//	@JsonProperty("workprove")
	private String workprove;
	//母线ID
	@JsonProperty("busbarsection_id")
	private Long busBarSectionId;
	//文件审核状态(0:未通过;1:已通过;)
	@JsonProperty("status")
	private Integer status;
	/**
	 * 报告地址
	 */
	@JsonProperty("reportpath")
	private String reportPath;

	public OffLineImportData(Integer status,Long firsttime, String importer, String importfilepath, Integer importfiletype, Long importtime, Long lasttime,
			Long offlinetestId) {
		this.firsttime = firsttime;
		this.importer = importer;
		this.importfilepath = importfilepath;
		this.importfiletype = importfiletype;
		this.importtime = importtime;
		this.lasttime = lasttime;
		this.offlinetestId = offlinetestId;
		this.status = status;
	}

	public OffLineImportData(Integer status,Long firsttime, String importer, String importfilepath, Integer importfiletype, Long importtime, Long lasttime,
			Long offlinetestId, Long generalplanId) {
		this.firsttime = firsttime;
		this.importer = importer;
		this.importfilepath = importfilepath;
		this.importfiletype = importfiletype;
		this.importtime = importtime;
		this.lasttime = lasttime;
		this.offlinetestId = offlinetestId;
		this.generalplanId = generalplanId;
		this.status = status;
	}

}
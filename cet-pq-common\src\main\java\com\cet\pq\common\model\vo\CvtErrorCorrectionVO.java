package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/05/18 10:57
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CvtErrorCorrectionVO {

    /**
     * CVT影响的超标谐波次数
     */
    private int[] excessiveHarmonicCount;

    /**
     * 谐波测量失真识别上部分
     */
    private List<CvtErrorCorrectionPart1VO> part1;

    /**
     * 中间判断结果
     */
    private String result;

    /**
     * 谐波测量失真识别下部分
     */
    private Map<Integer, List<CvtErrorCorrectionPart2VO>> part2;


}

package com.cet.pq.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName EnumCoopStatus
 * @Description 数值运算的枚举
 * <AUTHOR>
 * @Date 2020/2/28 13:57
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum EnumOperationType {
	/**
	 * 减
	 */
	SUBTRACT(1, "减"),
	/**
	 * 加
	 */
	ADD(2, "加"),
	/**
	 * 乘
	 */
	MULTIPLICATION(3, "乘"),
	/**
	 * 除
	 */
	DIVISION(4, "除"),
	/**
	 * 增长率
	 */
	RATE(5, "增长率");

	/**
	 * 枚举值
	 */
	private int id;
	/**
	 * 描述
	 */
	private String text;

	/**
	 * 根据id获取text信息
	 *
	 * @param id
	 * @return
	 */
	public static String textOfId(int id){
		for (EnumOperationType status : EnumOperationType.values()) {
			if (status.id == id) {
				return status.text;
			}
		}
		throw new IllegalArgumentException("错误房间类型");
	}
}

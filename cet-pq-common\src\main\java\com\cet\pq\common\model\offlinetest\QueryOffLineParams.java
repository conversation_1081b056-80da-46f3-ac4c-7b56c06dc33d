package com.cet.pq.common.model.offlinetest;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/12/11 17:27
 * @Description 普测对象台账查询参数
 */
@Data
public class QueryOffLineParams {
    private String modelLabel;
    private Long modelId;
    private Integer pageSize;
    private Integer pageNum;
    @ApiModelProperty(value = "变电站电压等级")
    private Integer substationVoltclass;
    @ApiModelProperty(value = "普测对象电压等级")
    private Integer voltclass;
    @ApiModelProperty(value = "普测对象类型")
    private Integer testobjecttype;
}

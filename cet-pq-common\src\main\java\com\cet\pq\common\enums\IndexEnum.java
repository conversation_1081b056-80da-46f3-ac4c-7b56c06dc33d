package com.cet.pq.common.enums;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/23 9:43
 */
public enum IndexEnum {
    //物理量类型
    HZ(6097,"频率偏差"),VT(6094,"电压偏差"),
    TU(6069,"三相电压不平衡度"),HV(6032,"电压总谐波畸变率"),
    IV(6900,"间谐波电压"),FV(6082,"闪变"),
    HI(6600,"谐波电流"),NC(6037,"负序电流");

    private int code;
    private String name;
    private IndexEnum(int code,String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
}

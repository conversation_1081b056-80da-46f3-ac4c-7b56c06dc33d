package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.service.ReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2022/10/20 11:39
 * @Description
 */
@Api(value = "/pq/v1/report", tags = "电能质量报告下载")
@RestController
@RequestMapping(value = "/pq/v1/report")
public class ReportController {

    @Autowired
    private ReportService reportService;

    @ApiOperation(value = "电能质量月报")
    @GetMapping("/month")
    public void monthPqReportExport(HttpServletResponse response, @RequestParam @ApiParam(value = "startTime") Long startTime,
                                    @RequestParam @ApiParam(value = "endTime") Long endTime) {
        reportService.monthPqReportExport(response, startTime, endTime);
    }
}

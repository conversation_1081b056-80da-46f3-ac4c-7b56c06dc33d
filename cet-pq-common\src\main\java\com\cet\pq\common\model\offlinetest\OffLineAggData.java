package com.cet.pq.common.model.offlinetest;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2020-09-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OffLineAggData {

	private String modelLabel = "offlineaggdata";
	private Long id;
	//物理量模板id
	@JsonProperty("quantitymaptemplate_id")
	private Long quantitymaptemplateId;
	//数据值
	private Double value;
	//是否超标
	private Boolean isoverlimit;
	//超标程度
	private Double overlimitdegree;
	//聚合周期
	private Integer aggregationcycle;
	//聚合方式
	private Integer aggregationtype;
	//扩展值
	private String extendvalue;
	//导入时间
	private Long importtime;
	//测试开始时间
	private Long teststarttime;
	//测试结束时间
	private Long testendtime;
	//测点id
	@JsonProperty("offlinetest_id")
	private Long offlinetestId;
	//参数id
	private Long dataid;
	//母线id
	private Long busbarsection_id;
	
	public OffLineAggData(Long quantitymaptemplateId, Double value, Boolean isoverlimit, Integer aggregationcycle, Integer aggregationtype, String extendvalue,
			Long importtime, Long teststarttime, Long testendtime, Long offlinetestId, Long dataid) {
		this.quantitymaptemplateId = quantitymaptemplateId;
		this.value = value;
		this.isoverlimit = isoverlimit;
		this.aggregationcycle = aggregationcycle;
		this.aggregationtype = aggregationtype;
		this.extendvalue = extendvalue;
		this.importtime = importtime;
		this.teststarttime = teststarttime;
		this.testendtime = testendtime;
		this.offlinetestId = offlinetestId;
		this.dataid = dataid;
	}
	
}
package com.cet.pq.common.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/05/26 18:47
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "区域监测装置查询界面参数")
@Data
public class MonitorAreaDeviceQuery extends CommonQuery{

    /**
     * 装置状态：0:接线正常，1：接线异常
     */
    @ApiModelProperty(required = true, value = "装置状态", example = "0")
    private Integer deviceStatus;


}

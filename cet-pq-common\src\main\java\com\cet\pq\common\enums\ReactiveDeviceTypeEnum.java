package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/11/9 17:16
 * @Description
 */
public enum ReactiveDeviceTypeEnum {

    //电站类型
    electricreactor("电抗器", 1),
    powercapacitor("电力电容器", 2),
    couplingcapacitor("耦合电容器", 3),
;


    private String name;
    private Integer value;

    private ReactiveDeviceTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static Integer getReactiveDeviceType(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (ReactiveDeviceTypeEnum reactiveDeviceTypeEnum : ReactiveDeviceTypeEnum.values()) {
            if (reactiveDeviceTypeEnum.getName().equals(name)) {
                return reactiveDeviceTypeEnum.getValue();
            }
        }
        throw new CommonManagerException("无功设备类型值无效");
    }



    public static String getReactiveDeviceNameByValueNoThrows(Integer value) {
        for (ReactiveDeviceTypeEnum reactiveDeviceTypeEnum : ReactiveDeviceTypeEnum.values()) {
            if (reactiveDeviceTypeEnum.value.equals(value)) {
                return reactiveDeviceTypeEnum.name;
            }
        }
        return StringUtils.EMPTY;
    }
}

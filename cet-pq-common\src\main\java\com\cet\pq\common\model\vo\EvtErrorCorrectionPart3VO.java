package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 10:12
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class EvtErrorCorrectionPart3VO {

    /**
     * 数值类型：0：A相修正前3次谐波含有率95%概率大值，1：B相修正前3次谐波含有率95%概率大值，2：C相修正前3次谐波含有率95%概率大值，
     *          3：A相修正后3次谐波含有率95%概率大值，4：B相修正后3次谐波含有率95%概率大值，5：C相修正后3次谐波含有率95%概率大值
     */
    private Integer valueType;

    /**
     * 数值
     */
    private Double value;


}

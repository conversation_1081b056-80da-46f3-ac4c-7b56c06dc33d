package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 	装置电源电压枚举
 * <AUTHOR>
 */
public enum RatedvoltageEnum {
	//装置电源电压枚举

	vdc240("240vdc",1),
	v400("400v",2),
	kv10("10kv",3),
	kv20("20kv",4),
	kv35("35kv",5),
	kv110("110kv",6),
	dc("dc",7),
	kv6_6("6.6kv",8),
	kv6_9("6.9kv",9),
	kv33("33kv",10),
	kv66("66kv",11),
	kv220("220kv",12),
	kv330("330kv",13),
	kv500("550kv",14),
	kc1000("1000kv",15);
	
	private String key;
	private Integer value;
	
	private RatedvoltageEnum(String key, Integer value) {
		this.key = key;
		this.value = value;
	}

	public static Integer getRatedvoltage(String key) {
		if (StringUtils.isEmpty(key)) {
			return null;
		}
		for (RatedvoltageEnum ratedvoltageEnum : RatedvoltageEnum.values()) {
			String rakey = ratedvoltageEnum.key;
			if(rakey.equalsIgnoreCase(key)) {
				return ratedvoltageEnum.value;
			}
		}
		throw new CommonManagerException("电源电压等级枚举值转化异常");
	} 
	
}

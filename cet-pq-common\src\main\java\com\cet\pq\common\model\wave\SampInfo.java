package com.cet.pq.common.model.wave;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 采样率信息
 * @date 2021/6/17 10:37
 */
@Data
public class SampInfo {
    /**
     * 采样率，单位Hz，必填；实数；1～32字符宽度；可使用标准浮点数记法
     */
    private Float samp;

    /**
     * Samp采样率第一个采样的序号；必填；整数；1～9999999999
     */
    private Integer beginSamp;

    /**
     * Samp采样率最后一次采样的序号；必填；整数；1～9999999999
     */
    private Integer endSamp;

}

package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum ImportanceTypeEnum {
    //变电站类型

    first("一类变电站", 1), second("二类变电站", 2), third("三类变电站", 3), fourth("四类变电站", 4);

    private String name;
    private Integer value;

    private ImportanceTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static Integer getImportance(String name) {
        for (ImportanceTypeEnum importanceTypeEnum : ImportanceTypeEnum.values()) {
            if (name.equals(importanceTypeEnum.getName())) {
                return importanceTypeEnum.getValue();
            }
        }
        throw new CommonManagerException("电站重要级别值无效");
    }

    public static Integer getImportanceNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (ImportanceTypeEnum importanceTypeEnum : ImportanceTypeEnum.values()) {
            if (importanceTypeEnum.getName().equals(name)) {
                return importanceTypeEnum.getValue();
            }
        }
        return 0;
    }

    public static String getNameByValueNoThrows(Integer value) {
        for (ImportanceTypeEnum importanceTypeEnum : ImportanceTypeEnum.values()) {
            if (importanceTypeEnum.value.equals(value)) {
                return importanceTypeEnum.name;
            }
        }
        return StringUtils.EMPTY;
    }

}

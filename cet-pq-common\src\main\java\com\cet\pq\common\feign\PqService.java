package com.cet.pq.common.feign;

import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.realtime.WiredTypeResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 设备节点服务
 * <AUTHOR>
 */
@FeignClient(value = "pq-service")
public interface PqService {
     /**
      * 通过监测点id获取接线方式
      * @param id id
      * @return
      */
     @ApiOperation(value = "通过监测点id获取接线方式")
     @GetMapping(value = "/queryWiredTypeById",produces = "application/json")
     Result<WiredTypeResult> queryWiredTypeById(@RequestParam("id") Long id);

}

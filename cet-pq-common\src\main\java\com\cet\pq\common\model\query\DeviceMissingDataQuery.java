package com.cet.pq.common.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/06/06 8:51
 */
@ApiModel(value = "装置缺失数据界面参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceMissingDataQuery extends CommonQuery {

    @ApiModelProperty(required = true, value = "异常数据类型", example = "电压偏差")
    private String errorDataType;

}

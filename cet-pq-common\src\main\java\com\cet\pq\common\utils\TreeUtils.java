package com.cet.pq.common.utils;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.enums.MonitorTypeEnum;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.handle.SessionHandler;
import com.cet.pq.common.model.*;
import com.cet.pq.common.model.auth.ModelNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName TreeUtils
 * @Description 根据modelLabel，modelId，查出相关的树分支
 * @Date 2020/11/13
 */
public class TreeUtils {

    static List<String> basicProps = Arrays.asList(ColumnName.ID, ColumnName.NAME);



    /**
     * @Description: 根据modelLabel，modelId，查出相关的树分支
     * @Author: gongtong
     * @param modelId
     * @param modelLabel
     * @param isCompany
     **/
    public static List<Map<String, Object>> getTree(Long modelId, String modelLabel, Boolean isCompany) {
        List<Map<String, Object>> unit = getUnit(modelId, modelLabel, isCompany);
        List<Map<String, Object>> tree = new ArrayList<>();
        if (CollectionUtils.isEmpty(unit)) {
            return tree;
        }
        if (TableName.NETCOMPANY.equals(modelLabel) || TableName.COUNTRY.equals(modelLabel) ) {
            tree = unit;
        } else if (TableName.PROVINCECOMPANY.equals(modelLabel) || TableName.PROVINCE.equals(modelLabel) ) {
            tree = unit;
        } else if (TableName.CITYCOMPANY.equals(modelLabel) || TableName.CITY.equals(modelLabel)) {
            tree = unit;
        } else if (TableName.COUNTYCOMPANY.equals(modelLabel) || TableName.DISTRICT.equals(modelLabel)) {
            tree = getCountyTree(isCompany, unit);
        } else if (TableName.SUBSTATION.equals(modelLabel)) {
            tree = getSubstationTree(isCompany, unit);
        } else if (TableName.LINE.equals(modelLabel)) {
            tree = getLineTree(isCompany, unit);
        }
        return tree;
    }

    /**
     * 管理层级转换为行政层级
     *
     * @param rootNode 管理层级
     * @return 行政层级
     */
    public static String transformManagerToAdministrative(String rootNode) {
        String firstSubLayerModel = null;
        if (TableName.NETCOMPANY.equals(rootNode)) {
            firstSubLayerModel = TableName.COUNTRY;
        } else if (TableName.COUNTRY.equals(rootNode)){
            firstSubLayerModel = TableName.PROVINCE;
        } else if (TableName.PROVINCECOMPANY.equals(rootNode)) {
            firstSubLayerModel = TableName.PROVINCE;
        }else if (TableName.CITYCOMPANY.equals(rootNode)) {
            firstSubLayerModel = TableName.CITY;
        } else if (TableName.COUNTYCOMPANY.equals(rootNode)){
            firstSubLayerModel = TableName.DISTRICT;
        } else {
            throw new CommonManagerException("请传入正确的顶层节点模型名称");
        }
        return firstSubLayerModel;
    }

    /**
     * @Description: 获取行政层级树
     * @return 层级节点
     * @Author: Wisdom
     * @since 2024-07-27
     */
    public static Map<String, Object> getTreeByAdministrativeLevel() {
        ModelNode userModel = SessionHandler.getUserModel();
        String rootNode = CountTreeUtils.getAreaModelByCompanyModel(userModel.getModelLabel());
        Long modelId = userModel.getId();
        QueryCondition queryCondition = new QueryCondition();
        queryCondition.setRootId(modelId);
        queryCondition.setRootLabel(rootNode);
        List<SingleModelConditionDTO> subLayerConditions = Lists.newArrayList();
        queryCondition.setSubLayerConditions(subLayerConditions);
        if (TableName.COUNTRY.equals(rootNode)) {
            subLayerConditions.add(new SingleModelConditionDTO(TableName.PROVINCE));
            subLayerConditions.add(new SingleModelConditionDTO(TableName.CITY));
        } else if (TableName.PROVINCE.equals(rootNode)) {
            subLayerConditions.add(new SingleModelConditionDTO(TableName.CITY));
        }
        subLayerConditions.add(new SingleModelConditionDTO(TableName.DISTRICT));
        subLayerConditions.add(new SingleModelConditionDTO(TableName.SUBSTATION));
        subLayerConditions.add(new SingleModelConditionDTO(TableName.LINE));
        subLayerConditions.add(new SingleModelConditionDTO(TableName.PQTERMINAL));
        queryCondition.setTreeReturnEnable(false);
        List<Map> dataResults = ModelServiceUtils.queryModelData(queryCondition, Map.class);
        if (dataResults.isEmpty()) {
            return Maps.newHashMap();
        }
        return dataResults.get(0);
    }

    /**
     * @Description: 设置县级树分支
     * @Author: gongtong
     **/
    private static List<Map<String, Object>> getCountyTree(Boolean isCompany, List<Map<String, Object>> unit) {
        List<Map<String, Object>> tree;
        String cityModel;
        String countryModel;
        if (isCompany) {
            cityModel = TableName.CITYCOMPANY;
            countryModel = TableName.COUNTYCOMPANY;
        } else {
            cityModel = TableName.CITY;
            countryModel = TableName.DISTRICT;
        }
        String finalCityModel = cityModel;
        String finalCountryModel = countryModel;
        List<Map<String, Object>> relatedCity = new ArrayList<>();
        List<Map<String, Object>> relatedProvince = new ArrayList<>();
        unit.forEach(province -> {
            List<Map<String, Object>> cities = ParseDataUtil.parseList(province.get(finalCityModel + "_model"));
            if (CollectionUtils.isEmpty(cities)) {
                return;
            }
            for (Map<String, Object> city:cities) {
                List<Map<String, Object>> countries = ParseDataUtil.parseList(city.get(finalCountryModel + "_model"));
                if (CollectionUtils.isNotEmpty(countries)) {
                    // 存在县级单位
                    relatedCity.add(city);
                    relatedProvince.add(province);
                    return;
                }
            }
        });
        //组装tree
        if (CollectionUtils.isNotEmpty(relatedCity)) {
            relatedCity.get(0).remove(TableName.SUBSTATION  + "_model");
            relatedProvince.get(0).put(finalCityModel  + "_model", relatedCity);
        }
        tree = relatedProvince;
        return tree;
    }

    /**
     * @Description: 设置变电站树分支
     * @Author: gongtong
     **/
    private static List<Map<String, Object>> getSubstationTree(Boolean isCompany, List<Map<String, Object>> unit) {
        List<Map<String, Object>> tree;
        String cityModel;
        String countryModel;
        if (isCompany) {
            cityModel = TableName.CITYCOMPANY;
            countryModel = TableName.COUNTYCOMPANY;
        } else {
            cityModel = TableName.CITY;
            countryModel = TableName.DISTRICT;
        }
        String finalCityModel = cityModel;
        String finalCountryModel = countryModel;
        List<Map<String, Object>> relatedCity = new ArrayList<>();
        List<Map<String, Object>> relatedProvince = new ArrayList<>();
        List<Map<String, Object>> relatedCountry = new ArrayList<>();
        unit.forEach(province -> {
            List<Map<String, Object>> cities = ParseDataUtil.parseList(province.get(finalCityModel + "_model"));
            if (CollectionUtils.isEmpty(cities)) {
                return;
            }
            for (Map<String, Object> city:cities) {
                List<Map<String, Object>> countries = ParseDataUtil.parseList(city.get(finalCountryModel + "_model"));
                if (CollectionUtils.isNotEmpty(countries)) {
                    // 存在县级单位
                    for (Map<String, Object> country:countries) {
                        List<Map<String, Object>> substations = ParseDataUtil.parseList(country.get(TableName.SUBSTATION + "_model"));
                        if (CollectionUtils.isNotEmpty(substations)) {
                            relatedCity.add(city);
                            relatedProvince.add(province);
                            relatedCountry.add(country);
                            return;
                        }
                    }
                }
                //不存在县级单位
                List<Map<String, Object>> substations = ParseDataUtil.parseList(city.get(TableName.SUBSTATION + "_model"));
                if (CollectionUtils.isNotEmpty(substations)) {
                    relatedCity.add(city);
                    relatedProvince.add(province);
                    return;
                }
            }
        });
        //组装tree
        if (CollectionUtils.isNotEmpty(relatedCity)) {
            if (CollectionUtils.isNotEmpty(relatedCountry)) {
                relatedCity.get(0).put(finalCountryModel  + "_model", relatedCountry);
            }
            relatedProvince.get(0).put(finalCityModel  + "_model", relatedCity);
        }
        tree = relatedProvince;
        return tree;
    }

    /**
     * @Description: 设置监测点树分支
     * @Author: gongtong
     **/
    private static List<Map<String, Object>> getLineTree(List<Map<String, Object>> unit) {
        List<Map<String, Object>> tree;
        String cityModel = TableName.CITYCOMPANY;
        String countryModel = TableName.COUNTYCOMPANY;

        String finalCityModel = cityModel;
        String finalCountryModel = countryModel;
        List<Map<String, Object>> relatedCity = new ArrayList<>();
        List<Map<String, Object>> relatedSubstation = new ArrayList<>();
        List<Map<String, Object>> relatedPowerDistributionArea = new ArrayList<>();
        List<Map<String, Object>> relatedProvince = new ArrayList<>();
        List<Map<String, Object>> relatedCountry = new ArrayList<>();
        unit.forEach(province -> {
            List<Map<String, Object>> cities = ParseDataUtil.parseList(province.get(finalCityModel + "_model"));
            if (CollectionUtils.isEmpty(cities)) {
                return;
            }
            for (Map<String, Object> city:cities) {
                List<Map<String, Object>> countries = ParseDataUtil.parseList(city.get(finalCountryModel + "_model"));
                if (CollectionUtils.isNotEmpty(countries)) {
                    // 存在县级单位
                    for (Map<String, Object> country:countries) {
                        List<Map<String, Object>> substations = ParseDataUtil.parseList(country.get(TableName.SUBSTATION + "_model"));
                        if (CollectionUtils.isNotEmpty(substations)) {
                            for (Map<String, Object> substation:substations) {
                                List<Map<String, Object>> powerDistributionAreas = ParseDataUtil.parseList(substation.get(TableName.POWERDISTRIBUTIONAREA + "_model"));
                                if (CollectionUtils.isNotEmpty(powerDistributionAreas)) {
                                    // 存在台区
                                    for (Map<String, Object> powerDistributionArea : powerDistributionAreas) {
                                        List<Map<String, Object>> lines = ParseDataUtil.parseList(powerDistributionArea.get(TableName.LINE + "_model"));
                                        if (CollectionUtils.isNotEmpty(lines)) {
                                            relatedPowerDistributionArea.add(powerDistributionArea);
                                            relatedSubstation.add(substation);
                                            relatedCity.add(city);
                                            relatedProvince.add(province);
                                            relatedCountry.add(country);
                                            return;
                                        }
                                    }

                                }
                                // 不存在
                                List<Map<String, Object>> lines = ParseDataUtil.parseList(substation.get(TableName.LINE + "_model"));
                                if (CollectionUtils.isNotEmpty(lines)) {
                                    relatedSubstation.add(substation);
                                    relatedCity.add(city);
                                    relatedProvince.add(province);
                                    relatedCountry.add(country);
                                    return;
                                }
                            }
                        }
                    }
                }
                //不存在县级单位
                List<Map<String, Object>> substations = ParseDataUtil.parseList(city.get(TableName.SUBSTATION + "_model"));
                if (CollectionUtils.isNotEmpty(substations)) {
                    for (Map<String, Object> substation:substations) {
                        List<Map<String, Object>> powerDistributionAreas = ParseDataUtil.parseList(substation.get(TableName.POWERDISTRIBUTIONAREA + "_model"));
                        if (CollectionUtils.isNotEmpty(powerDistributionAreas)) {
                            // 存在台区
                            for (Map<String, Object> powerDistributionArea : powerDistributionAreas) {
                                List<Map<String, Object>> lines = ParseDataUtil.parseList(powerDistributionArea.get(TableName.LINE + "_model"));
                                if (CollectionUtils.isNotEmpty(lines)) {
                                    relatedPowerDistributionArea.add(powerDistributionArea);
                                    relatedSubstation.add(substation);
                                    relatedCity.add(city);
                                    relatedProvince.add(province);
                                    return;
                                }
                            }

                        }
                        //不存在
                        List<Map<String, Object>> lines = ParseDataUtil.parseList(substation.get(TableName.LINE + "_model"));
                        if (CollectionUtils.isNotEmpty(lines)) {
                            relatedSubstation.add(substation);
                            relatedCity.add(city);
                            relatedProvince.add(province);
                            return;
                        }
                    }
                }
            }
        });
        //组装tree
        if (CollectionUtils.isNotEmpty(relatedSubstation)) {
            if (CollectionUtils.isNotEmpty(relatedCountry)) {
                if(CollectionUtils.isNotEmpty(relatedPowerDistributionArea)){
                    relatedSubstation.get(0).put(TableName.POWERDISTRIBUTIONAREA + "_model", relatedPowerDistributionArea);
                }
                relatedCountry.get(0).put(TableName.SUBSTATION + "_model", relatedSubstation);
                relatedCity.get(0).put(finalCountryModel  + "_model", relatedCountry);
            } else {
                if(CollectionUtils.isNotEmpty(relatedPowerDistributionArea)){
                    relatedSubstation.get(0).put(TableName.POWERDISTRIBUTIONAREA + "_model", relatedPowerDistributionArea);
                }
                relatedCity.get(0).put(TableName.SUBSTATION  + "_model", relatedSubstation);
            }
            relatedProvince.get(0).put(finalCityModel  + "_model", relatedCity);
        }
        tree = relatedProvince;
        return tree;
    }


    /**
     * @Description: 设置监测点树分支
     * @Author: gongtong
     **/
    private static List<Map<String, Object>> getLineTree(Boolean isCompany, List<Map<String, Object>> unit) {
        List<Map<String, Object>> tree;
        String cityModel;
        String countryModel;
        if (isCompany) {
            cityModel = TableName.CITYCOMPANY;
            countryModel = TableName.COUNTYCOMPANY;
        } else {
            cityModel = TableName.CITY;
            countryModel = TableName.DISTRICT;
        }
        String finalCityModel = cityModel;
        String finalCountryModel = countryModel;
        List<Map<String, Object>> relatedCity = new ArrayList<>();
        List<Map<String, Object>> relatedSubstation = new ArrayList<>();
        List<Map<String, Object>> relatedProvince = new ArrayList<>();
        List<Map<String, Object>> relatedCountry = new ArrayList<>();
        unit.forEach(province -> {
            List<Map<String, Object>> cities = ParseDataUtil.parseList(province.get(finalCityModel + "_model"));
            if (CollectionUtils.isEmpty(cities)) {
                return;
            }
            for (Map<String, Object> city:cities) {
                List<Map<String, Object>> countries = ParseDataUtil.parseList(city.get(finalCountryModel + "_model"));
                if (CollectionUtils.isNotEmpty(countries)) {
                    // 存在县级单位
                    for (Map<String, Object> country:countries) {
                        List<Map<String, Object>> substations = ParseDataUtil.parseList(country.get(TableName.SUBSTATION + "_model"));
                        if (CollectionUtils.isNotEmpty(substations)) {
                            for (Map<String, Object> substation:substations) {
                                List<Map<String, Object>> lines = ParseDataUtil.parseList(substation.get(TableName.LINE + "_model"));
                                if (CollectionUtils.isNotEmpty(lines)) {
                                    relatedSubstation.add(substation);
                                    relatedCity.add(city);
                                    relatedProvince.add(province);
                                    relatedCountry.add(country);
                                    return;
                                }
                            }
                        }
                    }
                }
                //不存在县级单位
                List<Map<String, Object>> substations = ParseDataUtil.parseList(city.get(TableName.SUBSTATION + "_model"));
                if (CollectionUtils.isNotEmpty(substations)) {
                    for (Map<String, Object> substation:substations) {
                        List<Map<String, Object>> lines = ParseDataUtil.parseList(substation.get(TableName.LINE + "_model"));
                        if (CollectionUtils.isNotEmpty(lines)) {
                            relatedSubstation.add(substation);
                            relatedCity.add(city);
                            relatedProvince.add(province);
                            return;
                        }
                    }
                }
            }
        });
        //组装tree
        if (CollectionUtils.isNotEmpty(relatedSubstation)) {
            if (CollectionUtils.isNotEmpty(relatedCountry)) {
                relatedCountry.get(0).put(TableName.SUBSTATION + "_model", relatedSubstation);
                relatedCity.get(0).put(finalCountryModel  + "_model", relatedCountry);
            } else {
                relatedCity.get(0).put(TableName.SUBSTATION  + "_model", relatedSubstation);
            }
            relatedProvince.get(0).put(finalCityModel  + "_model", relatedCity);
        }
        tree = relatedProvince;
        return tree;
    }

    /**
     * @Description: 设置变电站树分支
     * @Author: gongtong
     **/
    static List<Map<String, Object>> getUnit(Long modelId, String modelLabel, Boolean isCompany) {
        List<SingleModelConditionDTO> subConditions = new ArrayList<>(Arrays.asList(
                new SingleModelConditionDTO(isCompany ? TableName.NETCOMPANY : TableName.COUNTRY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(isCompany ? TableName.PROVINCECOMPANY : TableName.PROVINCE, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(isCompany ? TableName.CITYCOMPANY : TableName.CITY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(isCompany ? TableName.COUNTYCOMPANY : TableName.DISTRICT, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
                new SingleModelConditionDTO(TableName.LINE, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.MONITORSORT,
                        ColumnName.MONITORMODE, ColumnName.MONITORTYPE, ColumnName.PQMONITORTYPE, ColumnName.ISUPLOAD, ColumnName.VOLTCLASS, ColumnName.OBJECTTYPE))
        ));
        String table = isCompany ? TableName.PROVINCECOMPANY : TableName.PROVINCE;
        ModelIdPairDTO modelIdPairDTO = new ModelIdPairDTO();
        Long id = null;
        if (TableName.NETCOMPANY.equals(modelLabel) || TableName.COUNTRY.equals(modelLabel)) {
            subConditions.get(0).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            table = modelLabel;
            id = modelId;
        } else if (TableName.PROVINCECOMPANY.equals(modelLabel) || TableName.PROVINCE.equals(modelLabel)) {
            subConditions.get(1).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            id = modelId;
        } else if (TableName.CITYCOMPANY.equals(modelLabel) || TableName.CITY.equals(modelLabel)) {
            subConditions.get(2).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            modelIdPairDTO.setId(modelId);
            modelIdPairDTO.setModelLabel(modelLabel);
        } else if (TableName.COUNTYCOMPANY.equals(modelLabel) || TableName.DISTRICT.equals(modelLabel)) {
            subConditions.get(3).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            modelIdPairDTO.setId(modelId);
            modelIdPairDTO.setModelLabel(modelLabel);
        } else if (TableName.SUBSTATION.equals(modelLabel)) {
            subConditions.get(4).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            modelIdPairDTO.setId(modelId);
            modelIdPairDTO.setModelLabel(modelLabel);
        } else if (TableName.LINE.equals(modelLabel)) {
            subConditions.get(5).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            modelIdPairDTO.setId(modelId);
            modelIdPairDTO.setModelLabel(modelLabel);
        }
        return ModelServiceUtils.getInterRelations(Collections.singletonList(id), table, null, modelIdPairDTO, subConditions, false);
    }

    /**
     * @description: 获取节点字段（id, name）的SingleModelConditionDTO
     **/
    public static SingleModelConditionDTO getPlainSingleModelConditionDTO(String modelLabel, List<String> otherProps, List<ConditionBlock> blockList){
        if(StringUtils.isEmpty(modelLabel)){
            return null;
        }
        List<String> props = new ArrayList<>(basicProps);
        if(CollectionUtils.isNotEmpty(otherProps)) {
            otherProps.forEach(op->{
                if(!props.contains(op)){
                    props.add(op);
                }
            });
        }
        SingleModelConditionDTO singleModelConditionDTO = new SingleModelConditionDTO();
        singleModelConditionDTO.setModelLabel(modelLabel);
        singleModelConditionDTO.setProps(props);
        if(CollectionUtils.isNotEmpty(blockList)){
            ConditionBlockCompose conditionBlockCompose = new ConditionBlockCompose(blockList);
            singleModelConditionDTO.setFilter(conditionBlockCompose);
        }
        return singleModelConditionDTO;
    }

    /**
     * @description: 根据modelLabel获取从省级到监测点的节点树分支，包含每层级所有字段

     **/
    public static List<Map<String, Object>> getProvinceTree(Long modelId, String modelLabel, Integer monitorType, Integer monitorSort) {
        List<SingleModelConditionDTO> subConditions = new ArrayList<>(Arrays.asList(
                new SingleModelConditionDTO(TableName.PROVINCECOMPANY),
                new SingleModelConditionDTO(TableName.CITYCOMPANY),
                new SingleModelConditionDTO(TableName.COUNTYCOMPANY),
                new SingleModelConditionDTO(TableName.SUBSTATION),
                new SingleModelConditionDTO(TableName.POWERDISTRIBUTIONAREA),
                new SingleModelConditionDTO(TableName.LINE)
        ));
        String table =  TableName.PROVINCECOMPANY;
        ModelIdPairDTO modelIdPairDTO = new ModelIdPairDTO();
        Long id = null;
        if (TableName.PROVINCECOMPANY.equals(modelLabel)) {
            id = modelId;
        } else if (TableName.CITYCOMPANY.equals(modelLabel)) {
            subConditions.get(1).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.COUNTYCOMPANY.equals(modelLabel)) {
            subConditions.get(2).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.SUBSTATION.equals(modelLabel)) {
            subConditions.get(3).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.POWERDISTRIBUTIONAREA.equals(modelLabel)) {
            subConditions.get(4).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.LINE.equals(modelLabel)) {
            subConditions.get(5).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        }
        if(!TableName.PROVINCECOMPANY.equals(modelLabel)){
            modelIdPairDTO.setId(modelId);
            modelIdPairDTO.setModelLabel(modelLabel);
        }
        List<ConditionBlock> conditionBlockList = setLineCondition(monitorType, monitorSort);
        if(subConditions.get(5).getFilter()!=null && !org.springframework.util.CollectionUtils.isEmpty(subConditions.get(5).getFilter().getExpressions())){
            conditionBlockList.addAll(subConditions.get(5).getFilter().getExpressions());
        }
        subConditions.get(5).setFilter(new ConditionBlockCompose(conditionBlockList));
        return ModelServiceUtils.getInterRelations(Collections.singletonList(id), table, null, modelIdPairDTO, subConditions, false);
    }

    /**
     * @description: 根据modelLabel获取从省级到监测点的节点树分支，包含层级字段（id，name）和其他自定义字段
     **/
    public static List<Map<String, Object>> getPlainProvinceTree(Long modelId, String modelLabel,
                                                                 List<String> provinceCompanyOtherProps,
                                                                 List<String> cityCompanyOtherProps,
                                                                 List<String> countyCompanyOtherProps,
                                                                 List<String> substationOtherProps,
                                                                 List<String> powerDistributionAreaOtherProps,
                                                                 List<String> monitorOtherProps,
                                                                 List<ConditionBlock> monitorFilterList) {
        List<SingleModelConditionDTO> subConditions = new ArrayList<>(Arrays.asList(
                TreeUtils.getPlainSingleModelConditionDTO(TableName.PROVINCECOMPANY, provinceCompanyOtherProps, null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.CITYCOMPANY, cityCompanyOtherProps, null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.COUNTYCOMPANY, countyCompanyOtherProps, null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.SUBSTATION, substationOtherProps, null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.POWERDISTRIBUTIONAREA, powerDistributionAreaOtherProps, null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.LINE, monitorOtherProps, monitorFilterList)
        ));
        String table =  TableName.PROVINCECOMPANY;
        ModelIdPairDTO modelIdPairDTO = new ModelIdPairDTO();
        Long id = null;
        if (TableName.PROVINCECOMPANY.equals(modelLabel)) {
            id = modelId;
        } else if (TableName.CITYCOMPANY.equals(modelLabel)) {
            subConditions.get(1).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.COUNTYCOMPANY.equals(modelLabel)) {
            subConditions.get(2).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.SUBSTATION.equals(modelLabel)) {
            subConditions.get(3).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.POWERDISTRIBUTIONAREA.equals(modelLabel)) {
            subConditions.get(4).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        } else if (TableName.LINE.equals(modelLabel)) {
            subConditions.get(5).setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
        }
        if(!TableName.PROVINCECOMPANY.equals(modelLabel)){
            modelIdPairDTO.setId(modelId);
            modelIdPairDTO.setModelLabel(modelLabel);
        }
        List<Map<String, Object>> interRelations = ModelServiceUtils.getInterRelations(Collections.singletonList(id), table, null, modelIdPairDTO, subConditions, false);
        //过滤分支
        return filterBranchByModelLabel(interRelations,modelLabel);
    }

    public static List<Map<String, Object>> filterBranchByModelLabel(List<Map<String, Object>> unit,String modelLabel) {
        List<Map<String, Object>> tree = new ArrayList<>();
        if (CollectionUtils.isEmpty(unit)) {
            return tree;
        }
        if (TableName.PROVINCECOMPANY.equals(modelLabel)) {
            tree = unit;
        } else if (TableName.CITYCOMPANY.equals(modelLabel)) {
            tree = unit;
        } else if (TableName.COUNTYCOMPANY.equals(modelLabel)) {
            tree = getCountyTree(Boolean.TRUE,unit);
        }else if (TableName.SUBSTATION.equals(modelLabel)) {
            tree = getSubstationTree(Boolean.TRUE,unit);
        } else if (TableName.POWERDISTRIBUTIONAREA.equals(modelLabel)) {
            tree = getPowerdistributionareaTree(unit);
        }else if (TableName.LINE.equals(modelLabel)) {
            tree = getLineTree(unit);
        }
        return tree;
    }

    private static List<Map<String, Object>> getPowerdistributionareaTree(List<Map<String, Object>> unit) {
        List<Map<String, Object>> tree;
        String cityModel = TableName.CITYCOMPANY;
        String countryModel = TableName.COUNTYCOMPANY;

        String finalCityModel = cityModel;
        String finalCountryModel = countryModel;
        List<Map<String, Object>> relatedStation = new ArrayList<>();
        List<Map<String, Object>> relatedCity = new ArrayList<>();
        List<Map<String, Object>> relatedProvince = new ArrayList<>();
        List<Map<String, Object>> relatedCountry = new ArrayList<>();
        unit.forEach(province -> {
            List<Map<String, Object>> cities = ParseDataUtil.parseList(province.get(finalCityModel + "_model"));
            if (CollectionUtils.isEmpty(cities)) {
                return;
            }
            for (Map<String, Object> city:cities) {
                List<Map<String, Object>> countries = ParseDataUtil.parseList(city.get(finalCountryModel + "_model"));
                if (CollectionUtils.isNotEmpty(countries)) {
                    // 存在县级单位
                    for (Map<String, Object> country:countries) {
                        List<Map<String, Object>> substations = ParseDataUtil.parseList(country.get(TableName.SUBSTATION + "_model"));
                        if (CollectionUtils.isNotEmpty(substations)) {
                            for (Map<String, Object> substation : substations) {
                                List<Map<String, Object>> powerDistributionAreas = ParseDataUtil.parseList(substation.get(TableName.POWERDISTRIBUTIONAREA + "_model"));
                                if (CollectionUtils.isNotEmpty(powerDistributionAreas)) {
                                    relatedStation.add(substation);
                                    relatedCity.add(city);
                                    relatedProvince.add(province);
                                    relatedCountry.add(country);
                                    return;
                                }
                            }
                        }
                    }
                }
                //不存在县级单位
                List<Map<String, Object>> substations = ParseDataUtil.parseList(city.get(TableName.SUBSTATION + "_model"));
                if (CollectionUtils.isNotEmpty(substations)) {
                    for (Map<String, Object> substation : substations) {
                        List<Map<String, Object>> powerDistributionAreas = ParseDataUtil.parseList(substation.get(TableName.POWERDISTRIBUTIONAREA + "_model"));
                        if (CollectionUtils.isNotEmpty(powerDistributionAreas)) {
                            relatedStation.add(substation);
                            city.keySet().removeIf(key -> key.equals(TableName.COUNTYCOMPANY + "_model"));
                            relatedCity.add(city);
                            relatedProvince.add(province);
                            return;
                        }
                    }
                }
            }
        });
        //组装tree
        if (CollectionUtils.isNotEmpty(relatedCity)) {
            if (CollectionUtils.isNotEmpty(relatedCountry)) {
                relatedCountry.get(0).put(TableName.SUBSTATION  + "_model", relatedStation);
                relatedCity.get(0).put(finalCountryModel  + "_model", relatedCountry);
            }else {
                relatedCity.get(0).put(TableName.SUBSTATION  + "_model", relatedStation);
            }
            relatedProvince.get(0).put(finalCityModel  + "_model", relatedCity);
        }
        tree = relatedProvince;
        return tree;

    }

    public static List<ConditionBlock> setLineCondition(Integer monitorType,Integer monitorSort) {
        List<ConditionBlock> conditionBlocks = new ArrayList<>();
        if(MonitorTypeEnum.MAIN.getValue().equals(monitorType)){
            //主网统计主配网类型为主网和主配网的监测点
            conditionBlocks.add( new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_IN,
                    Arrays.asList(MonitorTypeEnum.MAIN.getValue())));
        }else if(MonitorTypeEnum.DISTRIBUTION.getValue().equals(monitorType)){
            if(com.cet.pq.common.enums.MonitorSortEnum.ALL.getId().equals(monitorSort)){
                //如果为I类或者全部，加上主配网类型
                conditionBlocks.add( new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_IN,
                        Arrays.asList(MonitorTypeEnum.DISTRIBUTION.getValue())));
            }else if(com.cet.pq.common.enums.MonitorSortEnum.FIRST_CLASS.getId().equals(monitorSort)){
                conditionBlocks.add( new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_IN,
                        Arrays.asList(MonitorTypeEnum.DISTRIBUTION.getValue())));
                conditionBlocks.add( new ConditionBlock(ColumnName.MONITORSORT, ConditionBlock.OPERATOR_EQ, monitorSort));
            }else {
                conditionBlocks.add( new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_EQ,
                        MonitorTypeEnum.DISTRIBUTION.getValue()));
                conditionBlocks.add( new ConditionBlock(ColumnName.MONITORSORT, ConditionBlock.OPERATOR_EQ, monitorSort));
            }
        }
        return conditionBlocks;
    }

    /**
     * 获取行政层级下的监测点数据
     *
     * @param rootTree
     * @param subLayerLabel
     * @param subLayer2LineRelation
     * @return
     */
    public static List<Map<String, Object>> getLineInfosFromLevelTree(Map<String, Object> rootTree, String subLayerLabel, Map<String, List<Long>> subLayer2LineRelation) {
        List<Map<String, Object>> lineList = new ArrayList<>();
        List<Map<String, Object>> subLayerList = ParseDataUtil.parseList(rootTree.get(subLayerLabel.concat(TableName.PREFIX)));
        String stationLabel = TableName.SUBSTATION + TableName.PREFIX;
        String lineLabel = TableName.LINE + TableName.PREFIX;
        if (CollectionUtils.isNotEmpty(subLayerList)) {
            subLayerList.forEach(subLayer -> {
                List<Long> lineIdList = new ArrayList<>();
                Long subLayerId = ParseDataUtil.parseLong(subLayer.get(ColumnName.ID));
                String subLayerName = ParseDataUtil.parseString(subLayer.get(ColumnName.NAME));
                String key = subLayerId + CommonConstant.SEPARATOR_UNDER_LINE + subLayerName;
                List<Map<String, Object>> substationList = ParseDataUtil.parseList(subLayer.get(stationLabel));
                List<Map<String, Object>> districtList = ParseDataUtil.parseList(subLayer.get(TableName.DISTRICT + TableName.PREFIX));
                districtList.forEach(e -> substationList.addAll(ParseDataUtil.parseList(e.get(stationLabel))));
                substationList.forEach(substation -> {
                    List<Map<String, Object>> templineList = ParseDataUtil.parseList(substation.get(lineLabel));
                    List<Long> tempLineIdList = templineList.stream().map(line -> ParseDataUtil.parseLong(line.get(ColumnName.ID))).collect(Collectors.toList());
                    lineIdList.addAll(tempLineIdList);
                    lineList.addAll(templineList);
                });
                subLayer2LineRelation.put(key, lineIdList);
            });
        } else {
            List<Map<String, Object>> substationList = ParseDataUtil.parseList(rootTree.get(stationLabel));
            substationList.forEach(substation -> {
                List<Map<String, Object>> templineList = ParseDataUtil.parseList(substation.get(lineLabel));
                lineList.addAll(templineList);
            });
        }
        return lineList;
    }

}
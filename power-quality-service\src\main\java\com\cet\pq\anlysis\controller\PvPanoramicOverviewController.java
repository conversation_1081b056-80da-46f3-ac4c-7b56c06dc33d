package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.areastatistics.AreaEfficiency;
import com.cet.pq.anlysis.model.areastatistics.AreaPermeabilityParaAnalysis;
import com.cet.pq.anlysis.model.monitorstatistics.MonitorRate;
import com.cet.pq.anlysis.model.overStandardAnalysis.DistributionIndicatorLevel;
import com.cet.pq.anlysis.model.powertrend.ElectricityGenerationCurveVo;
import com.cet.pq.anlysis.model.qualifiedrate.QualifiedRateCountByAreaVo;
import com.cet.pq.anlysis.model.terminalstatistics.TerminalOverview;
import com.cet.pq.anlysis.service.PvPanoramicOverviewService;
import com.cet.pq.common.model.Result;
import com.cet.pq.inventoryservice.model.line.MonitorOnlineRate;
import com.cet.pq.inventoryservice.model.order.OrderOverview;
import com.cet.pq.inventoryservice.service.OrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/17 13:52
 * @Description
 */
@Api(value="PvPanoramicOverviewController",tags={"配网全景概览"})
@RestController
@RequestMapping(value = "/pq/v1/pvpanoramicoverview")
public class PvPanoramicOverviewController {
    @Autowired
    private PvPanoramicOverviewService pvPanoramicOverviewService;
    @Autowired
    private OrderService orderService;

    @ApiOperation(value = "配网监测点规模")
    @GetMapping("monitorRate")
    public Result<MonitorRate> pqMonitorRate() {
        return pvPanoramicOverviewService.pqMonitorRate();
    }

    @ApiOperation("工单管理")
    @GetMapping("order")
    public Result<OrderOverview> pqOrderOverview() {
        return orderService.pqOrderOverview();
    }

    @ApiOperation(value = "配网监测终端规模")
    @GetMapping("inventoryTotalCount2")
    public Result<List<TerminalOverview>> terminalOverview() {
        return Result.success(pvPanoramicOverviewService.terminalOverview());
    }

    @ApiOperation(value = "发电量曲线")
    @GetMapping("electricityGenerationCurve")
    public Result<List<ElectricityGenerationCurveVo>> getElectricityGenerationCurve(@RequestParam @ApiParam(value = "开始时间",required = true,example = "1638288000000")Long startTime, @RequestParam @ApiParam(value = "结束时间",required = true,example = "1640966400000")Long endTime){
        return Result.success(pvPanoramicOverviewService.getElectricityGenerationCurve(startTime,endTime));
    }

    @ApiOperation(value = "配网电能质量指标水平-上月")
    @GetMapping("pqIndexLevel")
    public Result<List<DistributionIndicatorLevel>> getPqIndexLevel() {
        return Result.success(pvPanoramicOverviewService.getPqIndexLevel());
    }

    @ApiOperation(value = "分布式光伏并网影响分析")
    @GetMapping("ongridImpactAnalysis")
    public Result<List<AreaPermeabilityParaAnalysis>> ongridImpactAnalysis() {
        return Result.success(pvPanoramicOverviewService.ongridImpactAnalysis());
    }

    @ApiOperation(value = "分布式光伏并网台区运行能效")
    @GetMapping("ongridEfficiency")
    public Result<AreaEfficiency> ongridEfficiency() {
        return Result.success(pvPanoramicOverviewService.ongridEfficiency());
    }
    @ApiOperation(value = "区域合格率信息")
    @GetMapping("qualifiedRateCountByArea")
    public Result<List<QualifiedRateCountByAreaVo>> qualifiedRateCountByArea(String modelLabel, Long modelId){
        return Result.success(pvPanoramicOverviewService.qualifiedRateCountByArea(modelLabel, modelId));
    }

    @ApiOperation(value = "监测点在线率-昨日、完整率-昨日、检测率")
    @GetMapping("onlineRate")
    public Result<List<MonitorOnlineRate>> monitorOnlineRate(String modelLabel, Long modelId) {
        return Result.success(pvPanoramicOverviewService.monitorOnlineRate(modelLabel, modelId));
    }

}

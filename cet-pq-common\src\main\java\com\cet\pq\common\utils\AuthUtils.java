package com.cet.pq.common.utils;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.handle.SessionHandler;
import com.cet.pq.common.model.auth.ModelNode;
import com.cet.pq.common.model.auth.Role;
import com.cet.pq.common.model.auth.User;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AuthUtils
 * @Description 权限检查帮助类
 * @Date 2020/11/10
 */
@Slf4j
public class AuthUtils {

    /**
     * 检查树权限
     *
     * @param modelId
     * @param modelLabel
     */
    public static void checkAuth(Long modelId, String modelLabel, boolean isCompany) {
        // todo 行政区域暂不处理
        if (!isCompany || StringUtils.isBlank(modelLabel)) {
            return;
        }
        Map<String, Set<Long>> treeMap = new ConcurrentHashMap<>(10);
        Map<String, List<Integer>> treeMapCache = null;
        boolean isExist = Boolean.TRUE;
        User user = SessionHandler.getUser();
        ModelNode userModel = SessionHandler.getModelNode(user);
        String redisResult = RedisUtils.get(CommonConstant.USER_TREE_MAP + user.getId());
        if (StringUtils.isNotEmpty(redisResult)) {
            List<Map> treeMaps = JsonTransferUtils.transferJsonString(redisResult, Map.class);
            if (CollectionUtils.isNotEmpty(treeMaps)) {
                treeMapCache = treeMaps.get(0);
                Map<String, Set<Long>> finalTreeMap = treeMap;
                treeMapCache.forEach((k, v) -> {
                    Set<Long> collect = Sets.newTreeSet(v.stream().map(t -> ParseDataUtil.parseLong(String.valueOf(t))).collect(Collectors.toList()));
                    finalTreeMap.put(k, collect);
                });
            }
        }
        if (MapUtils.isEmpty(treeMap)) {
            treeMap = new ConcurrentHashMap<>(10);
            long start = System.currentTimeMillis();
            List<Map<String, Object>> userSubUnit = SessionHandler.getUserSubUnit1();
            long end = System.currentTimeMillis();
            log.info("节点权限校验耗时：{}ms", + (end - start));
            if (CollectionUtils.isNotEmpty(userSubUnit)) {
                treeMap.put(userModel.getModelLabel(), Sets.newTreeSet(Collections.singleton(userModel.getId())));
                Map<String, Set<Long>> finalTreeMap = treeMap;
                userSubUnit.forEach(unit -> {
                    if (userModel.getModelLabel().equals(TableName.NETCOMPANY) || userModel.getModelLabel().equals(TableName.COUNTRY)) {
                        //网级用户
                        setTreeNode(finalTreeMap, unit);
                        List<Map<String, Object>> cities = ParseDataUtil.parseList(unit.get(TableName.CITYCOMPANY + TableName.PREFIX));
                        setCityTreeNode(finalTreeMap, unit, cities);
                    } else if (userModel.getModelLabel().equals(TableName.PROVINCECOMPANY)
                            || userModel.getModelLabel().equals(TableName.PROVINCE)) {
                        setTreeNode(finalTreeMap, unit);
                        List<Map<String, Object>> counties = ParseDataUtil.parseList(unit.get(TableName.COUNTYCOMPANY + TableName.PREFIX));
                        if (CollectionUtils.isEmpty(counties)) {
                            counties = ParseDataUtil.parseList(unit.get(TableName.DISTRICT + TableName.PREFIX));
                        }
                        setCityRelatedModel(finalTreeMap, unit, counties);
                    } else if (userModel.getModelLabel().equals(TableName.CITYCOMPANY)
                            || userModel.getModelLabel().equals(TableName.CITY)) {
                        //地市级用户展示所有县级，若没有县级展示当前市级
                        if (ParseDataUtil.parseString(unit.get(ColumnName.MODELLABEL)).equals(TableName.COUNTYCOMPANY) ||
                                ParseDataUtil.parseString(unit.get(ColumnName.MODELLABEL)).equals(TableName.DISTRICT)) {
                            setCityRelatedModel(finalTreeMap, unit, Collections.singletonList(unit));
                        } else {
                            setCityTreeNode(finalTreeMap, unit, Collections.singletonList(unit));
                        }
                    }
                });
                if (MapUtils.isNotEmpty(treeMap)) {
                    RedisUtils.setEx(CommonConstant.USER_TREE_MAP + user.getId(), JsonTransferUtils.toJsonString(Collections.singletonList(treeMap)), 60, TimeUnit.MINUTES);
                }
            } else {
                isExist = false;
            }
        }
        if (CollectionUtils.isNotEmpty(treeMap.get(modelLabel))) {
            Set<Long> modelIds = treeMap.get(modelLabel);
            if (!modelIds.contains(modelId)) {
                isExist = false;
            }
        } else {
            isExist = false;
        }
        if (!isExist) {
            log.error("检测所属地市{}， 地市id {}，用户所属地市id {} 异常", modelLabel, modelId, userModel.getId());
            throw new CommonManagerException("该单位不在用户所在区域下, 权限异常, 请尝试重新登录");
        }
    }

    static void setCityRelatedModel(Map<String, Set<Long>> finalTreeMap, Map<String, Object> unit, List<Map<String, Object>> counties) {
        setInterferenceTreeNode(finalTreeMap, unit);
        setCountryTreeNode(finalTreeMap, unit, counties);
        setTestInstrumentTreeNode(finalTreeMap, unit);
    }

    /**
     * 校验用户是否为某个角色
     *
     * @param authRoleId 用户角色id 1 管理员 2 用户管理员 3 高级用户 4 普通用户 5 审计
     */
    public static void cheakAuthRole(Long... authRoleId) {
        User user = SessionHandler.getUser();
        List<Role> roles = user.getRoles();
        if (CollectionUtils.isEmpty(roles)) {
            throw new CommonManagerException("该用户不具备角色");
        }
        List<Long> authRoleIdList = roles.stream().map(Role::getId).collect(Collectors.toList());
        boolean isExist = false;
        for(Long authRole : authRoleId) {
            if (authRoleIdList.contains(authRole)) {
                isExist = true;
                break;
            }
        }
        if (!isExist) {
            throw new CommonManagerException("该用户不具备该操作的权限");
        }
    }

    /**
     * @Description: 设置city树分支
     * @Author: gongtong
     **/
    private static void setCityTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> unit, List<Map<String, Object>> cities) {
        if (CollectionUtils.isNotEmpty(cities)) {
            cities.forEach(city -> {
                setTreeNode(treeMap, city);
                List<Map<String, Object>> countries = ParseDataUtil.parseList(city.get(TableName.COUNTYCOMPANY + TableName.PREFIX));
                if (CollectionUtils.isEmpty(countries)) {
                    countries = ParseDataUtil.parseList(city.get(TableName.DISTRICT + TableName.PREFIX));
                }
                setCityRelatedModel(treeMap, city, countries);
            });
        }
    }

    /**
     * @param treeMap
     * @param city
     * @Description: 设置干扰源树分支
     * @Author: gongtong
     **/
    private static void setInterferenceTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> city) {
        //设置干扰源
        List<Map<String, Object>> interferences = ParseDataUtil.parseList(city.get(TableName.INTERFERENCESOURCE + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(interferences)) {
            interferences.forEach(interference -> {
                setTreeNode(treeMap, interference);
                // 设置监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(interference.get(TableName.LINE + TableName.PREFIX));
                setLineTreeNode(treeMap, lines);
            });
        }
        List<Map<String, Object>> eletrics = ParseDataUtil.parseList(city.get(TableName.ELECTRICRAILWAY + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(eletrics)) {
            eletrics.forEach(eletric -> {
                setTreeNode(treeMap, eletric);
                // 设置监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(eletric.get(TableName.LINE + TableName.PREFIX));
                setLineTreeNode(treeMap, lines);
            });
        }
        List<Map<String, Object>> photovoltaicstations = ParseDataUtil.parseList(city.get(TableName.PHOTOVOLTAICSTATION + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(photovoltaicstations)) {
            photovoltaicstations.forEach(photovoltaicstation -> {
                setTreeNode(treeMap, photovoltaicstation);
                // 设置监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(photovoltaicstation.get(TableName.LINE + TableName.PREFIX));
                setLineTreeNode(treeMap, lines);
            });
        }
        List<Map<String, Object>> windpowerstations = ParseDataUtil.parseList(city.get(TableName.WINDPOWERSTATION + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(windpowerstations)) {
            windpowerstations.forEach(windpowerstation -> {
                setTreeNode(treeMap, windpowerstation);
                // 设置监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(windpowerstation.get(TableName.LINE + TableName.PREFIX));
                setLineTreeNode(treeMap, lines);
            });
        }
        // 设置冶炼负荷
        List<Map<String, Object>> smeltloads = ParseDataUtil.parseList(city.get(TableName.SMELTLOAD + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(smeltloads)) {
            smeltloads.forEach(smeltload -> {
                setTreeNode(treeMap, smeltload);
                // 设置监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(smeltload.get(TableName.LINE + TableName.PREFIX));
                setLineTreeNode(treeMap, lines);
            });
        }
        // 设置储能站
        List<Map<String, Object>> energyStorageStations = ParseDataUtil.parseList(city.get(TableName.ENERGY_STORAGE_STATION + TableName.PREFIX));
        if(CollectionUtils.isNotEmpty(energyStorageStations)) {
            for(Map<String, Object> energyStorageStation : energyStorageStations) {
                setTreeNode(treeMap, energyStorageStation);
                // 设置监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(energyStorageStation.get(TableName.LINE + TableName.PREFIX));
                setLineTreeNode(treeMap, lines);
            }
        }
        // 设置充电站
        List<Map<String, Object>> chargingStations = ParseDataUtil.parseList(city.get(TableName.CHARGING_STATION + TableName.PREFIX));
        if(CollectionUtils.isNotEmpty(chargingStations)) {
            for(Map<String, Object> chargingStation : chargingStations) {
                setTreeNode(treeMap, chargingStation);
                // 设置监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(chargingStation.get(TableName.LINE + TableName.PREFIX));
                setLineTreeNode(treeMap, lines);
            }
        }
    }

    /**
     * @Description: 设置country树分支
     * @Author: gongtong
     **/
    private static void setCountryTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> unit, List<Map<String, Object>> countries) {
        // 变电站直接关联县级
        List<Map<String, Object>> substations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(countries)) {
            countries.forEach(country -> {
                setTreeNode(treeMap, country);
                substations.addAll(ParseDataUtil.parseList(country.get(TableName.SUBSTATION + TableName.PREFIX)));
                // 设置干扰源
                setInterferenceTreeNode(treeMap, country);
            });
        }
        //变电站直接关联市级
        List<Map<String, Object>> citySubstations = ParseDataUtil.parseList(unit.get("citySubstation_model"));
        if (CollectionUtils.isNotEmpty(citySubstations)) {
            substations.addAll(citySubstations);
        }
        // 变电站直接关联市级
        substations.addAll(ParseDataUtil.parseList(unit.get(TableName.SUBSTATION + TableName.PREFIX)));
        if (CollectionUtils.isNotEmpty(substations)) {
            substations.forEach(substation -> {
                setTreeNode(treeMap, substation);
                List<Map<String, Object>> lines = ParseDataUtil.parseList(substation.get(TableName.LINE + TableName.PREFIX));
                if(substation.containsKey(TableName.POWERDISTRIBUTIONAREA + TableName.PREFIX)){
                    List<Map<String, Object>> powerdistributionareaList = ParseDataUtil.parseList(substation.get(TableName.POWERDISTRIBUTIONAREA + TableName.PREFIX));
                    lines.addAll(getPowerdistributionAreaLine(powerdistributionareaList));
                }
                // 设置监测点
                setLineTreeNode(treeMap, lines);
                // 设置终端
                setPqTerminalTreeNode(treeMap, substation);
                // 设置台区
                setPowerdistributionAreaTreeNode(treeMap, substation);
                // 设置治理设备
                setGovernanceDeviceTreeNode(treeMap, substation);
                // 设置发电用户
                setPowerGenerationUserTreeNode(treeMap, substation);
                // 设置变压器
                setTwoTransformerTreeNode(treeMap, substation);
            });
        }

    }

    private static void setTwoTransformerTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> substation) {
        List<Map<String, Object>> maintransformers = ParseDataUtil.parseList(substation.get(TableName.MAIN_TRANSFORMER + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(maintransformers)) {
            maintransformers.forEach(main -> {
                setTreeNode(treeMap, main);
            });
        }
        List<Map<String, Object>> distributiontransformers = ParseDataUtil.parseList(substation.get(TableName.DISTRIBUTION_TRANSFORMER + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(distributiontransformers)) {
            distributiontransformers.forEach(dis -> {
                setTreeNode(treeMap, dis);
            });
        }

    }

    private static List<Map<String, Object>> getPowerdistributionAreaLine(List<Map<String, Object>> powerdistributionareaList) {
        List<Map<String, Object>> lines = new ArrayList<>();
        powerdistributionareaList.forEach(power->{
            if(power.containsKey(TableName.LINE + ColumnName.MODEL1)){
                lines.addAll(ParseDataUtil.parseList(power.get(TableName.LINE + TableName.PREFIX)));
            }
        });
        return lines;
    }

    /**
     * @param treeMap
     * @param powerdistributionarea
     * @Description: 设置PqTerminal树分支
     * @Author: gongtong
     **/
    private static void setElectricityUserTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> powerdistributionarea) {
        List<Map<String, Object>> electricityusers = ParseDataUtil.parseList(powerdistributionarea.get(TableName.ELECTRICITYUSER + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(electricityusers)) {
            electricityusers.forEach(electricityuser -> {
                setTreeNode(treeMap, electricityuser);
            });
        }
    }

    /**
     * @param treeMap
     * @param substation
     * @Description: 设置PqTerminal树分支
     * @Author: gongtong
     **/
    private static void setPowerGenerationUserTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> substation) {
        List<Map<String, Object>> powergenerationusers = ParseDataUtil.parseList(substation.get(TableName.POWERGENERATIONUSER + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(powergenerationusers)) {
            powergenerationusers.forEach(powergenerationuser -> {
                setTreeNode(treeMap, powergenerationuser);
            });
        }
    }

    /**
     * @param treeMap
     * @param substation
     * @Description: 设置PqTerminal树分支
     * @Author: gongtong
     **/
    private static void setPqTerminalTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> substation) {
        List<Map<String, Object>> pqTerminals = ParseDataUtil.parseList(substation.get(TableName.PQTERMINAL + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(pqTerminals)) {
            pqTerminals.forEach(pqTerminal -> {
                setTreeNode(treeMap, pqTerminal);
            });
        }
    }

    /**
     * @param treeMap
     * @param substation
     * @Description: 设置PqTerminal树分支
     * @Author: gongtong
     **/
    private static void setPowerdistributionAreaTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> substation) {
        List<Map<String, Object>> powerdistributionAreas = ParseDataUtil.parseList(substation.get(TableName.POWERDISTRIBUTIONAREA + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(powerdistributionAreas)) {
            powerdistributionAreas.forEach(powerdistributionArea -> {
                setTreeNode(treeMap, powerdistributionArea);
                //台区下监测点
                List<Map<String, Object>> lines = ParseDataUtil.parseList(powerdistributionArea.get(TableName.LINE + TableName.PREFIX));
                if (CollectionUtils.isNotEmpty(lines)) {
                    setLineTreeNode(treeMap, lines);
                }
                //台区下监测点
                setPqTerminalTreeNode(treeMap, powerdistributionArea);
                //台区下的普通用户
                setElectricityUserTreeNode(treeMap, powerdistributionArea);
            });
        }
    }

    /**
     * @param treeMap
     * @param substation
     * @Description: 设置治理设备树分支
     * @Author: gongtong
     **/
    private static void setGovernanceDeviceTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> substation) {
        List<Map<String, Object>> governanceDevices = ParseDataUtil.parseList(substation.get(TableName.GOVERNANCEDEVICE + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(governanceDevices)) {
            governanceDevices.forEach(governanceDevice -> {
                setTreeNode(treeMap, governanceDevice);
            });
        }
    }

    /**
     * @param treeMap
     * @param city
     * @Description: 设置测试仪器树分支
     * @Author: gongtong
     **/
    private static void setTestInstrumentTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> city) {
        List<Map<String, Object>> testInstruments = ParseDataUtil.parseList(city.get(TableName.TESTINSTRUMENT + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(testInstruments)) {
            testInstruments.forEach(testInstrument -> {
                setTreeNode(treeMap, testInstrument);
            });
        }
    }

    /**
     * @param treeMap
     * @param lines
     * @Description: 设置line树分支
     * @Author: gongtong
     **/
    private static void setLineTreeNode(Map<String, Set<Long>> treeMap, List<Map<String, Object>> lines) {
        if (CollectionUtils.isNotEmpty(lines)) {
            lines.forEach(line -> {
                setTreeNode(treeMap, line);
                // 设置终端
                setPqTerminalTreeNode(treeMap, line);
            });
        }
    }

    /**
     * @Description: 设置树分支
     * @Author: gongtong
     **/
    private static void setTreeNode(Map<String, Set<Long>> treeMap, Map<String, Object> unit) {
        if (CollectionUtils.isNotEmpty(treeMap.get(ParseDataUtil.parseString(unit.get(ColumnName.MODELLABEL))))) {
            treeMap.get(ParseDataUtil.parseString(unit.get(ColumnName.MODELLABEL))).add(ParseDataUtil.parseLong(unit.get(ColumnName.ID)));
        } else {
            Set<Long> ids = Sets.newTreeSet(Arrays.asList(ParseDataUtil.parseLong(unit.get(ColumnName.ID))));
            treeMap.put(ParseDataUtil.parseString(unit.get(ColumnName.MODELLABEL)), ids);
        }
    }
}

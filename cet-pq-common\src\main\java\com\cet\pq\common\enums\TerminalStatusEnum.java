package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 终端状态枚举
 *
 * <AUTHOR>
 */
public enum TerminalStatusEnum {
    //终端状态枚举
    unused("未投运", 10),
    run("在运", 20),
    retired("退役", 30),
    remain("现场留用", 31),
    bak("库存备用", 32),
    scrap("待报废", 33),
    scrapped("报废", 40);

    private String status;
    private Integer value;

    private TerminalStatusEnum(String status, Integer value) {
        this.status = status;
        this.value = value;
    }

    public static Integer getStatus(String key) {
        for (TerminalStatusEnum terminalStatusEnum : TerminalStatusEnum.values()) {
            if (terminalStatusEnum.status.equals(key)) {
                return terminalStatusEnum.value;
            }
        }
        throw new CommonManagerException("终端状态枚举值转化异常");
    }

    public static Integer getStatusNoThrows(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        for (TerminalStatusEnum terminalStatusEnum : TerminalStatusEnum.values()) {
            if (terminalStatusEnum.status.equals(key)) {
                return terminalStatusEnum.value;
            }
        }
        return 0;
    }

    public static String getStatusByValueNoThrows(Integer value) {
        for (TerminalStatusEnum terminalStatusEnum : TerminalStatusEnum.values()) {
            if (terminalStatusEnum.value.equals(value)) {
                return terminalStatusEnum.status;
            }
        }
        return StringUtils.EMPTY;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}

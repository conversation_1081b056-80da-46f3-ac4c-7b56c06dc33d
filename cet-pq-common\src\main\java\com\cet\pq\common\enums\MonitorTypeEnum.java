package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/03/28 11:41
 */
public enum MonitorTypeEnum {

    ALL("全部", 0),
    MAIN("主网", 1),
    DISTRIBUTION("配网", 2),
    GENERATIONSIDE("发电侧", 3),
    USERSIDE("用户侧", 4),

    METERSYSTEM("计量系统", 5),
    DISPATCHSYSTEM("调度系统", 6),
    LOTSYSTEM("感知终端", 7),
    VOLTAGE("电压系统", 8),
    DEVICE("可调资源", 9)
    ;

    private String name;

    private Integer value;

    MonitorTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static String getNameByValue(Integer value) {
        for (MonitorTypeEnum monitorTypeEnum : values()) {
            if (monitorTypeEnum.getValue().equals(value)) {
                return monitorTypeEnum.getName();
            }
        }
        return null;
    }

    public static Integer getValueByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (MonitorTypeEnum monitorTypeEnum : values()) {
            if (monitorTypeEnum.getName().equals(name)) {
                return monitorTypeEnum.getValue();
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }


    public Integer getValue() {
        return value;
    }

}

package com.cet.pq.anlysis.common.enums.offlinetest;

import com.cet.pq.anlysis.model.offlinetest.ExcelModule;
import com.cet.pq.common.exception.CommonManagerException;

import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum ModuleEnum {
    //默认模板
	XIN_JIANG("默认模板","xinjiang",1),
	AN_HUI("安徽模板","anhui",2),
	SHAN_XI("山西模板","shanxi",3),
	;

	private String name;
	
	private String path;
	
	private Integer value;

	private ModuleEnum(String name, String path,Integer value) {
		this.name = name;
		this.path = path;
		this.value = value;
	}
	
	public static String getNameByValue(Integer value) {
		for(ModuleEnum moduleEnum : ModuleEnum.values()) {
			if(moduleEnum.value.intValue() == value) {
				return moduleEnum.name;
			}
		}
		throw new CommonManagerException("Excel模板不传在");
	}
	
	public static String getPathByValue(Integer value) {
		for(ModuleEnum moduleEnum : ModuleEnum.values()) {
			if(moduleEnum.value.intValue() == value) {
				return moduleEnum.path;
			}
		}
		throw new CommonManagerException("Excel模板不传在");
	}
	
	public static List<ExcelModule> getAllModule(){
		List<ExcelModule> moduleList = new ArrayList<>();
		ExcelModule excelModule;
		for(ModuleEnum moduleEnum : ModuleEnum.values()) {
			excelModule = new ExcelModule(moduleEnum.name, moduleEnum.path, moduleEnum.value);
			moduleList.add(excelModule);
		}
		return moduleList;
	} 
	
	
	
}

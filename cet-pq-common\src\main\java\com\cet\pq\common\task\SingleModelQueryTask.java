package com.cet.pq.common.task;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import com.cet.pq.common.feign.ModelDataService;
import com.cet.pq.common.model.QueryCondition;
import com.cet.pq.common.model.ResultWithTotal;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Async
@Component
@Lazy
public class SingleModelQueryTask {

	private static final Logger log = LoggerFactory.getLogger(SingleModelQueryTask.class);
	
	@Autowired
	private ModelDataService modelDataService;
	
	/**
	 * 分页查询
	 * @param <T>
	 * @return
	 */
	@Async(value = "threadPoolExecutor")
	public CompletableFuture<Object> asyncSingleModelQuery(QueryCondition condition){
		Object data = new ArrayList<>();
		try {
			ResultWithTotal<List<Map<String, Object>>> result = modelDataService.queryObj(condition);
			data = CollectionUtils.isEmpty(result.getData())?new ArrayList<>():result.getData();
			log.debug(Thread.currentThread().getName() + "分页查询开始.............................................................................");
		} catch (Exception e) {
			return AsyncResult.forExecutionException(e).completable();
		}
		return AsyncResult.forValue(data).completable();
	}
	
	
	
}

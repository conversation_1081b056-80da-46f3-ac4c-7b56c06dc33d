package com.cet.pq.anlysis.common.enums.offlinetest;


//@AI-Generated-start

import com.cet.pq.common.enums.WiredTypeEnum;

import java.util.*;

/**
 * @Title: DataIdLine
 * @Package: com.cet.pq.anlysis.common.enums
 * @Description:
 * @Author: zhangyifu
 * @Date: 2025/4/23 9:39
 * @Version:1.0
 */
public enum DataIdLineEnums {
    //频率偏差（Hz）、电压偏差（V）、电压总畸变率(%)（2、3-25次中的奇次）、负序电压不平衡度(%)、长时间闪变、电流（A）、
    // 电流总畸变率(%)、谐波电流有效值(A)（2、3-25次中的奇次）、电流不平衡度(%)、正序电流、负序电流、零序电流
    // 通用 0 ，1 - 星， 2 - 角

    DATA_HZ(0, Arrays.asList(97L),"频率偏差","frequencyDeviation",7),
    DATA_V_S(1,Arrays.asList(94L,95L,96L),"电压偏差","deviation",8),
    DATA_V_I(2, Arrays.asList(100L, 101L, 102L),"电压偏差","deviation",8),
    DATA_V_DISTORTION_S(1,Arrays.asList(32L,33L,34L),"电压畸变率", "totalVoltageDistortionRate",9),

    DATA_V_DISTORTION_I(2,Arrays.asList(35L, 36L, 37L),"电压畸变率","totalVoltageDistortionRate",9),
    DATA_V_DISTORTION_ODD_2_S(1,Arrays.asList(10002L,20002L,30002L),"电压谐波含有率 - 2次","harmonicVoltageContent2",10),
    DATA_V_DISTORTION_ODD_3_S(1,Arrays.asList(10003L,20003L,30003L),"电压谐波含有率 - 3次", "harmonicVoltageContent3",11),
    DATA_V_DISTORTION_ODD_4_S(1,Arrays.asList(10004L,20004L,30004L),"电压谐波含有率 - 4次", "harmonicVoltageContent4",12),
    DATA_V_DISTORTION_ODD_5_S(1,Arrays.asList(10005L,20005L,30005L),"电压谐波含有率 - 5次", "harmonicVoltageContent5",13),
    DATA_V_DISTORTION_ODD_6_S(1,Arrays.asList(10006L,20006L,30006L),"电压谐波含有率 - 6次", "harmonicVoltageContent6",14),
    DATA_V_DISTORTION_ODD_7_S(1,Arrays.asList(10007L,20007L,30007L),"电压谐波含有率 - 7次", "harmonicVoltageContent7",15),
    DATA_V_DISTORTION_ODD_8_S(1,Arrays.asList(10008L,20008L,30008L),"电压谐波含有率 - 8次", "harmonicVoltageContent8",16),
    DATA_V_DISTORTION_ODD_9_S(1,Arrays.asList(10009L,20009L,30009L),"电压谐波含有率 - 9次", "harmonicVoltageContent9",17),
    DATA_V_DISTORTION_ODD_10_S(1,Arrays.asList(10010L,20010L,30010L),"电压谐波含有率 - 10次", "harmonicVoltageContent10",18),
    DATA_V_DISTORTION_ODD_11_S(1,Arrays.asList(10011L,20011L,30011L),"电压谐波含有率 - 11次", "harmonicVoltageContent11",19),
    DATA_V_DISTORTION_ODD_12_S(1,Arrays.asList(10012L,20012L,30012L),"电压谐波含有率 - 12次", "harmonicVoltageContent12",20),
    DATA_V_DISTORTION_ODD_13_S(1,Arrays.asList(10013L,20013L,30013L),"电压谐波含有率 - 13次", "harmonicVoltageContent13",21),
    DATA_V_DISTORTION_ODD_14_S(1,Arrays.asList(10014L,20014L,30014L),"电压谐波含有率 - 14次", "harmonicVoltageContent14",22),
    DATA_V_DISTORTION_ODD_15_S(1,Arrays.asList(10015L,20015L,30015L),"电压谐波含有率 - 15次", "harmonicVoltageContent15",23),
    DATA_V_DISTORTION_ODD_16_S(1,Arrays.asList(10016L,20016L,30016L),"电压谐波含有率 - 16次", "harmonicVoltageContent16",24),
    DATA_V_DISTORTION_ODD_17_S(1,Arrays.asList(10017L,20017L,30017L),"电压谐波含有率 - 17次", "harmonicVoltageContent17",25),
    DATA_V_DISTORTION_ODD_18_S(1,Arrays.asList(10018L,20018L,30018L),"电压谐波含有率 - 18次", "harmonicVoltageContent18",26),
    DATA_V_DISTORTION_ODD_19_S(1,Arrays.asList(10019L,20019L,30019L),"电压谐波含有率 - 19次", "harmonicVoltageContent19",27),
    DATA_V_DISTORTION_ODD_20_S(1,Arrays.asList(10020L,20020L,30020L),"电压谐波含有率 - 20次", "harmonicVoltageContent20",28),
    DATA_V_DISTORTION_ODD_21_S(1,Arrays.asList(10021L,20021L,30021L),"电压谐波含有率 - 21次", "harmonicVoltageContent21",29),
    DATA_V_DISTORTION_ODD_22_S(1,Arrays.asList(10022L,20022L,30022L),"电压谐波含有率 - 22次", "harmonicVoltageContent22",30),
    DATA_V_DISTORTION_ODD_23_S(1,Arrays.asList(10023L,20023L,30023L),"电压谐波含有率 - 23次", "harmonicVoltageContent23",31),
    DATA_V_DISTORTION_ODD_24_S(1,Arrays.asList(10024L,20024L,30024L),"电压谐波含有率 - 24次", "harmonicVoltageContent24",32),
    DATA_V_DISTORTION_ODD_25_S(1,Arrays.asList(10025L,20025L,30025L),"电压谐波含有率 - 25次", "harmonicVoltageContent25",33),

    DATA_V_DISTORTION_ODD_2_I(2,Arrays.asList(40002L, 50002L, 60002L),"电压谐波含有率 - 2次", "harmonicVoltageContent2",10),
    DATA_V_DISTORTION_ODD_3_I(2, Arrays.asList(40003L, 50003L, 60003L),"电压谐波含有率 - 3次", "harmonicVoltageContent3",11),
    DATA_V_DISTORTION_ODD_4_I(2,Arrays.asList(40004L,50004L,60004L),"电压谐波含有率 - 4次", "harmonicVoltageContent4",12),
    DATA_V_DISTORTION_ODD_5_I(2, Arrays.asList(40005L, 50005L, 60005L),"电压谐波含有率 - 5次", "harmonicVoltageContent5",13),
    DATA_V_DISTORTION_ODD_6_I(2,Arrays.asList(40006L,50006L,60006L),"电压谐波含有率 - 6次", "harmonicVoltageContent6",14),
    DATA_V_DISTORTION_ODD_7_I(2,Arrays.asList(40007L,50007L,60007L),"电压谐波含有率 - 7次", "harmonicVoltageContent7",15),
    DATA_V_DISTORTION_ODD_8_I(2,Arrays.asList(40008L,50008L,60008L),"电压谐波含有率 - 8次", "harmonicVoltageContent8",16),
    DATA_V_DISTORTION_ODD_9_I(2,Arrays.asList(40009L,50009L,60009L),"电压谐波含有率 - 9次", "harmonicVoltageContent9",17),
    DATA_V_DISTORTION_ODD_10_I(2,Arrays.asList(40010L,50010L,60010L),"电压谐波含有率 - 10次", "harmonicVoltageContent10",18),
    DATA_V_DISTORTION_ODD_11_I(2,Arrays.asList(40011L,50011L,60011L),"电压谐波含有率 - 11次", "harmonicVoltageContent11",19),
    DATA_V_DISTORTION_ODD_12_I(2,Arrays.asList(40012L,50012L,60012L),"电压谐波含有率 - 12次", "harmonicVoltageContent12",20),
    DATA_V_DISTORTION_ODD_13_I(2,Arrays.asList(40013L,50013L,60013L),"电压谐波含有率 - 13次", "harmonicVoltageContent13",21),
    DATA_V_DISTORTION_ODD_14_I(2,Arrays.asList(40014L,50014L,60014L),"电压谐波含有率 - 14次", "harmonicVoltageContent14",22),
    DATA_V_DISTORTION_ODD_15_I(2,Arrays.asList(40015L,50015L,60015L),"电压谐波含有率 - 15次", "harmonicVoltageContent15",23),
    DATA_V_DISTORTION_ODD_16_I(2,Arrays.asList(40016L,50016L,60016L),"电压谐波含有率 - 16次", "harmonicVoltageContent16",24),
    DATA_V_DISTORTION_ODD_17_I(2,Arrays.asList(40017L,50017L,60017L),"电压谐波含有率 - 17次", "harmonicVoltageContent17",25),
    DATA_V_DISTORTION_ODD_18_I(2,Arrays.asList(40018L,50018L,60018L),"电压谐波含有率 - 18次", "harmonicVoltageContent18",26),
    DATA_V_DISTORTION_ODD_19_I(2,Arrays.asList(40019L,50019L,60019L),"电压谐波含有率 - 19次" , "harmonicVoltageContent19",27),
    DATA_V_DISTORTION_ODD_20_I(2,Arrays.asList(40020L,50020L,60020L),"电压谐波含有率 - 20次", "harmonicVoltageContent20",28),
    DATA_V_DISTORTION_ODD_21_I(2,Arrays.asList(40021L,50021L,60021L),"电压谐波含有率 - 21次", "harmonicVoltageContent21",29),
    DATA_V_DISTORTION_ODD_22_I(2,Arrays.asList(40022L,50022L,60022L),"电压谐波含有率 - 22次", "harmonicVoltageContent22",30),
    DATA_V_DISTORTION_ODD_23_I(2,Arrays.asList(40023L,50023L,60023L),"电压谐波含有率 - 23次", "harmonicVoltageContent23",31),
    DATA_V_DISTORTION_ODD_24_I(2,Arrays.asList(40024L,50024L,60024L),"电压谐波含有率 - 24次", "harmonicVoltageContent24",32),
    DATA_V_DISTORTION_ODD_25_I(2,Arrays.asList(40025L,50025L,60025L),"电压谐波含有率 - 25次", "harmonicVoltageContent25",33),
    DATA_V_SEQUENCE(0,Arrays.asList(69L),"电压负序不平衡度", "negativeSequenceVoltageImbalance",34),
    DATA_FLICKER_S(1,Arrays.asList(82L,83L,84L),"长时间闪变", "longTermFlicker",35),
    DATA_FLICKER_I(2, Arrays.asList(85L, 86L, 87L),"长时间闪变", "longTermFlicker",35),
    DATA_I(0,Arrays.asList(1000001L,1000002L,1000003L),"电流", "electricCurrent",36),
    DATA_I_DISTORTION(0,Arrays.asList(1000016L,1000017L,1000018L),"电流畸变率", "totalCurrentDistortionRate",37),

    DATA_I_DISTORTION_ODD_2(0,Arrays.asList(1040002L,1050002L,1060002L),"电流谐波有效值 - 2次", "effectiveValueOfHarmonicCurrent2",38),
    DATA_I_DISTORTION_ODD_3(0,Arrays.asList(1040003L,1050003L,1060003L),"电流谐波有效值 - 3次", "effectiveValueOfHarmonicCurrent3",39),
    DATA_I_DISTORTION_ODD_4(0,Arrays.asList(1040004L,1050004L,1060004L),"电流谐波有效值 - 4次", "effectiveValueOfHarmonicCurrent4",40),
    DATA_I_DISTORTION_ODD_5(0,Arrays.asList(1040005L,1050005L,1060005L),"电流谐波有效值 - 5次", "effectiveValueOfHarmonicCurrent5",41),
    DATA_I_DISTORTION_ODD_6(0,Arrays.asList(1040006L,1050006L,1060006L),"电流谐波有效值 - 6次", "effectiveValueOfHarmonicCurrent6",42),
    DATA_I_DISTORTION_ODD_7(0,Arrays.asList(1040007L,1050007L,1060007L),"电流谐波有效值 - 7次", "effectiveValueOfHarmonicCurrent7",43),
    DATA_I_DISTORTION_ODD_8(0,Arrays.asList(1040008L,1050008L,1060008L),"电流谐波有效值 - 8次", "effectiveValueOfHarmonicCurrent8",44),
    DATA_I_DISTORTION_ODD_9(0,Arrays.asList(1040009L,1050009L,1060009L),"电流谐波有效值 - 9次", "effectiveValueOfHarmonicCurrent9",45),
    DATA_I_DISTORTION_ODD_10(0,Arrays.asList(1040010L,1050010L,1060010L),"电流谐波有效值 - 10次", "effectiveValueOfHarmonicCurrent10",46),
    DATA_I_DISTORTION_ODD_11(0,Arrays.asList(1040011L,1050011L,1060011L),"电流谐波有效值 - 11次", "effectiveValueOfHarmonicCurrent11",47),
    DATA_I_DISTORTION_ODD_12(0,Arrays.asList(1040012L,1050012L,1060012L),"电流谐波有效值 - 12次", "effectiveValueOfHarmonicCurrent12",48),
    DATA_I_DISTORTION_ODD_13(0,Arrays.asList(1040013L,1050013L,1060013L),"电流谐波有效值 - 13次", "effectiveValueOfHarmonicCurrent13",49),
    DATA_I_DISTORTION_ODD_14(0,Arrays.asList(1040014L,1050014L,1060014L),"电流谐波有效值 - 14次", "effectiveValueOfHarmonicCurrent14",50),
    DATA_I_DISTORTION_ODD_15(0,Arrays.asList(1040015L,1050015L,1060015L),"电流谐波有效值 - 15次", "effectiveValueOfHarmonicCurrent15",51),
    DATA_I_DISTORTION_ODD_16(0,Arrays.asList(1040016L,1050016L,1060016L),"电流谐波有效值 - 16次", "effectiveValueOfHarmonicCurrent16",52),
    DATA_I_DISTORTION_ODD_17(0,Arrays.asList(1040017L,1050017L,1060017L),"电流谐波有效值 - 17次", "effectiveValueOfHarmonicCurrent17",53),
    DATA_I_DISTORTION_ODD_18(0,Arrays.asList(1040018L,1050018L,1060018L),"电流谐波有效值 - 18次", "effectiveValueOfHarmonicCurrent18",54),
    DATA_I_DISTORTION_ODD_19(0,Arrays.asList(1040019L,1050019L,1060019L),"电流谐波有效值 - 19次", "effectiveValueOfHarmonicCurrent19",55),
    DATA_I_DISTORTION_ODD_20(0,Arrays.asList(1040020L,1050020L,1060020L),"电流谐波有效值 - 20次", "effectiveValueOfHarmonicCurrent20",56),
    DATA_I_DISTORTION_ODD_21(0,Arrays.asList(1040021L,1050021L,1060021L),"电流谐波有效值 - 21次", "effectiveValueOfHarmonicCurrent21",57),
    DATA_I_DISTORTION_ODD_22(0,Arrays.asList(1040022L,1050022L,1060022L),"电流谐波有效值 - 22次", "effectiveValueOfHarmonicCurrent22",58),
    DATA_I_DISTORTION_ODD_23(0,Arrays.asList(1040023L,1050023L,1060023L),"电流谐波有效值 - 23次", "effectiveValueOfHarmonicCurrent23",59),
    DATA_I_DISTORTION_ODD_24(0,Arrays.asList(1040024L,1050024L,1060024L),"电流谐波有效值 - 24次", "effectiveValueOfHarmonicCurrent24",60),
    DATA_I_DISTORTION_ODD_25(0,Arrays.asList(1040025L,1050025L,1060025L),"电流谐波有效值 - 25次", "effectiveValueOfHarmonicCurrent25",61),
    DATA_I_SEQUENCE(0,Arrays.asList(1000035L),"电流不平衡度", "currentImbalanceDegree",62),
    DATA_I_ZERO(0,Arrays.asList(1000038L),"零序电流", "zeroSequenceCurrent",63),
    DATA_I_POSITIVE(0,Arrays.asList(1000036L),"正序电流", "positiveSequenceCurrent",64),
    DATA_I_NEGATIVE(0,Arrays.asList(1000037L),"负序电流", "negativeSequenceCurrent",65)

    ;

    private Integer type;

    private List<Long> dataIdS;
    private String mean;

    private String fieldName;

    private Integer columns;

    DataIdLineEnums(Integer type, List<Long> dataIdS, String mean, String fieldName, Integer columns) {
        this.type = type;
        this.dataIdS = dataIdS;
        this.mean = mean;
        this.fieldName = fieldName;
        this.columns = columns;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<Long> getDataIdS() {
        return dataIdS;
    }

    public void setDataIdS(List<Long> dataIdS) {
        this.dataIdS = dataIdS;
    }

    public String getMean() {
        return mean;
    }

    public void setMean(String mean) {
        this.mean = mean;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public Integer getColumns() {
        return columns;
    }

    public void setColumns(Integer columns) {
        this.columns = columns;
    }

    public static List<Long> getAllDataIdLine95_S() {
        List<Long> list = new ArrayList<>();
        for(DataIdLineEnums enums : DataIdLineEnums.values()) {
            // 剔除角型
            if (enums.getType() == 2) {
                continue;
            }
            // 剔除长时间闪变
            if (DATA_FLICKER_S.equals(enums)) {
                continue;
            }
            list.addAll(enums.getDataIdS());
        }
        return list;
    }

    public static List<Long> getAllDataIdLine95_I() {
        List<Long> list = new ArrayList<>();
        for(DataIdLineEnums enums : DataIdLineEnums.values()) {
            // 剔除星型
            if (enums.getType() == 1) {
                continue;
            }
            // 剔除长时间闪变
            if (DATA_FLICKER_I.equals(enums)) {
                continue;
            }
            list.addAll(enums.getDataIdS());
        }
        return list;
    }

    public static List<DataIdLineEnums> getEnumStar() {
        List<DataIdLineEnums> list = new ArrayList<>();
        for(DataIdLineEnums enums : DataIdLineEnums.values()) {
            // 剔除角型
            if (enums.getType() == 2) {
                continue;
            }
            list.add(enums);
        }
        return list;
    }

    public static List<DataIdLineEnums> getEnumIndustry() {
        List<DataIdLineEnums> list = new ArrayList<>();
        for(DataIdLineEnums enums : DataIdLineEnums.values()) {
            // 剔除星型
            if (enums.getType() == 1) {
                continue;
            }
            list.add(enums);
        }
        return list;
    }

    public static Map<String,Boolean> getFiledMap(Integer wiredType, Map<Long,Boolean> flagMap) {
        Map<String,Boolean> filedMap= new HashMap<>();
        if (Objects.equals(WiredTypeEnum.star.getValue(), wiredType)) {
            for(DataIdLineEnums enums : DataIdLineEnums.getEnumStar()) {
                flagMap.forEach((k,v)->{
                    if (enums.getDataIdS().contains(k)) {
                        filedMap.put(enums.getFieldName(),v);
                    }
                });
            }
        } else if (Objects.equals(WiredTypeEnum.triangle.getValue(), wiredType)) {
            for(DataIdLineEnums enums : DataIdLineEnums.getEnumIndustry()) {
                flagMap.forEach((k,v)->{
                    if (enums.getDataIdS().contains(k)) {
                        filedMap.put(enums.getFieldName(),v);
                    }
                });

            }
        }

        return filedMap;
    }


    public static List<Long> getElectricList() {
        List<Long> list = new ArrayList<>();
        for(DataIdLineEnums enums : DataIdLineEnums.values()) {
            if (enums.getMean().contains("电流谐波有效值")) {
                list.addAll(enums.getDataIdS()) ;
            }
        }
        return list;
    }


    public static List<Integer> getColumnsList(Map<String,Boolean> map, Integer wiredType) {
        if (map == null || map.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> list = new ArrayList<>();
        for(DataIdLineEnums enums : DataIdLineEnums.values()) {
            if (!enums.getType().equals(wiredType) && !enums.getType().equals(0)) {
                continue;
            }
            if (map.containsKey(enums.getFieldName()) && Boolean.TRUE.equals(map.get(enums.getFieldName()))) {
                list.add(enums.getColumns() - 2 );
            }
        }
        return list;
    }


    public static List<Long> getAllDataIdLineMax_S() {
        return DATA_FLICKER_S.getDataIdS();
    }

    public static List<Long> getAllDataIdLineMax_I() {
        return DATA_FLICKER_I.getDataIdS();
    }
}
//@AI-Generated-end
package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum CoverterTypeEnum {
	//换流站类型
	conventionalDC("常规直流",1),
	backToBackDC("背靠背直流",2),
	UHVDC("特高压直流",3);
	
	private String name;
	private Integer value;

	private CoverterTypeEnum(String name, Integer value) {
		this.name = name;
		this.value = value;
	}

	public static Integer getValueByName(String name) {
		for (CoverterTypeEnum coverterTypeEnum : CoverterTypeEnum.values()) {
			if(name.equals(coverterTypeEnum.name)) {
				return coverterTypeEnum.value;
			}
		}
		throw new CommonManagerException("换流站类型值无效");
	}
	
	public static Integer getValueByNameNoThrows(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (CoverterTypeEnum coverterTypeEnum : CoverterTypeEnum.values()) {
			if(coverterTypeEnum.name.equals(name)) {
				return coverterTypeEnum.value;
			}
		}
		return 0;
	}

	public static String getNameByValueNoThrows(Integer value) {
		for (CoverterTypeEnum coverterTypeEnum : CoverterTypeEnum.values()) {
			if (coverterTypeEnum.value.equals(value)) {
				return coverterTypeEnum.name;
			}
		}
		return StringUtils.EMPTY;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}
}

package com.cet.pq.anlysis.model.monitordatastatistics;

import lombok.Data;

import java.util.Objects;

/**
 * 关键数据指标
 * <AUTHOR>
 *
 */
@Data
public class LineHistoryDataCount {


    //国网指标类型平均数
    private Double indexAvgCount;
    //国网纵向指标类型完整率
    private Double indexTypeComplexRate;
    //国网纵向指标数据完整率
    private Double dataComplexRate;
    //国网纵向指标数据准确率
    private Double dataCorrectRate;

    //关键指标类型平均数
    private Double keyIndexAvgCount;
    //关键指标类型完整率
    private Double keyIndexTypeComplexRate;
    //关键指标数据完整率
    private Double keyDataComplexRate;
    //关键指标数据准确率
    private Double keyDataCorrectRate;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LineHistoryDataCount that = (LineHistoryDataCount) o;
        return Objects.equals(indexAvgCount, that.indexAvgCount) && Objects.equals(indexTypeComplexRate, that.indexTypeComplexRate) && Objects.equals(dataComplexRate, that.dataComplexRate) && Objects.equals(dataCorrectRate, that.dataCorrectRate) && Objects.equals(keyIndexAvgCount, that.keyIndexAvgCount) && Objects.equals(keyIndexTypeComplexRate, that.keyIndexTypeComplexRate) && Objects.equals(keyDataComplexRate, that.keyDataComplexRate) && Objects.equals(keyDataCorrectRate, that.keyDataCorrectRate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(indexAvgCount, indexTypeComplexRate, dataComplexRate, dataCorrectRate, keyIndexAvgCount, keyIndexTypeComplexRate, keyDataComplexRate, keyDataCorrectRate);
    }
}

package com.cet.pq.common.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/17 17:10
 */
@ApiModel(value = "电容式电压互感器界面查询参数")
@Data
public class CvtVoltageTransformerQuery {

    @ApiModelProperty(required = true, value = "品牌", example = "桂林")
    private String brand;

    @ApiModelProperty(required = true, value = "额定电压", example = "35")
    private Double ratedVoltage;

    @ApiModelProperty(required = true, value = "额定电容", example = "35")
    private Double ratedCapacitance;

    /**
     *谐波测量误差曲线来源: -1:无(默认), 0:试验数据, 1:模型运算
     */
    @ApiModelProperty(required = true, value = "谐波测量误差曲线来源", example = "0")
    private Integer harmonicMeasurementErrorCurve;

    @ApiModelProperty(required = true, value = "开始时间", example = "1111111")
    private Long startTime;

    @ApiModelProperty(required = true, value = "结束时间", example = "2222222")
    private Long endTime;

    @ApiModelProperty(required = true, value = "节点模型id", example = "123")
    private Long modelId;

    @ApiModelProperty(required = true, value = "节点模型名称", example = "line")
    private String modelLabel;

    @ApiModelProperty(value = "参数设置表格数据")
    private List<ModelParameterQuery> modelParameterQueryList;

}

package com.cet.pq.common.model.objective.physicalquantity;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 物理量模型定义
 */
@Data
public class QuantityDefinition {
    private long id;

    private Integer aggregationCycle;

    private Integer aggregationType;

    private String alias;

    private boolean cumulative;

    private Long dataId;

    private boolean electrical;

    private String name;

    private Integer unaturalBeginDay;

    private Integer unaturalBeginHour;

    private Integer unitMultiplier;

    private Integer unitSymbol;
}

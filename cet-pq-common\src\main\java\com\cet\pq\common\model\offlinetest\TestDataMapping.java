package com.cet.pq.common.model.offlinetest;

import com.cet.pq.common.constant.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/6/5 17:33
 * @Description 测试数据表格映射关系表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestDataMapping {

    private String modelLabel = TableName.TEST_DATA_MAPPING;
    private Long id;
    @JsonProperty("templateid")
    private Long templateId;
    @JsonProperty("sheetname")
    private String sheetName;
    @JsonProperty("rownumber")
    private String rowNumber;
    @JsonProperty("colnumber")
    private String colNumber;
    @JsonProperty("dataid")
    private Long dataId;
    @JsonProperty("aggregationtype")
    private Integer aggregationType;
    @JsonProperty("cellmean")
    private String cellMean;
    @JsonProperty("wiredtype")
    private Integer wiredType;
    @JsonProperty("processmethod")
    private Integer processMethod;

    public TestDataMapping(Long templateId, String sheetName, String rowNumber, String colNumber, Long dataId, String cellMean, Integer wiredType, Integer aggregationType) {
        this.templateId = templateId;
        this.sheetName = sheetName;
        this.rowNumber = rowNumber;
        this.colNumber = colNumber;
        this.dataId = dataId;
        this.cellMean = cellMean;
        this.wiredType = wiredType;
        this.aggregationType = aggregationType;
    }
}

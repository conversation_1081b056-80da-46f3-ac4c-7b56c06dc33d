package com.cet.pq.common.enums;

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.exception.ExcelException;
import org.apache.commons.lang3.StringUtils;

/**
 * 导出文件sheet名、模板sheet名、图片位置枚举、写数据起始行
 * <AUTHOR>
 * @date 2021/2/7 11:06
 */
public enum TemplateDataEnum {

    // 监测点台账统计
    SheetAndTemplateNameEnum1("monitorCountByCompany", "管理单位统计", "管理单位统计", null, 9, 14, 18),
    SheetAndTemplateNameEnum2("monitorCountByObject", "监测对象类型统计", "监测对象类型统计", null, 9, 14, 18),
    SheetAndTemplateNameEnum3("monitorCountByVoltClass", "电压等级统计", "电压等级统计", null, 9, 14, 18),
    SheetAndTemplateNameEnum4("monitorCountByCompany_VoltClass", "管理单位 - 电压等级统计", "管理单位 - 电压等级统计", null, 6, 14, 18),
    SheetAndTemplateNameEnum5("monitorCountByCompany_ObjectType", "管理单位 - 监测对象类型统计", "管理单位 - 监测对象类型统计", null, 6, 14, 18),
    SheetAndTemplateNameEnum6("monitorCountByVoltClass_ObjectType", "电压等级 - 监测对象类型统计", "电压等级 - 监测对象类型统计", null, 6, 14, 18),
    SheetAndTemplateNameEnum7("monitorCountByCompany_MonitorType", "管理单位 - 监测点类型统计", "管理单位 - 监测点类型统计", null, 7, 14, 18),
    SheetAndTemplateNameEnum8("monitorCountByVoltClass_MonitorType", "电压等级 - 监测点类型统计", "电压等级 - 监测点类型统计", null, 7, 14, 18),
    SheetAndTemplateNameEnum9("monitorCountByObjectType_VoltClass", "监测对象类型 - 监测点类型统计", "监测对象类型 - 监测点类型统计", null, 7, 14, 18),
    SheetAndTemplateNameEnum10("monitorCountByType", "监测点类型统计", "监测点类型统计", null, 9, 14, 18),
    SheetAndTemplateNameEnum11("monitorCountByNewCreate", "近五年新增监测点统计", "近五年新增监测点统计", null, 6, 14, 74),
    // 监测点终端台账统计
    SheetAndTemplateNameEnum12("terminalCountByCompany", "管理单位统计", "监测终端台账 - 管理单位统计", null, 9, 18, 22),
    SheetAndTemplateNameEnum13("terminalCountByVendor", "厂家统计", "监测终端台账 - 厂家统计", null, 9, 18, 22),
    SheetAndTemplateNameEnum14("terminalCountByCompanyAndVendor", "管理单位 - 厂家统计", "监测终端台账 - 管理单位 - 厂家统计", null, 6, 18, 22),
    SheetAndTemplateNameEnum15("terminalCountByNewCreate1", "第一年新增终端统计", "监测终端台账 - 近五年新增终端统计", null, 6, 18, 22),
    SheetAndTemplateNameEnum16("terminalCountByNewCreate2", "第二年新增终端统计", "监测终端台账 - 近五年新增终端统计", null, 6, 18, 22),
    SheetAndTemplateNameEnum17("terminalCountByNewCreate3", "第三年新增终端统计", "监测终端台账 - 近五年新增终端统计", null, 6, 18, 22),
    SheetAndTemplateNameEnum18("terminalCountByNewCreate4", "第四年新增终端统计", "监测终端台账 - 近五年新增终端统计", null, 6, 18, 22),
    SheetAndTemplateNameEnum19("terminalCountByNewCreate5", "第五年新增终端统计", "监测终端台账 - 近五年新增终端统计", null, 6, 18, 22),
    // 报表管理 - 稳态指标超标统计报表
    SheetAndTemplateNameEnum20("reportOfOverStandardCount", "稳态超标综合统计", "稳态指标超标统计报表 - 稳态超标综合统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum21("reportOfOverStandardCountByCompany", "按管理单位统计", "稳态指标超标统计报表 - 按管理单位统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum22("reportOfOverStandardCountBySubstation", "按变电站统计", "稳态指标超标统计报表 - 按变电站统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum23("reportOfOverStandardCountDetails", "稳态指标超标统计报表 - 超标明细统计", "稳态指标超标统计报表 - 超标明细统计", null, 0, 0, 6),
    SheetAndTemplateNameEnum214("reportOfMainOverStandardCountDetails", "稳态指标超标统计报表 - 主网超标明细统计", "稳态指标超标统计报表 - 主网超标明细统计", null, 0, 0, 5),

    // 报表管理 - 稳态指标合格率统计报表
    SheetAndTemplateNameEnum24("reportOfPassRateCountByCompany", "按管理单位统计", "稳态指标合格率统计报表 - 按管理单位统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum25("reportOfPassRateCountByArea", "按行政区域统计", "稳态指标合格率统计报表 - 按行政区域统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum26("reportOfPassRateCountBySubstation", "按变电站统计", "稳态指标合格率统计报表 - 按变电站统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum27("reportOfPassRateCountByObject", "按监测对象统计", "稳态指标合格率统计报表 - 按监测对象统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum162("reportOfPassRateCountByVoltage", "按电压等级统计", "稳态指标合格率统计报表 - 按电压等级统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum28("reportOfPassRateCountDetail", "稳态指标合格率明细", "稳态指标合格率统计报表 - 稳态指标合格率明细", null, 0, 0, 3),
    SheetAndTemplateNameEnum221("distributionPassRateCountDetail", "稳态指标合格率明细", "配网稳态指标合格率统计报表 - 稳态指标合格率明细", null, 0, 0, 3),
    SheetAndTemplateNameEnum183("reportOfPassRateCountByMonitorType", "按监测点类别统计", "稳态指标合格率统计报表 - 按监测点类别统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum187("overStandardCountByMonth", "合格率趋势统计", "稳态指标合格率统计报表 - 合格率趋势统计", null, 0, 0, 3),
    // 报表管理 - 稳态指标数据报表
    SheetAndTemplateNameEnum29("singlePointReport", "单点数据报表", "稳态指标数据报表 - 单点数据报表", null, 0, 0, 1),
    SHEETANDTEMPLATENAMEENUM258("exportIndicatorDataReportLine", "稳态指标数据报表-监测点数据报表", "稳态指标数据报表-监测点数据报表", null, 0, 0, 3),
    SheetAndTemplateNameEnum30("singlePointDetailReport", "单点详细月报表", "数据详情报表", null, 0, 0, 1),
    // 报表管理 - 暂态事件分类统计报表
    SheetAndTemplateNameEnum31("eventsReport", "暂态事件分类统计", "暂态事件分类统计报表 - 暂态事件分类统计", null, 0, 0, 5),
    SheetAndTemplateNameEnum32("managementUnitReport", "按管理单位统计", "暂态事件分类统计报表 - 按管理单位统计", null, 0, 0, 6),
    SheetAndTemplateNameEnum33("substationReport", "按变电站统计", "暂态事件分类统计报表 - 按变电站统计", null, 0, 0, 4),
    SheetAndTemplateNameEnum34("monitorReport", "按监测点统计", "暂态事件分类统计报表 - 按监测点统计", null, 0, 0, 4),
    // 报表管理 - 告警管理
    SheetAndTemplateNameEnum35("alarmTrendTotal", "指标告警趋势总览", "告警管理 - 告警总览 - 指标告警趋势总览", null, 7, 18, 23),
    SheetAndTemplateNameEnum36("unitAlarmTotal", "各单位指标告警总览", "告警管理 - 告警总览 - 各单位指标告警趋势总览", null, 8, 18, 23),
    SheetAndTemplateNameEnum37("stationAlarmTotal", "电站告警统计", "告警管理 - 告警总览 - 电站告警统计", null, 0, 0, 3),
    SheetAndTemplateNameEnum38("alarmsStatics", "告警统计表", "告警管理 - 告警统计表", null, 0, 0, 5),
    SheetAndTemplateNameEnum39("alarmStationDetails", "告警变电站明细", "告警管理 - 告警变电站明细", null, 0, 0, 4),
    SheetAndTemplateNameEnum40("alarmMonitorLineDetails", "告警监测点明细", "告警管理 - 告警监测点明细", null, 0, 0, 5),
    SheetAndTemplateNameEnum213("mainAlarmMonitorLineDetails", "主网告警监测点明细", "告警管理 - 主网告警监测点明细", null, 0, 0, 5),

    // 主网系统运行指标 - 台帐指标
    SHEETANDTEMPLATENAMEENUM41("bookStatistics", "台帐指标综合统计", "系统运行指标 - 台帐指标综合统计", null, 5, 20, 26),
    SHEETANDTEMPLATENAMEENUM42("bookDistribution", "分布统计", "系统运行指标 - 分布统计", null, 5, 20, 66),
    SHEETANDTEMPLATENAMEENUM43("bookIndexCount", "台帐指标统计", "系统运行指标 - 台帐指标统计", null, 5, 20, 66),
    SHEETANDTEMPLATENAMEENUM44("bookDetailCount", "台帐指标详情", "系统运行指标 - 台帐指标详情", null, 0, 0, 5),
    // 系统运行指标 - 监测数据指标
    SHEETANDTEMPLATENAMEENUM45("dataStatistics", "监测数据指标综合统计", "监测数据指标 - 监测数据指标综合统计", null, 5, 15, 4),
    SHEETANDTEMPLATENAMEENUM46("dataIndexStatistics", "监测数据指标统计", "监测数据指标 - 监测数据指标统计", null, 6, 15, 20),
    SHEETANDTEMPLATENAMEENUM47("dataDetailStatistics", "监测数据指标详情 - 按管理单位统计", "监测数据指标 - 监测数据指标详情", null, 0, 0, 4),
    SHEETANDTEMPLATENAMEENUM78("dataDetailStatisticsByVendor", "监测数据指标详情 - 按厂家统计", "监测数据指标 - 监测数据指标详情", null, 0, 0, 4),
    // 电能质量评价指标 - 符合性评价指标
    SHEETANDTEMPLATENAMEENUM48("evaluationIndexUnit", "管理单位", "符合性评价指标 - 管理单位", null, 4, 17, 22),
    SHEETANDTEMPLATENAMEENUM49("evaluationIndexVoltageLevel", "电压等级", "符合性评价指标 - 电压等级", null, 4, 17, 22),
    SHEETANDTEMPLATENAMEENUM50("evaluationIndexUnitVoltageLevel", "管理单位 - 电压等级", "符合性评价指标 - 管理单位 - 电压等级", null,
            4, 17, 21),
    SHEETANDTEMPLATENAMEENUM51("evaluationIndexDetailByArea", "区域", "符合性评价指标明细 - 区域", null, 0, 0, 5),
    SHEETANDTEMPLATENAMEENUM52("evaluationIndexDetailByMonitor", "监测点", "符合性评价指标明细 - 监测点", null, 0, 0, 5),
    SHEETANDTEMPLATENAMEENUM53("evaluationIndexDetailCommonByArea", "公共连接点 - 区域", "符合性评价指标明细 - 公共连接点 - 区域", null,
            0, 0, 5),
    SHEETANDTEMPLATENAMEENUM54("evaluationIndexDetailCommonByMonitor", "公共连接点 - 监测点", "符合性评价指标明细 - 公共连接点 - 监测点", null,
            0, 0, 5),
    SHEETANDTEMPLATENAMEENUM253("evaluationIndexUnitOfCommonConnection", "管理单位", "符合性评价指标 - 管理单位 - 公共连接点", null, 4, 17, 22),
    SHEETANDTEMPLATENAMEENUM254("evaluationIndexVoltageLevelOfCommonConnection", "电压等级", "符合性评价指标 - 电压等级 - 公共连接点", null, 4, 17, 22),
    // 电能质量评价指标 - 水平评价指标
    SheetAndTemplateNameEnum55("lineEvaluationIndexDetailByArea", "稳态电能质量水平评价明细 - 区域", "水平评价指标 - 稳态电能质量水平评价明细 - 区域", null, 0, 0, 5),
    SheetAndTemplateNameEnum56("lineEvaluationIndexDetailByMonitor", "稳态电能质量水平评价明细 - 监测点", "水平评价指标 - 稳态电能质量水平评价明细 - 监测点", null, 0, 0, 5),
    SheetAndTemplateNameEnum57("voltageSagEvaluation", "电压暂降和短时中断统计", "暂态电能质量水平评价明细 - 电压暂降和短时中断统计", null, 5, 15, 20),
    SheetAndTemplateNameEnum58("voltageSwellEvaluation", "电压暂升统计", "暂态电能质量水平评价明细 - 电压暂升统计", null, 5, 15, 20),
    // 暂态分析 - 容忍度分析
    SheetAndTemplateNameEnum59("toleranceAnalysisCountITIC", "ITIC分析 - 暂态事件分布统计", "容忍度分析 - 暂态事件次数统计 - ITIC", null, 4, 26, 33),
    SheetAndTemplateNameEnum60("toleranceAnalysisCountSEMI", "SEMI分析 - 暂态事件分布统计", "容忍度分析 - 暂态事件次数统计 - SEMI", null, 4, 26, 33),
    SheetAndTemplateNameEnum61("toleranceAnalysisCurve", "曲线", "容忍度分析 - 曲线", null, 15, 18, 23),
    // 暂态分析 - 暂态事件原因分析
    SheetAndTemplateNameEnum62("eventReasonAnalysis", "类型识别及原因分析", "类型识别及原因分析", null, 0, 0, 3),
    SheetAndTemplateNameEnum63("disturbanceSource", "电压暂态扰动源定位", "电压暂态扰动源定位", null, 0, 0, 4),
    disturbanceSourceMap("disturbanceSourceMap", "电压暂态扰动源定位地图", "电压暂态扰动源定位地图", null, 4, 35, 40),
    // 稳态分析 - 波形矢量图
    SheetAndTemplateNameEnum64("realData", "波形矢量图", "波形矢量图", null, 8, 18, 0),
    SheetAndTemplateNameEnum65("realDataReport", "实时数据表", "实时数据表", null, 0, 0, 0),
    // 运维管理 - 日常巡视
    SheetAndTemplateNameEnum66("monitorData", "日常巡视", "日常巡视", null, 0, 0, 3),
    SheetAndTemplateNameEnum67("warnLog", "告警日志", "告警日志", null, 0, 0, 3),
    // 数据异常核查
    SheetAndTemplateNameEnum68("steadyDataCheck1", "稳态指标核查 - 规则一", "稳态指标核查 - 规则一", null, 0, 0, 4),
    SheetAndTemplateNameEnum69("steadyDataCheck2", "稳态指标核查 - 规则二", "稳态指标核查 - 规则二", null, 0, 0, 4),
    SheetAndTemplateNameEnum70("steadyDataCheck3", "稳态指标核查 - 规则三", "稳态指标核查 - 规则三", null, 0, 0, 4),
    SheetAndTemplateNameEnum71("steadyDataCheck4", "稳态指标核查 - 规则四", "稳态指标核查 - 规则四", null, 0, 0, 4),
    SheetAndTemplateNameEnum72("steadyDataCheck5", "稳态指标核查 - 规则五", "稳态指标核查 - 规则五", null, 0, 0, 4),
    SheetAndTemplateNameEnum73("steadyDataCheck6", "稳态指标核查 - 规则六", "稳态指标核查 - 规则六", null, 0, 0, 4),
    SheetAndTemplateNameEnum74("temporaryDataCheck1", "暂态指标核查 - 规则一", "暂态指标核查 - 规则一", null, 0, 0, 4),
    SheetAndTemplateNameEnum75("temporaryDataCheck2", "暂态指标核查 - 规则二", "暂态指标核查 - 规则二", null, 0, 0, 4),
    SheetAndTemplateNameEnum76("temporaryDataCheck3", "暂态指标核查 - 规则三", "暂态指标核查 - 规则三", null, 0, 0, 4),
    SheetAndTemplateNameEnum77("temporaryDataCheck4", "暂态指标核查 - 规则四", "暂态指标核查 - 规则四", null, 0, 0, 4),
    // 补充表格
    SheetAndTemplateNameEnum79("realGraph", "实时矢量图", "波形矢量图", null, 7, 46, 0),
    SheetAndTemplateNameEnum80("realWaveFrequency", "实时谐波频谱", "波形矢量图", null, 5, 30, 0),
    SheetAndTemplateNameEnum81("trendsCurvesAnalysis", "趋势曲线分析", "波形矢量图", null, 5, 30, 0),
    SheetAndTemplateNameEnum82("distributionITIC", "ITIC分析 - 暂态事件容忍度分布统计", "容忍度分析 - 暂态事件容忍度分布统计 - ITIC", null, 4, 26, 33),
    SheetAndTemplateNameEnum83("distributionSEMI", "SEMI分析 - 暂态事件容忍度分布统计", "容忍度分析 - 暂态事件容忍度分布统计 - SEMI", null,3, 26, 33),

    // 暂态分析 - SARFI-X指标分析
    SheetAndTemplateNameEnum84("analysisSARFI", "SARFI-X指标分析", "SARFI-X指标分析", null, 12, 0, 3),
    // 暂态分析 - 暂态事件分布统计
    SheetAndTemplateNameEnum85("temporaryDistributionCompanyAndObject", "暂态事件分布统计 - 管理单位+监测对象类型", "暂态事件分布统计 - 管理单位+监测对象类型", null, 0, 0, 2),
    SheetAndTemplateNameEnum86("temporaryDistributionCompanyAndVoltage", "暂态事件分布统计 - 管理单位+电压等级", "暂态事件分布统计 - 管理单位+电压等级", null, 0, 0, 2),
    SheetAndTemplateNameEnum87("temporaryDistributionObjectTypeAndVoltage", "暂态事件分布统计 - 监测对象类型+电压等级", "暂态事件分布统计 - 监测对象类型+电压等级", null, 0, 0, 2),
    SheetAndTemplateNameEnum88("temporaryDistribution3D", "暂态事件三维特征图", "暂态事件三维特征图", null, 11, 20, 0),
    SheetAndTemplateNameEnum89("temporaryDistributionTrends", "暂态事件趋势图", "暂态事件趋势图", null, 11, 20, 0),
    // 暂态分析 - 暂态事件分类统计
    SheetAndTemplateNameEnum90("transientEventTotal", "暂态事件统计总览", "暂态事件统计总览", null, 6, 20, 26),
    SheetAndTemplateNameEnum91("transientEventUnitTotal", "各管理单位发生暂态事件的监测点统计", "各管理单位发生暂态事件的监测点统计", null, 10, 20, 26),
    SheetAndTemplateNameEnum92("transientEventMonitorTotal", "暂态事件各监测点统计表", "暂态事件各监测点统计表", null, 0, 0, 5),
    SheetAndTemplateNameEnum93("transientEventUnit", "发生暂态监测点数", "管理单位统计-发生暂态监测点数", null, 12, 20, 24),
    SheetAndTemplateNameEnum94("transientEventUnit2", "发生暂态监测点占比", "管理单位统计-发生暂态监测点占比", null, 12, 20, 24),
    SheetAndTemplateNameEnum95("transientEventUnit3", "暂态发生次数", "管理单位统计-暂态发生次数", null, 12, 20, 24),
    SheetAndTemplateNameEnum96("transientEventUnit4", "暂态发生频次", "管理单位统计-暂态发生频次", null, 12, 20, 24),
    //    SheetAndTemplateNameEnum94("transientEventUnitMonitorRate", "暂态事件按管理单位统计发生暂态监测点占比", "暂态事件按管理单位统计发生暂态监测点占比", null, 0, 0, 0),
//    SheetAndTemplateNameEnum95("transientEventUnitTime", "暂态事件按管理单位统计暂态发生次数", "暂态事件按管理单位统计暂态发生次数", null, 0, 0, 0),
//    SheetAndTemplateNameEnum96("transientEventUnitFrequency", "暂态事件按管理单位统计暂态发生频次", "暂态事件按管理单位统计暂态发生频次", null, 0, 0, 0),
    SheetAndTemplateNameEnum97("transientEventObjectType", "发生暂态监测点数", "监测对象类型统计-发生暂态监测点数", null, 6, 25, 57),
        SheetAndTemplateNameEnum98("transientEventObjectType2", "发生暂态监测点占比", "监测对象类型统计-发生暂态监测点占比", null, 6, 25, 57),
    SheetAndTemplateNameEnum99("transientEventObjectType3", "暂态发生次数", "监测对象类型统计-暂态发生次数", null, 6, 25, 57),
    SheetAndTemplateNameEnum100("transientEventObjectType4", "暂态发生频次", "监测对象类型统计-暂态发生频次", null, 6, 25, 57),
    SheetAndTemplateNameEnum101("transientEventSubstationFrequency", "变电站暂态分布情况 - 按发生频次", "变电站暂态分布情况 - 按发生频次", null, 8, 20, 24),
    SheetAndTemplateNameEnum102("transientEventSubstationVoltage", "变电站暂态分布情况 - 按电压等级", "变电站暂态分布情况 - 按发生频次", null, 8, 20, 24),
    SheetAndTemplateNameEnum103("transientEventSubstation", "监测到暂态指标的变电站", "监测到暂态指标的变电站", null, 12, 20, 26),
    // 暂态分析 - 暂态严重程度
    SheetAndTemplateNameEnum104("severityTendency", "暂态严重程度趋势图", "暂态严重程度趋势图", null, 4, 20, 25),
    SheetAndTemplateNameEnum105("severityVoltage", "各电压暂降严重程度", "各电压暂降严重程度", null, 4, 20, 25),
    SheetAndTemplateNameEnum106("severitySubstationTop5", "暂降严重程度TOP5变电站", "暂降严重程度TOP5变电站", null, 4, 20, 25),
    SheetAndTemplateNameEnum107("severityArea", "区域严重程度评估", "区域严重程度评估", null, 0, 0, 3),
    SheetAndTemplateNameEnum108("severitySubstation", "变电站严重程度评估", "变电站严重程度评估", null, 0, 0, 3),
    SheetAndTemplateNameEnum109("severityMonitor", "监测点严重程度评估", "监测点严重程度评估", null, 0, 0, 3),
    //权值函数导出
    SheetAndTemplateNameEnum225("weightfuncseverityArea", "区域严重程度评估", "权值函数分析区域严重程度评估", null, 0, 0, 3),
    SheetAndTemplateNameEnum226("weightfuncseveritySubstation", "变电站严重程度评估", "权值函数分析变电站严重程度评估", null, 0, 0, 3),
    SheetAndTemplateNameEnum227("weightfuncseverityMonitor", "监测点严重程度评估", "权值函数分析监测点严重程度评估", null, 0, 0, 3),
    // 专项分析 - 电铁专项
    SheetAndTemplateNameEnum110("electricRailwayByVoltClass", "电铁 - 电铁概况 - 按电压等级", "电铁 - 电铁概况 - 按电压等级", null, 6, 30, 35),
    SheetAndTemplateNameEnum111("electricRailwayByValue", "电铁 - 电铁概况 - 按x值", "电铁 - 电铁概况 - 按x值", null, 0, 0, 4),
    SheetAndTemplateNameEnum112("electricRailwayByCurrent", "电铁专项 - 电能质量总览 - 电流指标", "电铁专项 - 电能质量总览 - 电流指标", null, 7, 18, 23),
    SheetAndTemplateNameEnum113("electricRailwayByVoltage", "电铁专项 - 电能质量总览 - 电压指标", "电铁专项 - 电能质量总览 - 电流指标", null, 7, 18, 23),
    SheetAndTemplateNameEnum114("electricRailwayByCompany", "电铁专项 - 电能质量总览 - 各单位指标", "电铁专项 - 电能质量总览 - 各单位指标", null, 12, 18, 23),
    SheetAndTemplateNameEnum115("electricRailwayByStation", "电铁专项 - 发射特性 - 牵引站分布", "电铁专项 - 发射特性 - 牵引站分布", null, 7, 18, 24),
    SheetAndTemplateNameEnum116("electricRailwayByNegativeCurrent", "电铁专项 - 发射特性 - 负序电流特性", "电铁专项 - 发射特性 - 负序电流特性", null, 7, 18, 23),
    SheetAndTemplateNameEnum117("electricRailwayByHarmonicCurrent", "电铁专项 - 发射特性 - 谐波电流特性", "电铁专项 - 发射特性 - 谐波电流特性", null, 12, 18, 23),
    SheetAndTemplateNameEnum118("electricRailwayByStationDetailed", "电铁专项 - 电网影响特性 - 牵引站分布", "电铁专项 - 电网影响特性 - 牵引站分布", null, 7, 18, 23),
    SheetAndTemplateNameEnum119("electricRailwayByNegativeVoltage", "电铁专项 - 电网影响特性 - 负序电压特性", "电铁专项 - 电网影响特性 - 负序电压特性", null, 7, 18, 24),
    SheetAndTemplateNameEnum120("electricRailwayByHarmonicVoltage", "电铁专项 - 电网影响特性 - 谐波电压特性", "电铁专项 - 电网影响特性 - 谐波电压特性", null, 12, 18, 24),
    SheetAndTemplateNameEnum121("electricRailwayDetailed", "电铁专项 - 详细数据表", "电铁专项 - 详细数据表", null, 0, 0, 4),
    // 专项分析 - 风电专项
    SheetAndTemplateNameEnum122("windPowerMap", "风电专项 - 地图", "风电专项 - 地图", null, 6, 28, 32),
    SheetAndTemplateNameEnum123("windPowerTotal", "风电专项 - 风电概况", "风电专项 - 风电概况", null, 6, 10, 15),
    SheetAndTemplateNameEnum0123("windPowerCount", "风电专项 - 统计", "风电专项 - 统计", null, 6, 20, 25),
    SheetAndTemplateNameEnum124("windPowerByCurrent", "风电专项 - 电能质量总览 - 电流指标", "光伏专项 - 电能质量总览 - 电流指标", null, 7, 18, 23),
    SheetAndTemplateNameEnum125("windPowerByVoltage", "风电专项 - 电能质量总览 - 电压指标", "光伏专项 - 电能质量总览 - 电流指标", null, 7, 18, 23),
    SheetAndTemplateNameEnum126("windPowerByCompany", "风电专项 - 电能质量总览 - 各单位指标", "风电专项 - 电能质量总览 - 各单位指标", null, 12, 18, 23),
    SheetAndTemplateNameEnum127("windPowerByStation", "风电专项 - 发射特性 - 风电场分布", "风电专项 - 发射特性 - 风电场分布", null, 12, 18, 24),
    SheetAndTemplateNameEnum128("windPowerByHarmonicCurrent", "风电专项 - 发射特性 - 谐波电流特性", "风电专项 - 发射特性 - 谐波电流特性", null, 12, 18, 23),
    SheetAndTemplateNameEnum129("windPowerByStationDetailed", "风电专项 - 电网影响特性 - 风电场分布", "风电专项 - 电网影响特性 - 风电场分布", null, 12, 18, 23),
    SheetAndTemplateNameEnum130("windPowerByHarmonicVoltage", "风电专项 - 电网影响特性 - 谐波电压特性", "风电专项 - 电网影响特性 - 谐波电压特性", null, 12, 18, 24),
    SheetAndTemplateNameEnum131("windPowerDetailed", "风电专项 - 详细数据表", "风电专项 - 详细数据表", null, 0, 0, 4),
    // 专项分析 - 光伏专项
    SheetAndTemplateNameEnum132("photovoltaicMap", "光伏专项 - 地图", "光伏专项 - 地图", null, 6, 28, 32),
    SheetAndTemplateNameEnum133("photovoltaicTotal", "光伏专项 - 光伏概况", "光伏专项 - 光伏概况", null, 6, 10, 15),
    SheetAndTemplateNameEnum0133("photovoltaicCount", "光伏专项 - 统计", "光伏专项 - 统计", null, 6, 20, 25),
    SheetAndTemplateNameEnum134("photovoltaicByCurrent", "光伏专项 - 电能质量总览 - 电流指标", "光伏专项 - 电能质量总览 - 电流指标", null, 7, 18, 23),
    SheetAndTemplateNameEnum135("photovoltaicByVoltage", "光伏专项 - 电能质量总览 - 电压指标", "光伏专项 - 电能质量总览 - 电流指标", null, 7, 18, 23),
    SheetAndTemplateNameEnum136("photovoltaicByCompany", "光伏专项 - 电能质量总览 - 各单位指标", "光伏专项 - 电能质量总览 - 各单位指标", null, 12, 18, 23),
    SheetAndTemplateNameEnum137("photovoltaicByStation", "光伏专项 - 发射特性 - 牵引站分布", "光伏专项 - 发射特性 - 牵引站分布", null, 12, 18, 24),
    SheetAndTemplateNameEnum138("photovoltaicByHarmonicCurrent", "光伏专项 - 发射特性 - 谐波电流特性", "光伏专项 - 发射特性 - 谐波电流特性", null, 12, 18, 23),
    SheetAndTemplateNameEnum139("photovoltaicByStationDetailed", "光伏专项 - 电网影响特性 - 光伏电站分布", "光伏专项 - 电网影响特性 - 光伏电站分布", null, 12, 18, 23),
    SheetAndTemplateNameEnum140("photovoltaicByHarmonicVoltage", "光伏专项 - 电网影响特性 - 谐波电压特性", "光伏专项 - 电网影响特性 - 谐波电压特性", null, 12, 18, 24),
    SheetAndTemplateNameEnum141("photovoltaicDetailed", "光伏专项 - 详细数据表", "光伏专项 - 详细数据表", null, 0, 0, 4),
    // 测试台账
    SheetAndTemplateNameEnum142("testInventory", "测试台账", "测试台账", null, 0, 0, 3),
    // 数据异常核查
    SheetAndTemplateNameEnum143("anomalCheck", "数据异常核查", "数据异常核查", null, 0, 0, 5),
    // 稳态扰动主成因
    SheetAndTemplateNameEnum144("overLimitEvent", "稳态扰动主成因 - 单事件列表", "稳态扰动主成因 - 单事件列表", null, 0, 0, 5),
    SheetAndTemplateNameEnum145("roOverLimitEvent", "稳态扰动主成因 - 区域分析", "稳态扰动主成因 - 区域分析", null, 0, 0, 5),
    SheetAndTemplateNameEnum146("primaryCause", "稳态扰动主成因 - 主成因分析", "稳态扰动主成因 - 主成因分析", null, 7, 38, 46),
    //敏感重要用户统计
    SheetAndTemplateNameEnum147("sensitiveUserByType", "敏感重要用户统计 - 按类型统计", "敏感重要用户统计 - 按类型统计", null, 4, 35, 39),
    SheetAndTemplateNameEnum148("sensitiveUserByCompany", "敏感重要用户统计 - 按单位统计", "敏感重要用户统计 - 按单位统计", null, 12, 20, 25),
    //干扰源统计
    SheetAndTemplateNameEnum149("interferenceByType", "干扰源统计 - 按类型统计", "干扰源统计 - 按类型统计", null, 6, 25, 56),
    SheetAndTemplateNameEnum150("interferenceByCompany", "干扰源统计 - 按单位统计", "干扰源统计 - 按单位统计", null, 12, 20, 25),
    //超标分析
    SHEETANDTEMPLATENAMEENUM151("companyOverStandard", "超标分析 - 稳态超标总览", "超标分析 - 稳态超标总览", null, 16, 11, 28),
    SHEETANDTEMPLATENAMEENUM152("standardCountByCompany1", "按管理单位统计 - 超标监测点（个）", CommonConstant.CONSTANT_STANDARD_COUNT_BY_COMPANY, null, 24, 16, 28),
    SHEETANDTEMPLATENAMEENUM153("standardCountByCompany2", "按管理单位统计 - 超标监测点占比（%）", CommonConstant.CONSTANT_STANDARD_COUNT_BY_COMPANY, null, 24, 16, 28),
    SHEETANDTEMPLATENAMEENUM154("standardCountByCompany3", "按管理单位统计 - 平均超标天数", CommonConstant.CONSTANT_STANDARD_COUNT_BY_COMPANY, null,
            24, 16, 28),
    SHEETANDTEMPLATENAMEENUM155("countByMonitorObject1", "按监测对象类型统计 - 超标监测点（个）", CommonConstant.CONSTANT_STANDARD_COUNT_BY_COMPANY, null, 13, 16, 39),
    SHEETANDTEMPLATENAMEENUM156("countByMonitorObject2", "按监测对象类型统计 - 超标监测点占比（%）", CommonConstant.CONSTANT_STANDARD_COUNT_BY_COMPANY, null, 13, 16, 39),
    SHEETANDTEMPLATENAMEENUM157("countByMonitorObject3", "按监测对象类型统计 - 平均超标天数", CommonConstant.CONSTANT_STANDARD_COUNT_BY_COMPANY, null, 13, 16, 39),
    SHEETANDTEMPLATENAMEENUM158("countBySubstationByDay1", "变电站稳态指标超标分布情况","超标分析 - 按变电站统计1",  null, 9, 21, 28),
    SHEETANDTEMPLATENAMEENUM159("countBySubstationByDay2", "变电站稳态指标超标分布情况（按电压等级）", "超标分析 - 按变电站统计2", null, 9, 21, 27),
    SHEETANDTEMPLATENAMEENUM160("countBySubstationByDay3", "稳态指标超标的变电站", "超标分析 - 按变电站统计3", null, 20, 21, 28),
    SheetAndTemplateNameEnum184("standardCountByMonitorsort1", "按监测类别统计 - 超标监测点数（个）", "超标分析 - 按监测类别统计1", null, 12, 25, 3),
    SheetAndTemplateNameEnum185("standardCountByMonitorsort2", "按监测类别统计 - 超标监测点占比（%）", "超标分析 - 按监测类别统计2", null, 12, 25, 3),
    SheetAndTemplateNameEnum186("standardCountByMonitorsort3", "按监测类别统计 - 平均超标天数", "超标分析 - 按监测类别统计3", null, 12, 25, 3),
    //操作日志
    SheetAndTemplateNameEnum161("operationLog", "操作日志", "操作日志", null, 0, 0, 3),
    SheetAndTemplateNameEnum0("", "", "", null, 0, 0, 0),
    SheetAndTemplateNameEnum163("historyHarmonic", "历史谐波频谱", "历史谐波频谱", null, 0, 0, 4),
    SheetAndTemplateNameEnum164("troubleTerminal", "故障停运所属终端", "故障停运所属终端", null, 0, 0, 3),
    SheetAndTemplateNameEnum165("terminalReliability", "可靠性评估-按区域分析", "可靠性评估-按区域分析", null, 5, 18, 3),
    SheetAndTemplateNameEnum166("terminalReliabilityByVendor", "可靠性评估-按厂家分析", "可靠性评估-按厂家分析", null, 5, 18, 3),
    SheetAndTemplateNameEnum167("terminalReliabilityByNode", "可靠性评估-单点分析", "可靠性评估-单点分析", null, 5, 18, 3),
    SheetAndTemplateNameEnum168("terminalReliabilityByOperation", "可靠性评估-按投运年份分析", "可靠性评估-按投运年份分析", null, 5, 18, 3),
    SheetAndTemplateNameEnum169("terminalReliabilityByVoltclass", "可靠性评估-按电压等级分析", "可靠性评估-按电压等级分析", null, 5, 18, 3),
    //综合告警
    SheetAndTemplateNameEnum170("syntheticalalarmsteadystate", "综合告警-稳态超标", "综合告警-稳态超标", null, 0, 0, 3),
    SheetAndTemplateNameEnum171("syntheticalalarmtransientstate", "综合告警-暂态事件", "综合告警-暂态事件", null, 0, 0, 3),
    SheetAndTemplateNameEnum172("syntheticalalarmqualificationrate", "综合告警-合格率", "综合告警-合格率", null, 0, 0, 3),
    SheetAndTemplateNameEnum173("syntheticalalarmregularcheck", "综合告警-定检提醒", "综合告警-定检提醒", null, 0, 0, 3),
    SheetAndTemplateNameEnum174("syntheticalalarmline", "综合告警-监测点在线", "综合告警-监测点在线", null, 0, 0, 3),
    //短信告警
    SheetAndTemplateNameEnum175("alarmvariationtarget", "告警对象", "告警对象", null, 0, 0, 3),
    // 历史跌落数据模板
    SheetAndTemplateNameEnum176("voltagedipsrecord", "历史跌落", "历史跌落", null, 0, 0, 3),
    SheetAndTemplateNameEnum177("voltagedipsstation", "电压支撑能力评估-系统参数", "电压支撑能力评估-系统参数",null, 10,18, 21),
    SheetAndTemplateNameEnum178("voltagedipsrealtimedata", "电压支撑能力评估-电压参数", "电压支撑能力评估-电压参数", null, 10,18, 22),
    SheetAndTemplateNameEnum179("voltagedipssupportdata", "电压支撑能力评估-评估结果(跌落)", "电压支撑能力评估-评估结果(跌落)", null, 11,18, 23),
    SheetAndTemplateNameEnum180("voltagedipsnormaldata", "电压支撑能力评估-评估结果(正常)", "电压支撑能力评估-评估结果(正常)", null, 3,18, 21),
    // 功率因数
    SheetAndTemplateNameEnum181("areapowerfactor", "功率因数明细", "功率因数明细", null, 0, 0, 3),
    //台区渗透率水平评估
    SheetAndTemplateNameEnum182("platformPermeabilityEvaluationByArea", "台区渗透率水平评估", "台区渗透率水平评估", null, 0, 0, 5),
    // 配网系统运行指标 - 台帐指标
    SheetAndTemplateNameEnum188("distributionBookStatistics", "配网台帐指标综合统计", "配网系统运行指标 - 台帐指标综合统计", null, 5, 20, 26),
    SheetAndTemplateNameEnum189("distributionBookDistribution", "配网分布统计", "配网系统运行指标 - 分布统计", null, 5, 20, 66),
    SheetAndTemplateNameEnum190("distributionBookIndexCount", "配网台帐指标统计", "配网系统运行指标 - 台帐指标统计", null, 5, 20, 66),
    SheetAndTemplateNameEnum191("distributionBookDetailCount", "配网台帐指标详情", "配网系统运行指标 - 台帐指标详情", null, 0, 0, 5),
    // 配网系统运行指标 - 监测数据指标
    SheetAndTemplateNameEnum192("distributionDataStatistics", "配网监测数据指标综合统计", "配网监测数据指标 - 监测数据指标综合统计", null, 5, 15, 4),
    SheetAndTemplateNameEnum193("distributionDataIndexStatistics", "配网监测数据指标统计", "配网监测数据指标 - 监测数据指标统计", null, 6, 15, 20),
    SheetAndTemplateNameEnum194("distributionDataDetailStatistics", "配网监测数据指标详情 - 按管理单位统计", "配网监测数据指标 - 监测数据指标详情", null, 0, 0, 4),
    SheetAndTemplateNameEnum195("distributionDetailStatisticsByVendor", "配网监测数据指标详情 - 按厂家统计", "配网监测数据指标 - 监测数据指标详情", null, 0, 0, 4),
    // 配变重过载
    SheetAndTemplateNameEnum196("overloaddetail", "配变重过载明细", "配变重过载明细", null, 0, 0, 3),
    SheetAndTemplateNameEnum197("overloadstatistics", "配变重过载统计", "配变重过载统计", null, 0, 0, 4),
    // 台区电能质量
    SheetAndTemplateNameEnum198("pqareadetail", "台区电能质量明细", "台区电能质量明细", null, 0, 0, 4),
    SheetAndTemplateNameEnum199("pqareastatistics", "台区电能质量统计", "台区电能质量统计", null, 0, 0, 5),
    SheetAndTemplateNameEnum224("pqareaoverlimitdetail", "台区电能超标明细", "台区电能超标明细", null, 0, 0, 4),
    // 配网电能质量监测率
    SheetAndTemplateNameEnum200("pqMonitorRate","电能质量监测率", "电能质量监测率",null, 0, 0, 4),
    // 报表管理 - 配网 - 稳态指标数据报表
    SheetAndTemplateNameEnum201("singlePointReport1", "配网单点数据报表1", "稳态指标数据报表 - 配网单点数据报表1", null, 0, 0, 1),
    SheetAndTemplateNameEnum203("singlePointReport2", "配网单点数据报表2", "稳态指标数据报表 - 配网单点数据报表2", null, 0, 0, 1),
    SheetAndTemplateNameEnum204("singlePointDetailReport2", "配网数据详情报表2", "配网数据详情报表2", null, 0, 0, 1),
    SheetAndTemplateNameEnum205("singlePointReport3", "配网单点数据报表3", "稳态指标数据报表 - 配网单点数据报表3", null, 0, 0, 1),
    SheetAndTemplateNameEnum206("singlePointDetailReport3", "配网数据详情报表3", "配网数据详情报表3", null, 0, 0, 1),
    SheetAndTemplateNameEnum207("singlePointReport4", "配网单点数据报表4", "稳态指标数据报表 - 配网单点数据报表4", null, 0, 0, 1),
    SheetAndTemplateNameEnum208("singlePointDetailReport4", "配网数据详情报表4", "配网数据详情报表4", null, 0, 0, 1),
    SHEET_AND_TEMPLATE_NAME_ENUM_214("energyStorageStation", "储能站台账", "储能站台账", null, 0, 0, 2),
    SheetAndTemplateNameEnum215("timeAndVoltageLifting", "暂态指标分布统计", "电压暂升暂降和短时中断统计", null, 0, 0, 1),
    SheetAndTemplateNameEnum216("onLineNodeCnt", "在线监测点数", "在线监测点数", null, 0, 0, 2),

    // 超标总览
    SheetAndTemplateNameEnum209("overByCompanyByQuantity", "指标分析统计-各单位类总览-稳态-主网", "指标分析统计-各单位类总览", null, 0, 0, 3),
    SheetAndTemplateNameEnum210("pvOverByCompanyByQuantity", "指标分析统计-各单位类总览-稳态-配网", "指标分析统计-各单位类总览", null, 0, 0, 3),
    SheetAndTemplateNameEnum211("transientByCompanyByQuantity", "指标分析统计-各单位类总览-暂态-主网", "指标分析统计-各单位类总览", null, 0, 0, 3),
    SheetAndTemplateNameEnum212("pvtransientByCompanyByQuantity", "指标分析统计-各单位类总览-暂态-配网", "指标分析统计-各单位类总览", null, 0, 0, 3),
    // 工单
    SheetAndTemplateNameEnum217("alarmOrderList", "工单列表", "工单列表", null, 0, 0,2),
    SheetAndTemplateNameEnum218("alarmOrderStatistics", " ", "工单统计", null, 0, 0,3),

    //变电站总览导出
    SheetAndTemplateNameEnum219("substationCount", "变电站总览", "变电站总览", null, 0, 0, 3),
    //实时损耗分析导出
    SheetAndTemplateNameEnum220("realTimeHarmonicLoss", "实时损耗分析", "实时损耗分析", null, 0, 0, 0),
    //干扰源用户管理
    SheetAndTemplateNameEnum222("interferencesourcenotfiled", "未建档干扰源用户", "未建档干扰源用户", null, 0, 0, 3),
    SheetAndTemplateNameEnum223("interferencesourcenormalize", "常态化干扰源用户", "常态化干扰源用户", null, 0, 0, 4),
    //页面访问记录
    SheetAndTemplateNameEnum228("pqWebView", "页面访问记录", "页面访问记录", null, 0, 0, 3),
    SheetAndTemplateNameEnum229("compileSinglePointReport", "普测计划结果查询表", "普测计划结果查询表", null, 0, 0, 1),
    SheetAndTemplateNameEnum230("converterStationDetailed", "换流站详细数据", "换流站详细数据", null, 0, 0, 4),

    // 报表管理 - 稳态指标自定义数据报表
    SheetAndTemplateNameEnum231("steadyCustomReport", "稳态指标自定义数据报表", "稳态指标自定义数据报表", null, 0, 0, 1),
    // 数据缺失核查
    SheetAndTemplateNameEnum232("missingCheck", "数据缺失核查", "数据缺失核查", null, 0, 0, 3),
    SheetAndTemplateNameEnum233("missingCheckParaDetail", "指标完整率缺失明细", "指标完整率缺失明细", null, 0, 0, 3),
    SheetAndTemplateNameEnum234("missingCheckDataDetail", "数据完整率缺失明细", "数据完整率缺失明细", null, 0, 0, 3),

    SheetAndTemplateNameEnum235("bearingCapacityEvaluation", "承载力评估", "承载力评估", null, 0, 0, 0),
    SheetAndTemplateNameEnum236("reportOfOffLineOverStandardDetails", "普测结果(超标)明细", "普测结果(超标)明细", null, 0, 0, 5),
    SheetAndTemplateNameEnum237("typicalSourceElectricrailway", "典型源荷统计-牵引站", "典型源荷统计-牵引站", null, 0, 0, 3),
    SheetAndTemplateNameEnum238("typicalSourcePhotovoltaicstation", "典型源荷统计-光伏电站", "典型源荷统计-光伏电站", null, 0, 0, 3),
    SheetAndTemplateNameEnum239("typicalSourceWindpowerstation", "典型源荷统计-风电场", "典型源荷统计-风电场", null, 0, 0, 3),
    SheetAndTemplateNameEnum240("typicalSourceInterferencesource", "典型源荷统计-其他干扰源用户", "典型源荷统计-其他干扰源用户", null, 0, 0, 3),
    SheetAndTemplateNameEnum241("exportEvaluation", "基准水平评估", "基准水平评估", null, 0, 0, 3),
    SheetAndTemplateNameEnum242("exportMonitor", "主网监测点统计", "主网监测点统计", null, 0, 0, 3),
    SheetAndTemplateNameEnum243("exportConverterMonitorIndex", "换流站指标汇总", "换流站指标汇总", null, 0, 0, 4),
    SheetAndTemplateNameEnum244("exportConverterMonitorDetail", "换流站指标明细", "换流站指标明细", null, 0, 0, 3),
    SheetAndTemplateNameEnum245("smeltLoadDetailed", "冶炼负荷 - 详细数据表", "冶炼负荷 - 详细数据表", null, 0, 0, 4),

    SheetAndTemplateNameEnum246("intelligentInspection", "智能巡视", "智能巡视", null, 0, 0, 4),

    SheetAndTemplateNameEnum247("mainTransformerList", "主变压器台账", "主变压器台账", null, 0, 0, 5),
    SHEETANDTEMPLATENAMEENUM252("companyOverStandardDetail", "超标分析-稳态超标总览-各管理单位稳态指标分析表", "超标分析-稳态超标总览-各管理单位稳态指标分析表", null,
            0, 0, 4),
    SheetAndTemplateNameEnum253("thdPrediction", "谐波实时预警", "谐波实时预警", null, 0, 0, 5),
    SheetAndTemplateNameEnum254("thdPredictionRate", "谐波实时预警准确率", "谐波实时预警准确率", null, 0, 0, 5),
    SheetAndTemplateNameEnum255("thdSeriousness", "谐波超标严重性", "谐波超标严重性", null, 0, 0, 3),
    SheetAndTemplateNameEnum256("unbalancePredictionHis", "不平衡度预警严重程度详情", "不平衡度预警严重程度详情", null, 0, 0, 4),
    SheetAndTemplateNameEnum257("voltagePredictionHis", "电压预警严重程度详情", "电压预警严重程度详情", null, 0, 0, 4),
    SHEETANDTEMPLATENAMEENUM259("voltageSagGovernanceUsers", "电压暂降治理辅助决策", "电压暂降治理辅助决策", null, 0, 0, 5),
    ;

    private String id;
    /**
     * 要输出的sheet名
     */
    private String sheetName;
    /**
     * 模板文件中对应的sheet名
     */
    private String templateName;
    /**
     * 模板对应的实体类（按字段定义顺序排序数据）
     */
    private Class<?> clazz;
    /**
     * 图片右下角列号
     */
    private int col;
    /**
     * 图片右下角行号
     */
    private int row;
    /**
     * 开始写数据的行号
     */
    private int writeRow;

    TemplateDataEnum(String id, String sheetName, String templateName,Class<?> clazz, int col, int row, int writeRow) {
        this.id = id;
        this.sheetName = sheetName;
        this.templateName = templateName;
        this.clazz = clazz;
        this.col = col;
        this.row = row;
        this.writeRow = writeRow;
    }

    public static String getSheetNameById(String id) {
        for (TemplateDataEnum templateDataEnum : TemplateDataEnum.values()) {
            if (id.equals(templateDataEnum.id)) {
                return templateDataEnum.sheetName;
            }
        }
        return StringUtils.EMPTY;
    }

    public static String getTemplateNameById(String id) {
        for (TemplateDataEnum templateDataEnum : TemplateDataEnum.values() ) {
            if (templateDataEnum.id.equals(id)) {
                return templateDataEnum.templateName;
            }
        }
        return StringUtils.EMPTY;
    }

    public static int getColById(String id) {
        for (TemplateDataEnum templateDataEnum : TemplateDataEnum.values()) {
            if (id.equals(templateDataEnum.id)) {
                return templateDataEnum.col;
            }
        }
        return 0;
    }
    public static int getRowById(String id) {
        for (TemplateDataEnum templateDataEnum : TemplateDataEnum.values()) {
            if (id.equals(templateDataEnum.id)) {
                return templateDataEnum.row;
            }
        }
        return 0;
    }
    public static Class getClazzById(String id) {
        for (TemplateDataEnum templateDataEnum : TemplateDataEnum.values()) {
            if (id.equals(templateDataEnum.id)) {
                return templateDataEnum.clazz;
            }
        }
        throw new ExcelException("未找到模板对应的实体类");
    }

    public static int getWriteRowById(String id) {
        for (TemplateDataEnum templateDataEnum : TemplateDataEnum.values()) {
            if (id.equals(templateDataEnum.id)) {
                return templateDataEnum.writeRow;
            }
        }
        return 0;
    }

    public String getId() {
        return id;
    }

    public int getWriteRow() {
        return writeRow;
    }

    public String getSheetName() {
        return sheetName;
    }
}

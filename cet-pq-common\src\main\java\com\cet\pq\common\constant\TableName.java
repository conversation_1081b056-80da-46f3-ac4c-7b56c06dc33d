package com.cet.pq.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName TableNameConfig
 * @Description 存放modelLabel常量的类
 * <AUTHOR>
 * @Date 2020/2/24 15:25
 */
public class TableName {
    /**
     * 项目
     */
    public static final String PROJECT = "project";
    /**
     * 建筑物
     */
    public static final String BUILDING = "building";
    /**
     * 楼层
     */
    public static final String FLOOR = "floor";
    /**
     * 房间
     */
    public static final String ROOM = "room";
    /**
     * it机柜
     */
    public static final String IT_CABINET = "itcabinet";
    /**
     * ups
     */
    public static final String UPS = "ups";

    /**
     * pqdefaultlimit
     */
    public static final String PQDEFAULTLIMIT = "pqdefaultlimit";
    /**
     * 母线
     */
    public static final String BUS_BAR_SECTION = "busbarsection";
    /**
     * 母线
     */
    public static final String REACTIVEDEVICE = "reactivedevice";
    /**
     * 变压器
     */
    public static final String POWER_TRANS_FORMER = "powertransformer";
    /**
     * 蓄电池
     */
    public static final String BATTERY = "battery";
    /**
     * 高压直流设备
     */
    public static final String HVDC = "hvdc";
    /**
     * 电容柜
     */
    public static final String CAPACITOR = "capacitor";
    /**
     * 计量柜
     */
    public static final String METERING_CABINET = "meteringcabinet";
    /**
     * 发电机
     */
    public static final String GENERATOR = "generator";
    /**
     * 母联
     */
    public static final String BUS_BAR_CONNECTOR = "busbarconnector";
    /**
     * 带开关柜的线路段
     */
    public static final String LINE_SEGMENT_WITH_SWITCH = "linesegmentwithswitch";
    /**
     * 低压回路
     */
    public static final String LOW_VOLTAGE_CIRCUIT = "lowvoltagecircuit";
    /**
     * 总能耗
     */
    public static final String TOTAL_ENERGY_CONSUMPTION = "totalenergyconsumption";
    /**
     * 物理量对象
     */
    public static final String QUANTITY_OBJECT = "quantityobject";
    /**
     * 物理量聚合配置
     */
    public static final String QUANTITY_AGGREGATION = "quantityaggregation";
    /**
     * 物理量对象聚合数据
     */
    public static final String QUANTITY_AGGREGATION_DATA = "quantityaggregationdata";
    /**
     * peccore事件扩展，主要是事件确认相关的信息
     */
    public static final String PEC_EVENT_EXTEND = "peceventextend";
    /**
     * pqexcellog 导入状态表
     */
    public static final String PQEXCELLOG = "pqexcellog";
    /**
     * 电能质量事件
     */
    public static final String PQ_VARIATION_EVENT = "pqvariationevent";
    /**
     * 设备关联
     */
    public static final String MEASURED_BY = "measuredby";
    /**
     * 系统事件
     */
    public static final String SYSTEM_EVENT = "systemevent";
    /**
     * 用能设备
     */
    public static final String MANUFACTURE_EQUIPMENT_TEMPLATE = "manufactureequipmenttemplate";
    /**
     * 供能关系
     */
    public static final String ENERGY_SUPPLY_TO = "energysupplyto";
    /**
     * 扩展
     */
    public static final String PREFIX = "_model";
    /**
     * 机采设备
     */
    public static final String MECHANICAL_MINING_MACHINE = "mechanicalminingmachine";
    /**
     * 泵
     */
    public static final String PUMP = "pump";
    /**
     * 加热炉
     */
    public static final String HEATING_FURNACE = "heatingfurnace";
    /**
     * 管道
     */
    public static final String PIPELINE = "pipeline";
    /**
     * 作业区
     */
    public static final String OPERATION_AREA = "operationarea";
    /**
     * 采油队
     */
    public static final String OIL_PRODUCTION_CREW = "oilproductioncrew";
    /**
     * 产品产量
     */
    public static final String PRODUCTION_DATA = "productiondata";
    /**
     * 油井产量数据
     */
    public static final String OIL_WELL_DATA = "oilwelldata";
    /**
     * 油井
     */
    public static final String OIL_WELL = "oilwell";
    /**
     * 能耗数据
     */
    public static final String ENERGY_CONSUMPTION = "energyconsumption";
    /**
     * 非自然配置
     */
    public static final String UNNATURAL_SET = "unnaturalset";
    /**
     * 被检测id
     */
    public static final String MONITORED_ID = "monitoredid";
    /**
     * 被检测label
     */
    public static final String MONITORED_LABEL = "monitoredlabel";
    /**
     * 表realtimeparasetgroup
     */
    public static final String REALTIMEPARASETGROUP = "realtimeparasetgroup";
    /**
     * 表RealtimeParaSet
     */
    public static final String REALTIMEPARASET = "realtimeparaset";
    /**
     * 表line
     */
    public static final String LINE = "line";

    /**
     * 表line
     */
    public static final String ZHIKANDATA = "zhikandata";


    /**
     * electricityuser
     */
    public static final String ELECTRICITYUSER = "electricityuser";

    /**
     * powergenerationuser
     */
    public static final String POWERGENERATIONUSER = "powergenerationuser";
    /**
     * QUANTITYTEMPLATEGROUP
     */
    public static final String QUANTITYTEMPLATEGROUP = "quantitytemplategroup";
    /**
     * 表pecdeviceextend
     */
    public static final String PECDEVICEEXTEND = "pecdeviceextend";
    /**
     * 表phaselinedataidmap
     */
    public static final String PHASELINEDATAIDMAP = "phaselinedataidmap";
    /**
     * 表QuantityLimit
     */
    public static final String QUANTITYLIMIT = "quantitylimit";
    /**
     * 表quantitymaptemplate
     */
    public static final String QUANTITYMAPTEMPLATE = "quantitymaptemplate";

    /**
     * 表citycompany
     */
    public static final String CITYCOMPANY = "citycompany";

    /**
     * 表district
     */
    public static final String DISTRICT = "district";

    /**
     * 表provincecompany
     */
    public static final String PROVINCECOMPANY = "provincecompany";

    /**
     * 表countycompany
     */
    public static final String COUNTYCOMPANY = "countycompany";

    /**
     * 表province
     */
    public static final String PROVINCE = "province";

    /**
     * 表city
     */
    public static final String CITY = "city";

    /**
     * 表substation
     */
    public static final String SUBSTATION = "substation";

    /**
     * 表pqterminqal
     */
    public static final String PQTERMINAL = "pqterminal";

    /**
     * 表pqterminqal
     */
    public static final String PLATFORMAREA = "powerdistributionarea";

    /**
     * 表pqterminqal
     */
    public static final String REGULARCHECK = "regularcheck";

    /**
     * 表model_instance_relationship
     */
    public static final String MODEL_INSTANCE_RELATIONSHIP = "model_instance_relationship";

    /**
     * 表rmsmessage
     */
    public static final String RMS_MESSAGE = "rmsmessage";

    /**
     * 表rmsstatmessage
     */
    public static final String RMS_STAT_MESSAGE = "rmsstatmessage";

    /**
     * 表iticscheme
     */
    public static final String ITICSCHEME = "iticscheme";

    /**
     * 表iticschemeitem
     */
    public static final String ITICSCHEMEITEM = "iticschemeitem";

    /**
     * 表semischeme
     */
    public static final String SEMISCHEME = "semischeme";

    /**
     * 表iticschemeitem
     */
    public static final String SEMISCHEMEITEM = "semischemeitem";

    /**
     * 表OfflineGBLimit
     */
    public static final String OFFLINEGBLIMIT = "offlinegblimit";

    /**
     * 表OfflineGBLimit
     */
    public static final String OFFLINETEST = "offlinetest";

    /**
     * 表offlineaggdata
     */
    public static final String OFFLINEAGGDATA = "offlineaggdata";

    /**
     * 表pqalarmdata
     */
    public static final String PQALARMDATA = "pqalarmdata";

    /**
     * 表substationalarmdata
     */
    public static final String SUBSTATIONALARMDATA = "substationalarmdata";

    /**
     * 表ro_alarmnodestat
     */
    public static final String ROALARMNODESTAT = "ro_alarmnodestat";

    /**
     * 表ro_alarmsubstationstat
     */
    public static final String ROALARMSUBSTATIONSTAT = "ro_alarmsubstationstat";

    /**
     * 表OFFLINEIMPORTDATA
     */
    public static final String OFFLINEIMPORTDATA = "offlineimportdata";

    /**
     * 表ro_pqvariationtypecount
     */
    public static final String RO_PQVARIATIONTYPECOUNT = "ro_pqvariationtypecount";

    /**
     * 表sub_pqvariationtype
     */
    public static final String SUB_PQVARIATIONTYPE = "sub_pqvariationtype";

    /**
     * 表mo_transienttypestatic
     */
    public static final String MO_TRANSIENTTYPESTATIC = "mo_transienttypestatic";

    /**
     * 表rmsdistribution
     */
    public static final String RMSDISTRIBUTION = "rmsdistribution";

    /**
     * 表PQHistoryStatus
     */
    public static final String PQHISTORYSTATUS = "pqhistorystatus";

    /**
     * 表pqdataintegrity
     */
    public static final String PQDATAINTEGRITY = "pqdataintegrity";

    /**
     * 表ro_pqdataintegrity
     */
    public static final String RO_PQDATAINTEGRITY = "ro_pqdataintegrity";

    /**
     * 表pqquantityaggdata
     */
    public static final String PQQUANTITYAGGDATA = "pqquantityaggdata";


    /**
     * 表sub_rmsfrequency
     */
    public static final String SUB_RMSFREQUENCY = "sub_rmsfrequency";

    /**
     * pqnodestatus
     */
    public static final String PQNODESTATUS = "pqnodestatus";

    /**
     * 表ro_pqqualiticationrate
     */
    public static final String RO_PQQUALITICATIONRATE = "ro_pqqualiticationrate";
    /**
     * offlinevariationevent
     */
    public static final String OFFLINEVARIATIONEVENT = "offlinevariationevent";

    /**
     * 表pqqualiticationrate
     */
    public static final String PQQUALITICATIONRATE = "pqqualiticationrate";
    /**
     * 表stablelevelevaluation
     */
    public static final String TABLE_STABLE_LEVEL_EVALUATION = "stablelevelevaluation";
    /**
     * 表ro_stablelevelevaluation
     */
    public static final String TABLE_RO_STABLE_LEVEL_EVALUATION = "ro_stablelevelevaluation";
    /**
     * 表saglevelevaluation
     */
    public static final String TABLE_SAG_LEVEL_EVALUATION = "saglevelevaluation";
    /**
     * 表swellllevelevaluation
     */
    public static final String TABLE_SWELL_LEVEL_EVALUATION = "swelllevelevaluation";
    /**
     * 表ro_saglevelevaluation
     */
    public static final String TABLE_RO_SAG_LEVEL_EVALUATION = "ro_saglevelevaluation";
    /**
     * 表ro_swellllevelevaluation
     */
    public static final String TABLE_RO_SWELL_LEVEL_EVALUATION = "ro_swelllevelevaluation";
    /**
     * 表mo_transienttypestatic
     */
    public static final String TABLE_MO_TRANSIENTTYPESTATIC = "mo_transienttypestatic";
    /**
     * 表ro_qualifiedevaluation
     */
    public static final String TABLE_RO_QUALIFIEDEVALUATION = "ro_qualifiedevaluation";
    /**
     * 表qualifiedevaluation
     */
    public static final String TABLE_QUALIFIEDEVALUATION = "qualifiedevaluation";
    /**
     * 表pqparasetoverlimitdata
     */
    public static final String PQPARASETOVERLIMITDATA = "pqparasetoverlimitdata";

    /**
     * 表voltgroupmap
     */
    public static final String VOLTGROUPMAP = "voltgroupmap";

    /**
     * 表mo_pqvariationstatistic
     */
    public static final String MO_PQVARIATIONSTATISTIC = "mo_pqvariationstatistic";

    /**
     * 表electricrailway 电气化铁路
     */
    public static final String ELECTRICRAILWAY = "electricrailway";

    /**
     * 表photovoltaicstation 光伏电站
     */
    public static final String PHOTOVOLTAICSTATION = "photovoltaicstation";

    /**
     * 表photovoltaicstation 风电场
     */
    public static final String WINDPOWERSTATION = "windpowerstation";

    /**
     * smeltload 冶炼负荷
     */
    public static final String SMELTLOAD = "smeltload";

    /**
     * InterferenceSource
     */
    public static final String INTERFERENCESOURCE = "interferencesource";


    /**
     * 表SubParaSetOverlimitData
     */
    public static final String SUBPARASETOVERLIMITDATA = "subparasetoverlimitdata";
    /**
     * 表voltagegroup
     */
    public static final String VOLTAGEGROUP = "voltagegroup";

    /**
     * 表devicenode
     */
    public static final String DEVICENODE = "devicenode";

    /**
     * 表accountproperty
     */
    public static final String ACCOUNTPROPERTY = "accountproperty";

    /**
     * 表country
     */
    public static final String COUNTRY = "country";

    /**
     * 表netcompany
     */
    public static final String NETCOMPANY = "netcompany";

    /**
     * 表displayposition
     */
    public static final String DISPLAYPOSITION = "displayposition";

    /**
     * 表pqvariationeventsemiinfo
     */
    public static final String PQ_VARIATIONEVENT_SEMIINFO = "pqvariationeventsemiinfo";

    /**
     * 表pqvariationeventitic
     */
    public static final String PQ_VARIATIONEVENT_ITIC = "pqvariationeventitic";

    /**
     * 表Ro_ParaSetOverlimitData
     */
    public static final String RO_PARASETOVERLIMITDATA = "ro_parasetoverlimitdata";

    /**
     * 表interferenceparaoverdata
     */
    public static final String INTERFERENCEPARASETOVERLIMITDATA = "interferenceparaoverdata";

    /**
     * 表LineCorrelationData
     */
    public static final String LINECORRELATIONDATA = "linecorrelationdata";

    /**
     * 表Anomalcheckcount
     */
    public static final String ANOMALCHECKCOUNT = "anomalcheckcount";

    /**
     * 表pqvariationserveritydegree
     */
    public static final String PQVARIATIONSERVERITYDEGREE = "pqvariationserveritydegree";

    /**
     * 表PQOverlimitData
     */
    public static final String PQOVERLIMITDATA = "pqoverlimitdata";

    /**
     * 表PQOverlimitData
     */
    public static final String STEADYOVERLIMITEVENT = "steadyoverlimitevent";

    /**
     * 表quantityparasetmap
     */
    public static final String QUANTITYPARASETMAP = "quantityparasetmap";

    /**
     * 表steadystatedisturebance
     */
    public static final String STEADYSTATEDISTUREBANCE = "steadystatedisturebance";

    /**
     * 表substationtopology
     */
    public static final String SUBSTATIONTOPOLOGY = "substationtopology";

    public static final String SYSVERSIONUPDATEINFO = "sysversionupdateinfo";

    /**
     * 表linetopology
     */
    public static final String LINETOPOLOGY = "linetopology";
    /**
     * 表linefusion
     */
    public static final String LINEFUSION = "linefusion";

    /**
     * 表governancedevice
     */
    public static final String GOVERNANCEDEVICE = "governancedevice";

    /**
     * 表governancedevice
     */
    public static final String GOVERNANCEDATA = "governancedata";
    /**
     * 表testinstrument
     */
    public static final String TESTINSTRUMENT = "testinstrument";
    /**
     * 终端消缺明细
     */
    public static final String DEFECTDETAILS = "defectdetails";

    /**
     * 表generalplan,离线监测点普测计划
     */
    public static final String GENERALPLAN = "generalplan";

    /**
     * 审批流程
     */
    public static final String AUDITFLOW = "auditflow";

    /**
     * 审批明细表
     */
    public static final String AUDITFLOWDETAIL = "auditflowdetail";

    /**
     * 审批日志表
     */
    public static final String AUDITLOG = "auditlog";

    /**
     * 告警对象
     */
    public static final String ALARMVARIATIONTARGET = "alarmvariationtarget";

    /**
     * 电压骤降表
     */
    public static final String VOLTAGEDIPSRECORD = "voltagedipsrecord";
    /**
     * 告警对象管理台账
     */
    public static final String ALARMOBJECT = "alarmobject";

    /**
     * 告警对象反馈材料表
     */
    public static final String FEEDBACKFILE = "feedbackfile";

    /**
     * 审批业务表(监测点)
     */
    public static final String LINEAUDITFLOW = "lineauditflow";

    /**
     * 应设点
     */
    public static final String SUB_LINEESTIMATECOUNT = "sub_lineestimatecount";

    /**
     * 审批业务表(应设点)
     */
    public static final String SUB_LINEESTIMATECOUNTAUDITFLOW = "sub_lineestimatecountauditflow";

    /**
     * 台区
     */
    public static final String POWERDISTRIBUTIONAREA = "powerdistributionarea";

    /**
     * 报表参数
     */
    public static final String REPORTPARASET = "reportparaset";

    /**
     * 谐波损耗参数
     */
    public static final String HARMONICLOSSPARA = "harmoniclosspara";
    /**
     * 谐波损耗数据
     */
    public static final String HARMONICLOSSDATA = "harmoniclossdata";

    /**
     * 事件原因分析
     */
    public static final String EVENTCAUSEANALYSIS = "eventcauseanalysis";


    /**
     * 未建档干扰源
     */
    public static final String INTERFERENCESOURCE_NOT_FILED = "interferencesource_not_filed";

    /**
     * 常态化干扰源
     */
    public static final String INTERFERENCESOURCE_NORMALIZE = "interferencesource_normalize";

    /**
     * 网站访问统计
     */
    public static final String PQWEBVIEW = "pqwebview";

    /**
     * 电能质量标准库
     */
    public static final String PQSTANDARDSTORE = "pqstandardstore";

    /**
     * 治理信息库
     */
    public static final String GOVERNANCESTORE = "governancestore";

    /**
     * 电能质量技术成果库
     */
    public static final String PQSCIENCESTORE = "pqsciencestore";

    /**
     * 承载力评估参数表
     */
    public static final String BCE_BEARING_CAPACITY = "bce_bearingcapacity";
    /**
     * 承载力评估 - 项目名称表
     */
    public static final String BCE_PROJECTBASICINFO = "bce_projectbasicinfo";
    /**
     * 承载力评估 - 母线台账
     */
    public static final String BCE_BUSLEDGER = "bce_busledger";
    /**
     * 承载力评估 - 干扰源台账
     */
    public static final String BCE_INTERFERENCESOURCELEDGER = "bce_interferencesourceledger";
    /**
     * 承载力评估 - 线路台账
     */
    public static final String BCE_LINELEDGER = "bce_lineledger";
    /**
     * 承载力评估 - 变压器台账
     */
    public static final String BCE_TRANSFORMERLEDGER = "bce_transformerledger";
    /**
     * 承载力评估数据表
     */
    public static final String BCE_BEARING_CAPACITY_DATA = "bce_bearingcapacitydata";
    /**
     * 薄弱点评估数据表
     */
    public static final String BCE_WEAKNESSES_DATA = "bce_weaknessesdata";

    /**
     * 系统配置表
     */
    public static final String SYSTEMCONFIG = "systemconfig";

    /**
     * 单点自定义报表
     */
    public static final String STEADY_CUSTOM_PLAN = "steadycustomplan";

    /**
     * 两级上送
     */
    public static final String PQ_WM_UPLOAD_DATA = "pqwmuploaddata";


    /**
     * ro_powerquantity
     */
    public static final String RO_POWERQUANTITY = "ro_powerquantity";

    public static final String LINE_INSPECTION_RESULT = "lineinspectionresult";

    /**
     *  干扰源类型-储能站
     **/
    public static final String ENERGY_STORAGE_STATION = "energystoragestation";


    public static final String CHARGING_STATION = "chargingstation";

    /**
     * 变压器配变
     */
    public static final String DISTRIBUTION_TRANSFORMER = "distributiontransformer";

    /**
     * 变压器主变
     */
    public static final String MAIN_TRANSFORMER = "maintransformer";

    /**
     * 变压器主变
     */
    public static final String COMPANY_SUBSTATION_COUNT = "companysubstationcount";

    /**
     * 管理单位关联变电站数量表
     */
    public static final String COMPANYSUBSTATIONCOUNT = "companysubstationcount";


    public static final String REPORT_LOG = "reportlog";
    /**
     * 电容器
     */
    public static final String CONDENSER = "condenser";
    public static final String CONDENSER_MODEL = "condenser_model";
    /**
     * 电抗器
     */
    public static final String REACTOR = "reactor";
    public static final String REACTOR_MODEL = "reactor_model";
    /**
     * SVC
     */
    public static final String STATICVARCOMPENSATOR = "staticvarcompensator";
    /**
     * SVG
     */
    public static final String STATICVARGENERATOR ="staticvargenerator";
    /**
     * DVR
     */
    public static final String DYNAMICVOLTAGERESTORER ="dynamicvoltagerestorer";
    /**
     * APF
     */
    public static final String ACTIVEPOWERFILTER ="activepowerfilter";
    /**
     * 滤波器
     */
    public static final String WAVEFILTER ="wavefilter";
    /**
     * 变流器
     */
    public static final String CONVERTER ="converter";
    /**
     * 充电桩
     */
    public static final String CHARGINGPOINT ="chargingpoint";
    /**
     * 中压多馈线电能质量柔性控制装备
     */
    public static final String PQFLEXIBLEEQUIPMENT ="pqflexibleequipment";
    /**
     * 换相开关
     */
    public static final String PHASECHANGESWITCH ="phasechangeswitch";
    /**
     * 治理设备
     */
    public static final List<String> GOVERNANCEDEVICES = Arrays.asList(CONDENSER,REACTOR,STATICVARCOMPENSATOR,STATICVARGENERATOR,DYNAMICVOLTAGERESTORER,ACTIVEPOWERFILTER,WAVEFILTER,
            CONVERTER,CHARGINGPOINT,PQFLEXIBLEEQUIPMENT,PHASECHANGESWITCH);


    public static final String PQ_DEFAULT_LIMIT = "pqdefaultlimit";

    /**
     * 监测点预测数据记录表
     */
    public static final String PREDICTION_DATA ="predictiondata";

    /**
     * 扰动溯源记录
     */
    public static final String DISTURBANCETRACERECORD ="disturbancetracerecord";
    /**
     * 控制下发参数
     */
    public static final String CONTROLDISTRIBUTION ="controldistribution";

    /**
     * 监测点预测数据记录表
     */
    public static final String RO_PREDICTION_RATE ="ro_predictionrate";


    public static final String TEST_DATA_TEMPLATE = "testdatatemplate";


    public static final String TEST_DATA_MAPPING = "testdatamapping";


    public static final String ZHI_KAN_DATA = "zhikandata";
    public static final String PLATFORMCONTROLLOG ="platformcontrollog";

    /**
     * @Description 扰动反演节点信息记录表
     */
    public static final String PERTURBATION_NODE ="perturbationnode";

    /**
     * 电能质量综合评分
     */
    public static final String PQCOMPOSITEEVALUATION ="pqcompositeevaluation";

    /**
     * 光伏
     */
    public static final String PVDEVICE ="pvdevice";

    /**
     * 柔性负荷侧变流器
     */
    public static final String FLEXIBLELOADSIDEINVERTER ="flexibleloadsideinverter";

    /**
     * 储能
     */
    public static final String ENERGYSTORAGE ="energystorage";

    /**
     * 稳态告警事件表
     */
    public static final String STEADY_ALARM_EVENT = "steadyalarmevent";

    /**
     * 稳态告警事件表
     */
    public static final String RO_STEADY_ALARM_EVENT = "ro_steadyalarmevent";


    /**
     * 新治理设备
     */
    public static final List<String> NEWGOVERNANCEDEVICES = Arrays.asList(PVDEVICE,STATICVARGENERATOR,CHARGINGPOINT, FLEXIBLELOADSIDEINVERTER, ENERGYSTORAGE, ACTIVEPOWERFILTER, PQFLEXIBLEEQUIPMENT, PHASECHANGESWITCH);
    public static final String COUNTY = "county";

    public static final String HARMONICGOVERNANCEPLAN = "harmonicgovernanceplan";
    public static final String SAGGOVERNANCEPLAN = "saggovernanceplan";
}

package com.cet.pq.common.encode;
import java.math.BigInteger;

import org.bouncycastle.math.ec.ECPoint;
/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/13 15:32
 */
public class SM2KeyPair {
    private final ECPoint publicKey;
    private final BigInteger privateKey;

    SM2KeyPair(ECPoint publicKey, BigInteger privateKey) {
        this.publicKey = publicKey;
        this.privateKey = privateKey;
    }

    public ECPoint getPublicKey() {
        return publicKey;
    }

    public BigInteger getPrivateKey() {
        return privateKey;
    }
}

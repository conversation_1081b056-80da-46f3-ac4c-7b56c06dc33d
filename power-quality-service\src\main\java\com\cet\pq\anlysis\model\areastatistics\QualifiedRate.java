package com.cet.pq.anlysis.model.areastatistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/6 17:26
 * @description
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QualifiedRate {
    @ApiModelProperty("渗透率范围")
    private String permeabilityrange;
    @ApiModelProperty("合格率")
    private Double qualifiedrate;

    public QualifiedRate(String permeabilityrange) {
        this.permeabilityrange = permeabilityrange;
    }
}

package com.cet.pq.common.utils;

import cn.hutool.poi.excel.ExcelWriter;
import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.ExcelConstant;
import com.cet.pq.common.enums.TemplateDataEnum;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.exception.ExcelException;
import com.cet.pq.common.model.ErrorExcelMsg;
import com.cet.pq.common.model.excel.*;
import com.cet.pq.common.time.DateUtils;
import com.cet.pq.common.time.TimeFormat;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @date 2023/7/13 17:04
 * @description
 **/
public class SXSSFUtil {

    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * 获取Excel文件的所有数据
     *
     * @return
     */
    @SuppressWarnings("resource")
    public static List<ExcelData> getAllData(MultipartFile excel) {
        // 准备返回值
        List<ExcelData> dataList = new ArrayList<>();
        InputStream fis = null;
        Workbook workBook = null;
        String fileType = null;
        if(StringUtils.isNotEmpty(excel.getOriginalFilename())){
            fileType = excel.getOriginalFilename().split("\\.")[1];
        }
        try {
            fis = excel.getInputStream();
            if (ExcelConstant.EXCEL_TYPE_XLSX.equals(fileType)) {
                workBook = new XSSFWorkbook(fis);
            } else {
                workBook = new HSSFWorkbook(fis);
            }
            Integer sheetNum = workBook.getNumberOfSheets();
            for (int i = 0; i < sheetNum; i++) {
                Sheet sheet = workBook.getSheetAt(i);
                List<ExcelData> sheetDataList = getAllDataBySheet(sheet);
                dataList.addAll(sheetDataList);
            }
        } catch (IOException e) {
            logger.debug(CommonConstant.CONTEXT, e);
            throw new CommonManagerException("Excel获取失败", e);
        } finally {
            IOUtils.closeQuietly(fis);
        }
        return dataList;
    }

    /**
     * 获取Excel文件中一个sheet的所有数据
     *
     * @return
     */
    private static List<ExcelData> getAllDataBySheet(Sheet sheet) {
        // 准备返回值
        List<ExcelData> sheetDataList = new ArrayList<>();
        String sheetName = sheet.getSheetName();
        String row;
        String col;
        // 获取sheet的行数
        Integer rowNum = sheet.getLastRowNum();
        for (int i = 0; i < rowNum; i++) {
            Row rowIns = sheet.getRow(i);
            if (null == rowIns) {
                continue;
            }
            row = ParseDataUtil.parseString(i + 1);
            short colNum = (short) (rowIns.getLastCellNum() - 1);
            for (int j = 0; j < colNum; j++) {
                Cell cell = rowIns.getCell(j);
                if (null == cell) {
                    continue;
                }
                col = getExcelColName(j);
                CellType cellType = cell.getCellTypeEnum();
                ExcelData data;
                switch (cellType) {
                    case NUMERIC:
                        Double numValue = cell.getNumericCellValue();
                        data = new ExcelData(sheetName, row, col, numValue);
                        break;
                    case BOOLEAN:
                        Boolean boolValue = cell.getBooleanCellValue();
                        data = new ExcelData(sheetName, row, col, boolValue);
                        break;
                    default:
                        if (null == cell.getStringCellValue()) {
                            data = new ExcelData(sheetName, row, col, null);
                        } else {
                            String strValue = cell.getStringCellValue();
                            data = new ExcelData(sheetName, row, col, strValue);
                        }
                        break;
                }
                sheetDataList.add(data);
            }
        }
        return sheetDataList;
    }

    /**
     * 根据列号获取列名
     *
     * @param col
     * @return
     */
    public static String getExcelColName(int col) {
        StringBuilder str = new StringBuilder(2);
        calcExcelColumnName(str, col);
        return str.toString();
    }

    /**
     * 计算列名
     *
     * @param str
     * @param col
     */
    private static void calcExcelColumnName(StringBuilder str, int col) {
        int tmp = col / 26;
        if (tmp > 26) {
            calcExcelColumnName(str, tmp - 1);
        } else if (tmp > 0) {
            str.append((char) (tmp + 64));
        }
        str.append((char) (col % 26 + 65));
    }

    /**
     * 读取模板坐标单元格的文本
     */
    public static String readCol(Workbook template, String id, int rowNum, int colNum) {
        Sheet templateSheet = template.getSheet(TemplateDataEnum.getTemplateNameById(id));
        Row row = templateSheet.getRow(rowNum);
        Cell cell = row.getCell(colNum);
        return cell.getStringCellValue();
    }

    /**
     * 读取模板
     */
    public static Workbook getTemplate() {
        InputStream templateIn = null;
        Workbook template;
        try {
            templateIn = Thread.currentThread().getContextClassLoader().getResourceAsStream("templates/export/" + "图表导出模板.xlsx");
            template = new XSSFWorkbook(templateIn);
        } catch (IOException e) {
            throw new ExcelException("读取模板失败");
        } finally {
            try {
                if(null != templateIn){
                    templateIn.close();
                }
            } catch (IOException e) {
            }
        }
        return template;
    }

    /**
     * 导出图表方法
     */
    public static void commonExport(HttpServletResponse response, ExportParameter exportParameter) throws ExcelException {
        commonExport(response, exportParameter, "templates/export/图表导出模板.xlsx");
    }

    /**
     * 导出图表方法
     */
    public static void commonExport(HttpServletResponse response, ExportParameter exportParameter, String exportTemplatePath) throws ExcelException {
        commonExport(response, exportParameter, new ArrayList<>(), exportTemplatePath);
    }

    /**
     *
     * @param response
     * @param exportParameter
     * @throws ExcelException
     */
    public static void commonExport(HttpServletResponse response, ExportParameter exportParameter, List<CellRangeAddress> mergeList, String exportTemplatePath) throws ExcelException {
        InputStream templateIn = null;
        // 创建文件
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFWorkbook template;
        SXSSFWorkbook wb = new SXSSFWorkbook(workbook,-1,Boolean.FALSE, Boolean.TRUE);
        List<Sheets> sheets1 = exportParameter.getSheets();
        for (int i2 = 0; i2 < sheets1.size(); i2++) {
            Sheets sheets = sheets1.get(i2);
            SXSSFSheet sheet;
            String id = sheets.getId();
            // id为空时创建空白sheet；id不为空时复制模板到新建sheet
            if (StringUtils.isBlank(id)) {
                sheet = wb.createSheet(sheets.getSheetName());
                // 写表头
                List<String> head = sheets.getHead();
                Row headRow = sheet.createRow(sheets.getWriteRow() + 1);
                for (int i = 0; i < head.size(); i++) {
                    Cell headCell = headRow.createCell(i);
                    headCell.setCellValue(head.get(i));
                }
            } else {
                // 读取模板
                try {
                    templateIn = Thread.currentThread().getContextClassLoader().getResourceAsStream(exportTemplatePath);
                    template = new XSSFWorkbook(templateIn);
                } catch (IOException e) {
                    throw new ExcelException("读取模板失败");
                } finally {
                    try {
                        templateIn.close();
                    } catch (Exception e) {
                    }
                }
                // 获取模板sheet
                String templateSheetName = TemplateDataEnum.getTemplateNameById(id);
                Sheet templateSheet = template.getSheet(templateSheetName);
                // 创建要写数据的目标工作表
                sheet = wb.createSheet(sheets.getSheetName());
//                try {
//                    sheet.flushRows();
//                }catch (IOException e){
//                    throw new ExcelException("sheet.flushRows()失败0");
//                }
                for (CellRangeAddress x : mergeList) {
                    sheet.addMergedRegion(x);
                }
                // 复制模板工作表到目标工作表
                CopySheetUtil.copySheet(templateSheet, sheet, CopySheetUtil.copyCellStyle(template, wb));
            }
            // 图片导出
            int lastCol = sheets.getCol();
            int lastRow = sheets.getRow();
            writePicture(wb, sheets, lastCol, lastRow, sheet);

            List<LinkedHashMap<String, Object>> data = sheets.getData();
            // 如果表数据为空，返回模板空表
            if (CollectionUtils.isEmpty(data) && CollectionUtils.isEmpty(sheets.getInsertData())) {
                continue;
            }
            // 行数据列表
            List<List> valueList = new ArrayList();
            Class clazz = TemplateDataEnum.getClazzById(id);
            // 单元格格式
            CellStyle comm = wb.createCellStyle();
            // 水平居中
            comm.setAlignment(HorizontalAlignment.CENTER);
            // 垂直居中
            comm.setVerticalAlignment(VerticalAlignment.CENTER);
//			// 单元格背景色
//			comm.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
//			comm.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            // 单元格边框
            comm.setBorderBottom(BorderStyle.THIN);
            comm.setBorderLeft(BorderStyle.THIN);
            comm.setBorderRight(BorderStyle.THIN);
            comm.setBorderTop(BorderStyle.THIN);
            //红色单元格样式
            CellStyle redStyle = getRedCellStyle(wb);
            if (CollectionUtils.isNotEmpty(data)) {
                if (clazz == null) {
                    // 调用导出方法时数据字段需要按表头顺序排序好
                    for (LinkedHashMap<String, Object> map : data) {
                        List values = new ArrayList();
                        for (Object value : map.values()) {
                            if (value == null) {
                                value = "--";
                            }
                            values.add(value);
                        }
                        valueList.add(values);
                    }
                } else {
                    // 通过实体类确定数据顺序
                    List sortData = JsonTransferUtils.transferList(data, clazz);
                    // 获取要导出的行数据列表
                    valueList = getRowData(clazz, sortData, valueList);
                }
                // 顺序写入数据
                writeData(sheet, valueList, sheets.getWriteRow(), comm);
            }
            // 特定行写入数据
            sheet.setDefaultColumnWidth(15);
            if (CollectionUtils.isNotEmpty(sheets.getInsertData())) {
                List<InsertData> insertData1 = sheets.getInsertData();
                int pagesize = 200;
                for (int i1 = 0; i1 < insertData1.size(); i1++) {
                    InsertData insertData = insertData1.get(i1);
                    Row row = sheet.getRow(insertData.getRow());
                    if (row == null) {
                        row = wb.getXSSFWorkbook().getSheetAt(i2).createRow(insertData.getRow());
                        row.setHeight(wb.getXSSFWorkbook().getSheetAt(i2).getDefaultRowHeight());
                    }
                    for (int i = 0; i < insertData.getData().size(); i++) {
                        Cell cell = row.getCell(insertData.getCol() + i);
                        if (null == cell) {
                            cell = row.createCell(insertData.getCol() + i);
                        }
                        Object value = insertData.getData().get(i);
                        if (value == null) {
                            value = "--";
                        }
                        if (ParseDataUtil.parseString(value).equals(ExcelConstant.IS_OVER_LIMIT)) {
                            cell.setCellStyle(redStyle);
                        } else {
                            cell.setCellStyle(comm);
                        }
                        setCellValue(cell, value);
                    }
                    try {
                        if (i1 % pagesize == 0) {
                            sheet.flushRows();
                        }
                    } catch (IOException e) {
                        throw new ExcelException("sheet.flushRows()失败2");
                    }
                }
            }
        }
        // 返回文件
        String fileName = exportParameter.getFileName();
        response.reset();
        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        try {
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.close();
        } catch (IOException e) {
            throw new ExcelException("文件输出失败"+e.getLocalizedMessage());
        }
    }

    private static CellStyle getRedCellStyle(Workbook workbook) {
        CellStyle redStyle = workbook.createCellStyle();
        // 水平居中
        redStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        redStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//			// 单元格背景色
//			comm.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
//			comm.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 单元格边框
        redStyle.setBorderBottom(BorderStyle.THIN);
        redStyle.setBorderLeft(BorderStyle.THIN);
        redStyle.setBorderRight(BorderStyle.THIN);
        redStyle.setBorderTop(BorderStyle.THIN);
        Font font = workbook.createFont();
        font.setColor(Font.COLOR_RED);
        redStyle.setFont(font);
        return redStyle;
    }

    private static void writeData(Sheet sheet, List<List> valueList, int writeRow, CellStyle comm) {
        try {
            int pagesize = 200;
            for (int i = 0; i < valueList.size(); i++) {
                List dataArray = valueList.get(i);
                Row row = sheet.getRow(i + writeRow - 1);
                if (row == null) {
                    row = sheet.createRow(i + writeRow - 1);
                    row.setHeight(sheet.getDefaultRowHeight());
                }
                for (int j = 0; j < dataArray.size(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        cell = row.createCell(j);
                    }
                    cell.setCellStyle(comm);
                    Object value = dataArray.get(j);
                    setCellValue(cell, value);
                }
                try {
                    if(i%pagesize==0){
                        ((SXSSFSheet)sheet).flushRows();
                    }
                }catch (IOException e){
                    throw new ExcelException("sheet.flushRows()失败");
                }
            }
        } catch (Exception e) {
            throw new ExcelException("数据导出失败");
        }
    }

    private static void setCellValue(Cell cell, Object value) {
        switch (value.getClass().getName()) {
            case "java.lang.Integer":
            case "java.lang.Double":
            case "java.lang.Float":
            case "java.lang.Long":
                cell.setCellValue(Double.parseDouble(value.toString()));
                break;
            case "java.lang.Boolean":
                cell.setCellValue(Boolean.parseBoolean(value.toString()));
                break;
            case "java.lang.String":
                cell.setCellValue(String.valueOf(value));
                break;
            default:
                break;
        }
    }

    private static List<List> getRowData(Class clazz, List sortData, List<List> valueList) {
        try {
            for (Object entity : sortData) {
                List values = new ArrayList();
                for (Field field : entity.getClass().getDeclaredFields()) {
                    if ("serialVersionUID".equals(field.getName())) {
                        continue;
                    }
                    PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                    Method getMethod = pd.getReadMethod();
                    Object value = getMethod.invoke(entity);
                    values.add(value);
                }
                valueList.add(values);
            }
        } catch (IllegalAccessException | IntrospectionException | InvocationTargetException e) {
            throw new ExcelException("数据排序失败");
        }
        return valueList;
    }

    private static void writePicture(Workbook workbook, Sheets sheets, int lastCol, int lastRow, Sheet sheet) {
        if (CollectionUtils.isNotEmpty(sheets.getPicture())) {
            try {
                for (int j = 0; j < sheets.getPicture().size(); j++) {
                    // 图片字符串转为字节数组
                    byte[] picture = Base64.decodeBase64(sheets.getPicture().get(j).replace("data:image/png;base64,", ""));
                    // 第一张图片右下角坐标
                    XSSFDrawing xssfDrawing = (XSSFDrawing) sheet.createDrawingPatriarch();
                    // 前四个参数是控制图片距离单元格left，top，right，bottom的像素距离,后四个参数表示图片左上角所在的cellNum和rowNum、右下角所在的cellNum和rowNum
                    XSSFClientAnchor anchor = new XSSFClientAnchor(0,0,0,0, 0,lastRow*j, lastCol, lastRow*(j + 1));
                    xssfDrawing.createPicture(anchor, workbook.addPicture(picture, XSSFWorkbook.PICTURE_TYPE_PNG));
                }
            } catch (Exception e) {
                throw new ExcelException("图片导出失败");
            }
        }
    }

    /**
     * 设置图片的位置大小、写数据的起始行、sheet名
     * @param sheets
     */
    public static void setSheets(Sheets sheets) {
        String id = sheets.getId();
        sheets.setSheetName(TemplateDataEnum.getSheetNameById(id));
        sheets.setCol(TemplateDataEnum.getColById(id));
        sheets.setRow(TemplateDataEnum.getRowById(id));
        sheets.setWriteRow(TemplateDataEnum.getWriteRowById(id));
    }

    /**
     * 设置首行标题数据（在标题中插入查询时间、全网、上送）
     * @param sheets
     * @param isUpload
     * @param row 标题行
     * @param col 标题列
     */
    public static void setSheetAndTitle(Long startTime, Long endTime, Sheets sheets, int row, int col) {
        setSheets(sheets);
        String title = TemplateDataEnum.getSheetNameById(sheets.getId());
//		if (isUpload != null) {
//			if (isUpload) {
//				title += " - " + ExcelConstant.HEAD_UPLOAD_TRUE;
//			} else {
//				title += " - " + ExcelConstant.HEAD_UPLOAD_FALSE;
//			}
//		}
        LinkedList<Object> objects = new LinkedList<>();
        if (startTime != null) {
            title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
        }
        objects.add(title);
        if (CollectionUtils.isEmpty(sheets.getInsertData())) {
            sheets.setInsertData(Collections.singletonList(new InsertData(row, col, objects)));
        } else {
            sheets.getInsertData().add(new InsertData(row, col, objects));
        }
    }

    /**
     * 设置首行标题数据（在标题中插入查询时间、全网、上送）
     * @param sheets
     * @param isUpload
     * @param row 标题行
     * @param col 标题列
     */
    public static void setSheetAndTitle(Long startTime, Long endTime, Sheets sheets,Boolean isUpload, int row, int col) {
        setSheets(sheets);
        String title = TemplateDataEnum.getSheetNameById(sheets.getId());
//		if (isUpload != null) {
//			if (isUpload) {
//				title += " - " + ExcelConstant.HEAD_UPLOAD_TRUE;
//			} else {
//				title += " - " + ExcelConstant.HEAD_UPLOAD_FALSE;
//			}
//		}
        LinkedList<Object> objects = new LinkedList<>();
        if (startTime != null) {
            title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
        }
        objects.add(title);
        if (CollectionUtils.isEmpty(sheets.getInsertData())) {
            sheets.setInsertData(Collections.singletonList(new InsertData(row, col, objects)));
        } else {
            sheets.getInsertData().add(new InsertData(row, col, objects));
        }
    }


    /**
     * 设置首行标题数据（在标题中插入查询时间、全网、上送）
     * @param sheets
     * @param isUpload
     * @param row 标题行
     * @param col 标题列
     */
    public static void setSheetAndTitleWithoutUpload(Long startTime, Long endTime, Sheets sheets, int row, int col) {
        setSheets(sheets);
        String title = TemplateDataEnum.getSheetNameById(sheets.getId());
        LinkedList<Object> objects = new LinkedList<>();
        if (startTime != null) {
            title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
        }
        objects.add(title);
        if (CollectionUtils.isEmpty(sheets.getInsertData())) {
            sheets.setInsertData(Collections.singletonList(new InsertData(row, col, objects)));
        } else {
            sheets.getInsertData().add(new InsertData(row, col, objects));
        }
    }
    /**
     * 合并单元格
     * @param sheet
     * @param firstRow
     * @param lastRow
     * @param firstCol
     * @param lastCol
     */
    public static void addMergeRegion(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
    }

    /**
     * 设置列宽
     * @param sheet
     * @param i2
     * @param width
     */
    public static void setColumnWidth(Sheet sheet, int i2, int width) {
        sheet.setColumnWidth(i2, width * 256);
    }

    /**
     *
     * @param sheet
     * @param index    行号（从0开始）
     * @param height   行高
     * @return
     */
    public static Row createRowSetRowHeight(Sheet sheet, int index, int height) {
        Row row1 = sheet.createRow(index);
        row1.setHeight((short) (height * 20));
        return row1;
    }

    /**
     *
     * @param headerCellStyle 单元格样式
     * @param row   行
     * @param index 一行中的列号（从0开始）
     * @param value 单元格的值
     */
    public static void addCell(XSSFCellStyle headerCellStyle, Row row, int index, Object value) {
        Cell cell = row.createCell(index);
        if (value == null) {
            value = "";
        }
        switch (value.getClass().getName()) {
            case "java.lang.Integer":
            case "java.lang.Double":
            case "java.lang.Float":
            case "java.lang.Long":
                cell.setCellValue(Double.parseDouble(value.toString()));
                break;
            case "java.lang.Boolean":
                cell.setCellValue(Boolean.parseBoolean(value.toString()));
                break;
            case "java.lang.String":
                cell.setCellValue(String.valueOf(value));
                break;
            default:
                break;
        }
        cell.setCellStyle(headerCellStyle);
    }

    public static ExcelWriter getWriter() {
        return cn.hutool.poi.excel.ExcelUtil.getWriter(true);
    }

    /**
     *
     * @param writer
     * @param index     sheetNo（从0开始）
     * @param sheetName sheet名
     * @return
     */
    public static Sheet getSheetAndSetSheetName(ExcelWriter writer, int index, String sheetName) {
        Sheet sheet = writer.getSheet();
        writer.getWorkbook().setSheetName(index,sheetName);
        return sheet;
    }

    /**
     * 数据单元格样式 边框黑细
     * @param writer
     * @return
     */
    public static XSSFCellStyle getDataCellStyle(ExcelWriter writer, int row, int col) {
        // 获取当前单元格样式
        XSSFCellStyle cellStyle = (XSSFCellStyle )writer.createCellStyle();
        // 上下居中 左右居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 下边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        // 左边框
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        // 右边框
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 上边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        return cellStyle;
    }

    /**
     * 表头样式 黑色边框 灰色背景色 字体加粗
     * @param writer
     * @return
     */
    public static XSSFCellStyle getHeaderCellStyle(ExcelWriter writer) {
        // 获取当前单元格样式
        XSSFCellStyle  cellStyle = (XSSFCellStyle )writer.getCellStyle();
        // 上下居中 左右居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 下边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        // 左边框
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        // 右边框
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 上边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        // 背景色RGB灰色 纯色填充 ,new DefaultIndexedColorMap()
        cellStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(228,230,234)));
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 字体加粗
        Font font = writer.getWorkbook().createFont();
        font.setBold(true);
        cellStyle.setFont(font);
        return cellStyle;
    }

    /**
     * 输出表格
     *
     * @param writer
     * @param response
     */
    public static void exportExcel(ExcelWriter writer, HttpServletResponse response, String description) throws IOException {
        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String current = formatter.format(date);
        String excelName = description + current + ".xlsx";
        response.setHeader("Content-disposition", "attachment;filename=" + encodeFileName(excelName));
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8 ");
        writer.flush(response.getOutputStream());
        writer.close();
    }

    public static String encodeFileName(String fileName) throws UnsupportedEncodingException {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        return fileName.replace("+", "%20");
    }


    /**
     * @Description: 变电站匹配台区单位
     * @return: void
     **/
    public static void matchSubAreaUnit(List<ErrorExcelMsg> errorMessageList, String provincecompanyname,String countycompanyname, String citycompanyname, String powerdistributionareaname,
                                        String rowid, AtomicReference<Long> stationId, List<Map<String, Object>> stationOpt, AtomicReference<String> modelLabel) {
        if (CollectionUtils.isNotEmpty(stationOpt)) {
            stationOpt.forEach(substationObjectMap -> {
                //台区
                List<Map<String, Object>> areaList = ParseDataUtil.parseList(substationObjectMap.get(ColumnName.POWERDISTRIBUTIONAREA_MODEL));
                if (CollectionUtils.isNotEmpty(areaList) && StringUtils.isNotEmpty(powerdistributionareaname)) {
                    modelLabel.set(ColumnName.POWERDISTRIBUTIONAREA);
                    List<Map<String, Object>> matchAreaList = areaList.stream().filter(t -> ParseDataUtil.parseString(t.get("name")).equals(powerdistributionareaname)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(matchAreaList)) {
                        //变电站匹配管理单位
                        ExcelUtil.matchSubCompanyUnit(errorMessageList, stationId, substationObjectMap, provincecompanyname, countycompanyname, citycompanyname, rowid, ParseDataUtil.parseLong(matchAreaList.get(0).get("id")));
                    } else {
                        errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 5, "所属台区匹配错误."));
                    }
                } else {
                    //变电站匹配管理单位
                    ExcelUtil.matchSubCompanyUnit(errorMessageList, stationId, substationObjectMap, provincecompanyname, countycompanyname, citycompanyname, rowid, ParseDataUtil.parseLong(substationObjectMap.get("id")));
                }
            });
        } else {
            errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 4, "所属电站匹配错误."));
        }
    }


    /**
     * @Description: 变电站匹配市级单位
     * @return: void
     **/
    public static void matchSubCompanyUnit(List<ErrorExcelMsg> errorMessageList, AtomicReference<Long> stationId, Map<String, Object> substationObjectMap, String provincecompanyname, String countycompanyname,
                                           String citycompanyname, String rowid, Long modelId) {
        List<Map<String, Object>> countyList = ParseDataUtil.parseList(substationObjectMap.get(ColumnName.COUNTYCOMPANY_MODEL));
        if (CollectionUtils.isNotEmpty(countyList)) {
            if (countyList.get(0).get("name").equals(countycompanyname)) {
                //县级和市级都需要匹配
                List<Map<String, Object>> cityList = ParseDataUtil.parseList(countyList.get(0).get(ColumnName.CITYCOMPANY_MODEL));
                if (CollectionUtils.isNotEmpty(cityList) && cityList.get(0).get("name").equals(citycompanyname)) {
                    List<Map<String, Object>> provinceList = ParseDataUtil.parseList(cityList.get(0).get(ColumnName.PROVINCECOMPANY_MODEL));
                    if (CollectionUtils.isNotEmpty(provinceList) && provinceList.get(0).get("name").equals(provincecompanyname)) {
                        stationId.set(modelId);
                    } else {
                        errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 1, "所属省级单位匹配错误."));
                    }
                } else {
                    errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 2, "所属市级单位匹配错误."));
                }
            } else {
                errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 3, "所属县级单位匹配错误."));
            }
        } else {
            // 关联市级
            List<Map<String, Object>> cityList = ParseDataUtil.parseList(substationObjectMap.get(ColumnName.CITYCOMPANY_MODEL));
            if (CollectionUtils.isNotEmpty(cityList) && cityList.get(0).get("name").equals(citycompanyname)) {
                stationId.set(modelId);
            } else {
                errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 2, "所属市级单位匹配错误."));
            }
        }
    }
}

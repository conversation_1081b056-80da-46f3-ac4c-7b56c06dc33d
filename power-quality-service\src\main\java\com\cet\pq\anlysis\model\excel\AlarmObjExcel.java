package com.cet.pq.anlysis.model.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "导出告警对象台账")
public class AlarmObjExcel {

    @ExcelIgnore
    private Long id;

    @ExcelProperty(index = 0)
    private String rowid;

    /**
     * 所属单位
     */
    @ExcelProperty(index = 1)
    private String citycompanyName;
    /**
     * 姓名
     */
    @ExcelProperty(index = 2)
    private String name;
    /**
     * 联系方式
     */
    @ExcelProperty(index = 3)
    private String mobile;
    /**
     * 是否反馈
     */
    @ExcelProperty(index = 4)
    private String iffeedback;
    /**
     * 处理情况说明
     */
    @ExcelProperty(index = 5)
    private String solveexplain;
    /**
     * 创建时间
     */
    @ExcelProperty(index = 6)
    @DateTimeFormat("yyyy年MM月dd日")
    private Date logtime;
    /**
     * 审核状态(1:审核未通过;2:审核已通过;)
     */
    @ExcelProperty(index = 7)
    private String alarmstatus;

}

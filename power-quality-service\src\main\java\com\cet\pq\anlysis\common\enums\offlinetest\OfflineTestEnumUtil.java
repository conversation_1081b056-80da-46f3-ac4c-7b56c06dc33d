package com.cet.pq.anlysis.common.enums.offlinetest;

import com.cet.pq.anlysis.model.offlinetest.ExcelDataLoaction;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.enums.WiredTypeEnum;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.pqperception.Quantitymaptemplate;
import com.cet.pq.common.utils.ParseDataUtil;
import com.cet.pq.pqservice.constant.Constants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.netflix.eventbus.spi.FilterLanguage.Constant;

/**
 * 离线测试数据位置枚举帮助类
 *
 * <AUTHOR>
 */
public class OfflineTestEnumUtil {

    private static final String BASE_PACKAGE_DIR = "com.cet.pq.anlysis.common.enums.offlinetest";

    private static final List<String> enumStarDataList = new ArrayList<>();

    private static final List<String> enumTriangleDataList = new ArrayList<>();

    private static final List<String> inventoryDataList = new ArrayList<>();

    static {
        //台账数据位置枚举
        inventoryDataList.add("InventoryData");
        //星型接线方式数据位置枚举
        enumStarDataList.add("ElectrictyData");
        enumStarDataList.add("SumData");
        enumStarDataList.add("StarVoltageData");
        //角型接线方式数据位置枚举
        enumTriangleDataList.add("ElectrictyData");
        enumTriangleDataList.add("TriangleSumData");
        enumTriangleDataList.add("TriangleVoltageData");
    }

    /**
     * 获取台账信息
     *
     * @return
     */
    public static List<ExcelDataLoaction> enumValue2InventoryList(Integer moduleValue) {
        String moduleName = ModuleEnum.getPathByValue(moduleValue);
        return enum2List(inventoryDataList, moduleName);
    }

    /**
     * 将指定路径下同属性的枚举值转化为数据列表
     *
     * @param moduleValue
     * @param wiredType   接线方式
     * @return
     */
    public static List<ExcelDataLoaction> enumValue2DataList(Integer moduleValue, Integer wiredType) {
        String moduleName = ModuleEnum.getPathByValue(moduleValue);
        if (WiredTypeEnum.star.getValue().equals(wiredType)) {
            return enum2List(enumStarDataList, moduleName);
        } else if (WiredTypeEnum.triangle.getValue().equals(wiredType)) {
            return enum2List(enumTriangleDataList, moduleName);
        } else {
            throw new CommonManagerException("接线方式无效，请去台账确认测点的接线方式");
        }
    }

    /**
     * 获取离线测试所有的dataID，用新疆的模板获取所有的dataId
     *
     * @return
     */
    public static List<Long> getAllDataIdInOfflineTest() {
        String moduleName = ModuleEnum.getPathByValue(CommonConstant.NUMBER_ONE);
        List<ExcelDataLoaction> starLocation = enum2List(enumStarDataList, moduleName);
        List<Long> starDataIdList = starLocation.stream().map(ExcelDataLoaction::getDataId).collect(Collectors.toList());
        List<ExcelDataLoaction> triangleLocation = enum2List(enumTriangleDataList, moduleName);
        List<Long> triangleDataIdList = triangleLocation.stream().map(ExcelDataLoaction::getDataId).collect(Collectors.toList());
        starDataIdList.addAll(triangleDataIdList);
        List<Long> dataIdList = starDataIdList.stream().distinct().collect(Collectors.toList());
        return dataIdList;
    }


    private static List<ExcelDataLoaction> enum2List(List<String> enumlist, String moduleName) {
        List<ExcelDataLoaction> list = new ArrayList<>();
        enumlist.forEach(enumName -> {
            try {
                String classPath = BASE_PACKAGE_DIR.concat(CommonConstant.POINT).concat(moduleName).concat(CommonConstant.POINT).concat(enumName);
                // 1. 获取枚举类对象
                Class<?> clz = Class.forName(classPath);
                // 2. 得到所有的枚举常量
                Object[] enumConstants = clz.getEnumConstants();
                Method getDataId = clz.getMethod("getDataId");
                Method getAggregationType = clz.getMethod("getAggregationType");
                Method getDataName = clz.getMethod("getDataName");
                Method getSheetName = clz.getMethod("getSheetName");
                Method getCol = clz.getMethod("getCol");
                Method getRow = clz.getMethod("getRow");
                // 3. 调用对应的方法，得到枚举常量中的字段的值
                ExcelDataLoaction enumEntity = new ExcelDataLoaction();
                for (Object enumConstant : enumConstants) {
                    Long dataId = ParseDataUtil.parseLong(getDataId.invoke(enumConstant));
                    Integer aggregationType = ParseDataUtil.parseInteger(getAggregationType.invoke(enumConstant));
                    String dataName = ParseDataUtil.parseString(getDataName.invoke(enumConstant));
                    String sheetName = ParseDataUtil.parseString(getSheetName.invoke(enumConstant));
                    String col = ParseDataUtil.parseString(getCol.invoke(enumConstant));
                    String row = ParseDataUtil.parseString(getRow.invoke(enumConstant));
                    enumEntity = new ExcelDataLoaction(dataId, aggregationType, dataName, sheetName, col, row);
                    list.add(enumEntity);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return list;
    }

    public static ExcelDataLoaction ExcelDataLoaction(List<ExcelDataLoaction> dataLoactionList, String dataName) {
        List<ExcelDataLoaction> filter = dataLoactionList.stream().filter(dataLocation -> dataName.equals(dataLocation.getDataName())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filter)) {
            return filter.get(0);
        }
        throw new CommonManagerException("获取指定数据失败");
    }

    public static ExcelDataLoaction ExcelDataLoaction(List<ExcelDataLoaction> dataLoactionList, Long dataId) {
        List<ExcelDataLoaction> filter = dataLoactionList.stream().filter(dataLocation -> dataId.intValue() == dataLocation.getDataId().intValue()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filter)) {
            return filter.get(0);
        }
        throw new CommonManagerException("获取指定数据失败");
    }

    /**
     * 根据数据聚合类型获取判断超标的数据id
     *
     * @param dataNameList
     * @return
     */
    public static Integer getOverTypeDataIdList(List<Quantitymaptemplate> dataNameList, Long dataId) {
        List<Quantitymaptemplate> filter = dataNameList.stream().filter(d -> dataId.equals(d.getDataid())).collect(Collectors.toList());
        String dataName = CollectionUtils.isEmpty(filter) ? StringUtils.EMPTY : filter.get(0).getTemplatename();
        if (dataName.contains(Constants.FREQUENCY_DEVIATION) || dataName.contains(Constants.VOLTAGE_DEVIATION) || dataName.contains(Constants.FLICKER) || dataName.contains(Constants.VOLTAGE_UNBALANCE) || dataName.contains(Constants.POSITIVE_SEQUENCE)) {
            return CommonConstant.AGGREGATIONTYPE_MAX;
        } else if (dataName.contains(Constants.FREQUENCY) || dataName.contains(Constants.VOLTAGE)) {
            return CommonConstant.AGGREGATIONTYPE_AVG;
        } else if (dataName.contains(Constants.NEGATIVE_SEQUENCE)) {
            return CommonConstant.AGGREGATIONTYPE_MIN;
        } else {
            return CommonConstant.AGGREGATIONTYPE_CP;
        }
    }

    /**
     * 根据数据聚合类型获取判断超标的数据id
     * 最大值标红：频率偏差、频率、长时闪变
     * 最小值、最大值标红：电压偏差
     * 其他指标均是CP95值标红
     * 不平衡度及序分量指标中只有电压不平衡度的不平衡度（%）存在上限值2，且仅电压不平衡度的不平衡度（%）的CP95用于数据标红判断
     * @param dataNameList
     * @return
     */
    public static List<Integer> getOverTypeDataIdList2(List<Quantitymaptemplate> dataNameList, Long dataId) {
        List<Quantitymaptemplate> filter = dataNameList.stream().filter(d -> dataId.equals(d.getDataid())).collect(Collectors.toList());
        String dataName = CollectionUtils.isEmpty(filter) ? StringUtils.EMPTY : filter.get(0).getTemplatename();
        if (dataName.contains(Constants.FREQUENCY_DEVIATION) || dataName.contains(Constants.FLICKER)|| dataName.contains(Constants.FREQUENCY)) {
            return Arrays.asList(CommonConstant.AGGREGATIONTYPE_MAX);
        } else if (dataName.contains(Constants.VOLTAGEDEVIATION)) {
            return Arrays.asList(CommonConstant.AGGREGATIONTYPE_MIN,CommonConstant.AGGREGATIONTYPE_MAX);
        } else if (dataName.contains(Constants.CURRENTUNBALANCE)) {
            return new ArrayList<>();
        } else {
            return Arrays.asList(CommonConstant.AGGREGATIONTYPE_CP);
        }
    }

    /**
     * 根据数据聚合类型获取判断超标的数据id
     *
     * @param dataNameList
     * @return
     */
    public static List<Integer> getOverTypeByDataId(List<Quantitymaptemplate> dataNameList, Long dataId) {
        List<Quantitymaptemplate> filter = dataNameList.stream().filter(d -> dataId.equals(d.getDataid())).collect(Collectors.toList());
        String dataName = CollectionUtils.isEmpty(filter) ? StringUtils.EMPTY : filter.get(0).getTemplatename();
        if (dataName.contains(Constants.FREQUENCY_DEVIATION) || dataName.contains(Constants.LONG_FLICKER)|| dataName.contains(Constants.FREQUENCY)) {
            return Arrays.asList(CommonConstant.AGGREGATIONTYPE_MAX);
        } else if (dataName.contains(Constants.VOLTAGEDEVIATION)) {
            return Arrays.asList(CommonConstant.AGGREGATIONTYPE_MIN,CommonConstant.AGGREGATIONTYPE_MAX);
        } else if (dataName.contains(Constants.CURRENTUNBALANCE)) {
            return new ArrayList<>();
        }else {
            return Arrays.asList(CommonConstant.AGGREGATIONTYPE_CP);
        }
    }
}

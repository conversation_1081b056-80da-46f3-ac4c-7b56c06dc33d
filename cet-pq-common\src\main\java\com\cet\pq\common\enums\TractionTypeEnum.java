package com.cet.pq.common.enums;

import com.cet.pq.common.model.IdTextPair;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 牵引变类型枚举
 * @date 2021/2/2 16:44
 */
public enum TractionTypeEnum {
    //牵引变类型枚举
    S_traction(1, "单相牵引变压器(I,i)"),
    S_Vvtraction(2, "单相V.v联结牵引变压器"),
    T_Vvtraction(3, "三相V.v联结牵引变压器"),
    T_YNd11traction(4, "三相YN.d11联结牵引变压器"),
    T_YNd11d1traction(5, "三相YN,d11,d1组成的十字交叉联结牵引变压器"),
    Scotttraction(6, "SCOTT牵引变压器"),
    YNvtraction(7, "YN,v联结平衡牵引变压器"),
    YNAtraction(8, "YN,A联结平衡牵引变压器");

    private Integer id;
    private String text;

    TractionTypeEnum(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public static List<IdTextPair> toList() {
        List<IdTextPair> result = new ArrayList<>();
        for (int i = 0; i < TractionTypeEnum.values().length; i++) {
            TractionTypeEnum tractionTypeEnum = TractionTypeEnum.values()[i];
            result.add(new IdTextPair(tractionTypeEnum.id, tractionTypeEnum.text));
        }
        return result;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (TractionTypeEnum tractionTypeEnum : TractionTypeEnum.values()) {
            if (tractionTypeEnum.id.equals(id)) {
                return tractionTypeEnum.text;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String name) {
        for (TractionTypeEnum tractionTypeEnum : TractionTypeEnum.values()) {
            if (tractionTypeEnum.text.equals(name)) {
                return tractionTypeEnum.id;
            }
        }
        return null;
    }
}

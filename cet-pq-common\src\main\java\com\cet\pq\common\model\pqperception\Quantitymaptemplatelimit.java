package com.cet.pq.common.model.pqperception;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description Quantitymaptemplate
 * <AUTHOR>
 * @Date 2020/7/6 13:31
 */
@Data
@NoArgsConstructor
public class Quantitymaptemplatelimit {
	
	private String modelLabel = "quantitymaptemplate";
	
	private Long id;
	private String unit;
	private String templatename;
	private Integer quantitytype;
	private Integer quantitycategory;
	private Integer phasor;
	private Integer frequency;
	private Integer energytype;
	private Long dataid;
	private Double limitlow;
	private Double limithigh;
}

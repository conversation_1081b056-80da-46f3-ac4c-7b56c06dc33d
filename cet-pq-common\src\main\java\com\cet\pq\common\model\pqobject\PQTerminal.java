package com.cet.pq.common.model.pqobject;

import java.util.List;

import com.cet.pq.common.model.realtime.Line;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PQTerminal {

	private String modelLabel = "pqterminal";
	
	private Long id;
	
	private String addway;
	private String assetcode;
	private String belongcabinet;
	private String cabinetcode;
	private String cabinetname;
	private String cabinettype;
	private String changestatus;
	private String company;
	private Integer currentmeasurecount;
	private String defectcause;
	private String domainname;
	private String equipmentcode;
	private String equipmentmodel;
	private String gate;
	private String hardwareversion;
	private String inputtype;
	private Long installationdate;
	private String ip;
	private boolean islocked;
	private Boolean isupload;
	private String mac;
	private String maintenancecompanyname;
	private String maintenancecompany;
	private String maintenanceteamname;
	private String maintenanceteam;
	private String master;
	private String name;
	private String objid;
	private String odsdistrictname;
	private String odsstationname;
	private Integer port;
	private String productioncode;
	private Long productiondate;
	private String projectcode;
	private String projectname;
	private String property;
	private String ratedvoltage;
	private String softwareversion;
	private Integer status;
	private String storagecapacity;
	private String submask;
	private Integer terminalchannelnum;
	private String terminalcode;
	private Integer terminallevel;
	private Integer terminaltype;
	private String timingmode;
	private Long updatetime;
	private Long validperiod;
	private String vendor;
	private String vendorcode;
	private Long verificationdate;
	private Integer voltagemeasurecount;
	private Long planchecktime;
	private Integer checkcycle;
	private Integer monitortype;

	/**
	 * 新增用电类别
	 */
	@JsonProperty("electricitycategory")
	private String electricityCategory;

	/**
	 * 以下为国重新增字段
	 */
	@JsonProperty("equipmentname")
	public String equipmentName;

	@JsonProperty("equipmenttype")
	public Integer equipmentType;

	@JsonProperty("equipmentlocation")
	public String equipmentLocation;

	@JsonProperty("communicationprotocol")
	public String communicationProtocol;

	@JsonProperty("apn")
	public String apn;

	@JsonProperty("sim")
	public String sim;

	@JsonProperty("gatewayclientid")
	public String gatewayClientId;
	
	private List<Substation> substation_model;

	private List<Line> line_model;
}
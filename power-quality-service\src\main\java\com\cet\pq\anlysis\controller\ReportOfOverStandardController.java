package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.ReportOfOverStandard.*;
import com.cet.pq.anlysis.model.excel.ExportReportOfOverStandardParams;
import com.cet.pq.anlysis.service.ReportOfOverStandardService;
import com.cet.pq.common.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/19 9:49
 * @Description 报表管理 - 稳态指标超标统计报表
 */
@Api(value = "/pq/v1/report/overStandard", tags = "报表管理 - 稳态指标超标统计报表")
@RestController
@RequestMapping(value = "/pq/v1/report/overStandard")
public class ReportOfOverStandardController {

    @Autowired
    private ReportOfOverStandardService reportOfOverStandardService;

    @ApiOperation(value = "稳态指标超标统计查询")
    @PostMapping(value = "/statisticalReport")
    public PageResult<List<ReportOfOverStandardCount>> reportOfOverStandardCount(@RequestBody ReportOfOverStandardParams reportOfOverStandardParams) {
        return reportOfOverStandardService.reportOfOverStandardCount(reportOfOverStandardParams);
    }

    @ApiOperation(value = "导出稳态指标超标统计报表")
    @PostMapping(value = "/exportStatisticalReport")
    public void exportReportOfOverStandardCount(@RequestBody @ApiParam ExportReportOfOverStandardParams exportReportOfOverStandardParams, HttpServletResponse response) {
        reportOfOverStandardService.exportReportOfOverStandardCount(exportReportOfOverStandardParams, response);
    }

    @ApiOperation(value = "稳态指标超标统计报表按管理单位查询")
    @PostMapping(value = "/managementUnitReport")
    public PageResult<List<ReportOfOverStandardByCompany>> reportOfOverStandardCountbyCompany(@RequestBody ReportOfOverStandardParams reportOfOverStandardParams) {
        return reportOfOverStandardService.reportOfOverStandardCountByCompany(reportOfOverStandardParams);
    }

    @ApiOperation(value = "导出稳态指标超标统计报表按管理单位查询")
    @PostMapping(value = "/exportManagementUnitReport")
    public void exportReportOfOverStandardCountByCompany(@RequestBody @ApiParam ExportReportOfOverStandardParams exportReportOfOverStandardParams, HttpServletResponse response) {
        reportOfOverStandardService.exportReportOfOverStandardCountByCompany(exportReportOfOverStandardParams, response);
    }

    @ApiOperation(value = "稳态指标超标统计报表按变电站查询")
    @PostMapping(value = "/substationReport")
    public PageResult<List<ReportOfOverStandardBySubstation>> reportOfOverStandardCountbySubstation(@RequestBody ReportOfOverStandardParams reportOfOverStandardParams) {
        return reportOfOverStandardService.reportOfOverStandardCountBySubstation(reportOfOverStandardParams);
    }

    @ApiOperation(value = "导出稳态指标超标统计报表按变电站查询")
    @PostMapping(value = "/exportSubstationReport")
    public void exportReportOfOverStandardCountBySubstation(@RequestBody @ApiParam ExportReportOfOverStandardParams exportReportOfOverStandardParams, HttpServletResponse response) {
        reportOfOverStandardService.exportReportOfOverStandardCountBySubstation(exportReportOfOverStandardParams, response);
    }

    @ApiOperation(value = "稳态指标超标统计报表超标明细")
    @PostMapping(value = "/standardDetails")
    public PageResult<List<ReportOfOverStandardDetails>> reportOfOverStandardCountDetails(@RequestBody ReportOfOverStandardParams reportOfOverStandardParams) {
        return reportOfOverStandardService.reportOfOverStandardCountDetails(reportOfOverStandardParams);
    }

    @ApiOperation(value = "导出稳态指标超标统计报表超标明细")
    @PostMapping(value = "/exportStandardDetails")
    public void exportReportOfOverStandardCountDetails(@RequestBody @ApiParam ExportReportOfOverStandardParams exportReportOfOverStandardParams, HttpServletResponse response) {
        reportOfOverStandardService.exportReportOfOverStandardCountDetails(exportReportOfOverStandardParams, response);
    }

}

package com.cet.pq.common.enums;

import com.cet.pq.common.model.IdTextPair;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 风机类型
 * @date 2021/2/22 9:47
 */
public enum FanTypeEnum {
    //风机类型
    blend(1, "混合"), doublyfed(2, "双馈"), permanentmagnet(3, "永磁"), constantspeed(4, "定速");

    private Integer id;
    private String name;

    FanTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static List<IdTextPair> toList() {
        List<IdTextPair> result = new ArrayList<>();
        FanTypeEnum[] values = FanTypeEnum.values();
        for (FanTypeEnum fanType : values) {
            Integer id = fanType.getId();
            String name = fanType.getName();
            result.add(new IdTextPair(id, name));
        }
        return result;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (FanTypeEnum fanTypeEnum : FanTypeEnum.values()) {
            if (fanTypeEnum.id.equals(id)) {
                return fanTypeEnum.name;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (FanTypeEnum typeEnum : FanTypeEnum.values()) {
            if (typeEnum.getName().trim().equalsIgnoreCase(name)) {
                return typeEnum.getId();
            }
        }
        return null;
    }
}

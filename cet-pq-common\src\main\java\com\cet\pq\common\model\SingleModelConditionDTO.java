package com.cet.pq.common.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 单模条件
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SingleModelConditionDTO {
	
	private String modelLabel;
	
	private ConditionBlockCompose filter;
	
	private List<String> props;

	private List<String> include_relations;

	private List<GroupBy> groupbys;

	private Integer depth;

	public SingleModelConditionDTO depth(Integer depth){
		this.setDepth(depth);
		return this;
	}

	public SingleModelConditionDTO(String modelLabel) {
		this.modelLabel = modelLabel;
	}

	public SingleModelConditionDTO(String modelLabel, ConditionBlock... blocks) {
		this.modelLabel = modelLabel;
		List<ConditionBlock> blockList = new ArrayList<>();
		for (ConditionBlock conditionBlock : blocks) {
			blockList.add(conditionBlock);
		}
		ConditionBlockCompose conditionBlockCompose = new ConditionBlockCompose(blockList);
		this.filter = conditionBlockCompose;
	}

	public SingleModelConditionDTO(String modelLabel, List<String> props, ConditionBlock... blocks) {
		this.modelLabel = modelLabel;
		List<ConditionBlock> blockList = new ArrayList<>();
		for (ConditionBlock conditionBlock : blocks) {
			blockList.add(conditionBlock);
		}
		ConditionBlockCompose conditionBlockCompose = new ConditionBlockCompose(blockList);
		this.filter = conditionBlockCompose;
		this.props = props;
	}

	public SingleModelConditionDTO(String modelLabel, List<String> props, List<String> include_relations, ConditionBlock... blocks) {
		this.modelLabel = modelLabel;
		this.props = props;
		this.include_relations = include_relations;
		List<ConditionBlock> blockList = new ArrayList<>();
		for (ConditionBlock conditionBlock : blocks) {
			if (conditionBlock != null){
				blockList.add(conditionBlock);
			}
		}
		if (CollectionUtils.isNotEmpty(blockList)){
			this.filter = new ConditionBlockCompose(blockList);
		}
	}

	public SingleModelConditionDTO(String modelLabel, Boolean composemethod, ConditionBlock... blocks) {
		this.modelLabel = modelLabel;
		List<ConditionBlock> blockList = new ArrayList<>();
		for (ConditionBlock conditionBlock : blocks) {
			blockList.add(conditionBlock);
		}
		ConditionBlockCompose conditionBlockCompose = new ConditionBlockCompose(blockList);
		conditionBlockCompose.setComposemethod(composemethod);
		this.filter = conditionBlockCompose;
	}

	public SingleModelConditionDTO(String modelLabel, List<String> props) {
		this.modelLabel = modelLabel;
		this.props = props;
	}

	@Override
	public String toString() {
		return "SingleModelConditionDTO{" +
				"modelLabel='" + modelLabel + '\'' +
				", filter='" + filter + '\'' +
				", props=" + props +
				", include_relations=" + include_relations +
				'}';
	}
}

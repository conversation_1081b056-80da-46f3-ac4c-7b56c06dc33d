package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import com.google.common.base.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * 单位等级登记枚举
 * <AUTHOR>
 *
 */
public enum UnitLevelEnum {
	//单位等级登记枚举
	provinceLevel("provincecompany", "省级"),
	cityLevel("citycompany", "地市级"),
	countyLevel("countycompany", "县级");

	private String unitModelLabel;
	private String unitLevel;
	
	private UnitLevelEnum(String unitModelLabel, String unitLevel) {
		this.unitModelLabel = unitModelLabel;
		this.unitLevel = unitLevel;
	}

	public String getUnitLevel() {
		return unitLevel;
	}

	public String getUnitModelLabel() {
		return unitModelLabel;
	}
	public static String getUnitLevelByModelLabel(String unitModelLabel) {
		if(StringUtils.isEmpty(unitModelLabel)) {
			return StringUtils.EMPTY;
		}
		for(UnitLevelEnum unitLevel : values()) {
			if(Objects.equal(unitLevel.getUnitModelLabel(), unitModelLabel)) {
				return unitLevel.getUnitLevel();
			}
		}
		return StringUtils.EMPTY;
	}

	public static String getUnitLevelsOrThrows(String unitLevel) {
		for (UnitLevelEnum unitLevelEnum : UnitLevelEnum.values()) {
			if (unitLevelEnum.unitLevel.equals(unitLevel)) {
				return unitLevelEnum.unitLevel;
			}
		}
		throw new CommonManagerException("单位级别枚举异常");
	}

}

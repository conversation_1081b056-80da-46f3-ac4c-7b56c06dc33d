package com.cet.pq.common.model.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019年10月29日
 * 
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventCondition {

	private Integer channelId;

	private List<Integer> code1s;

	private List<Integer> code2s;

	private List<Long> deviceIds;

	private List<Integer> eventBytes;

	private List<Integer> eventClasses;

	private List<Integer> eventTypes;

	private String keyWord;

	private Integer maxRowCount;

	private Integer stationId;

	private List<EventOrder> orders;

	public EventCondition(List<Integer> eventClasses) {
		this.eventClasses = eventClasses;
	}

}

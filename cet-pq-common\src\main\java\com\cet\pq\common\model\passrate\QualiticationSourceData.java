package com.cet.pq.common.model.passrate;

import com.cet.pq.common.utils.ParseDataUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 合格率指标数据
 * @date 2020/10/27 9:40
 */
@Data
public class QualiticationSourceData {
    private String name;
    //当前合格率占比
    private Double passRateByNow;
    //当年合格率占比
    private Double passRateByYear;

    public QualiticationSourceData(String name, Double passRateByNow, Double passRateByYear) {
        this.name = name;
        this.passRateByNow = passRateByNow == null ? null : ParseDataUtil.round(passRateByNow);
        this.passRateByYear = passRateByYear == null ? null : ParseDataUtil.round(passRateByYear);
    }

}
package com.cet.pq.common.utils;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.TreeProperty;

/**
 * Map工具类
 * 
 * <AUTHOR>
 */
public class MapUtil {

	private static final Logger logger = LoggerFactory.getLogger(MapUtil.class);

	/**
	 * 将map转化为JavaBean
	 * 
	 * @param map       待转化的map
	 * @param beanClass 要转化的JAVABean
	 * @return
	 */
	public static <T> T mapToObject(Map<String, Object> map, Class<T> beanClass) {
		if (map == null) {
			return null;
		}
		T obj;
		try {
			obj = beanClass.newInstance();
			BeanUtils.populate(obj, map);
		} catch (InstantiationException | IllegalAccessException e) {
			logger.debug(CommonConstant.CONTEXT, e);
			throw new CommonManagerException("Map转化JAVA BEAN 失败", e);
		} catch (InvocationTargetException e) {
			logger.debug(CommonConstant.CONTEXT, e);
			throw new CommonManagerException("Map转化JAVA BEAN 失败", e);
		}
		return obj;
	}

	/**
	 * 将Map列表 转化为对象列表
	 * 
	 * @param mapList 要转化的mapList
	 * @param t       转化对象
	 * @return
	 */
	public static <T> List<T> mapListToBeanList(List<Map<String, Object>> mapList, Class<T> beanClass) {
		List<T> beanList = JsonTransferUtils.transferList(mapList, beanClass);
		return beanList;
	}

	public static Map<String, Object> Bean2Map(Object data) {
		Map<String, Object> map = new ConcurrentHashMap<>();
		BeanInfo beanInfo;
		try {
			beanInfo = Introspector.getBeanInfo(data.getClass(), Object.class);
			PropertyDescriptor[] pro = beanInfo.getPropertyDescriptors();
			for (PropertyDescriptor property : pro) {
				String key = property.getName();
				Method get = property.getReadMethod();
				Object value = get.invoke(data);
				if (null != key && null != value) {
					map.put(key, value);
				}
			}
			return map;
		} catch (IntrospectionException | IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
			logger.debug(CommonConstant.CONTEXT, e);
			throw new CommonManagerException("Map转化JAVA BEANList失败", e);
		}

	}

	/**
	 * 获取单个对象
	 * 
	 * @param <T>
	 * @param map
	 * @param key
	 * @return
	 */
	public static <T> T getObjectFromMap(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if (null == obj) {
			return null;
		}
		return (T) obj;
	}

	/**
	 * 获取集合单个对象
	 * 
	 * @param <T>
	 * @param map
	 * @param key
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> getListFromMap(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if (null == obj) {
			return null;
		}
		return (List<T>) obj;
	}

	/**
	 * 获取集合中的一个成员
	 * 
	 * @param <T>
	 * @param map
	 * @param key
	 * @return
	 */
	public static <T> T getOneOfListFromMap(Map<String, Object> map, String key) {
		List<T> list = getListFromMap(map, key);
		if (CollectionUtils.isNotEmpty(list)) {
			return list.get(0);
		}
		return null;
	}

	/**
	 * Map中从变电站一级向上递归找到树的属性
	 * 
	 * @param substation
	 * @return
	 */
	public static TreeProperty getTreeProPertyFromMap(Map<String, Object> substation) {
		Long substationId = null;
		String substationName = null;
		Integer substationVoltClass = null;
		Long countyCompanyId = null;
		String countyCompanyName = null;
		Long cityCompanyId = null;
		String cityCompanyName = null;
		Long provinceCompanyId = null;
		String provinceCompanyName = null;
		// 变电站
		if (null != substation) {
			substationId = ParseDataUtil.parseLong(substation.get("id"));
			substationName = ParseDataUtil.parseString(substation.get("name"));
			substationVoltClass = ParseDataUtil.parseInteger(substation.get("voltclass"));
			// 县级市
			Map<String, Object> countyCompany = null == substation.get("countycompany_model") ? null : getOneOfListFromMap(substation, "countycompany_model");
			Map<String, Object> cityCompany;
			if (null != countyCompany) {
				countyCompanyId = ParseDataUtil.parseLong(countyCompany.get("id"));
				countyCompanyName = ParseDataUtil.parseString(countyCompany.get("name"));
				cityCompany = null == countyCompany.get("citycompany_model") ? null : getOneOfListFromMap(countyCompany, "citycompany_model");
			} else {
				cityCompany = null == substation.get("citycompany_model") ? null : getOneOfListFromMap(substation, "citycompany_model");
			}
			// 地级市
			if (null != cityCompany) {
				cityCompanyId = ParseDataUtil.parseLong(cityCompany.get("id"));
				cityCompanyName = ParseDataUtil.parseString(cityCompany.get("name"));
				// 省级单位
				Map<String, Object> provinceCompany = null == cityCompany.get("provincecompany_model") ? null : getOneOfListFromMap(cityCompany, "provincecompany_model");
				if (null != provinceCompany) {
					provinceCompanyId = ParseDataUtil.parseLong(provinceCompany.get("id"));
					provinceCompanyName = ParseDataUtil.parseString(provinceCompany.get("name"));
				}
			}
		}
		TreeProperty treeProperty = new TreeProperty(provinceCompanyId, provinceCompanyName, countyCompanyId, countyCompanyName, cityCompanyId, cityCompanyName, substationId, substationName,
				substationVoltClass);
		return treeProperty;
	}

}
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="68d2018b-7080-4b0a-8275-51081f11c250" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.lingma/rules/backend-coding.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.lingma/rules/project_rule.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/power-quality-starter/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/power-quality-starter/src/main/resources/application.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:/dev_tools/apache-maven-3.5.0" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\dev_tools\apache-maven-3.5.0\conf\setting-sz.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zMy6ckbHPUEz4eTabMhh9k6FFs" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SONARLINT_PRECOMMIT_ANALYSIS&quot;: &quot;true&quot;,
    &quot;Spring Boot.PowerQualityApplication.executor&quot;: &quot;Debug&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;785cf082&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="PowerQualityApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="power-quality-starter" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cet.electric.PowerQualityApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="68d2018b-7080-4b0a-8275-51081f11c250" name="Changes" comment="" />
      <created>1751555063896</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751555063896</updated>
      <workItem from="1751555065170" duration="80027000" />
      <workItem from="1752060228793" duration="15497000" />
      <workItem from="1752223479540" duration="17137000" />
      <workItem from="1752749470236" duration="34319000" />
      <workItem from="1753249893293" duration="34620000" />
      <workItem from="1753351225138" duration="13486000" />
      <workItem from="1753422862699" duration="5332000" />
      <workItem from="1753876947655" duration="69620000" />
      <workItem from="1754367469261" duration="131000" />
      <workItem from="1754367637203" duration="830000" />
      <workItem from="1754371635802" duration="4048000" />
      <workItem from="1754375756596" duration="14364000" />
      <workItem from="1754392366919" duration="747000" />
      <workItem from="1754393157048" duration="1440000" />
      <workItem from="1754394835380" duration="187000" />
      <workItem from="1754395034726" duration="53000" />
      <workItem from="1754395110080" duration="330000" />
      <workItem from="1754395455158" duration="65000" />
      <workItem from="1754395534452" duration="10357000" />
    </task>
    <task id="LOCAL-00001" summary="power-quality-service[2.1.126]支持组件引用问题解决+治理辅助决策消缺">
      <option name="closed" value="true" />
      <created>1751614497697</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751614497697</updated>
    </task>
    <task id="LOCAL-00002" summary="power-quality-service[2.1.126]提交2">
      <option name="closed" value="true" />
      <created>1751619239529</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751619239529</updated>
    </task>
    <task id="LOCAL-00003" summary="power-quality-service[2.1.129]广州技改反馈问题处理">
      <option name="closed" value="true" />
      <created>1751943993946</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751943993946</updated>
    </task>
    <task id="LOCAL-00004" summary="power-quality-service[2.1.130]国重谐波溯源算法调整">
      <option name="closed" value="true" />
      <created>1751967807846</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751967807846</updated>
    </task>
    <task id="LOCAL-00005" summary="power-quality-service[2.1.130]提交1">
      <option name="closed" value="true" />
      <created>1751969297668</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751969297668</updated>
    </task>
    <task id="LOCAL-00006" summary="power-quality-service[2.1.130]提交2">
      <option name="closed" value="true" />
      <created>1751969800595</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751969800595</updated>
    </task>
    <task id="LOCAL-00007" summary="power-quality-service[2.1.130]提交3">
      <option name="closed" value="true" />
      <created>1751970280517</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751970280517</updated>
    </task>
    <task id="LOCAL-00008" summary="power-quality-service[2.1.131]国重暂降溯源修改">
      <option name="closed" value="true" />
      <created>1752032176920</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752032176920</updated>
    </task>
    <task id="LOCAL-00009" summary="power-quality-service[2.1.132]国重暂降溯源定制修改">
      <option name="closed" value="true" />
      <created>1752053904339</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752053904339</updated>
    </task>
    <task id="LOCAL-00010" summary="power-quality-service[2.1.133]谐波溯源事件查询修改">
      <option name="closed" value="true" />
      <created>1752060649228</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752060649228</updated>
    </task>
    <task id="LOCAL-00011" summary="power-quality-service[2.1.131]国重暂降溯源修改">
      <option name="closed" value="true" />
      <created>1752063895779</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752063895779</updated>
    </task>
    <task id="LOCAL-00012" summary="power-quality-service[2.1.134]趋势曲线优化">
      <option name="closed" value="true" />
      <created>1752119284642</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752119284642</updated>
    </task>
    <task id="LOCAL-00013" summary="power-quality-service[2.1.134]修改2">
      <option name="closed" value="true" />
      <created>1752121310733</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752121310733</updated>
    </task>
    <task id="LOCAL-00014" summary="power-quality-service[2.1.136]省级单位创建判断唯一">
      <option name="closed" value="true" />
      <created>1752831746936</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752831746936</updated>
    </task>
    <task id="LOCAL-00015" summary="power-quality-service[2.1.136]省级单位创建判断唯一">
      <option name="closed" value="true" />
      <created>1752836262998</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752836262998</updated>
    </task>
    <task id="LOCAL-00016" summary="power-quality-service[2.1.137]对比分析+超标分析导出消缺">
      <option name="closed" value="true" />
      <created>1753149215137</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753149215137</updated>
    </task>
    <task id="LOCAL-00017" summary="power-quality-service[2.1.137]fix2">
      <option name="closed" value="true" />
      <created>1753151560943</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753151560943</updated>
    </task>
    <task id="LOCAL-00018" summary="power-quality-service[2.1.137]谐波溯源最小数据量修改为8">
      <option name="closed" value="true" />
      <created>1753172593630</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753172593630</updated>
    </task>
    <task id="LOCAL-00019" summary="power-quality-service[2.1.137]修改谐波溯源最新数据量为8">
      <option name="closed" value="true" />
      <created>1753173163184</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753173163184</updated>
    </task>
    <task id="LOCAL-00020" summary="power-quality-service[2.1.137]fix2">
      <option name="closed" value="true" />
      <created>1753175123301</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753175123301</updated>
    </task>
    <task id="LOCAL-00021" summary="power-quality-service[2.1.140]国重716反馈谐波超标事件及排序需求处理">
      <option name="closed" value="true" />
      <created>1753342812415</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1753342812415</updated>
    </task>
    <task id="LOCAL-00022" summary="power-quality-service[2.1.140]fix2">
      <option name="closed" value="true" />
      <created>1753343851357</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1753343851357</updated>
    </task>
    <task id="LOCAL-00023" summary="power-quality-service[2.1.141]趋势曲线分批查询补点逻辑修改">
      <option name="closed" value="true" />
      <created>1753410915013</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1753410915013</updated>
    </task>
    <task id="LOCAL-00024" summary="power-quality-service[2.1.142]趋势曲线取消分批查询">
      <option name="closed" value="true" />
      <created>1753428176134</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753428176134</updated>
    </task>
    <option name="localTasksCounter" value="25" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="power-quality-service[2.1.126]支持组件引用问题解决+治理辅助决策消缺" />
    <MESSAGE value="power-quality-service[2.1.129]广州技改反馈问题处理" />
    <MESSAGE value="power-quality-service[2.1.126]提交2" />
    <MESSAGE value="power-quality-service[2.1.130]国重谐波溯源算法调整" />
    <MESSAGE value="power-quality-service[2.1.130]提交1" />
    <MESSAGE value="power-quality-service[2.1.130]提交3" />
    <MESSAGE value="power-quality-service[2.1.130]提交2" />
    <MESSAGE value="power-quality-service[2.1.132]国重暂降溯源定制修改" />
    <MESSAGE value="power-quality-service[2.1.133]谐波溯源事件查询修改" />
    <MESSAGE value="power-quality-service[2.1.134]趋势曲线优化" />
    <MESSAGE value="power-quality-service[2.1.134]修改2" />
    <MESSAGE value="power-quality-service[2.1.131]国重暂降溯源修改" />
    <MESSAGE value="power-quality-service[2.1.137]对比分析+超标分析导出消缺" />
    <MESSAGE value="power-quality-service[2.1.137]谐波溯源最小数据量修改为8" />
    <MESSAGE value="power-quality-service[2.1.137]修改谐波溯源最新数据量为8" />
    <MESSAGE value="power-quality-service[2.1.137]fix2" />
    <MESSAGE value="power-quality-service[2.1.140]国重716反馈谐波超标事件及排序需求处理" />
    <MESSAGE value="power-quality-service[2.1.140]fix2" />
    <MESSAGE value="power-quality-service[2.1.141]趋势曲线分批查询补点逻辑修改" />
    <MESSAGE value="power-quality-service[2.1.136]省级单位创建判断唯一" />
    <MESSAGE value="power-quality-service[2.1.142]趋势曲线取消分批查询" />
    <option name="LAST_COMMIT_MESSAGE" value="power-quality-service[2.1.142]趋势曲线取消分批查询" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/GovernanceDecisionServiceImpl.java</url>
          <line>396</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/GovernanceDecisionServiceImpl.java</url>
          <line>467</line>
          <option name="timeStamp" value="10" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/GovernanceDecisionServiceImpl.java</url>
          <line>474</line>
          <option name="timeStamp" value="11" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/inventoryservice/service/impl/PqTerminalCountServiceImpl.java</url>
          <line>68</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>1834</line>
          <option name="timeStamp" value="28" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>1559</line>
          <option name="timeStamp" value="29" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>3301</line>
          <option name="timeStamp" value="36" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/cet-pq-common/src/main/java/com/cet/pq/common/model/PageResult.java</url>
          <line>188</line>
          <option name="timeStamp" value="37" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>2467</line>
          <option name="timeStamp" value="39" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>2471</line>
          <option name="timeStamp" value="40" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>2452</line>
          <option name="timeStamp" value="41" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>870</line>
          <option name="timeStamp" value="49" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>849</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>798</line>
          <option name="timeStamp" value="51" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>845</line>
          <option name="timeStamp" value="52" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>1287</line>
          <option name="timeStamp" value="53" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/inventoryservice/service/impl/TerminalLineServiceImpl.java</url>
          <line>1592</line>
          <option name="timeStamp" value="54" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/inventoryservice/service/impl/TerminalLineServiceImpl.java</url>
          <line>1752</line>
          <properties>
            <option name="lambda-ordinal" value="-1" />
          </properties>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/inventoryservice/service/impl/TerminalLineServiceImpl.java</url>
          <line>1758</line>
          <option name="timeStamp" value="56" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/pq-advance-service/src/main/java/com/cet/pq/pqadvanceservice/service/impl/DisturbanceTraceServiceImpl.java</url>
          <line>4116</line>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/anlysis/service/impl/PowerQualityEvaluationIndexServiceImpl.java</url>
          <line>820</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/anlysis/service/impl/PowerQualityEvaluationIndexServiceImpl.java</url>
          <line>817</line>
          <option name="timeStamp" value="67" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/inventoryservice/service/impl/MonitorServiceImpl.java</url>
          <line>156</line>
          <option name="timeStamp" value="74" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>223</line>
          <option name="timeStamp" value="76" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>229</line>
          <option name="timeStamp" value="77" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>1111</line>
          <option name="timeStamp" value="78" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>1102</line>
          <option name="timeStamp" value="79" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>915</line>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>825</line>
          <option name="timeStamp" value="101" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>907</line>
          <option name="timeStamp" value="104" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/RealDataAnalysisServiceImpl.java</url>
          <line>866</line>
          <option name="timeStamp" value="105" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java</url>
          <line>588</line>
          <option name="timeStamp" value="111" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java</url>
          <line>591</line>
          <option name="timeStamp" value="114" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java</url>
          <line>579</line>
          <option name="timeStamp" value="117" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java</url>
          <line>599</line>
          <option name="timeStamp" value="119" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java</url>
          <line>798</line>
          <option name="timeStamp" value="130" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java</url>
          <line>733</line>
          <option name="timeStamp" value="131" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/power-quality-service/src/main/java/com/cet/pq/pqservice/service/impl/PqEventServiceImpl.java</url>
          <line>532</line>
          <option name="timeStamp" value="132" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.cet.pq.inventoryservice.model.vo.LineVO" memberName="terminalcode" />
        <PinnedItemInfo parentTag="com.cet.pq.common.model.Result" memberName="code" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
</project>
package com.cet.pq.common.utils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

/**
 * Base64工具类
 * 
 * <AUTHOR>
 * @date 2020-11-03
 */
public class Base64Util {

	/**
	 * 文件转化为Base64字符
	 * @return
	 */
	public static String file2Base64(File inFile) {
		// 将文件转化为字节码
		byte[] bytes = file2Byte(inFile);
		if (bytes == null) {
			return null;
		}
		// base64,将字节码转化为base64的字符串
		String result = Base64.getEncoder().encodeToString(bytes);
		return result;
	}

	/**
	 * 文件转化为字节流
	 * 
	 * @param inFile
	 * @return
	 */
	public static byte[] file2Byte(File file) {
		InputStream in = null;
		try {
			in = new FileInputStream(file);
			// 文件长度
			int len = in.available();
			// 定义数组
			byte[] bytes = new byte[len];
			// 读取到数组里面
			in.read(bytes);
			return bytes;
		} catch (Exception e) {
			return null;
		} finally {
			try {
				if (in != null) {
					in.close();
				}
			} catch (Exception e) {
			}
		}
	}

	/**
	 * 将字符串转化为文件
	 * 
	 * @param strBase64 base64 编码的文件
	 * @param outFile   输出的目标文件地址
	 * @return
	 */
	public static boolean base64ToFile(String strBase64, File outFile) {
		try {
			// 解码，然后将字节转换为文件
			byte[] bytes = Base64.getDecoder().decode(strBase64);
			return byte2File(bytes, outFile);
		} catch (Exception ioe) {
			return false;
		}
	}

	/**
	 * 将字节码转化为文件
	 * 
	 * @param bytes
	 * @param file
	 */
	public static boolean byte2File(byte[] bytes,File file) {
		FileOutputStream out = null;
		try {
			// 转化为输入流
			ByteArrayInputStream in = new ByteArrayInputStream(bytes);
			// 写出文件
			byte[] buffer = new byte[1024];
			out = new FileOutputStream(file);
			// 写文件
			int len = 0;
			while ((len = in.read(buffer)) != -1) {
				out.write(buffer, 0, len); // 文件写操作
			}
			return true;
		} catch (Exception e) {
		} finally {
			try {
				if (out != null) {
					out.close();
				}
			} catch (IOException e) {
			}
		}
		return false;
	}
}

package com.cet.pq.common.model.wave;


import lombok.Data;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @ClassName ：LibSerializeHelper
 * @date ：Created in 2021/3/29 15:59
 * @description：
 */
@Data
public class LibSerializeHelper {

    private static byte[] mShortAr = new byte[2];
    private static byte[] mIntAr = new byte[4];
    private static byte[] mLongAr = new byte[8];

    private LibSerializeHelper() {
        throw new IllegalStateException("Utility class");
    }

    public static byte[] toLH(short n) {
        return new byte[]{(byte)(n & 255), (byte)(n >> 8 & 255)};
    }

    public static byte[] toHH(short n) {
        return new byte[]{(byte)(n >> 8 & 255), (byte)(n & 255)};
    }

    public static byte[] toLH(int n) {
        return new byte[]{(byte)(n & 255), (byte)(n >> 8 & 255), (byte)(n >> 16 & 255), (byte)(n >> 24 & 255)};
    }

    public static byte[] toLH(long n) {
        return new byte[]{(byte)((int)(n & 255L)), (byte)((int)(n >> 8 & 255L)), (byte)((int)(n >> 16 & 255L)), (byte)((int)(n >> 24 & 255L)), (byte)((int)(n >> 32 & 255L)), (byte)((int)(n >> 40 & 255L)), (byte)((int)(n >> 48 & 255L)), (byte)((int)(n >> 56 & 255L))};
    }

    public static byte[] toHH(int n) {
        return new byte[]{(byte)(n >> 24 & 255), (byte)(n >> 16 & 255), (byte)(n >> 8 & 255), (byte)(n & 255)};
    }

    public static byte[] toHH(long n) {
        return new byte[]{(byte)((int)(n >> 56 & 255L)), (byte)((int)(n >> 48 & 255L)), (byte)((int)(n >> 40 & 255L)), (byte)((int)(n >> 32 & 255L)), (byte)((int)(n >> 24 & 255L)), (byte)((int)(n >> 16 & 255L)), (byte)((int)(n >> 8 & 255L)), (byte)((int)(n & 255L))};
    }

    public static int lBytesToInt(byte[] b) {
        int s = 0;

        for(int i = 0; i < 3; ++i) {
            if (b[3 - i] >= 0) {
                s += b[3 - i];
            } else {
                s = s + 256 + b[3 - i];
            }

            s *= 256;
        }

        if (b[0] >= 0) {
            s += b[0];
        } else {
            s = s + 256 + b[0];
        }

        return s;
    }

    public static byte[] longReverseOrder(long l) {
        byte[] bytes;
        ByteBuffer byteBuffer = ByteBuffer.allocate(8);
        byteBuffer.putLong(l);
        bytes = bytesReverseOrder(byteBuffer.array());
        return bytes;
    }

    public static byte[] bytesReverseOrder(byte[] b) {
        int length = b.length;
        byte[] result = new byte[length];

        for(int i = 0; i < length; ++i) {
            result[length - i - 1] = b[i];
        }

        return result;
    }

    public static short hBytesToShort(byte[] b) {
        int s = 0;
        if (b[0] >= 0) {
            s = s + b[0];
        } else {
            s = s + 256 + b[0];
        }

        s *= 256;
        if (b[1] >= 0) {
            s += b[1];
        } else {
            s = s + 256 + b[1];
        }

        return  (short)s;
    }

    public static short lBytesToShort(byte[] b) {
        int s = 0;
        if (b[1] >= 0) {
            s = s + b[1];
        } else {
            s = s + 256 + b[1];
        }

        s *= 256;
        if (b[0] >= 0) {
            s += b[0];
        } else {
            s = s + 256 + b[0];
        }

        return  (short)s;
    }

    public static synchronized byte readByte(DataInputStream din) throws IOException {
        return din.readByte();
    }


    public static synchronized char[] readCharArray(int length, DataInputStream din) throws IOException {
        if (length > 0 && length < 1048576) {
            char[] result = new char[length];

            for(int i = 0; i < length; ++i) {
                result[i] = din.readChar();
            }

            return result;
        } else {
            return new char[0];
        }
    }

    public static synchronized int readInt() {
        clearIntAr();
        mIntAr = arrayInversion(mIntAr);
        return reverseInt(mIntAr);
    }


    public static synchronized long readUnsignedIntOrLong(DataInputStream din) throws IOException {
        clearIntAr();
        clearLongAr();

        readFully(din, mIntAr);
        int j = 3;

        for(int i = 4; i < 8; ++i) {
            mLongAr[i] = mIntAr[j--];
        }
        return reverseLong(mLongAr);
    }

    // 工具方法：安全读满buffer
    private static void readFully(DataInputStream din, byte[] buffer) throws IOException {
        int totalRead = 0;
        int bytesRead;
        while (totalRead < buffer.length && (bytesRead = din.read(buffer, totalRead, buffer.length - totalRead)) != -1) {
            totalRead += bytesRead;
        }
        if (totalRead != buffer.length) {
            throw new IOException("Failed to read expected number of bytes: expected " + buffer.length + ", got " + totalRead);
        }
    }

    public static synchronized byte[] arrayInversion(byte[] buf) {
        int length = buf.length;
        byte[] result = new byte[length];

        for(int i = 0; i < length; ++i) {
            result[i] = buf[length - 1 - i];
        }

        return result;
    }

    private static synchronized void clearIntAr() {
        mIntAr[0] = 0;
        mIntAr[1] = 0;
        mIntAr[2] = 0;
        mIntAr[3] = 0;
    }

    private static synchronized void clearLongAr() {
        for(int i = 0; i < 8; ++i) {
            mLongAr[i] = 0;
        }

    }

    public static int reverseInt(byte[] b) {
        return reverseInt(b, ByteOrder.BIG_ENDIAN);
    }

    public static int reverseInt(byte[] b, ByteOrder order) {
        legal(b, 4);
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        int value = 0;
        int i;
        if (ByteOrder.BIG_ENDIAN.equals(mOrder)) {
            for(i = 0; i < 4; ++i) {
                value |= (b[i] & 255) << (3 - i) * 8;
            }
        } else {
            for(i = 0; i < 4; ++i) {
                value |= (b[i] & 255) << i * 8;
            }
        }

        return value;
    }

    public static short reverseShort(byte[] b) {
        return reverseShort(b, ByteOrder.BIG_ENDIAN);
    }

    public static short reverseShort(byte[] b, ByteOrder order) {
        legal(b, 2);
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        short value = 0;
        if (ByteOrder.BIG_ENDIAN.equals(mOrder)) {
            value = (short)(value | (b[0] & 255) << 8);
            value = (short)(value | b[1] & 255);
        } else {
            value = (short)(value | b[0] & 255);
            value = (short)(value | (b[1] & 255) << 8);
        }

        return value;
    }

    public static long reverseLong(byte[] b) {
        return reverseLong(b, ByteOrder.BIG_ENDIAN);
    }

    public static long reverseLong(byte[] b, ByteOrder order) {
        legal(b, 8);
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        long value = 0L;
        int i;
        if (ByteOrder.BIG_ENDIAN.equals(mOrder)) {
            for(i = 0; i < 8; ++i) {
                value |= (long)(b[i] & 255) << (7 - i) * 8;
            }
        } else {
            for(i = 0; i < 8; ++i) {
                value |= (long)(b[i] & 255) << i * 8;
            }
        }

        return value;
    }

    public static byte[] convert(int number) {
        return convert(number, ByteOrder.BIG_ENDIAN);
    }

    public static byte[] convert(int number, ByteOrder order) {
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        return ByteOrder.BIG_ENDIAN.equals(mOrder) ? new byte[]{(byte)(number >> 24), (byte)(number >> 16 & 255), (byte)(number >> 8 & 255), (byte)(number & 255)} : new byte[]{(byte)(number & 255), (byte)(number >> 8 & 255), (byte)(number >> 16 & 255), (byte)(number >> 24)};
    }

    public static byte[] convert(short number) {
        return convert(number, ByteOrder.BIG_ENDIAN);
    }

    public static byte[] convert(short number, ByteOrder order) {
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        return ByteOrder.BIG_ENDIAN.equals(mOrder) ? new byte[]{(byte)(number >> 8), (byte)(number & 255)} : new byte[]{(byte)(number & 255), (byte)(number >> 8)};
    }

    public static byte[] convert(long number, ByteOrder order) {
        ByteOrder mOrder = order == null ? ByteOrder.BIG_ENDIAN : order;
        return ByteOrder.BIG_ENDIAN.equals(mOrder) ? new byte[]{(byte)((int)(number >> 56)), (byte)((int)(number >> 48 & 255L)), (byte)((int)(number >> 40 & 255L)), (byte)((int)(number >> 32 & 255L)), (byte)((int)(number >> 24 & 255L)), (byte)((int)(number >> 16 & 255L)), (byte)((int)(number >> 8 & 255L)), (byte)((int)(number & 255L))} : new byte[]{(byte)((int)(number & 255L)), (byte)((int)(number >> 8 & 255L)), (byte)((int)(number >> 16 & 255L)), (byte)((int)(number >> 24 & 255L)), (byte)((int)(number >> 32 & 255L)), (byte)((int)(number >> 40 & 255L)), (byte)((int)(number >> 48 & 255L)), (byte)((int)(number >> 56 & 255L))};
    }

    private static void legal(byte[] b, int length) {
        if (b == null || b.length != length) {
            throw new IllegalArgumentException("the length of array should be " + length);
        }
    }

    public static int writeByte(byte b, OutputStream os) throws IOException {
        os.write(b);
        return 1;
    }

    public static int writeShort(short i, OutputStream os) throws IOException {
        os.write(toLH(i));
        return 2;
    }

    public static int writeInt(int i, OutputStream os) throws IOException {
        os.write(toLH(i));
        return 4;
    }

    public static int writeLong(long i, OutputStream os) throws IOException {
        os.write(toLH(i));
        return 8;
    }

    public static int writeBuffer(byte[] buf, OutputStream os) throws IOException {
        int length = writeInt(buf.length, os);
        os.write(buf);
        length += buf.length;
        return length;
    }

    public static long writeString(String msg, OutputStream os) throws IOException {
        return msg == null ? (long)writeInt(0, os) : (long)writeBuffer(msg.getBytes(StandardCharsets.UTF_8), os);
    }

    public static long writeString(String msg, OutputStream os, String coding) throws IOException {
        return msg == null ? (long)writeInt(0, os) : (long)writeBuffer(msg.getBytes(coding), os);
    }

}

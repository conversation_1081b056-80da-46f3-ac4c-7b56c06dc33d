package com.cet.pq.common.model.pecnode;

import lombok.Data;

import java.util.List;

/**
 * 新增设备节点请求参数
 */
@Data
public class DeviceInfoParam {

    private int channelId;
    private int circuitId;
    private ComParam comParam;
    private int communicationId;
    private CommunicationParam communicationParam;
    private List<CustomInfParamList> customInfParamList;
    private DeviceAccessParam deviceAccessParam;
    private String nodeName;
    private String portType;
    private Long deviceId;

//    private List<> controlPointList;
    private String deviceName;
    private Boolean enableVirtualDataLog;
//    private List<> dataInfoList;
    private Long stationId;
}

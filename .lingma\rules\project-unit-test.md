JUnit 4 和 Mockito 单元测试规范（更新版）


技术框架
**核心框架**
JUnit 4：主要测试框架
Mockito：用于创建和配置模拟对象
JaCoCo：测试覆盖率工具

**重点关注**
测试覆盖率
条件分支覆盖
异常处理路径
边界条件
如需要写单元测试过多，请分批生成，每次生成完等用户确认完毕再继续生成。

**要求**
业务代码整体代码行覆盖率在90%以上，分支覆盖率在60%以上，业务方法的覆盖率为100%

测试规范
1. 测试类结构
```
public class ServiceNameImplTest {
    @Mock
    private DependencyService dependencyService;
    
    @InjectMocks
    private ServiceNameImpl service;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
	}
```

2. 命名规范
测试类名：被测类名 + Test 后缀（例如：FileManagementServiceImplTest）
测试方法名：使用 testMethodName_Scenario_ExpectedBehavior 格式
例如：testSaveFile_WithValidInput_ReturnSuccess
或：testSaveFile_WithInvalidPath_ThrowsException

3. 测试数据初始化（重要）
- 3.1. 在每个测试方法内部初始化测试数据，而不是在公共方法中
```
@Test
public void testProcessData_WithValidInput_ReturnsProcessedData() {
    // 在每个测试方法内部初始化测试数据，而不是在公共方法中
    DataInput input = new DataInput();
    input.setId(1L);
    input.setName("测试数据");
    input.setValue("test-value");
    
    // 测试特定的模拟数据也在方法内定义
    when(dependencyService.fetchData(eq(1L))).thenReturn(someSpecificData);
```

- 3.2 Arrange - 在方法内准备测试数据和预期结果
```
@Test
public void testProcessData_WithValidInput_ReturnsProcessedData() {
    // Arrange - 在方法内准备测试数据和预期结果
    DataInput input = new DataInput();
    input.setId(1L);
    input.setName("测试数据");
    
    DataResult expected = new DataResult("processed");
    when(dependencyService.fetchData(eq(1L))).thenReturn(someData);
```

4. 异常测试
```
@Test(expected = IllegalArgumentException.class)
public void testProcessData_WithNullInput_ThrowsIllegalArgumentException() {
    // 直接在方法中定义测试场景
    DataInput nullInput = null;
    
    service.processData(nullInput);
}
```

5. Mockito 使用规范
使用when对设置模拟对象的行为。
使用Assert API对返回结果进行判断。
```
@Test
public void testWithMockito() {
    // 在测试方法内定义模拟行为
    when(mockObject.method(any())).thenReturn(value);

    // 验证调用
    assertEquals();
```



 7. 使用MockedStatic对静态类情况
**（谨慎使用）**：并非所有静态方法都需要mock。
使用try-with-resource模拟静态对象
```
@Test
public void testMethod_Wrong() throws IOException {
    try (MockedStatic<SomeUtils> mockedUtils = Mockito.mockStatic(ModelServiceUtils.class)) {
        mockedUtils.when(() -> SomeUtils.someMethod()).thenReturn("mocked");
        // 执行测试
        String result = serviceUnderTest.doSomething();
        // 这些 assert 错误会被 try-catch 捕获，导致测试失败信息不清晰
        assertEquals("Expected value", result);
        assertTrue("Should be true", someCondition);
    }
}
```

// 带异常处理的正确做法
```
@Test(expected = SomeException.class)
public void testMethod_WithException_Correct() throws IOException {
    MockedStatic<SomeUtils> mockedUtils = null;
    try {
        mockedUtils = mockStatic(SomeUtils.class);
        mockedUtils.when(() -> SomeUtils.someMethod()).thenThrow(new RuntimeException("Mock exception"));
        // 执行测试 - 期望抛出异常
        serviceUnderTest.methodThatShouldThrow();
        // 如果没有抛出异常，这个 assert 会正确执行
        fail("Should have thrown an exception");
    } finally {
        if (mockedUtils != null) {
            mockedUtils.close();
        }
    }
}
```


 8. 监视对象（Spy）使用准则
当真实对象存在时，但需要mock部分方法桩方法。
```
示例：
// Mock getBasePath 方法返回隔离的测试路径
FileManagementServiceImpl spyService = spy(fileManagementService);
doReturn(projectPath.toString()).when(spyService).getBasePath(testTenantId, testProjectId);
// 执行测试 - 移动文件到目标目录
FileNode result = spyService.moveFile(testTenantId, testProjectId, sourceFileNameWithUuid, targetDirName);
```


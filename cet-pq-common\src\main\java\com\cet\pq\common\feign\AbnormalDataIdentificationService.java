package com.cet.pq.common.feign;

import com.cet.pq.common.model.AnomalCheckResult;
import com.cet.pq.common.model.AnomalDeatilResult;
import com.cet.pq.common.model.PageResult;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.query.*;
import com.cet.pq.common.model.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/06/27 9:19
 */
@FeignClient(value = "pq-abnormaldataidentification-datastore", path = "/abnormaldataidentification")
public interface AbnormalDataIdentificationService {

    /**
     *电压互感器类型判断
     * @param voltageClass 电压等级
     * @return 0: 电磁式电压互感器初始化界面, 1: 电容式电压互感器初始化界面
     */
    @GetMapping(value = "/voltageTransFormerType/{voltageClass}")
    Result<Integer> voltageTransFormerType(@PathVariable("voltageClass") Integer voltageClass);

    /**
     * 得到电磁式电压互感器界面状态的值，判断测量零序误差识别按钮是否可点击
     * @param evtVoltageTransformerQuery 电磁式电压互感器界面查询参数
     * @return 电磁式电压互感器界面状态的值，测量零序误差识别按钮是否可点击
     */
    @PostMapping(value = "/evtVoltageTransFormerInit")
    Result<VoltageTransformerInitVO> evtVoltageTransFormerInit(@RequestBody EvtVoltageTransformerQuery evtVoltageTransformerQuery);

    /**
     * 测量零序误差识别界面初始化
     * @param evtErrorCorrectionQuery 测量零序误差识别界面查询参数
     * @return 测量零序误差识别界面所需参数值
     */
    @PostMapping(value = "/evtErrorCorrectionDistinguish")
    Result<EvtErrorCorrectionVO> evtErrorCorrectionDistinguish(@RequestBody EvtErrorCorrectionQuery evtErrorCorrectionQuery);

    /**
     * 得到电容式电压互感器界面状态的值，判断谐波测量失真识别按钮是否可点击
     * @param cvtVoltageTransformerQuery 电容式电压互感器界面查询参数
     * @return 电容式电压互感器界面状态的值，谐波测量失真识别按钮是否可点击
     */
    @PostMapping(value = "/cvtVoltageTransFormerInit")
    Result<VoltageTransformerInitVO> cvtVoltageTransFormerInit(@RequestBody CvtVoltageTransformerQuery cvtVoltageTransformerQuery);

    /**
     * 谐波测量失真识别界面初始化
     * @param cvtVoltageTransformerQuery 电容式电压互感器界面查询参数
     * @return 谐波测量失真识别界面初始化所需参数值
     */
    @PostMapping(value = "/cvtErrorCorrectionInit")
    Result<CvtErrorCorrectionVO> cvtErrorCorrectionInit(@RequestBody CvtVoltageTransformerQuery cvtVoltageTransformerQuery);

    /**
     * 异常接线识别及校正界面初始化
     * @param commonQuery 通用查询参数
     * @return 异常接线识别及校正界面模型集合
     */
    @PostMapping(value = "/abnormalWiredIdentificationInit")
    PageResult<List<AbnormalWiredModelVO>> abnormalWiredIdentificationInit(@RequestBody CommonQuery commonQuery);

    /**
     * 区域监测装置查询界面初始化
     * @param monitorAreaDeviceQuery 区域监测装置查询界面参数
     * @return 区域监测装置查询界面模型
     */
    @PostMapping(value = "/monitorAreaDeviceInit")
    Result<Map<String, Object>> monitorAreaDeviceInit(@RequestBody MonitorAreaDeviceQuery monitorAreaDeviceQuery);

    /**
     * 修改校正状态字段的值
     * @param commonQuery 通用查询参数
     * @return 修改后的异常接线实体的值
     */
    @PostMapping(value = "/updateCorrectiveStatus")
    Result<AbnormalWiredModelVO> updateCorrectiveStatus(@RequestBody CommonQuery commonQuery);

    /**
     * 时钟异常识别校正界面初始化
     * @param commonQuery 通用查询参数
     * @return  时钟异常识别校正界面模型集合
     */
    @PostMapping(value = "/clockAbnormalIdentificationInit")
    PageResult<List<ClockAbnormalIdentificationVO>> clockAbnormalIdentificationInit(@RequestBody CommonQuery commonQuery);

    /**
     * 单台监测装置查询界面初始化
     * @param clockMonitorDeviceQuery 单台监测装置查询参数
     * @return 单台监测装置查询界面所需参数值
     */
    @PostMapping(value = "/clockMonitorDeviceInit")
    Result<List<ClockMonitorDeviceVO>> clockMonitorDeviceInit(@RequestBody ClockMonitorDeviceQuery clockMonitorDeviceQuery);

    /**
     * 统计信息异常数据识别界面初始化
     * @param commonQuery 通用查询参数
     * @return 异常数据核查对象的集合
     */
    @PostMapping(value = "/statisticsAbnormalDataInit")
    PageResult<List<AnomalCheckResult>> statisticsAbnormalDataInit(@RequestBody CommonQuery commonQuery);

    /**
     * 异常数据详情界面初始化
     * @param abnormalDataDetailQuery 异常数据详情界面查询参数
     * @return 异常数据的详细信息
     */
    @PostMapping(value = "/abnormalDataDetail")
    PageResult<List<AnomalDeatilResult>> abnormalDataDetail(@RequestBody AbnormalDataDetailQuery abnormalDataDetailQuery);

    /**
     * 长时闪变异常数据详情界面初始化
     * @param abnormalDataDetailQuery 异常数据详情界面查询参数
     * @return 异常数据的详细信息
     */
    @PostMapping(value = "/longTermFlickerAbnormalDataDetail")
    Result<LongTermFlickerVO> longTermFlickerAbnormalDataDetail(@RequestBody AbnormalDataDetailQuery abnormalDataDetailQuery);

    /**
     * 数据质量整体评估界面初始化
     * @param commonQuery 通用查询参数
     * @return 数据质量整体评估界面模型集合
     */
    @PostMapping(value = "/overallDataQualityAssessmentInit")
    PageResult<List<DataQualityAssessmentVO>> overallDataQualityAssessment(@RequestBody CommonQuery commonQuery);

    /**
     * 单台监测装置数据质量评估界面初始化
     * @param commonQuery 通用查询参数
     * @return 单台监测装置数据质量评估界面所需参数集合
     */
    @PostMapping(value = "/deviceQualityAssessmentInit")
    Result<List<DeviceQualityAssessmentVO>> deviceQualityAssessmentInit(@RequestBody CommonQuery commonQuery);

    /**
     * 装置缺失数据详情界面初始化
     * @param deviceMissingDataQuery 装置缺失数据界面参数
     * @return 装置缺失数据详情界面所需参数
     */
    @PostMapping(value = "/deviceMissingDataInit")
    Result<DeviceMissingDataVO> deviceMissingDataInit(@RequestBody DeviceMissingDataQuery deviceMissingDataQuery);





}

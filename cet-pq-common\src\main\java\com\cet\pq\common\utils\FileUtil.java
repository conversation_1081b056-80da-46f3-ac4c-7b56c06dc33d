package com.cet.pq.common.utils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

import com.cet.pq.common.constant.ExcelConstant;
import com.cet.pq.common.model.excel.ExportParameter;
import com.cet.pq.common.model.excel.InsertData;
import com.cet.pq.common.model.excel.MergeData;
import com.cet.pq.common.model.excel.Sheets;
import com.cet.pq.common.model.offlinetest.DataReport;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.wave.Wave;

/**
 * 文件上传下载工具
 *
 * <AUTHOR>
 */
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    //离线数据Excel上传下载位置
    public static final String OFFLINE_TEST_EXCEL_UPLOAD_PATH = "/usr/local/offlinetest/excel/";
    //    public static final String OFFLINE_TEST_EXCEL_UPLOAD_PATH = "D://upload//";
    //离线数据Pqdif文件上传下载数据
    public static final String OFFLINE_TEST_PQDIF_UPLOAD_PATH = "/usr/local/offlinetest/pqdif/";

    // 拒绝的文件名称非法字符
    public static final List<String> FILE_NAME_EXCLUDE = Arrays.asList("..", "/", "\\", "\r", "\n", "http", "ok", "cookie");

    // 允许通过的文件名 这个正则表达式可以匹配数字、大小写字母、中文字符、连字符-、等号=、下划线_、圆括号(和)以及全角的圆括号（和），还包括英文句号.。
    // + 表示匹配前面的字符集合中的字符一次或多次。
    public static final String pattern = "[0-9A-Za-z\\u4e00-\\u9fa5\\-\\=\\_\\(\\)\\（\\）\\.]+";

    public static String upload(MultipartFile file, String fileName, String uploadPath) {
        OutputStream os = null;
        InputStream inputStream = null;
        // 2、保存到临时文件
        // 1K的数据缓冲
        byte[] bs = new byte[1024];
        // 读取到的数据长度
        int len;
        // 输出的文件流保存到本地文件
        String uploadpath = uploadPath.concat(fileName);
        try {
            inputStream = file.getInputStream();
            os = new FileOutputStream(uploadpath);
            // 开始读取
            while ((len = inputStream.read(bs)) != -1) {
                os.write(bs, 0, len);
            }
        } catch (IOException e) {
            logger.debug(CommonConstant.CONTEXT, e);
            throw new CommonManagerException("Excel上传失败", e);
        } finally {
            // 完毕，关闭所有链接
            IOUtils.closeQuietly(os);
            IOUtils.closeQuietly(inputStream);
        }
        return uploadpath;
    }

    /**
     * 需要合并单元格数据
     * @param sheets
     */
    public static void writeMergeData(Sheets sheets,int firstRow, int lastRow, int firstCol, int lastCol) {
        List<MergeData> mergeData = new ArrayList<>();
        mergeData.add(new MergeData(firstRow, lastRow,firstCol,lastCol));
        //设置标红的行和列
        if (sheets.getMergeData() == null) {
            LinkedList<MergeData> mergeDataLinkedList = new LinkedList<>();
            mergeDataLinkedList.addAll(mergeData);
            sheets.setMergeData(mergeDataLinkedList);
        } else {
            sheets.getMergeData().addAll(mergeData);
        }
    }

    /**
     * 删除文件
     *
     * @param uploadPath
     * @param fileName
     * @return
     */
    public static Boolean delete(String uploadPath, String fileName) {
        String filePath = uploadPath.concat(fileName);
        File file = new File(filePath);
        if (file.exists() && file.isFile() && file.delete()) {
            return Boolean.TRUE;
        }
        throw new CommonManagerException("删除文件失败");
    }

    /**
     * 下载文件
     *
     * @param path     不包含文件名称
     * @param fileName
     */
    public static void download(HttpServletResponse response, String path, String fileName) {
        String filePath = path.concat(fileName);
        InputStream in = null;
        try {
            // 设置response
            filePath = URLDecoder.decode(filePath, StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/x-download");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            in = new FileInputStream(filePath);
            int len = 0;
            byte[] buffer = new byte[1024];
            OutputStream out = response.getOutputStream();
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            throw new CommonManagerException("模板文件不存在", e);
        } finally {
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * 下载波形文件
     *
     * @param response
     * @param wave
     * @param zipFileName
     */
    public static void downloadWaveFile(HttpServletResponse response, Wave wave, Long waveformlogtime, String fileName) {
        if (null == wave) {
            throw new CommonManagerException("波形文件不存在");
        }
        if (StringUtils.isEmpty(fileName)) {
            fileName = "comtrade";
        }
        //生成.cfg文件的流,文件名：deviceID_waveformlogtime.cfg
        String config = wave.getConfig();
        String cfgFileName = fileName.concat("_").concat(String.valueOf(waveformlogtime)).concat(CommonConstant.POINT).concat("cfg");
        InputStream cfgInput = new ByteArrayInputStream(config.getBytes());
        //生成.dat文件的流,文件名：deviceID_waveformlogtime.dat
        String data = wave.getData();
        String datFileName = fileName.concat("_").concat(String.valueOf(waveformlogtime)).concat(CommonConstant.POINT).concat("dat");
        InputStream datInput = new ByteArrayInputStream(Base64.getDecoder().decode(data));
        //将上述两个输入流缩成offlinetestName_comtrade.zip文件，返回流
        Map<String, InputStream> files = new ConcurrentHashMap<>();
        files.put(cfgFileName, cfgInput);
        files.put(datFileName, datInput);
        //压缩后的文件名
        String zipFileName = fileName.concat("_").concat(String.valueOf(waveformlogtime)).concat(CommonConstant.POINT).concat("zip");
        response.setHeader("Content-disposition", "attachment; filename=" + zipFileName);
        response.setContentType("application/x-download");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        ZipOutputStream ZipOutputStream = null;
        try {
            ZipOutputStream = new ZipOutputStream(response.getOutputStream(), StandardCharsets.UTF_8);
        } catch (IOException e) {
        }
        ZipUtil.compressFile(files, ZipOutputStream);
    }


    /**
     * 20230714：不平衡，限值和结论取最后一个的值，不平衡度
     *
     * @param exportParameter 导出参数
     * @param rowList         所有数据行
     * @param rowTotal        此表格行数
     * @param writeRow        此表格开始行数
     */
    public static void writeNorm3(ExportParameter exportParameter, LinkedList<List<DataReport>> rowList, int rowTotal, int writeRow) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            exportParameter.getSheets().stream()
                    .peek(
                            sheets -> {
//                                DecimalFormat df = new DecimalFormat("0.00#");
                                //添加背景
                                int i = 0;
                                while (i < rowTotal) {
                                    List<DataReport> dataReportList = rowList.get(0);
                                    // 没有数据的行
                                    if (CollectionUtils.isEmpty(dataReportList)) {
                                        rowList.remove(0);
                                        i++;
                                        continue;
                                    }
                                    InsertData insertData = new InsertData();
                                    insertData.setRow(writeRow + i);
                                    insertData.setCol(2);
                                    LinkedList<Object> data = new LinkedList<>();
                                    dataReportList.forEach(
                                            dataReport -> {
                                                data.add(Objects.isNull(dataReport.getMaxValue())? "--": CommonUtils.formatNumber(dataReport.getMaxValue(),2));
                                                data.add(Objects.isNull(dataReport.getMinValue())? "--": CommonUtils.formatNumber(dataReport.getMinValue(),2));
                                                data.add(Objects.isNull(dataReport.getCpValue())? "--": CommonUtils.formatNumber(dataReport.getCpValue(),2));
                                                data.add(Objects.isNull(dataReport.getAvgValue())? "--": CommonUtils.formatNumber(dataReport.getAvgValue(),2));
                                            });
                                    DataReport dataReportRow = dataReportList.get(0);
                                    if (dataReportRow.getLowlimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getLowlimit(),2));
                                    }
                                    if (dataReportRow.getUplimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getUplimit(),2));
                                    }

                                    Boolean isOverLimit = null;
                                    int num = 0;
                                    Iterator var13 = dataReportList.iterator();

                                    while(var13.hasNext()) {
                                        DataReport dataReport = (DataReport)var13.next();
                                        writeRedData(writeRow + i, num * 4, sheets, dataReport);
                                        ++num;
                                        if (!Objects.equals(isOverLimit, Boolean.TRUE) && dataReport.getIsoverlimit() != null) {
                                            if (dataReport.getIsoverlimit()) {
                                                isOverLimit = true;
                                            } else {
                                                isOverLimit = false;
                                            }
                                        }
                                    }

                                    if (isOverLimit == null) {
                                        data.add("--");
                                    } else if (isOverLimit) {
                                        data.add("不合格");
                                    } else {
                                        data.add("合格");
                                    }

                                    insertData.setData(data);
                                    if (sheets.getInsertData() == null) {
                                        LinkedList<InsertData> insertDataList = new LinkedList<>();
                                        insertDataList.add(insertData);
                                        sheets.setInsertData(insertDataList);
                                    } else {
                                        sheets.getInsertData().add(insertData);
                                    }
                                    rowList.remove(0);
                                    i++;
                                }
                            })
                    .collect(Collectors.toList());
        }
    }

    // @AI-Generated-start
    /**
     * @Description 指定限值列
     */
    public static void writeNorm3(ExportParameter exportParameter, LinkedList<List<DataReport>> rowList, int rowTotal, int writeRow, int limitRow) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            exportParameter.getSheets().forEach(sheets -> processSheet(sheets, rowList, rowTotal, writeRow, limitRow));
        }
    }

    private static void processSheet(Sheets sheets, LinkedList<List<DataReport>> rowList, int rowTotal, int writeRow, int limitRow) {
        int i = 0;
        while (i < rowTotal && !rowList.isEmpty()) {
            List<DataReport> dataReportList = rowList.peekFirst();
            if (CollectionUtils.isEmpty(dataReportList)) {
                rowList.pollFirst();
                i++;
                continue;
            }

            InsertData insertData = createInsertData(writeRow + i, 2);
            LinkedList<Object> data = new LinkedList<>();

            addFormattedValues(dataReportList, data);
            addLimitValues(dataReportList, data, limitRow);
            Boolean isOverLimit = determineIsOverLimit(dataReportList, sheets, writeRow + i);

            addConclusion(data, isOverLimit);
            insertData.setData(data);

            updateSheetInsertData(sheets, insertData);
            rowList.pollFirst();
            i++;
        }
    }

    private static InsertData createInsertData(int row, int col) {
        InsertData insertData = new InsertData();
        insertData.setRow(row);
        insertData.setCol(col);
        return insertData;
    }

    private static void addFormattedValues(List<DataReport> dataReportList, LinkedList<Object> data) {
        dataReportList.forEach(dataReport -> {
            data.add(formatValue(dataReport.getMaxValue()));
            data.add(formatValue(dataReport.getMinValue()));
            data.add(formatValue(dataReport.getCpValue()));
            data.add(formatValue(dataReport.getAvgValue()));
        });
    }

    private static Object formatValue(Double value) {
        return Objects.isNull(value) ? "--" : CommonUtils.formatNumber(value, 2);
    }

    private static void addLimitValues(List<DataReport> dataReportList, LinkedList<Object> data, int limitRow) {
        DataReport dataReportRow = dataReportList.get(limitRow);
        data.add(formatValue(dataReportRow.getLowlimit()));
        data.add(formatValue(dataReportRow.getUplimit()));
    }

    private static Boolean determineIsOverLimit(List<DataReport> dataReportList, Sheets sheets, int rowNum) {
        Boolean isOverLimit = null;
        int num = 0;
        for (DataReport dataReport : dataReportList) {
            writeRedData(rowNum, num * 4, sheets, dataReport);
            num++;
            if (isOverLimit == null && dataReport.getIsoverlimit() != null) {
                isOverLimit = dataReport.getIsoverlimit();
            }
        }
        return isOverLimit;
    }

    private static void addConclusion(LinkedList<Object> data, Boolean isOverLimit) {
        if (isOverLimit == null) {
            data.add("--");
        } else if (isOverLimit) {
            data.add("不合格");
        } else {
            data.add("合格");
        }
    }

    private static void updateSheetInsertData(Sheets sheets, InsertData insertData) {
        if (sheets.getInsertData() == null) {
            LinkedList<InsertData> insertDataList = new LinkedList<>();
            insertDataList.add(insertData);
            sheets.setInsertData(insertDataList);
        } else {
            sheets.getInsertData().add(insertData);
        }
    }
    // @AI-Generated-end


    /**
     * 表格最后有“结论”单元格的数据处理.
     * 20230714：除不平衡，其余三相指标的限值和结论，限值取第一个，结论为三个都合格才合格，1-3个不合格为不合格。
     *
     * @param exportParameter 导出参数
     * @param rowList         所有数据行
     * @param rowTotal        此表格行数
     * @param writeRow        此表格开始行数
     */
    public static void writeNorm(ExportParameter exportParameter, LinkedList<List<DataReport>> rowList, int rowTotal, int writeRow) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            exportParameter.getSheets().stream()
                    .peek(
                            sheets -> {
//                                DecimalFormat df = new DecimalFormat("0.00#");
                                int i = 0;
                                while (i < rowTotal) {
                                    List<DataReport> dataReportList = rowList.get(0);
                                    // 没有数据的行
                                    if (CollectionUtils.isEmpty(dataReportList)) {
                                        rowList.remove(0);
                                        i++;
                                        continue;
                                    }
                                    LinkedList<Object> data = new LinkedList<>();
                                    InsertData insertData = new InsertData();
                                    insertData.setRow(writeRow + i);
                                    insertData.setCol(1);
                                    dataReportList.forEach(
                                            dataReport -> {
                                                data.add(Objects.isNull(dataReport.getMaxValue())? "--": CommonUtils.formatNumber(dataReport.getMaxValue(),2));
                                                data.add(Objects.isNull(dataReport.getMinValue())? "--": CommonUtils.formatNumber(dataReport.getMinValue(),2));
                                                data.add(Objects.isNull(dataReport.getCpValue())? "--": CommonUtils.formatNumber(dataReport.getCpValue(),2));
                                                data.add(Objects.isNull(dataReport.getAvgValue())? "--": CommonUtils.formatNumber(dataReport.getAvgValue(),2));
                                            });
                                    DataReport dataReportRow = dataReportList.get(0);
                                    if (dataReportRow.getLowlimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getLowlimit(),2));
                                    }
                                    if (dataReportRow.getUplimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getUplimit(),2));
                                    }
                                    Boolean isOverLimit = null;
                                    int num = 0;
                                    for (DataReport dataReport : dataReportList) {
                                        writeRedData(writeRow + i, num * 4, sheets, dataReport);
                                        num++;
                                        if(Objects.equals(isOverLimit, Boolean.TRUE)){
                                            continue;
                                        }
                                        if(dataReport.getIsoverlimit() == null){
                                            continue;
                                        }
                                        if (dataReport.getIsoverlimit()) {
                                            isOverLimit = true;
                                        }else {
                                            isOverLimit = false;
                                        }
                                    }
                                    if (isOverLimit == null) {
                                        data.add("--");
                                    } else if (isOverLimit) {
                                        data.add(ExcelConstant.IS_OVER_LIMIT);
                                    } else {
                                        data.add(ExcelConstant.NOT_OVER_LIMIT);
                                    }
                                    insertData.setData(data);
                                    if (sheets.getInsertData() == null) {
                                        LinkedList<InsertData> insertDataList = new LinkedList<>();
                                        insertDataList.add(insertData);
                                        sheets.setInsertData(insertDataList);
                                    } else {
                                        sheets.getInsertData().add(insertData);
                                    }
                                    rowList.remove(0);
                                    i++;
                                }
                            })
                    .collect(Collectors.toList());
        }
    }

    public static void writeAddBgData(int rowNum, Integer column, Sheets sheets) {
        List<InsertData> addBgData = new ArrayList<>();
        addBgData.add(new InsertData(rowNum, column));
        //设置标红的行和列
        if (sheets.getAddBgData() == null) {
            LinkedList<InsertData> insertDataList = new LinkedList<>();
            insertDataList.addAll(addBgData);
            sheets.setAddBgData(insertDataList);
        } else {
            sheets.getAddBgData().addAll(addBgData);
        }
    }

    public static void writeNorm(ExportParameter exportParameter, LinkedList<List<DataReport>> rowList, int padding, int rowTotal, int writeRow, int writeCol) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            exportParameter.getSheets().stream().peek((sheets) -> {
                int i = 0;

                while(true) {
                    while(i < rowTotal) {
                        List<DataReport> dataReportList = (List)rowList.get(0);
                        if (org.springframework.util.CollectionUtils.isEmpty(dataReportList)) {
                            rowList.remove(0);
                            ++i;
                        } else {
                            InsertData insertData = new InsertData();
                            insertData.setRow(writeRow + i);
                            insertData.setCol(writeCol);
                            LinkedList<Object> data = new LinkedList();
                            dataReportList.forEach((dataReportx) -> {
                                data.add(dataReportx.getMaxValue());
                                data.add(dataReportx.getMinValue());
                                data.add(dataReportx.getCpValue());
                                data.add(dataReportx.getAvgValue());
                            });

                            for(int j = 0; j < padding; ++j) {
                                data.add("");
                            }

                            DataReport dataReportRow = (DataReport)dataReportList.get(0);
                            if (dataReportRow.getLowlimit() == null) {
                                data.add("--");
                            } else {
                                data.add(dataReportRow.getLowlimit());
                            }

                            if (dataReportRow.getUplimit() == null) {
                                data.add("--");
                            } else {
                                data.add(dataReportRow.getUplimit());
                            }

                            Boolean isOverLimit = null;
                            int num = 0;
                            Iterator var13 = dataReportList.iterator();

                            while(var13.hasNext()) {
                                DataReport dataReport = (DataReport)var13.next();
                                writeRedData(writeRow + i, num * 4, sheets, dataReport);
                                ++num;
                                if (!Objects.equals(isOverLimit, Boolean.TRUE) && dataReport.getIsoverlimit() != null) {
                                    if (dataReport.getIsoverlimit()) {
                                        isOverLimit = true;
                                    } else {
                                        isOverLimit = false;
                                    }
                                }
                            }

                            if (isOverLimit == null) {
                                data.add("--");
                            } else if (isOverLimit) {
                                data.add("不合格");
                            } else {
                                data.add("合格");
                            }

                            insertData.setData(data);
                            if (sheets.getInsertData() == null) {
                                LinkedList<InsertData> insertDataList = new LinkedList();
                                insertDataList.add(insertData);
                                sheets.setInsertData(insertDataList);
                            } else {
                                sheets.getInsertData().add(insertData);
                            }

                            rowList.remove(0);
                            ++i;
                        }
                    }

                    return;
                }
            }).collect(Collectors.toList());
        }

    }

    /**
     * 表格最后有“结论”单元格的数据处理(不分相填充空格宽度和上下列表保持一致)
     *
     * @param exportParameter 导出参数
     * @param rowList         所有数据行
     * @param rowTotal        此表格行数
     * @param writeRow        此表格开始行数
     */
    public static void writeNormFill(ExportParameter exportParameter, LinkedList<List<DataReport>> rowList, int rowTotal, int writeRow) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            exportParameter.getSheets().stream()
                    .peek(
                            sheets -> {
//                                DecimalFormat df = new DecimalFormat("0.00#");
                                int i = 0;
                                while (i < rowTotal) {
                                    List<DataReport> dataReportList = rowList.get(0);
                                    // 没有数据的行
                                    if (CollectionUtils.isEmpty(dataReportList)) {
                                        rowList.remove(0);
                                        i++;
                                        continue;
                                    }
                                    InsertData insertData = new InsertData();
                                    insertData.setRow(writeRow + i);
                                    insertData.setCol(1);

                                    LinkedList<Object> data = new LinkedList<>();
                                    dataReportList.forEach(
                                            dataReport -> {
                                                data.add(Objects.isNull(dataReport.getMaxValue())? "--": CommonUtils.formatNumber(dataReport.getMaxValue(),2));
                                                data.add("--");
                                                data.add("--");
                                                data.add(Objects.isNull(dataReport.getMinValue())? "--": CommonUtils.formatNumber(dataReport.getMinValue(),2));
                                                data.add("--");
                                                data.add("--");
                                                data.add(Objects.isNull(dataReport.getCpValue())? "--": CommonUtils.formatNumber(dataReport.getCpValue(),2));
                                                data.add("--");
                                                data.add("--");
                                                data.add(Objects.isNull(dataReport.getAvgValue())? "--": CommonUtils.formatNumber(dataReport.getAvgValue(),2));
                                                data.add("--");
                                                data.add("--");
                                            });

                                    DataReport dataReportRow = dataReportList.get(0);
                                    writeRedData(writeRow + i,0, sheets, dataReportRow);
                                    if (dataReportRow.getLowlimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getLowlimit(),2));
                                    }
                                    if (dataReportRow.getUplimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getUplimit(),2));
                                    }
                                    if (dataReportRow.getIsoverlimit() == null) {
                                        data.add("--");
                                    } else if (dataReportRow.getIsoverlimit()) {
                                        data.add(ExcelConstant.IS_OVER_LIMIT);
                                    } else {
                                        data.add(ExcelConstant.NOT_OVER_LIMIT);
                                    }
                                    insertData.setData(data);
                                    if (sheets.getInsertData() == null) {
                                        LinkedList<InsertData> insertDataList = new LinkedList<>();
                                        insertDataList.add(insertData);
                                        sheets.setInsertData(insertDataList);
                                    } else {
                                        sheets.getInsertData().add(insertData);
                                    }
                                    rowList.remove(0);
                                    i++;
                                }
                            })
                    .collect(Collectors.toList());
        }
    }

    private static void writeRedData(int rowNum, Integer column, Sheets sheets, DataReport dataReportRow) {
        List<InsertData> redData = new ArrayList<>();
        if (ParseDataUtil.parseBoolean(dataReportRow.getIsoverlimit()) && !CollectionUtils.isEmpty(dataReportRow.getOverValues())) {
            dataReportRow.getOverValues().forEach(t -> {
                if (CommonConstant.AGGREGATIONTYPE_MAX.equals(t)) {
                    redData.add(new InsertData(rowNum, column + 2));
                } else if (CommonConstant.AGGREGATIONTYPE_MIN.equals(t)) {
                    redData.add(new InsertData(rowNum, column + 3));
                } else if (CommonConstant.AGGREGATIONTYPE_CP.equals(t)) {
                    redData.add(new InsertData(rowNum, column + 4));
                } else if (CommonConstant.AGGREGATIONTYPE_AVG.equals(t)) {
                    redData.add(new InsertData(rowNum, column + 5));
                }
            });
        }
        //设置标红的行和列
        if (sheets.getRedData() == null) {
            LinkedList<InsertData> insertDataList = new LinkedList<>();
            insertDataList.addAll(redData);
            sheets.setRedData(insertDataList);
        } else {
            sheets.getRedData().addAll(redData);
        }
    }

    /**
     * 表格最后没有“结论”单元格的数据处理
     *
     * @param exportParameter 导出参数
     * @param rowList         所有数据行
     * @param rowTotal        此表格行数
     * @param writeRow        此表格开始行数
     */
    public static void writeNorm2(ExportParameter exportParameter, LinkedList<List<DataReport>> rowList, int rowTotal, int writeRow,int writecol) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            exportParameter.getSheets().stream()
                    .peek(
                            sheets -> {
//                                DecimalFormat df = new DecimalFormat("0.00#");
                                int i = 0;
                                while (i < rowTotal) {
                                    List<DataReport> dataReportList = rowList.get(0);
                                    if (CollectionUtils.isEmpty(dataReportList)) {
                                        rowList.remove(0);
                                        i++;
                                        continue;
                                    }
                                    LinkedList<Object> data = new LinkedList<>();
                                    InsertData insertData = new InsertData();
                                    insertData.setRow(writeRow + i);
                                    insertData.setCol(1);
                                    dataReportList.forEach(
                                            dataReport -> {
                                                data.add(Objects.isNull(dataReport.getMaxValue())? "--": CommonUtils.formatNumber(dataReport.getMaxValue(),2));
                                                data.add(Objects.isNull(dataReport.getMinValue())? "--": CommonUtils.formatNumber(dataReport.getMinValue(),2));
                                                data.add(Objects.isNull(dataReport.getCpValue())? "--": CommonUtils.formatNumber(dataReport.getCpValue(),2));
                                                data.add(Objects.isNull(dataReport.getAvgValue())? "--": CommonUtils.formatNumber(dataReport.getAvgValue(),2));
                                            });
                                    DataReport dataReportRow = dataReportList.get(0);
                                    if (dataReportRow.getLowlimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getLowlimit(),2));
                                    }
                                    if (dataReportRow.getUplimit() == null) {
                                        data.add("--");
                                    } else {
                                        data.add(CommonUtils.formatNumber(dataReportRow.getUplimit(),2));
                                    }
                                    Boolean isOverLimit = null;
                                    int num = 0;
                                    for (DataReport dataReport : dataReportList) {
                                        writeRedData(writeRow + i, num * 4, sheets, dataReport);
                                        num++;
                                        if(Objects.equals(isOverLimit, Boolean.TRUE)){
                                            continue;
                                        }
                                        if(dataReport.getIsoverlimit() == null){
                                            continue;
                                        }
                                        if (dataReport.getIsoverlimit()) {
                                            isOverLimit = true;
                                        }else {
                                            isOverLimit = false;
                                        }
                                    }
                                    if (isOverLimit == null) {
                                        data.add("--");
                                    } else if (isOverLimit) {
                                        data.add(ExcelConstant.IS_OVER_LIMIT);
                                    } else {
                                        data.add(ExcelConstant.NOT_OVER_LIMIT);
                                    }
                                    insertData.setData(data);
                                    if (sheets.getInsertData() == null) {
                                        LinkedList<InsertData> insertDataList = new LinkedList<>();
                                        insertDataList.add(insertData);
                                        sheets.setInsertData(insertDataList);
                                    } else {
                                        sheets.getInsertData().add(insertData);
                                    }
                                    rowList.remove(0);
                                    i++;
                                }
                            })
                    .collect(Collectors.toList());
        }
    }

    public static void writeNorm2(ExportParameter exportParameter, LinkedList<List<DataReport>> rowList, int rowTotal, int writeRow) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(rowList)) {
            exportParameter.getSheets().stream().peek((sheets) -> {
                int i = 0;

                while(true) {
                    while(i < rowTotal) {
                        List<DataReport> dataReportList = (List)rowList.get(0);
                        if (org.springframework.util.CollectionUtils.isEmpty(dataReportList)) {
                            rowList.remove(0);
                            ++i;
                        } else {
                            InsertData insertData = new InsertData();
                            insertData.setRow(writeRow + i);
                            insertData.setCol(2);
                            LinkedList<Object> data = new LinkedList();
                            dataReportList.forEach((dataReportx) -> {
                                data.add(dataReportx.getMaxValue());
                                data.add(dataReportx.getMinValue());
                                data.add(dataReportx.getCpValue());
                                data.add(dataReportx.getAvgValue());
                            });
                            DataReport dataReportRow = (DataReport)dataReportList.get(0);
                            if (dataReportRow.getLowlimit() == null) {
                                data.add("--");
                            } else {
                                data.add(dataReportRow.getLowlimit());
                            }

                            if (dataReportRow.getUplimit() == null) {
                                data.add("--");
                            } else {
                                data.add(dataReportRow.getUplimit());
                            }
                            Boolean isOverLimit = null;
                            int num = 0;
                            for (DataReport dataReport : dataReportList) {
                                writeRedData(writeRow + i, num * 4, sheets, dataReport);
                                num++;
                                if(Objects.equals(isOverLimit, Boolean.TRUE)){
                                    continue;
                                }
                                if(dataReport.getIsoverlimit() == null){
                                    continue;
                                }
                                if (dataReport.getIsoverlimit()) {
                                    isOverLimit = true;
                                }else {
                                    isOverLimit = false;
                                }
                            }
                            if (isOverLimit == null) {
                                data.add("--");
                            } else if (isOverLimit) {
                                data.add(ExcelConstant.IS_OVER_LIMIT);
                            } else {
                                data.add(ExcelConstant.NOT_OVER_LIMIT);
                            }

                            insertData.setData(data);
                            if (sheets.getInsertData() == null) {
                                LinkedList<InsertData> insertDataList = new LinkedList();
                                insertDataList.add(insertData);
                                sheets.setInsertData(insertDataList);
                            } else {
                                sheets.getInsertData().add(insertData);
                            }

                            rowList.remove(0);
                            ++i;
                        }
                    }

                    return;
                }
            }).collect(Collectors.toList());
        }

    }

    /**
     * @Description: 复制文件
     * @param multipartFile
     * @param path
     * @return: java.io.File
     **/
    public static File copyFile(MultipartFile multipartFile, String path){
        //根目录下生成临时文件
        File file = new File(path);
        try {
            FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
        } catch (IOException e) {
            logger.error("copy file error.", e);
        }
        return file;
    }

    /**
     * @Description: 校验文件
     * @param file
     * @param types
     * @return: java.io.File
     **/
    public static boolean checkFile(MultipartFile file, List<String> types) {
        String fileName = file.getOriginalFilename();
        /*// 校验文件名称和非法字符
        if(!checkFileNameValid(fileName)) {
            return false;
        }*/
        String fileType = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        if (!types.contains(fileType) || file.getSize() > 50 * 1024 *1024) {
            return false;
        }
        return true;
    }

    // 校验文件名称，不适用校验全路径
    public static boolean checkFileNameValid(String fileName) {
        //校验文件名称
        if(StringUtils.isEmpty(fileName)) {
            return false;
        }
        String fName = fileName.toLowerCase(Locale.ENGLISH);
        for(String n : FILE_NAME_EXCLUDE) {
            if(fName.contains(n)){
                return false;
            }
        }
        if(!Pattern.matches(pattern, fileName)) {
            return false;
        }
        return true;
    }
}

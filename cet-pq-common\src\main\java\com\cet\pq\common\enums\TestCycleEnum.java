package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

public enum TestCycleEnum {

    OneYear("一年", 1, 1),
    <PERSON><PERSON><PERSON>("两年", 2, 1),
    <PERSON><PERSON><PERSON>("三年", 3, 1),
    <PERSON><PERSON><PERSON>("六年", 4, 2);

    private String name;
    private Integer value;
    private Integer subVoltage;

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public Integer getSubVoltage() {
        return subVoltage;
    }

    TestCycleEnum(String name, Integer value, Integer subVoltage) {
        this.name = name;
        this.value = value;
        this.subVoltage = subVoltage;
    }

    public static String getCycleName(Integer value) {
        for (TestCycleEnum testCycleEnum : TestCycleEnum.values()) {
            if (value.equals(testCycleEnum.getValue())) {
                return testCycleEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getCycleName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (TestCycleEnum testCycleEnum : TestCycleEnum.values()) {
            if (name.equalsIgnoreCase(testCycleEnum.getName().trim())) {
                return testCycleEnum.getValue();
            }
        }
        return 0;
    }
}

package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/9/10 11:05
 * @Description 预测严重等级
 */
public enum PredictionDegreeEnum {
    NOT_WARNING(0,"未预警"),
    ONE(1,"1级"),
    TWO(2,"2级"),
    THREE(3,"3级"),
    ;

    private Integer id;
    private String text;

    PredictionDegreeEnum(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public static String getTextById(Integer id) {
        for (PredictionDegreeEnum oneEnum : PredictionDegreeEnum.values()) {
            if (oneEnum.id.equals(id)) {
                return oneEnum.text;
            }
        }
        return StringUtils.EMPTY;
    }
}

package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

public enum StoreEnergyTypeEnum {

    CENTRALIZED(1, "集中式"),
    DISTRIBUTED(2, "分布式");
    private Integer id;
    private String name;

    StoreEnergyTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (StoreEnergyTypeEnum storeEnergyTypeEnum : StoreEnergyTypeEnum.values()) {
            if (storeEnergyTypeEnum.id.equals(id)) {
                return storeEnergyTypeEnum.name;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (StoreEnergyTypeEnum storeEnergyTypeEnum : StoreEnergyTypeEnum.values()) {
            if (storeEnergyTypeEnum.getName().equals(name)) {
                return storeEnergyTypeEnum.getId();
            }
        }
        return null;
    }
    public static Integer getIdByText(String text) {
        for(StoreEnergyTypeEnum rs : values()) {
            if(rs.getName().equals(text)) {
                return rs.getId();
            }
        }
        return null;
    }

}

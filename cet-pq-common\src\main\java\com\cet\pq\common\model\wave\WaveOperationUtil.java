package com.cet.pq.common.model.wave;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 波形操作工具
 * @date 2021/6/15 10:32
 */
@Slf4j
public class WaveOperationUtil {

    /**
     * 2001版波形数据标志
     */
    private static final Long FLAG_2001 = 0xFFFFFFFFL;

    private static final String ASCII = "ASCII";
    private static final String BINARY = "BINARY";
    /**
     * 周期信号
     */
    private static final float T = 0.02f;

    /**
     * 解析波形datafile字段
     *
     * @param dataFile dataFile字段
     */
    public static WaveFileObject loadWaveData(byte[] dataFile) {
        ByteArrayInputStream in = new ByteArrayInputStream(dataFile);
        DataInputStream din = new DataInputStream(in);
        byte[] hdrFile = null;
        byte[] cfgFile = null;
        byte[] datFile = null;
        try {
            Long flag1 = LibSerializeHelper.readUnsignedIntOrLong(din);
            if (flag1.equals(FLAG_2001)) {
                LibSerializeHelper.readUnsignedIntOrLong(din);
                LibSerializeHelper.readUnsignedIntOrLong(din);
                LibSerializeHelper.readUnsignedIntOrLong(din);
                Long hdrLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                hdrFile = new byte[hdrLen.intValue()];
                readFully(din, hdrFile);
                Long cfgLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                cfgFile = new byte[cfgLen.intValue()];
                readFully(din, cfgFile);
                Long datLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                datFile = new byte[datLen.intValue()];
                readFully(din, datFile);
            } else {
                Integer cfgLen = flag1.intValue();
                cfgFile = new byte[cfgLen];
                readFully(din, cfgFile);
                Long datLen = LibSerializeHelper.readUnsignedIntOrLong(din);
                datFile = new byte[datLen.intValue()];
                readFully(din, datFile);
                hdrFile = null;
            }
        } catch (IOException e) {
            log.info("解析波形数据失败",e);
        } finally {
            try {
                in.close();
                din.close();
            } catch (IOException e) {
                log.info("解析波形数据失败",e);
            }
        }
        return new WaveFileObject(hdrFile, cfgFile, datFile);
    }

    /**
     * 解析波形数据
     *
     * @param cfgByte cfg byte 数组
     * @param datByte dat byte 数组
     * @return WaveDataInfo
     */
    public static WaveDataInfo parseWaveDataInfo(byte[] cfgByte, byte[] datByte) throws UnsupportedEncodingException {
        List<String> cfgFileLinesArrayList = cgfByteToStringList2(cfgByte);
        WaveDataInfo waveDataInfo = parseCfgStringList2(cfgFileLinesArrayList);
        String datType = waveDataInfo.getDatType();
        if (ASCII.equalsIgnoreCase(datType)) {
            List<String> datFileLinesArrayList = datByteToStringByAscii(datByte);
            return parseDatStringListByAscii(datFileLinesArrayList, waveDataInfo);
        } else if (BINARY.equalsIgnoreCase(datType)) {
            List<String> datFileLinesArrayList = datByteToStringByBinary(datByte, waveDataInfo);
            return parseDatStringListByBinary(datFileLinesArrayList, waveDataInfo);
        } else {
            throw new RuntimeException(datType + "格式波形暂无解析规则");
        }
    }

    /**
     * 计算电流电压有效值序列
     *
     * @param dataList 电压或电流
     * @param samprate 采样率
     * @return 有效值
     */
    public static List<Float> caleEffectiveValue(List<Float> dataList, float samprate) {
        if (CollectionUtils.isEmpty(dataList)){
            return Collections.emptyList();
        }
        // 半周期样本点数
        int cNum = Math.round((float) (0.5 * T * samprate));
        List<Float> result = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            float value;
            if (i <= cNum-1) {
                value = (float) Math.sqrt(dataList.subList(0,cNum).stream().mapToDouble(d-> Math.pow(d,2)).sum()/cNum);
            } else {
                value = (float) Math.sqrt(dataList.subList(i-cNum,i).stream().mapToDouble(d-> Math.pow(d,2)).sum()/cNum);
            }
            result.add(value);
        }
        return result;
    }

    /**
     * 计算零序电压电流
     * @param aDataList A相电压或电流
     * @param bDataList B相电压或电流
     * @param cDataList C相电压或电流
     * @return 零序电压或电流
     */
    public static List<Float> caleZeroSequence(List<Float> aDataList, List<Float> bDataList, List<Float> cDataList) {
        if (CollectionUtils.isEmpty(aDataList)|| CollectionUtils.isEmpty(bDataList)|| CollectionUtils.isEmpty(cDataList)){
            return Collections.emptyList();
        }
        List<Float> result = new ArrayList<>();
        for (int i = 0; i < aDataList.size(); i++) {
            result.add((aDataList.get(i)+bDataList.get(i)+cDataList.get(i))/3);
        }
        return result;
    }

    /**
     * ASCII 波形字符串解析
     *
     * @param datFileLinesArrayList 字符串列表
     * @param waveDataInfo          波形基本信息
     * @return WaveDataInfo
     */
    private static WaveDataInfo parseDatStringListByAscii(List<String> datFileLinesArrayList, WaveDataInfo waveDataInfo) {
        if (datFileLinesArrayList == null || waveDataInfo == null) {
            return waveDataInfo;
        }
        int ratesNum = waveDataInfo.getRatesNum();
        List<SampInfo> sampInfoList = waveDataInfo.getSampInfoList();
        float frequency = waveDataInfo.getFrequency();
        for (int rate = 0; rate < ratesNum; ++rate) {
            SampInfo sampInfo = (sampInfoList != null && rate < sampInfoList.size()) ? sampInfoList.get(rate) : null;
            if (sampInfo == null) continue;
            int samplePerCycle = (int) Math.pow(2, Math.round(Math.log(sampInfo.getSamp() / frequency) / Math.log(2)));
            processChannelsForRate(waveDataInfo, sampInfo, frequency, datFileLinesArrayList, samplePerCycle);
        }
        return waveDataInfo;
    }

    // 新增：处理每个rate下所有通道
    private static void processChannelsForRate(WaveDataInfo waveDataInfo, SampInfo sampInfo, float frequency, List<String> datFileLinesArrayList, int samplePerCycle) {
        int totalChannelNum = waveDataInfo.getTotalChannelNum();
        List<ChannelDetailInfo> channelDetailInfoList = waveDataInfo.getChannelDetailInfoList();
        for (int i = 0; i < totalChannelNum; ++i) {
            ChannelDetailInfo channelInfoObj = (channelDetailInfoList != null && i < channelDetailInfoList.size()) ? channelDetailInfoList.get(i) : null;
            if (channelInfoObj == null) continue;
            dispatchChannelData(i, channelInfoObj, sampInfo, frequency, datFileLinesArrayList, samplePerCycle, waveDataInfo);
        }
    }

    // 分派通道数据处理
    private static void dispatchChannelData(int i, ChannelDetailInfo channelInfoObj, SampInfo sampInfo, float frequency, List<String> datFileLinesArrayList, int samplePerCycle, WaveDataInfo waveDataInfo) {
        if (waveDataInfo.getCfgRatesNum() != 0) {
            handleWithSample(i, channelInfoObj, sampInfo, frequency, datFileLinesArrayList, samplePerCycle, waveDataInfo);
        } else {
            handleWithoutSample(i, channelInfoObj, sampInfo, datFileLinesArrayList, waveDataInfo);
        }
    }

    // 提取有采样率分支
    private static void handleWithSample(int i, ChannelDetailInfo channelInfoObj, SampInfo sampInfo, float frequency, List<String> datFileLinesArrayList, int samplePerCycle, WaveDataInfo waveDataInfo) {
        if (i < waveDataInfo.getAChannelNum()) {
            calcSimulateChannelDataWithSample(channelInfoObj, sampInfo, frequency, datFileLinesArrayList, samplePerCycle);
        } else {
            calcSwitchChannelDataWithSample(waveDataInfo, channelInfoObj, sampInfo, datFileLinesArrayList, samplePerCycle);
        }
    }

    // 提取无采样率分支
    private static void handleWithoutSample(int i, ChannelDetailInfo channelInfoObj, SampInfo sampInfo, List<String> datFileLinesArrayList, WaveDataInfo waveDataInfo) {
        if (i < waveDataInfo.getAChannelNum()) {
            calcSimulateChannelDataWithOutSample(channelInfoObj, sampInfo, datFileLinesArrayList, waveDataInfo.getTimeMult());
        } else {
            calcSwitchChannelDataWithOutSample(waveDataInfo, channelInfoObj, sampInfo, datFileLinesArrayList);
        }
    }

    /**
     * ASCII data byte 解析为字符串
     *
     * @param datByte data byte 数组
     * @return List<String>
     */
    private static List<String> datByteToStringByAscii(byte[] datByte) {
        String datStr = new String(datByte);
        List<String> datFileLinesArrayList = new ArrayList<>();
        BufferedReader bufferedReader;
        try (StringReader stringReader = new StringReader(datStr)) {
            bufferedReader = new BufferedReader(stringReader);
            String valueString;
            while ((valueString = bufferedReader.readLine()) != null) {
                if (valueString.length() > 3) {
                    String dataFileStr = "," + valueString;
                    datFileLinesArrayList.add(dataFileStr);
                }
            }
        } catch (IOException e) {
            log.info("解析为字符串失败",e);
        }
        return datFileLinesArrayList;
    }

    /**
     * 解析dat字符串数据
     *
     * @param datFileLinesArrayList dat字符串数据列表
     * @param waveDataInfo          波形基本信息
     * @return waveDataInfo
     */
    private static WaveDataInfo parseDatStringListByBinary(List<String> datFileLinesArrayList, WaveDataInfo waveDataInfo) {
        if (datFileLinesArrayList == null || waveDataInfo == null) {
            return waveDataInfo;
        }
        int ratesNum = waveDataInfo.getRatesNum();
        List<SampInfo> sampInfoList = waveDataInfo.getSampInfoList();
        float frequency = waveDataInfo.getFrequency();
        for (int rate = 0; rate < ratesNum; ++rate) {
            SampInfo sampInfo = (sampInfoList != null && rate < sampInfoList.size()) ? sampInfoList.get(rate) : null;
            if (sampInfo == null) continue;
            int samplePerCycle = (int) Math.pow(2, Math.round(Math.log(sampInfo.getSamp() / frequency) / Math.log(2)));
            processChannelsForRateBinary(waveDataInfo, sampInfo, frequency, datFileLinesArrayList, samplePerCycle);
        }
        return waveDataInfo;
    }

    // 新增：处理每个rate下所有通道（二进制）
    private static void processChannelsForRateBinary(WaveDataInfo waveDataInfo, SampInfo sampInfo, float frequency, List<String> datFileLinesArrayList, int samplePerCycle) {
        int totalChannelNum = waveDataInfo.getTotalChannelNum();
        List<ChannelDetailInfo> channelDetailInfoList = waveDataInfo.getChannelDetailInfoList();
        for (int i = 0; i < totalChannelNum; ++i) {
            ChannelDetailInfo channelInfoObj = (channelDetailInfoList != null && i < channelDetailInfoList.size()) ? channelDetailInfoList.get(i) : null;
            if (channelInfoObj == null) continue;
            dispatchChannelData(i, channelInfoObj, sampInfo, frequency, datFileLinesArrayList, samplePerCycle, waveDataInfo);
        }
    }

    /**
     * 不存在采样率的开关量通道解析
     *
     * @param waveDataInfo          波形基本信息
     * @param channelInfoObj        通道信息
     * @param sampleInfo            采样率信息
     * @param datFileLinesArrayList data字符串
     */
    private static void calcSwitchChannelDataWithOutSample(WaveDataInfo waveDataInfo, ChannelDetailInfo channelInfoObj,
                                                           SampInfo sampleInfo, List<String> datFileLinesArrayList) {
        // 获取从通道字符串中解析出来的数据
        List<KeyValuePair> dataArray = new ArrayList<>();
        // 获取该通道是一侧值还是二侧值
        int index = sampleInfo.getBeginSamp();
        // 创建临时存储通道数据的数组
        while (index < sampleInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            // 解析*.dat文件中数据
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 获取数据产生的时间
            long time = Long.valueOf(dataInfoStrs[2]);
            // 如果switchValueIndex>=0表示是开关量
            int switchValueIndex = channelInfoObj.getChannelIndex() - waveDataInfo.getAChannelNum();
            int switchValue = getSwitchValue(waveDataInfo, channelInfoObj, dataInfoStrs, switchValueIndex);
            dataArray.add(new KeyValuePair(time, switchValue));
            ++index;
        }
        // 计算通道数据
        for (KeyValuePair keyValue : dataArray) {
            keyValue.setXField(keyValue.getXField() * waveDataInfo.getTimeMult() / 1000);
            // 将通道数据保存起来
            channelInfoObj.getChannelData().add(keyValue);
        }
    }


    /**
     * 没有采样率的模拟通道解析
     *
     * @param channelInfoObj        通道信息
     * @param sampInfo              采样率信息
     * @param datFileLinesArrayList data字符串
     * @param timeMult              时间系数
     */
    private static void calcSimulateChannelDataWithOutSample(ChannelDetailInfo channelInfoObj, SampInfo sampInfo,
                                                             List<String> datFileLinesArrayList, Float timeMult) {
        // 获取从通道字符串中解析出来的数据
        List<KeyValuePair> dataArray = new ArrayList<>();
        // 获取该通道是一侧值还是二侧值
        boolean isSecondary = (channelInfoObj.getChannelPs() != null) && ("S".equalsIgnoreCase(channelInfoObj.getChannelPs()));
        int index = sampInfo.getBeginSamp();
        // 创建临时存储通道数据的数组
        while (index < sampInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            // 解析*.dat文件中数据
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 获取数据产生的时间
            float xField = Integer.valueOf(dataInfoStrs[2]);
            // 获取数值：（通道转换公式为ax+b， x取自.DAT文件， 单位就是uu）
            float yField = calcSimulateChannelValue(dataInfoStrs, channelInfoObj, isSecondary);
            // 保存获得值和时间
            KeyValuePair keyValue = new KeyValuePair(xField, yField);
            dataArray.add(keyValue);
            ++index;
        }
        // 计算通道数据
        for (KeyValuePair keyValue : dataArray) {
            keyValue.setXField(keyValue.getXField() * timeMult / 1000);
            // 将通道数据保存起来
            channelInfoObj.getChannelData().add(keyValue);
        }
    }

    /**
     * 有采样率的开关量通道解析
     *
     * @param waveDataInfo          波形基本信息
     * @param channelInfoObj        通道信息
     * @param sampInfo              采样率信息
     * @param datFileLinesArrayList data字符串
     * @param samplePerCycle        采样周期
     */
    private static void calcSwitchChannelDataWithSample(WaveDataInfo waveDataInfo, ChannelDetailInfo channelInfoObj, SampInfo sampInfo,
                                                        List<String> datFileLinesArrayList, int samplePerCycle) {
        int index = sampInfo.getBeginSamp();
        // 采样点之间的时间间隔=一周波时间（微秒）/每周波采样点数 1秒=1000毫秒=1000000微秒
        float dbinterval = 1000 * 1000 / (samplePerCycle * waveDataInfo.getFrequency());
        // 创建临时存储通道数据的数组
        while (index < sampInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 如果switchValueIndex>=0表示是开关量
            int switchValueIndex = channelInfoObj.getChannelIndex() - waveDataInfo.getAChannelNum();
            float yField = getSwitchValue(waveDataInfo, channelInfoObj, dataInfoStrs, switchValueIndex);
            float xField = dbinterval * (index - sampInfo.getBeginSamp());
            channelInfoObj.getChannelData().add(new KeyValuePair(xField, yField));
            index++;
        }
    }

    /**
     * 计算开关量的值
     *
     * @param waveDataInfo     波形基本信息
     * @param channelInfoObj   通道信息
     * @param dataInfoStrs     数据信息
     * @param switchValueIndex 值下标
     * @return int
     */
    private static int getSwitchValue(WaveDataInfo waveDataInfo, ChannelDetailInfo channelInfoObj, String[] dataInfoStrs, int switchValueIndex) {
        int switchValue;
        if (ASCII.equalsIgnoreCase(waveDataInfo.getDatType())) {
            switchValue = Integer.valueOf(dataInfoStrs[channelInfoObj.getChannelIndex() + 3]);
        } else {
            // 2个字节表示16个开关量，判断
            int groupIndex = (int) Math.floor(1d * switchValueIndex / 16);
            int switchValueIndexIn16 = switchValueIndex % 16;
            int index = waveDataInfo.getAChannelNum() + groupIndex + 3;
            int groupValue = 0;
            if(index < dataInfoStrs.length){
                groupValue = Integer.valueOf(dataInfoStrs[waveDataInfo.getAChannelNum() + groupIndex + 3].trim());
            }
            switchValue = (groupValue >> switchValueIndexIn16) & 1;
        }
        return switchValue;
    }

    /**
     * 有采样率的模拟量通道解析
     *
     * @param channelInfoObj        通道信息
     * @param sampInfo              采样率信息
     * @param frequency             频率
     * @param datFileLinesArrayList data字符串
     * @param samplePerCycle        采样周期
     */
    private static void calcSimulateChannelDataWithSample(ChannelDetailInfo channelInfoObj, SampInfo sampInfo, Float frequency,
                                                          List<String> datFileLinesArrayList, int samplePerCycle) {
        int index = sampInfo.getBeginSamp();
        // 获取该通道是一侧值还是二侧值
        boolean isSecondary = (channelInfoObj.getChannelPs() != null) && ("S".equalsIgnoreCase(channelInfoObj.getChannelPs()));
        // 采样点之间的时间间隔=一周波时间（微秒）/每周波采样点数 1秒=1000毫秒=1000000微秒
        float dbinterval = 1000 * 1000 / (frequency * samplePerCycle);
        // 创建临时存储通道数据的数组
        while (index < sampInfo.getEndSamp() && index < datFileLinesArrayList.size()) {
            // 解析*.dat文件中数据
            String[] dataInfoStrs = datFileLinesArrayList.get(index).split(",");
            // 数值：（通道转换公式为ax+b， x取自.DAT文件， 单位就是uu）
            float yField = calcSimulateChannelValue(dataInfoStrs, channelInfoObj, isSecondary);
            // 时间
            float xField = dbinterval * (index - sampInfo.getBeginSamp());
            KeyValuePair keyValue = new KeyValuePair(xField, yField);
            channelInfoObj.getChannelData().add(keyValue);
            ++index;
        }
    }

    /**
     * 计算模拟量通道的值
     *
     * @param dataInfoStrs 数据
     * @param channelInfo  通道信息
     * @param isSecondary  是否二侧值
     * @return float
     */
    private static float calcSimulateChannelValue(String[] dataInfoStrs, ChannelDetailInfo channelInfo, boolean isSecondary) {
        // 数值：（通道转换公式为ax+b， x取自.DAT文件， 单位就是uu）
        float value = channelInfo.getChannelCof() * Float.valueOf(dataInfoStrs[channelInfo.getChannelIndex() + 3])
                + channelInfo.getChannelOffset();
        if (isSecondary) {
            value *= channelInfo.getChannelTransRatio();
        }
        return value;
    }

    /**
     * 解析dat数据解析为字符串
     *
     * @param datByte      data byte 数组
     * @param waveDataInfo 波形信息
     */
    private static List<String> datByteToStringByBinary(byte[] datByte,WaveDataInfo waveDataInfo) {
        List<String> datFileLinesArrayList = new ArrayList<>();
        //2字节表示16个开关量
        int dChannelGroupCount = (int) Math.ceil(waveDataInfo.getDChannelNum() / 16);
        int bytesAvailable = 8 + (waveDataInfo.getAChannelNum() + dChannelGroupCount) * 2;
        ByteArrayInputStream datFileInputStream = new ByteArrayInputStream(datByte);
        byte[] recBytes = new byte[bytesAvailable];
        List<byte[]> bytesArrayList = new ArrayList<>();
        try {
            int res = datFileInputStream.read(recBytes);
            while (res == bytesAvailable) {
                bytesArrayList.add(recBytes);
                recBytes = new byte[bytesAvailable];
                res = datFileInputStream.read(recBytes);
            }
        } catch (IOException e) {
            log.info("解析dat文件失败",e);
        }
        for (byte[] byteArray : bytesArrayList) {
            ByteBuffer byteBuffer = ByteBuffer.allocate(byteArray.length);
            byteBuffer.put(byteArray);
            byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
            byteBuffer.position(0);
            int index = byteBuffer.getInt();
            int timeStamp = byteBuffer.getInt();
            String dataFileStr = String.format("%s, %d, %d", "", index, timeStamp);
            for (int j = 0; j < waveDataInfo.getAChannelNum() + dChannelGroupCount; ++j) {
                short value = byteBuffer.getShort();
                dataFileStr = String.format("%s, %d", dataFileStr, value);
            }
            datFileLinesArrayList.add(dataFileStr);
        }
        return datFileLinesArrayList;
    }

    private static List<String> cgfByteToStringList2(byte[] cfgByte) throws UnsupportedEncodingException {
        List<String> cfgFileLinesArrayList = new ArrayList<>();
        ByteArrayInputStream fileInputStream = new ByteArrayInputStream(cfgByte);
        // 显式指定字符编码为 UTF-8
        InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "GBK");
        BufferedReader cfgBufferedReader = new BufferedReader(inputStreamReader);
        try {
            String valueString;
            while ((valueString = cfgBufferedReader.readLine()) != null) {
                cfgFileLinesArrayList.add(valueString.trim());
            }
        } catch (IOException e) {
            log.info("解析cfg文件失败", e);
        } finally {
            try {
                fileInputStream.close();
                inputStreamReader.close();
                cfgBufferedReader.close();
            } catch (IOException e) {
                log.info("关闭文件失败", e);
            }
        }
        return cfgFileLinesArrayList;
    }

    /**
     * 解析cfg文件字符串
     *
     * @param cfgFileLinesArrayList cfg字符串
     * @return 波形基本信息
     */
    private static WaveDataInfo parseCfgStringList2(List<String> cfgFileLinesArrayList) {
        WaveDataInfo waveDataInfo = new WaveDataInfo();
        if (cfgFileLinesArrayList == null || cfgFileLinesArrayList.isEmpty()) {
            return waveDataInfo;
        }
        int offset = 0;
        //解析厂站、设备、版本信息
        String headInfo = getLine(cfgFileLinesArrayList, offset++);
        String[] headInfoList = (headInfo == null) ? new String[0] : headInfo.split(",");
        waveDataInfo.setStationName(getArrayValue(headInfoList, 0));
        waveDataInfo.setDeviceName(getArrayValue(headInfoList, 1));
        if (headInfoList.length > 2 && headInfoList[2] != null) {
            waveDataInfo.setVersion(headInfoList[2].trim());
        }
        //解析通道总数，模拟量通道个数，开关量通道个数
        String channelNumInfo = getLine(cfgFileLinesArrayList, offset++);
        String[] channelNumInfoList = channelNumInfo.split(",");
        waveDataInfo.setTotalChannelNum(parseIntSafe(getArrayValue(channelNumInfoList, 0)));
        waveDataInfo.setAChannelNum(parseIntSafe(trimSuffix(getArrayValue(channelNumInfoList, 1))));
        waveDataInfo.setDChannelNum(parseIntSafe(trimSuffix(getArrayValue(channelNumInfoList, 2))));
        //解析通道系数和通道偏移量
        List<ChannelDetailInfo> channelDetailInfoList = parseChannelDetailInfoList(cfgFileLinesArrayList, offset, waveDataInfo.getTotalChannelNum());
        waveDataInfo.setChannelDetailInfoList(channelDetailInfoList);
        offset += waveDataInfo.getTotalChannelNum();
        //解析频率
        waveDataInfo.setFrequency(parseFloatSafe(getLine(cfgFileLinesArrayList, offset++)));
        //解析采样率个数
        waveDataInfo.setCfgRatesNum(parseIntSafe(getLine(cfgFileLinesArrayList, offset)));
        int ratesNum = Math.max(1, waveDataInfo.getCfgRatesNum());
        waveDataInfo.setRatesNum(ratesNum);
        offset++;
        // 解析采样率
        List<SampInfo> sampInfoList = parseSampInfoList(cfgFileLinesArrayList, offset, ratesNum);
        waveDataInfo.setSampInfoList(sampInfoList);
        offset += ratesNum;
        //解析开始时间
        waveDataInfo.setStartTime(getLine(cfgFileLinesArrayList, offset++).trim());
        // 解析触发时间
        waveDataInfo.setTriggerTime(getLine(cfgFileLinesArrayList, offset++).trim());
        waveDataInfo.setDatType(getLine(cfgFileLinesArrayList, offset++).trim());
        //解析时间系数
        if (offset < cfgFileLinesArrayList.size()) {
            waveDataInfo.setTimeMult(parseFloatSafe(getLine(cfgFileLinesArrayList, offset).trim()));
        }
        return waveDataInfo;
    }

    // 工具方法：安全获取行
    private static String getLine(List<String> list, int idx) {
        return (list != null && idx < list.size()) ? list.get(idx) : "";
    }
    // 工具方法：安全获取数组元素
    private static String getArrayValue(String[] arr, int idx) {
        return (arr != null && idx < arr.length) ? arr[idx].trim() : "";
    }
    // 工具方法：去除通道数后缀
    private static String trimSuffix(String s) {
        return (s != null && s.length() > 0) ? s.substring(0, s.length() - 1) : "";
    }
    // 工具方法：安全转int
    private static int parseIntSafe(String s) {
        try { return Integer.parseInt(s); } catch (Exception e) { return 0; }
    }
    // 工具方法：安全转float
    private static float parseFloatSafe(String s) {
        try { return Float.parseFloat(s); } catch (Exception e) { return 0f; }
    }
    // 拆分通道解析
    private static List<ChannelDetailInfo> parseChannelDetailInfoList(List<String> lines, int offset, int totalChannelNum) {
        List<ChannelDetailInfo> list = new ArrayList<>();
        for (int i = 0; i < totalChannelNum; i++) {
            String[] channelInfoList = getLine(lines, offset + i).split(",");
            ChannelDetailInfo channelDetailInfo = new ChannelDetailInfo();
            channelDetailInfo.setChannelIndex(i);
            int length = channelInfoList.length;
            String channelType;
            if (length == 13) {
                channelType = "A";
            } else if (length == 5) {
                channelType = "D";
            } else {
                channelType = null;
            }
            channelDetailInfo.setChannelType(channelType);
            channelDetailInfo.setChannelName(getArrayValue(channelInfoList, 1));
            channelDetailInfo.setPhaseType(getArrayValue(channelInfoList, 2));
            if(channelInfoList.length > 3){
                channelDetailInfo.setCcbm(getArrayValue(channelInfoList, 3));
            }
            if (length == 13) {
                channelDetailInfo.setChannelUnit(getArrayValue(channelInfoList, 4));
                channelDetailInfo.setChannelCof(parseFloatSafe(getArrayValue(channelInfoList, 5)));
                channelDetailInfo.setChannelOffset(parseFloatSafe(getArrayValue(channelInfoList, 6)));
                float denominator = parseFloatSafe(getArrayValue(channelInfoList, 11));
                float numerator = parseFloatSafe(getArrayValue(channelInfoList, 10));
                if (denominator != 0f) {
                    channelDetailInfo.setChannelTransRatio(numerator / denominator);
                } else {
                    channelDetailInfo.setChannelTransRatio(0f); // 或1f，按实际业务需求
                }
                channelDetailInfo.setChannelPs(getArrayValue(channelInfoList, 12));
            }
            list.add(channelDetailInfo);
        }
        return list;
    }
    // 拆分采样率解析
    private static List<SampInfo> parseSampInfoList(List<String> lines, int offset, int ratesNum) {
        List<SampInfo> list = new ArrayList<>();
        for (int i = 0; i < ratesNum; i++) {
            String[] ratesInfoList = getLine(lines, offset + i).split(",");
            SampInfo sampInfo = new SampInfo();
            sampInfo.setSamp(parseFloatSafe(getArrayValue(ratesInfoList, 0)));
            sampInfo.setEndSamp(parseIntSafe(getArrayValue(ratesInfoList, 1)));
            int beginSamp = (i >= 1) ? list.get(i - 1).getEndSamp() : 0;
            sampInfo.setBeginSamp(beginSamp);
            list.add(sampInfo);
        }
        return list;
    }

    /**
     * 提取通道数据
     *
     * @param waveDataInfo 波形解析结果
     * @param channelNames  通道名 兼容多个名
     * @return
     */
    public static List<Float> getChannelData(WaveDataInfo waveDataInfo, String... channelNames) {
        List<ChannelDetailInfo> channelDetailInfoList = waveDataInfo.getChannelDetailInfoList();
        List<KeyValuePair> channelData = channelDetailInfoList.stream()
                .filter(c -> {
                    for (String channelName : channelNames) {
                        if (channelName.equalsIgnoreCase(c.getChannelName())){
                            return Boolean.TRUE;
                        }
                    }
                    return Boolean.FALSE;
                })
                .findFirst().orElse(new ChannelDetailInfo()).getChannelData();
        if (channelData == null) {
            return Collections.emptyList();
        }
        return channelData.stream().map(KeyValuePair::getYField).collect(Collectors.toList());
    }

    private static void readFully(DataInputStream din, byte[] buffer) throws IOException {
        int totalRead = 0;
        int bytesRead;
        while (totalRead < buffer.length && (bytesRead = din.read(buffer, totalRead, buffer.length - totalRead)) != -1) {
            totalRead += bytesRead;
        }
        if (totalRead != buffer.length) {
            throw new IOException("Failed to read expected number of bytes: expected " + buffer.length + ", got " + totalRead);
        }
    }

}

package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;


/**
 * 事件高级原因分析
 *
 * <AUTHOR>
 */
public enum EventCauseType {
    //电压等级
    LIGHTNING_STROKE("雷击", 1),
    LARGE_MOTOR("大电机启动", 2),
    TRANSFORMER_SWITCHING("变压器投切", 3),
    SHORT_CIRCUIT("短路", 4),
    OTHER("其他", 0);

    private String name;
    private Integer value;

    private EventCauseType(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }


    public static String getName(Integer value) {
        for (EventCauseType voltageLevelEnum : EventCauseType.values()) {
            if (value.equals(voltageLevelEnum.getValue())) {
                return voltageLevelEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }



}

package com.cet.pq.anlysis.common.enums.offlinetest.xinjiang;

import com.cet.pq.anlysis.common.constants.OfflineTestSheetName;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum InventoryData {
    //台账数据
    VOLTCLASS(0L, 0, "电压等级", OfflineTestSheetName.VOLTAGE, "C", "6"),
    SHORTCIRCUITCAPACITY(0L, 0, "最小短路容量", OfflineTestSheetName.VOLTAGE, "J", "7"),
    USERPROTOCOLCAPACITY(0L, 0, "用户协议容量", OfflineTestSheetName.VOLTAGE, "C", "8"),
    SUPPLYEQUIPMENTCAPACITY(0L, 0, "供电设备容量", OfflineTestSheetName.VOLTAGE, "J", "8"),

    DATA_TIME(0L, 0, "监测时间", OfflineTestSheetName.VOLTAGE, "J", "4");


    private Long dataId;

    private Integer aggregationType;

    private String dataName;

    private String sheetName;

    private String col;

    private String row;

    private InventoryData(Long dataId, Integer aggregationType, String dataName, String sheetName, String col, String row) {
        this.dataId = dataId;
        this.aggregationType = aggregationType;
        this.dataName = dataName;
        this.sheetName = sheetName;
        this.col = col;
        this.row = row;
    }

    public Long getDataId() {
        return dataId;
    }

    public Integer getAggregationType() {
        return aggregationType;
    }

    public String getDataName() {
        return dataName;
    }

    public String getSheetName() {
        return sheetName;
    }

    public String getCol() {
        return col;
    }

    public String getRow() {
        return row;
    }

}

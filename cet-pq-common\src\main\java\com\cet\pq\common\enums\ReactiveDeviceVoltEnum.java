package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/11/9 17:09
 * @Description
 */
public enum ReactiveDeviceVoltEnum {

    AC35("交流35kV",1),
    AC220("交流220kV",2),
    AC10("交流10kV",3),
    AC6("交流6kV",4),
    ;

    private String key;
    private Integer value;

    private ReactiveDeviceVoltEnum(String key, Integer value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getReactiveDeviceVolt(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        for (ReactiveDeviceVoltEnum reactiveDeviceVoltEnum : ReactiveDeviceVoltEnum.values()) {
            String rakey = reactiveDeviceVoltEnum.key;
            if(rakey.equalsIgnoreCase(key)) {
                return reactiveDeviceVoltEnum.value;
            }
        }
        throw new CommonManagerException("无功设备电压等级枚举值转化异常");
    }

    public static String getNameByValueNoThrows(Integer value) {
        for (ReactiveDeviceVoltEnum reactiveDeviceVoltEnum : ReactiveDeviceVoltEnum.values()) {
            if (reactiveDeviceVoltEnum.value.equals(value)) {
                return reactiveDeviceVoltEnum.key;
            }
        }
        return StringUtils.EMPTY;
    }

}

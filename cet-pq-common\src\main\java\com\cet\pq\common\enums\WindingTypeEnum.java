package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 绕组数量
 * @date 2024/8/26 14:21
 */
public enum WindingTypeEnum {

    TWO_WINDING("2", 2),
    THREE_WINDING("3", 3);

    private String name;
    private Integer id;

    private WindingTypeEnum(String n, Integer i) {
        this.name = n;
        this.id = i;
    }

    public static String validateIdOrThrows(String n) {
        if(Objects.isNull(n)) {
            throw new CommonManagerException("绕组数量匹配错误");
        }
        for(WindingTypeEnum bType : values()) {
            if(bType.getName().equals(n)) {
                return bType.getName();
            }
        }
        throw new CommonManagerException("绕组数量匹配错误");
    }

    public static Integer getIdByName(String n) {
        if(StringUtils.isEmpty(n)) {
            return null;
        }
        for(WindingTypeEnum b : WindingTypeEnum.values()) {
            if(b.getName().equals(n)) {
                return b.getId();
            }
        }
        return null;
    }

    public static String getNameById(Integer d) {
        if(Objects.isNull(d)) {
            return null;
        }
        for(WindingTypeEnum b : WindingTypeEnum.values()) {
            if(b.getId().equals(d)) {
                return b.getName();
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}

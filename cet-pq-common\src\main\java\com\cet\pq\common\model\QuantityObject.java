package com.cet.pq.common.model;

import java.util.List;

/**
 * <AUTHOR>
 */
public class QuantityObject {

    /**
     * modelLabel : project
     * nodes : [{"deviceIds":[1],"id":45,"modelLabel":"project"}]
     */

    private String modelLabel;
    private List<NodesBean> nodes;

    public String getModelLabel() {
        return modelLabel;
    }

    public void setModelLabel(String modelLabel) {
        this.modelLabel = modelLabel;
    }

    public List<NodesBean> getNodes() {
        return nodes;
    }

    public void setNodes(List<NodesBean> nodes) {
        this.nodes = nodes;
    }

    public static class NodesBean {
        /**
         * deviceIds : [1]
         * id : 45
         * modelLabel : project
         */

        private int id;
        private String modelLabel;
        private List<Integer> deviceIds;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getModelLabel() {
            return modelLabel;
        }

        public void setModelLabel(String modelLabel) {
            this.modelLabel = modelLabel;
        }

        public List<Integer> getDeviceIds() {
            return deviceIds;
        }

        public void setDeviceIds(List<Integer> deviceIds) {
            this.deviceIds = deviceIds;
        }
    }
}

package com.cet.pq.pqservice.service.impl;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.ExcelConstant;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.enums.CauseAnalysisEnum;
import com.cet.pq.common.enums.TemplateDataEnum;
import com.cet.pq.common.enums.VoltageLevelEnum;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.feign.DeviceDataService;
import com.cet.pq.common.feign.ModelDataService;
import com.cet.pq.common.handle.SessionHandler;
import com.cet.pq.common.model.*;
import com.cet.pq.common.model.auth.ModelNode;
import com.cet.pq.common.model.event.EventCondition;
import com.cet.pq.common.model.event.EventLogVo;
import com.cet.pq.common.model.event.EventOrder;
import com.cet.pq.common.model.excel.ExportParameter;
import com.cet.pq.common.model.excel.Sheets;
import com.cet.pq.common.model.wave.ChannelDetailInfo;
import com.cet.pq.common.model.wave.WaveDataInfo;
import com.cet.pq.common.model.wave.WaveFileObject;
import com.cet.pq.common.model.wave.WaveOperationUtil;
import com.cet.pq.common.time.DateUtils;
import com.cet.pq.common.time.TimeFormat;
import com.cet.pq.common.utils.*;
import com.cet.pq.inventoryservice.common.constants.TableColumnConstants;
import com.cet.pq.inventoryservice.model.company.AreaCommonNode;
import com.cet.pq.inventoryservice.model.line.MonitorUtil;
import com.cet.pq.inventoryservice.service.PvMonitorService;
import com.cet.pq.pqservice.model.LineAndCompanyProperty;
import com.cet.pq.pqservice.model.PqVariationEventParams;
import com.cet.pq.pqservice.model.dataQualityManagement.PQVariationevent;
import com.cet.pq.pqservice.model.pqEvent.PqEventStatistic;
import com.cet.pq.pqservice.model.pqEvent.PqVariationEventResult;
import com.cet.pq.pqservice.service.PqEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.cet.pq.common.model.wave.KeyValuePair;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.cet.pq.pqservice.constant.Constants.ONE_THOUSAND;

/**
 * <AUTHOR> Deng
 * @description 电能质量事件基础服务实现类
 * @date 2020/11/12 14:49
 */
@Component
@Slf4j
public class PqEventServiceImpl implements PqEventService {
    /**
     * 模型服务
     */
    @Autowired
    private ModelDataService modelServices;
    /**
     * 设备数据服务
     */
    @Autowired
    private DeviceDataService deviceDataService;
    @Autowired
    private PvMonitorService pvMonitorService;

    private static final List<String> U1 = Arrays.asList("U1", "UA","保护电压A相");
    private static final List<String> U2 = Arrays.asList("U2", "UB","保护电压B相");
    private static final List<String> U3 = Arrays.asList("U3", "UC","保护电压C相");
    /**
     * 获取事件列表通用接口
     */
    @Override
    public PageResult<List<PqVariationEventResult>> getPqVariationEvent(PqVariationEventParams params) {


        Integer pageNum = params.getPageNum();
        Integer pageSize = params.getPageSize();
        Page page = getPageForPqVariationEvent(pageSize, pageNum);

        PageResult pageResult = new PageResult();
        pageResult.setPageSize(pageSize);
        pageResult.setPageNum(pageNum);
        pageResult.setData(new ArrayList<>());

        //查询故障录波事件
        if(!params.getPqVariationEventType().isEmpty()
                && params.getPqVariationEventType().size() == 1
                && Integer.valueOf(5).equals(params.getPqVariationEventType().get(0))) {
            processFaultWaveEvent(params, pageResult);
            return pageResult;
        }

        List<Long> monitorIdList = params.getMonitoredId();
        // 获取监测点
        List<Map<String, Object>> lineMapList;
        //
        if (CollectionUtils.isEmpty(monitorIdList)) {
            Long modelId = params.getModelId();
            String modelLabel = params.getModelLabel();
            if (Objects.isNull(modelId) || StringUtils.isEmpty(modelLabel)) {
                ModelNode userModelNode = SessionHandler.getUserModel();
                modelId = userModelNode.getId();
                modelLabel = userModelNode.getModelLabel();
            }
            //查询登录用户所属区域
            if (StringUtils.isEmpty(modelLabel) || Objects.isNull(modelId)) {
                ModelNode userModel = SessionHandler.getUserModel();
                modelLabel = userModel.getModelLabel();
                modelId = userModel.getId();
            } else {
                AuthUtils.checkAuth(modelId, modelLabel, true);
            }
            // 没有监测点列表从modelLabel取，按条件匹配
            lineMapList = getLineData(modelLabel, modelId, params.getMonitorType(), null, params.getLineNameLike(), params.getLineVoltage());
        } else {
            // 获取监测点的变电站及上级单位组成集合
            List<SingleModelConditionDTO> subConditions = Arrays.asList(
                    new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.NAME, ColumnName.VOLTCLASS)),
                    new SingleModelConditionDTO(TableName.CITYCOMPANY),
                    new SingleModelConditionDTO(TableName.COUNTYCOMPANY),
                    new SingleModelConditionDTO(TableName.PROVINCECOMPANY));
            lineMapList = ModelServiceUtils.getInterRelations(monitorIdList, TableName.LINE, null, null, subConditions, false, null, null,
                    Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.VOLTCLASS)).getData();
        }


        List<Long> lineIdList = lineMapList.stream().map(line -> ParseDataUtil.parseLong(line.get(ColumnName.ID))).collect(Collectors.toList());
        params.setMonitoredId(lineIdList);
        ResultWithTotal<List<PqVariationEventResult>> result = getVariationEventList(params, page);
        List<PqVariationEventResult> eventList = result.getData();
        if (CollectionUtils.isEmpty(eventList)) {
            return pageResult;
        }
        // 将树结构扁平化处理，得到监测点和上级单位的集合
        Map<Long, AreaCommonNode> areaCommonNodeMap = new HashMap<>();
        for (Map<String, Object> map : lineMapList) {
            Long lineId = ParseDataUtil.parseLong(map.get(ColumnName.ID));
            AreaCommonNode acn = new AreaCommonNode();
            MonitorUtil.setAreaTreeNodeAttr(acn, map);
            areaCommonNodeMap.put(lineId, acn);
        }
        Map<Long, Map<String, Object>> lineMapMap = lineMapList.stream().collect(Collectors.toMap(map -> ParseDataUtil.parseLong(map.get(ColumnName.ID)), map -> map, (k1, k2)->k2));
        List<PqVariationEventResult> resultList = new ArrayList<>();
        for (PqVariationEventResult m1 : eventList) {
            AreaCommonNode s = areaCommonNodeMap.get(m1.getMonitoredid());
            if (s != null) {
                //设置管理单位字段
                m1.setSubstationId(s.getSubstationid());
                m1.setSubstationName(s.getSubstationname());
                m1.setCitycompanyId(s.getCitycompanyid());
                m1.setCitycompanyName(s.getCitycompanyname());
                m1.setProvincecompanyId(s.getProvincecompanyid());
                m1.setProvincecompanyName(s.getProvincecompanyname());
            }

            Map<String, Object> lineMap = lineMapMap.getOrDefault(m1.getMonitoredid(), new HashMap<>());
            //设置监测点和变电站字段
            if (lineMap != null) {
                m1.setLineId(ParseDataUtil.parseLong(lineMap.get(ColumnName.ID)));
                m1.setLineName(ParseDataUtil.parseString(lineMap.get(ColumnName.NAME)));
                m1.setLinevoltageclass(ParseDataUtil.parseInteger(lineMap.get(ColumnName.VOLTCLASS)));
                List<Map<String, Object>> substationMapList = ParseDataUtil.parseList(lineMap.get(TableName.SUBSTATION + TableName.PREFIX));
                if (CollectionUtils.isNotEmpty(substationMapList)) {
                    Map<String, Object> substationMap = substationMapList.get(0);
                    m1.setSubstationvoltageclass(ParseDataUtil.parseInteger(substationMap.get(ColumnName.VOLTCLASS)));
                    m1.setSubstationName(ParseDataUtil.parseString(substationMap.get(ColumnName.NAME)));
                    m1.setSubstationvoltageclassText(ParseDataUtil.parseString(substationMap.get(ColumnName.VOLTCLASS_TEXT)));
                }
            }

            m1.setMagnitude(CommonUtils.formatNumber(m1.getMagnitude(), 2));
            m1.setV1magnitude(CommonUtils.formatNumber(m1.getV1magnitude(), 2));
            m1.setV2magnitude(CommonUtils.formatNumber(m1.getV2magnitude(), 2));
            m1.setV3magnitude(CommonUtils.formatNumber(m1.getV3magnitude(), 2));
            m1.setReasonText(CauseAnalysisEnum.getNameById(m1.getReason()));
            resultList.add(m1);
        }
        pageResult.setTotal(result.getTotal());
        pageResult.setData(eventList);
        return pageResult;

    }

    private void processFaultWaveEvent(PqVariationEventParams params, PageResult pageResult) {
        long s = System.currentTimeMillis();
        List<EventLogVo> faultWaveEventList = queryEventLogs(params.getStartTime(),  params.getEndTime());
        long e = System.currentTimeMillis();
        log.info((e-s) +"ms");
        List<PQVariationevent> faultWaveList = parseFaultWaveEvent(faultWaveEventList);
        List<PqVariationEventResult> eventList = new ArrayList<>();
        faultWaveList.forEach(faultWave -> {
            PqVariationEventResult event = new PqVariationEventResult();
            BeanUtils.copyProperties(faultWave, event);
            event.setSrcdeviceid(faultWave.getMonitoredid());
            event.setDuration(faultWave.getDuration().longValue());
            event.setMagnitude(faultWave.getMagnitude().doubleValue());
            event.setWaveformlogtime(faultWave.getEventtime());
            String sameLineName = parseLineInfo(faultWave.getDescription());
            if(Objects.isNull(sameLineName)){
                sameLineName = faultWave.getDescription();
            }
            String sameEventStationName = extractSubstationName(faultWave.getDescription());
            event.setLineName(sameLineName);
            event.setSubstationName(sameEventStationName);
            eventList.add(event);
        });
        pageResult.setTotal(eventList.size());
        List<PqVariationEventResult> newEventList = eventList;
        if (Objects.nonNull(pageResult.getPageNum()) && Objects.nonNull(pageResult.getPageSize())) {
            newEventList = CommonUtils.getListPaging(newEventList, params.getPageNum(), params.getPageSize());
        }
        pageResult.setData(newEventList);
    }

    public static String extractSubstationName(String fileName) {
        // 假设文件名格式为：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
        String[] parts = fileName.split("_");

        if (parts.length >= 3) {
            // 返回变电站名称
            return parts[2];
        } else {
            return "";
        }
    }

    public static String parseLineInfo(String fileName) {
        // 定义正则表达式，匹配电压等级 + 线路名称
        String regex = "\\d+kV[^_]+"; // 匹配数字 + "kV" + 非下划线字符
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(fileName);

        // 查找匹配的线路信息
        if (matcher.find()) {
            return matcher.group(); // 返回匹配的线路信息
        }

        return null; // 如果未找到，返回 null
    }

    public List<PQVariationevent> parseFaultWaveEvent(List<EventLogVo> faultWaveEventList) {
        if (org.springframework.util.CollectionUtils.isEmpty(faultWaveEventList)) {
            return new ArrayList<>();
        }
        List<PQVariationevent> result = new ArrayList<>();
        faultWaveEventList.forEach(event -> {
            PQVariationevent pqVariationevent = new PQVariationevent();
            pqVariationevent.setEventtime(event.getEventTime() + event.getMsec());
            pqVariationevent.setMonitoredid(event.getDeviceId());
            pqVariationevent.setId(0L);
            log.info("事件描述为: {}", event.getDescription());
            pqVariationevent.setDescription(event.getDescription());
            //解析持续时间
            try {
                calculateWaveDuration(event.getDeviceId(), pqVariationevent.getEventtime(), pqVariationevent);
                log.info("持续时间为: {}ms, 幅值为: {}%", pqVariationevent.getDuration(), pqVariationevent.getMagnitude());
            } catch (Exception e) {
                log.error("解析波形持续时间失败,device is {},eventtime is {}", event.getDeviceId(), pqVariationevent.getEventtime(),e);
                if(Objects.isNull(pqVariationevent.getDuration())){
                    pqVariationevent.setDuration(0L);
                }
                if(Objects.isNull(pqVariationevent.getMagnitude())){
                    pqVariationevent.setMagnitude(0f);
                }
            }
            result.add(pqVariationevent);
        });
        return result;
    }

    private void calculateWaveDuration(Long deviceId, Long eventTime, PQVariationevent pqVariationevent) throws UnsupportedEncodingException {
        /*byte[] waveBytes = fetchWaveBytes(deviceId, eventTime);
        WaveFileObject waveFileObject = WaveOperationUtil.loadWaveData(waveBytes);
        WaveDataInfo waveDataInfo = WaveOperationUtil.parseWaveDataInfo(waveFileObject.getCfgFile(), waveFileObject.getDatFile());*/

        byte[] cfgBytes = readFileByBytes("E:\\work\\深圳国重项目\\国重项目资料\\真实录波文件\\demo驱动波形\\110kV故障录波器WDGL-VI(2025-07-25 18_15_04.294)_1753438504294.cfg");
        byte[] datBytes = readFileByBytes("E:\\work\\深圳国重项目\\国重项目资料\\真实录波文件\\demo驱动波形\\110kV故障录波器WDGL-VI(2025-07-25 18_15_04.294)_1753438504294.dat");
        WaveFileObject waveFileObject = new WaveFileObject(null,cfgBytes, datBytes);
        WaveDataInfo waveDataInfo = WaveOperationUtil.parseWaveDataInfo(waveFileObject.getCfgFile(), waveFileObject.getDatFile());


        List<Double> channelAData = getChannelData(waveDataInfo, U1);
        List<Double> channelBData = getChannelData(waveDataInfo, U2);
        List<Double> channelCData = getChannelData(waveDataInfo, U3);

        List<List<Double>> waveformData = Arrays.asList(channelAData, channelBData, channelCData);

        // 提取采样率
        double samplingRate = waveDataInfo.getSampInfoList().get(0).getSamp();
        // 每周波的点数
        int windowSize = (int) (samplingRate / waveDataInfo.getFrequency());
        List<List<Double>> rmsValuesList = new ArrayList<>();
        for (List<Double> phaseData : waveformData) {
            List<Double> rmsValues = new ArrayList<>();
            for (int i = 0; i < phaseData.size(); i += windowSize) {
                rmsValues.add(calculateRMS(phaseData, i, windowSize));
            }
            rmsValuesList.add(rmsValues);
        }
        // 使用波形文件中的触发时间
        String triggerTimeStr = waveDataInfo.getTriggerTime();

        // 计算触发点在RMS数组中的索引
        int triggerIndex = calculateTriggerIndex(triggerTimeStr, waveDataInfo, windowSize);
        // 临时使用简单的额定电压计算
        double nominalVoltage = getSimpleNominalVoltage(rmsValuesList, triggerIndex,null);

        // 检测事件结束点和计算幅值
        EventResult eventResult = analyzeEventFromTrigger(rmsValuesList, triggerIndex, nominalVoltage);

        // 计算持续时间（毫秒）
        double duration = calculateDurationFromTrigger(triggerIndex, eventResult.endIndex, windowSize, samplingRate) * 1000;

        // 如果持续时间为0，通过结束时间-触发时间来重新计算
        if (duration == 0.0) {
            try {
                // 使用第一个通道的数据长度作为总采样点数
                int totalSamples = channelAData.size();
                long endTime = calculateEventEndTime(triggerTimeStr, eventResult.endIndex, triggerIndex, windowSize, samplingRate, totalSamples, waveDataInfo);
                Date startTime = getWaveStartTime(waveDataInfo);
                long waveStartTime = startTime.getTime();

                if (endTime > waveStartTime) {
                    duration = endTime - waveStartTime;
                    log.info("持续时间为0，通过结束时间重新计算: {}ms", duration);
                }
            } catch (Exception e) {
                log.error("通过结束时间计算持续时间失败: {}", triggerTimeStr, e);
            }
        }

        pqVariationevent.setDuration((long)duration);

        // 如果幅值为0，尝试使用备用方法重新计算
        double magnitude = eventResult.magnitude;
        if (magnitude == 0.0) {
            try {
                magnitude = calculateAlternativeMagnitude(rmsValuesList, triggerIndex, nominalVoltage, waveformData, samplingRate);
                log.info("幅值为0，使用备用方法重新计算: {}%", magnitude);
            } catch (Exception e) {
                log.error("备用幅值计算失败", e);
            }
        }

        pqVariationevent.setMagnitude((float)magnitude);
    }

    public static byte[] readFileByBytes(String filePath) {
        File file = new File(filePath);
        InputStream inputStream = null;
        byte[] filebyte = null;
        try {
            inputStream = new FileInputStream(file);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
            /**
             * 读入文件的字节长度
             */
            long filelength = file.length();
            if (filelength > Integer.MAX_VALUE) {
                return filebyte;
            }
            filebyte = new byte[(int) filelength];
            try {
                int offset = 0;
                int numRead = 0;
                while (offset < filebyte.length && (numRead = bufferedInputStream.read(filebyte, offset, filebyte.length - offset)) >= 0) {
                    offset += numRead;
                }
                if (offset < filebyte.length) {
                    throw new IOException("Could not completely read file " + file.getName());
                }

            } catch (IOException e) {
            }
        } catch (FileNotFoundException e) {
        }
        return filebyte;
    }

    /**
     * 计算有效值（RMS）
     *
     * @param data       波形数据
     * @param startIndex 起始索引
     * @param windowSize 窗口大小（一个周期内的采样点数）
     * @return 有效值
     */
    public static double calculateRMS(List<Double> data, int startIndex, int windowSize) {
        double sum = 0;
        for (int i = startIndex; i < startIndex + windowSize && i < data.size(); i++) {
            sum += Math.pow(data.get(i), 2);
        }
        return Math.sqrt(sum / windowSize);
    }


    /**
     * 事件分析结果类
     */
    public static class EventResult {
        public static int endIndex = -1;
        public static double magnitude = 0;
    }

    /**
     * 根据triggerTime字符串计算在RMS数组中的索引位置
     * triggerTime格式: "02/03/2025,11:23:39.911000" (日/月/年)
     */
    public static int calculateTriggerIndex(String triggerTimeStr, WaveDataInfo waveDataInfo, int windowSize) {
        try {
            // 解析触发时间 - 注意：Java的SimpleDateFormat最多支持3位毫秒，需要特殊处理6位微秒
            Date triggerDate = parseTimeWithMicroseconds(triggerTimeStr);

            // 获取波形开始时间
            Date waveStartTime = getWaveStartTime(waveDataInfo);

            // 计算时间差（毫秒）
            long timeDiffMs = triggerDate.getTime() - waveStartTime.getTime();
            // 转换为秒
            double timeDiffSeconds = timeDiffMs / 1000.0;
            // 计算采样点索引
            double samplingRate = waveDataInfo.getSampInfoList().get(0).getSamp();
            int triggerSampleIndex = (int)(timeDiffSeconds * samplingRate);
            // 转换为RMS索引
            int triggerIndex = triggerSampleIndex / windowSize -1;
            // 计算总的RMS点数，用于边界检查
            int totalSamples = (int)(samplingRate * 10); // 假设最大10秒波形
            int maxRmsIndex = totalSamples / windowSize;
            // 边界检查
            if (triggerIndex < 0) {
                log.warn("计算的触发索引为负数: {}, 设置为0", triggerIndex);
                triggerIndex = 0;
            } else if (triggerIndex >= maxRmsIndex) {
                log.warn("计算的触发索引超出范围: {} >= {}, 设置为最大值", triggerIndex, maxRmsIndex);
                triggerIndex = Math.max(0, maxRmsIndex - 1);
            }
            return triggerIndex;
        } catch (Exception e) {
            log.error("解析触发时间失败: {}, 使用fallback策略", triggerTimeStr, e);
        }
        return 0;
    }

    /**
     * 获取波形开始时间
     */
    private static Date getWaveStartTime(WaveDataInfo waveDataInfo) {
        try {
            // 使用WaveDataInfo中的startTime字段
            String startTimeStr = waveDataInfo.getStartTime();
            if (startTimeStr != null && !startTimeStr.isEmpty()) {
                return parseTimeWithMicroseconds(startTimeStr);
            }

            log.warn("startTime字段为空，使用当前时间作为fallback");
            return new Date();

        } catch (Exception e) {
            log.error("解析波形开始时间失败: {}", waveDataInfo.getStartTime(), e);
            return new Date(); // 最后的fallback
        }
    }

    /**
     * 改进的触发索引计算方法 - 使用相对位置
     * 如果无法准确计算绝对时间差，可以使用这个方法
     */
    public static int calculateTriggerIndexByPosition(String triggerTimeStr, WaveDataInfo waveDataInfo, int windowSize) {
        try {
            // 获取RMS数组的总长度
            double samplingRate = waveDataInfo.getSampInfoList().get(0).getSamp();
            int totalSamples = (int) samplingRate * 2; // 假设波形总长度2秒
            int totalRmsPoints = totalSamples / windowSize;

            // 简单策略：假设触发点在波形的特定位置
            // 可以根据实际情况调整这个比例
            double triggerPositionRatio = 0.3; // 假设触发点在30%的位置
            int triggerIndex = (int)(totalRmsPoints * triggerPositionRatio);

            log.info("使用位置估算 - 触发时间: {}, 总RMS点数: {}, 触发索引: {} ({}%位置)",
                    triggerTimeStr, totalRmsPoints, triggerIndex, triggerPositionRatio * 100);

            return Math.max(0, Math.min(triggerIndex, totalRmsPoints - 1));

        } catch (Exception e) {
            log.error("位置估算触发索引失败: {}", triggerTimeStr, e);
            return 0;
        }
    }


    /**
     * 计算额定电压计算方法
     */
    public static double getSimpleNominalVoltage(List<List<Double>> rmsValuesList, int triggerIndex,Integer volt) {
        // 取触发点前3个点的平均值作为额定电压
        if(Objects.nonNull(volt)){
            return volt * 1000 / 1.732;
        }
        int startIndex = Math.max(0, triggerIndex - 3);
        int endIndex = triggerIndex;

        double sum = 0;
        int count = 0;

        for (int phase = 0; phase < rmsValuesList.size(); phase++) {
            for (int i = startIndex; i < endIndex && i < rmsValuesList.get(phase).size(); i++) {
                sum += rmsValuesList.get(phase).get(i);
                count++;
            }
        }
        return count > 0 ? sum / count : 5773;
    }

    /**
     * 从触发点开始分析事件
     */
    public static EventResult analyzeEventFromTrigger(List<List<Double>> rmsValuesList, int triggerIndex, double nominalVoltage) {
        EventResult result = new EventResult();

        if (triggerIndex < 0 || triggerIndex >= rmsValuesList.get(0).size()) {
            log.warn("触发索引超出范围: {}", triggerIndex);
            return result;
        }
        // 找到触发点的最小/最大RMS值来确定事件类型
        double triggerRms = Double.MAX_VALUE;
        double maxRms = Double.MIN_VALUE;
        int triggerPhase = 0;

        for (int phase = 0; phase < rmsValuesList.size(); phase++) {
            double rms = rmsValuesList.get(phase).get(triggerIndex);
            if (rms < triggerRms) {
                triggerRms = rms;
                triggerPhase = phase;
            }
            if (rms > maxRms) {
                maxRms = rms;
            }
        }
        result = calculateEventBasedOnExpectedDuration(rmsValuesList, triggerIndex, triggerPhase, nominalVoltage);
        return result;
    }

    /**
     * 智能事件边界检测
     * 基于RMS数据的实际变化来确定事件范围
     */
    public static EventResult calculateEventBasedOnExpectedDuration(List<List<Double>> rmsValuesList,
                                                                   int triggerIndex,
                                                                   int triggerPhase,
                                                                   double nominalVoltage) {
        EventResult result = new EventResult();

        if (triggerIndex < 0 || triggerIndex >= rmsValuesList.get(0).size()) {
            return result;
        }

        List<Double> phaseData = rmsValuesList.get(triggerPhase);
        double triggerRms = phaseData.get(triggerIndex);

        // 使用正确的暂降结束标准：90%额定电压
        double sagRecoveryThreshold = nominalVoltage * 0.9; // 90%额定电压
        // 向前搜索事件开始点
        int eventStartIndex = triggerIndex;
        // 向后搜索暂降事件结束点 - 使用90%额定电压标准
        int eventEndIndex = findEventEndIndex(triggerIndex,phaseData,sagRecoveryThreshold);
        // 确保事件至少持续1个RMS点
        if (eventEndIndex == triggerIndex) {
            eventEndIndex = Math.min(triggerIndex + 1, phaseData.size() - 1);
            log.info("事件至少持续1个RMS点，结束索引调整为: {}", eventEndIndex);
        }
        // 在事件范围内寻找极值
        double extremeValue = triggerRms;
        boolean isSag = triggerRms < nominalVoltage;

        for (int i = eventStartIndex; i <= eventEndIndex; i++) {
            double rms = phaseData.get(i);
            if (isSag) {
                if (rms < extremeValue) extremeValue = rms;
            } else {
                if (rms > extremeValue) extremeValue = rms;
            }
        }

        result.endIndex = eventEndIndex;
        result.magnitude = CommonUtils.formatNumber(extremeValue / nominalVoltage * 100,2);
        return result;
    }

    private static int findEventEndIndex(int triggerIndex, List<Double> phaseData, double sagRecoveryThreshold) {
        int eventEndIndex = triggerIndex;
        for (int i = triggerIndex + 1; i < phaseData.size(); i++) {
            double rms = phaseData.get(i);
            // 检查RMS值是否恢复到90%额定电压以上
            if (rms >= sagRecoveryThreshold) {
                eventEndIndex = i - 1; // 结束点是恢复前的最后一个点
                break;
            } else {
                // RMS值仍然低于90%额定电压，事件继续
                eventEndIndex = i;
            }
        }
        return eventEndIndex;
    }

    /**
     * 正确解析包含6位微秒的时间字符串
     * 格式: "02/03/2025,11:23:39.911000" (日/月/年)
     */
    private static Date parseTimeWithMicroseconds(String timeStr) throws Exception {
        // 分离日期时间部分和微秒部分
        // 格式: dd/MM/yyyy,HH:mm:ss.SSSSSS
        String[] parts = timeStr.split("\\.");
        if (parts.length != 2) {
            throw new IllegalArgumentException("时间格式错误: " + timeStr);
        }

        String dateTimePart = parts[0]; // "02/03/2025,11:23:39"
        String microsecondPart = parts[1]; // "911000"

        // 解析日期时间部分（精确到秒）- 使用日/月/年格式
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy,HH:mm:ss");
        Date baseDate = sdf.parse(dateTimePart);

        // 解析微秒部分并转换为毫秒
        long microseconds = Long.parseLong(microsecondPart);
        long milliseconds = microseconds / 1000; // 转换为毫秒

        // 创建最终的时间
        return new Date(baseDate.getTime() + milliseconds);
    }


    /**
     * 基于触发点计算持续时间 - 改进版本
     */
    public static double calculateDurationFromTrigger(int triggerIndex, int endIndex, int windowSize, double samplingRate) {
        if (triggerIndex == -1 || endIndex == -1 || endIndex <= triggerIndex) {
            log.warn("无效的索引范围: 触发点={}, 结束点={}", triggerIndex, endIndex);
            return 0.0;
        }

        // 将RMS索引转换为原始采样点索引
        // 触发点：RMS窗口的开始位置
        int triggerSampleIndex = triggerIndex * windowSize;

        // 结束点：RMS窗口的结束位置（而不是开始位置）
        int endSampleIndex = (endIndex + 1) * windowSize - 1;
        // 计算持续时间（秒）
        return  (endSampleIndex - triggerSampleIndex + 1) / samplingRate;
    }

    /**
     * 计算事件结束时间戳
     * @param triggerTimeStr 触发时间字符串
     * @param endIndex 结束点索引
     * @param triggerIndex 触发点索引
     * @param windowSize 窗口大小
     * @param samplingRate 采样率
     * @param totalSamples 总采样点数（从通道数据获取）
     * @param waveDataInfo 波形数据信息
     * @return 结束时间戳（毫秒）
     */
    private static long calculateEventEndTime(String triggerTimeStr, int endIndex, int triggerIndex,
                                            int windowSize, double samplingRate, int totalSamples, WaveDataInfo waveDataInfo) throws Exception {
        // 解析触发时间
        Date triggerTime = parseTimeWithMicroseconds(triggerTimeStr);
        long triggerTimestamp = triggerTime.getTime();

        // 如果结束索引无效，使用通道数据的总长度来估算结束时间
        if (endIndex == -1 || endIndex <= triggerIndex) {
            // 使用通道数据的总采样点数来计算录波总时长
            double totalDurationSeconds = totalSamples / samplingRate;
            long totalDurationMs = (long)(totalDurationSeconds * 1000);

            // 使用WaveDataInfo中的真实录波起始时间
            Date waveStartTime = getWaveStartTime(waveDataInfo);
            long waveStartTimestamp = waveStartTime.getTime();

            // 结束时间 = 录波起始时间 + 录波总时长
            return waveStartTimestamp + totalDurationMs;
        }

        // 计算从触发点到结束点的时间偏移
        double durationSeconds = calculateDurationFromTrigger(triggerIndex, endIndex, windowSize, samplingRate);
        long durationMs = (long)(durationSeconds * 1000);

        return triggerTimestamp + durationMs;
    }

    /**
     * 备用幅值计算方法
     * 当主要计算方法返回0时，使用多种备用方法尝试计算幅值
     * @param rmsValuesList RMS值列表
     * @param triggerIndex 触发点索引
     * @param nominalVoltage 额定电压
     * @param waveformData 原始波形数据
     * @param samplingRate 采样率
     * @return 计算得到的幅值（百分比）
     */
    private static double calculateAlternativeMagnitude(List<List<Double>> rmsValuesList, int triggerIndex,
                                                      double nominalVoltage, List<List<Double>> waveformData,
                                                      double samplingRate) {

        // 方法1: 使用触发点附近的最小/最大RMS值
        double magnitude1 = calculateMagnitudeAroundTrigger(rmsValuesList, triggerIndex, nominalVoltage);
        if (magnitude1 > 0) {
            log.info("备用方法1成功计算幅值: {}%", magnitude1);
            return magnitude1;
        }

        // 方法2: 使用全局最小/最大RMS值
        double magnitude2 = calculateGlobalExtremeMagnitude(rmsValuesList, nominalVoltage);
        if (magnitude2 > 0) {
            log.info("备用方法2成功计算幅值: {}%", magnitude2);
            return magnitude2;
        }

        // 方法3: 使用原始波形数据计算瞬时幅值
        double magnitude3 = calculateInstantaneousMagnitude(waveformData, triggerIndex, nominalVoltage, samplingRate);
        if (magnitude3 > 0) {
            log.info("备用方法3成功计算幅值: {}%", magnitude3);
            return magnitude3;
        }

        // 方法4: 使用触发点前后的RMS变化率
        double magnitude4 = calculateMagnitudeByVariation(rmsValuesList, triggerIndex, nominalVoltage);
        if (magnitude4 > 0) {
            log.info("备用方法4成功计算幅值: {}%", magnitude4);
            return magnitude4;
        }

        log.warn("所有备用幅值计算方法都失败，返回0");
        return 0.0;
    }

    /**
     * 方法1: 计算触发点附近的极值幅值
     */
    private static double calculateMagnitudeAroundTrigger(List<List<Double>> rmsValuesList, int triggerIndex, double nominalVoltage) {
        try {
            // 在触发点前后各3个点的范围内寻找极值
            int searchRange = 3;
            int startIndex = Math.max(0, triggerIndex - searchRange);
            int endIndex = Math.min(rmsValuesList.get(0).size() - 1, triggerIndex + searchRange);

            double extremeValue = 0;
            boolean foundExtreme = false;

            for (List<Double> phaseData : rmsValuesList) {
                for (int i = startIndex; i <= endIndex; i++) {
                    double rms = phaseData.get(i);
                    double deviation = Math.abs(rms - nominalVoltage) / nominalVoltage;

                    // 寻找偏差最大的点
                    if (deviation > 0.05) { // 偏差超过5%才认为是有效事件
                        if (!foundExtreme || Math.abs(rms - nominalVoltage) > Math.abs(extremeValue - nominalVoltage)) {
                            extremeValue = rms;
                            foundExtreme = true;
                        }
                    }
                }
            }

            return foundExtreme ? CommonUtils.formatNumber(extremeValue / nominalVoltage * 100, 2) : 0.0;
        } catch (Exception e) {
            log.error("方法1计算失败", e);
            return 0.0;
        }
    }

    /**
     * 方法2: 计算全局极值幅值
     */
    private static double calculateGlobalExtremeMagnitude(List<List<Double>> rmsValuesList, double nominalVoltage) {
        try {
            double minRms = Double.MAX_VALUE;
            double maxRms = Double.MIN_VALUE;

            for (List<Double> phaseData : rmsValuesList) {
                for (double rms : phaseData) {
                    if (rms < minRms) minRms = rms;
                    if (rms > maxRms) maxRms = rms;
                }
            }

            // 选择偏离额定值更远的那个作为特征幅值
            double sagMagnitude = minRms / nominalVoltage * 100;
            double swellMagnitude = maxRms / nominalVoltage * 100;

            // 判断是暂降还是暂升
            if (Math.abs(100 - sagMagnitude) > Math.abs(swellMagnitude - 100)) {
                return CommonUtils.formatNumber(sagMagnitude, 2);
            } else {
                return CommonUtils.formatNumber(swellMagnitude, 2);
            }
        } catch (Exception e) {
            log.error("方法2计算失败", e);
            return 0.0;
        }
    }

    /**
     * 方法3: 使用原始波形数据计算瞬时幅值
     */
    private static double calculateInstantaneousMagnitude(List<List<Double>> waveformData, int triggerIndex,
                                                        double nominalVoltage, double samplingRate) {
        try {
            // 计算每周波的采样点数
            int windowSize = (int) (samplingRate / 50.0); // 假设50Hz

            // 计算触发点对应的原始数据索引
            int triggerSampleIndex = triggerIndex * windowSize;

            double extremeValue = 0;
            boolean foundExtreme = false;

            for (List<Double> phaseData : waveformData) {
                if (triggerSampleIndex < phaseData.size()) {
                    // 在触发点附近一个周波内寻找峰值
                    int startIdx = Math.max(0, triggerSampleIndex - windowSize/2);
                    int endIdx = Math.min(phaseData.size() - 1, triggerSampleIndex + windowSize/2);

                    for (int i = startIdx; i <= endIdx; i++) {
                        double instantValue = Math.abs(phaseData.get(i));
                        if (!foundExtreme || instantValue > Math.abs(extremeValue)) {
                            extremeValue = instantValue;
                            foundExtreme = true;
                        }
                    }
                }
            }

            if (foundExtreme) {
                // 将瞬时值转换为RMS值（峰值/√2）
                double rmsValue = extremeValue / Math.sqrt(2);
                return CommonUtils.formatNumber(rmsValue / nominalVoltage * 100, 2);
            }

            return 0.0;
        } catch (Exception e) {
            log.error("方法3计算失败", e);
            return 0.0;
        }
    }

    /**
     * 方法4: 基于RMS变化率计算幅值
     */
    private static double calculateMagnitudeByVariation(List<List<Double>> rmsValuesList, int triggerIndex, double nominalVoltage) {
        try {
            if (triggerIndex <= 0 || triggerIndex >= rmsValuesList.get(0).size() - 1) {
                return 0.0;
            }

            double maxVariation = 0;
            double correspondingRms = nominalVoltage;

            for (List<Double> phaseData : rmsValuesList) {
                // 计算触发点前后的变化率
                double beforeRms = phaseData.get(triggerIndex - 1);
                double triggerRms = phaseData.get(triggerIndex);
                double afterRms = phaseData.get(triggerIndex + 1);

                // 计算最大变化率
                double variation1 = Math.abs(triggerRms - beforeRms);
                double variation2 = Math.abs(afterRms - triggerRms);

                double maxPhaseVariation = Math.max(variation1, variation2);
                if (maxPhaseVariation > maxVariation) {
                    maxVariation = maxPhaseVariation;
                    correspondingRms = triggerRms;
                }
            }

            // 如果变化率足够大，认为是有效事件
            if (maxVariation > nominalVoltage * 0.05) { // 变化超过5%额定值
                return CommonUtils.formatNumber(correspondingRms / nominalVoltage * 100, 2);
            }

            return 0.0;
        } catch (Exception e) {
            log.error("方法4计算失败", e);
            return 0.0;
        }
    }

    private byte[] fetchWaveBytes(Long deviceId, Long eventTime) {
        List<String> data = deviceDataService.queryWaveByDeviceId(deviceId, eventTime).getData();
        if(org.springframework.util.CollectionUtils.isEmpty(data)){
            log.error("未查询到波形数据,deviceId is {},eventTime is {}",deviceId,eventTime);
            throw new CommonManagerException("未查询到事件对应波形数据");
        }
        return Base64.getDecoder().decode(data.get(0));
    }

    public static double[][] calculateRMS(List<double[]> daa, int w, int n, int[] indices) {
        double[][] rms = new double[n][indices.length]; // 有效值数组

        // 遍历三相
        for (int i = 0; i < indices.length; i++) {
            // 计算有效值
            for (int k = w - 1; k < n; k++) {
                double sum = 0;
                for (int j = k - w + 1; j <= k; j++) {
                    sum += Math.pow(daa.get(j)[indices[i]], 2); // 平方和
                }
                rms[k][i] = Math.sqrt(sum / w); // 计算 RMS
            }

            // 填充前 w-1 个点的有效值
            for (int k = 0; k < w - 1; k++) {
                rms[k][i] = rms[w - 1][i];
            }
        }

        return rms;
    }

    /**
     * 计算给定数组的均方根（RMS）值。
     *
     * @param values 输入的Double数组
     * @return 计算得到的RMS值
     */
    public static double calculateRMS(List<Double> values) {
        if (values == null || values.isEmpty()) {
            throw new CommonManagerException("输入列表不能为空或长度为0");
        }

        double sumOfSquares = 0.0;
        for (Double value : values) {
            if (value == null) {
                throw new CommonManagerException("列表中不能包含null值");
            }
            sumOfSquares += value * value;
        }

        double meanSquare = sumOfSquares / values.size();
        return CommonUtils.formatNumber(Math.sqrt(meanSquare), 3);
    }



    public static List<Double> getChannelData(WaveDataInfo waveDataInfo, List<String> channelNameList) {
        List<ChannelDetailInfo> channelDetailInfoFilter = waveDataInfo.getChannelDetailInfoList().stream()
                .filter(channel -> channelNameList.stream()
                        .anyMatch(name -> channel.getChannelName().toLowerCase().contains(name.toLowerCase())))
                .collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(channelDetailInfoFilter)) {
            log.info("未找到通道:{}的数据", channelNameList);
            throw new CommonManagerException("未找到通道数据");
        }
        List<Float> collect = channelDetailInfoFilter.get(0).getChannelData().stream().map(KeyValuePair::getYField).collect(Collectors.toList());
        return collect.stream().map(x -> Double.parseDouble(String.valueOf(x))).collect(Collectors.toList());
    }



    /**
     * @deprecated 使用 calculateEventMagnitude 替代
     */
    @Deprecated
    public static float calculateMinRMS(List<List<Double>> rmsValuesList, int triggerIndex, int endIndex, int[] triggerPhaseIndex) {
        // 获取触发相的有效值列表
        List<Double> rmsValues = rmsValuesList.get(triggerPhaseIndex[0]);

        // 使用 Stream API 找到最小值
        Optional<Double> min = rmsValues.subList(triggerIndex, endIndex + 1)
                .stream()
                .min(Double::compare);

        // 返回最小值，如果列表为空则返回 0f
        return min.orElse(0D).floatValue();
    }



    private List<EventLogVo> queryEventLogs(Long startTime, Long endTime) {
        EventCondition eventCondition = genEventCondition(Arrays.asList(0));
        ResultWithTotal<List<EventLogVo>> queryResult = deviceDataService.queryEventData(0, 1000, startTime, endTime, eventCondition);
        ParamUtils.checkResultGeneric(queryResult);
        return queryResult.getData();
    }

    private EventCondition genEventCondition(List<Integer> eventTypeList) {
        EventCondition eventCondition = new EventCondition();
        eventCondition.setEventTypes(eventTypeList);

        List<EventOrder> orders = new ArrayList<>();
        //单时间降序
        EventOrder eventOrder = new EventOrder();
        eventOrder.setFieldName(ColumnName.EVENT_TIME);
        eventOrder.setOrderType("desc");
        orders.add(eventOrder);
        eventCondition.setOrders(org.apache.commons.collections4.CollectionUtils.isEmpty(orders) ? null : orders);
        return eventCondition;
    }

    private Page getPageForPqVariationEvent(Integer pageSize, Integer pageNum) {
        Page page = null;
        if (!Objects.isNull(pageSize) && !Objects.isNull(pageNum)) {
            Integer index = (pageNum - 1) * pageSize;
            page = new Page(index, pageSize);
        }
        return page;
    }

    /**
     * 根据id获取暂态事件明细
     *
     * @param id
     * @return
     */
    @Override
    public Result<List<PqVariationEventResult>> getEventById(Long id) {
        if (Objects.isNull(id)) {
            return Result.success();
        }
        List<PqVariationEventResult> eventList = ModelServiceUtils.getSingleModelByGroup(Collections.singletonList(id), TableName.PQ_VARIATION_EVENT,
                null, null, null, PqVariationEventResult.class);
        Long lineid = eventList.get(0).getMonitoredid();
        if (Objects.nonNull(lineid)) {
            List<Map<String, Object>> newlineList = getLineData(TableColumnConstants.LINE, lineid, null, null, null, null);
            if (CollectionUtils.isNotEmpty(newlineList)) {
                Map<String, Object> lineVo = newlineList.get(0);
                AreaCommonNode s = new AreaCommonNode();
                MonitorUtil.setTreeNodeAttr(s, lineVo);
                MonitorUtil.setAreaTreeNodeAttr(s, lineVo);
                for (PqVariationEventResult m1 : eventList) {
                    m1.setSubstationId(s.getSubstationid());
                    m1.setSubstationName(s.getSubstationname());
                    m1.setCitycompanyId(s.getCitycompanyid());
                    m1.setCitycompanyName(s.getCitycompanyname());
                    m1.setProvincecompanyId(s.getProvincecompanyid());
                    m1.setProvincecompanyName(s.getProvincecompanyname());
                    m1.setLineId(ParseDataUtil.parseLong(lineVo.get(ColumnName.ID)));
                    m1.setLineName(ParseDataUtil.parseString(lineVo.get(ColumnName.NAME)));
                    m1.setLinevoltageclass(ParseDataUtil.parseInteger(lineVo.get(ColumnName.VOLTCLASS)));
                    List<Map<String, Object>> substationMapList = ParseDataUtil.parseList(lineVo.get(TableName.SUBSTATION + "_model"));
                    if (CollectionUtils.isNotEmpty(substationMapList)) {
                        Map<String, Object> substationMap = substationMapList.get(0);
                        m1.setSubstationvoltageclass(ParseDataUtil.parseInteger(substationMap.get(ColumnName.VOLTCLASS)));
                        m1.setSubstationName(ParseDataUtil.parseString(substationMap.get(ColumnName.NAME)));
                        m1.setSubstationvoltageclassText(ParseDataUtil.parseString(substationMap.get(ColumnName.VOLTCLASS_TEXT)));
                    }
                }
            }
        }

        return Result.success(eventList);
    }

    private List<Order> eventSort(PqVariationEventParams pqVariationEventParams) {
        List<Order> orderList = new ArrayList<>();
        int priority = 1;
        if (!Objects.isNull(pqVariationEventParams.getDurationAsc())) {
            String sort = getOrderType(pqVariationEventParams.getDurationAsc());
            orderList.add(new Order(ColumnName.DURATION, sort, priority++));
        } else if (!Objects.isNull(pqVariationEventParams.getMagnitudeAsc())) {
            String sort = getOrderType(pqVariationEventParams.getMagnitudeAsc());
            orderList.add(new Order(ColumnName.MAGNITUDE, sort, priority++));
        } else if (!Objects.isNull(pqVariationEventParams.getEventTimeAsc())) {
            String sort = getOrderType(pqVariationEventParams.getEventTimeAsc());
            orderList.add(new Order(ColumnName.EVENT_TIME, sort, priority++));
        }
        orderList.add(new Order(ColumnName.ID, ConditionBlock.DESC, priority));

        return orderList;
    }

    private String getOrderType(Boolean hasMarkedAsc) {
        return Boolean.TRUE.equals(hasMarkedAsc) ? ConditionBlock.ASC : ConditionBlock.DESC;
    }


    @Override
    public String getTolerance(PqVariationEventResult pqVariationEvent) {
        String toleranceband = "";
        switch (pqVariationEvent.getToleranceband()) {
            case 1:
                toleranceband = "图形区域外";
                break;
            case 2:
                toleranceband = "设备容忍区";
                break;
            case 3:
                toleranceband = "设备损坏区";
                break;
            case 4:
                toleranceband = "设备无损区";
                break;
            default:
        }
        return toleranceband;
    }

    @Override
    public void exportEventReasonAnalysis(PqVariationEventParams pqVariationEventParams, HttpServletResponse response) {

        List<PqVariationEventResult> pqVariationEvents = getPqVariationEvent(pqVariationEventParams).getData();
        ExportParameter exportParameter = new ExportParameter();
        List<Sheets> sheetList = new ArrayList<>();
        Sheets sheet = new Sheets();
        sheet.setId(TemplateDataEnum.SheetAndTemplateNameEnum62.getId());
        sheetList.add(sheet);
        exportParameter.setSheets(sheetList);
        /*String fileName = TemplateDataEnum.SheetAndTemplateNameEnum62.getSheetName() + Constants.CHARACTER_UNDERLINE +
                DateUtils.formatDate(exportPqVariationEventParams.getStartTime(), TimeFormat.DATETIMEFORMAT) + Constants.CHARACTER_HYPHEN +
                DateUtils.formatDate(exportPqVariationEventParams.getEndTime(), TimeFormat.DATETIMEFORMAT) + Constants.EXCEL_SUFFIX;
        exportParameter.setFileName(fileName);*/

        exportParameter.getSheets().stream().peek(sheets -> {
            List<LinkedHashMap<String, Object>> data = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(pqVariationEvents)) {
                for (int i = 0; i < pqVariationEvents.size(); i++) {
                    LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                    int n = 1;
                    map.put(ExcelConstant.HEAD_ROWID, i + 1);
                    map.put(ColumnName.COL + ++n, pqVariationEvents.get(i).getCitycompanyName());
                    map.put(ColumnName.COL + ++n, pqVariationEvents.get(i).getSubstationName());
                    map.put(ColumnName.COL + ++n, pqVariationEvents.get(i).getSubstationvoltageclassText());
                    map.put(ColumnName.COL + ++n, pqVariationEvents.get(i).getLineName());
                    map.put(ColumnName.COL + ++n, VoltageLevelEnum.getVoltageLevel(pqVariationEvents.get(i).getLinevoltageclass()));
                    map.put(ColumnName.COL + ++n, pqVariationEvents.get(i).getPqvariationeventtypeText());
                    map.put(ColumnName.COL + ++n, DateUtils.formatDate(pqVariationEvents.get(i).getEventtime(), TimeFormat.SUPPER_LONG_TIME_FORMAT));
                    map.put(ColumnName.COL + ++n, Objects.isNull(pqVariationEvents.get(i).getDuration()) ? "--" : CommonUtils.formatNumber(1d * pqVariationEvents.get(i).getDuration() / 1000, 2));
                    map.put(ColumnName.COL + ++n, CommonUtils.formatNumber(pqVariationEvents.get(i).getMagnitude(),2));
                    map.put(ColumnName.COL + ++n, CommonUtils.formatNumber(pqVariationEvents.get(i).getV1magnitude(),2));
                    map.put(ColumnName.COL + ++n, CommonUtils.formatNumber(pqVariationEvents.get(i).getV2magnitude(),2));
                    map.put(ColumnName.COL + ++n, CommonUtils.formatNumber(pqVariationEvents.get(i).getV3magnitude(),2));
                    String trigger = getTrigger(pqVariationEvents.get(i));
                    String toleranceBand = Objects.nonNull(pqVariationEvents.get(i).getToleranceband()) ? getTolerance(pqVariationEvents.get(i)) : null;
                    map.put(ColumnName.COL + ++n, trigger);
                    map.put(ColumnName.COL + ++n, toleranceBand);
                    map.put(ColumnName.COL + ++n, pqVariationEvents.get(i).getReasonText());
					map.put(ColumnName.COL + ++n, pqVariationEvents.get(i).getRemark());
                    data.add(map);
                }
            }
            sheets.setData(data);
            ExcelUtil.setSheetAndTitle(pqVariationEventParams.getStartTime(), pqVariationEventParams.getEndTime(), sheets, null, 0, 0);
        }).collect(Collectors.toList());
        ExcelUtil.commonExport(response, exportParameter, "templates/export/暂态事件类型识别导出模板.xlsx");
    }

    @Override
    public String getTrigger(PqVariationEventResult pqVariationEvent) {
        String trigger = "未知相别";
        if (isNotNullEventTrigger(pqVariationEvent)) {
            if (whichTrigger(pqVariationEvent.getV1trigger(), pqVariationEvent.getV2trigger(), pqVariationEvent.getV3trigger())) {
                trigger = "A相";
            }
            if (whichTrigger(pqVariationEvent.getV2trigger(), pqVariationEvent.getV1trigger(), pqVariationEvent.getV3trigger())) {
                trigger = "B相";
            }
            if (whichTrigger(pqVariationEvent.getV3trigger(), pqVariationEvent.getV1trigger(), pqVariationEvent.getV2trigger())) {
                trigger = "C相";
            }
        }
        return trigger;
    }

    private static boolean isNotNullEventTrigger(PqVariationEventResult pqVariationEvent) {
        return Objects.nonNull(pqVariationEvent.getV1trigger()) && Objects.nonNull(pqVariationEvent.getV2trigger()) && Objects.nonNull(pqVariationEvent.getV3trigger());
    }

    private static boolean whichTrigger(Boolean triggerMain, Boolean triggerA, Boolean triggerB) {
        return triggerMain && !triggerA && !triggerB;
    }


    @Override
    public Result<PqEventStatistic> getPqVariationEventStatistic(String userId, PqVariationEventParams params) {
        // 获取电能质量事件模型数据
        Integer pageNum = params.getPageNum();
        Integer pageSize = params.getPageSize();
        Page page = getPageForPqVariationEvent(pageSize, pageNum);
        ResultWithTotal<List<PqVariationEventResult>> resultTotal = getVariationEventList(params, page);
        List<PqVariationEventResult> eventList = resultTotal.getData();
        // 暂态事件分类统计：2 - 暂升事件（swell），3 - 暂降事件（dip），4 - 中断事件（Interruption）
        Map<Integer, Long> classifiedCount = eventList.stream().collect(Collectors.groupingBy(PqVariationEventResult::getPqvariationeventtype, Collectors.counting()));
        // 暂态事件分布统计：1 - 无（区域外），2 - 设备容忍区（tolerance），3 - 设备损坏区（prohibited），4 -
        // 设备无损区（nodamage）
        Map<Integer, Long> distributionCount = eventList.stream().collect(Collectors.groupingBy(PqVariationEventResult::getToleranceband, Collectors.counting()));
        PqEventStatistic.ClassifiedStatistic classifiedStatistic = new PqEventStatistic.ClassifiedStatistic(classifiedCount.get(2), classifiedCount.get(3), classifiedCount.get(4));
        PqEventStatistic.DistributionStatistic distributionStatistic = new PqEventStatistic.DistributionStatistic(distributionCount.get(1), distributionCount.get(2),
                distributionCount.get(3), distributionCount.get(4));
        PqEventStatistic pqEventStatistic = new PqEventStatistic(eventList.size(), classifiedStatistic, distributionStatistic);
        Result<PqEventStatistic> result = new Result<>();
        result.setData(pqEventStatistic);
        return result;
    }

    /*@Override
    public Result<Object> updatePqVariationEventRemark(PqEventRemark pqEventRemark) {
        pqEventRemark.setModelLabel(TableName.PQ_VARIATION_EVENT);
        return modelServices.write(Arrays.asList(pqEventRemark), true, true);
    }*/

    @Override
    public ResultWithTotal<List<String>> getWaveform(String userId, Long id, Long waveTime) {
        return deviceDataService.queryWaveByDeviceId(id, waveTime);
//        return null;
    }

    /*@Override
    public Result updateEventRemark(Long id, Integer causeId) {
        if(Objects.isNull(id)) {
            throw new CommonManagerException("暂态事件不存在");
        }
        //获取现有的原因分析
        List<PqVariationevent> pqVariationevents = ModelServiceUtils.querySingleModel(Collections.singletonList(id), TableName.PQ_VARIATION_EVENT, null, null, null, PqVariationevent.class);
        if (CollectionUtils.isNotEmpty(pqVariationevents)) {
            PqVariationevent pqVariationevent = pqVariationevents.get(0);
            if (EventCauseType.getName(causeId).equals(pqVariationevent.getRemark())) {
                return Result.success();
            } else {
                Map<String, Object> map = new HashMap<>();
                map.put("id", id);
                map.put("remark", EventCauseType.getName(causeId));
                map.put("ismanual", true);
                map.put("modelLabel", "pqvariationevent");
                return ModelServiceUtils.write(Collections.singletonList(map));
            }
        } else {
            throw new CommonManagerException("该事件已删除.");
        }
    }*/

    /**
     * Map中从监测点一级向上递归找到树的属性
     *
     * @param line
     * @return
     */
    public static LineAndCompanyProperty getLineTreePropertyFromMap(Map<String, Object> line) {
        Long lineId = null;
        String lineName = null;
        Integer lineVoltClass = null;
        String lineVoltClass$text = null;
        String lineObjectLabel = null;
        Long substationId = null;
        String substationName = null;
        Integer substationVoltClass = null;
        String substationVoltClass$text = null;
        Long countyCompanyId = null;
        String countyCompanyName = null;
        String countyCompanyFullName = null;
        Long cityCompanyId = null;
        String cityCompanyName = null;
        String cityCompanyFullName = null;
        Long provinceCompanyId = null;
        String provinceCompanyName = null;
        String statusText = null;
        String objecttypeText = null;
        String lineCode = null;
        // 监测点
        if (null != line) {
            lineId = ParseDataUtil.parseLong(line.get("id"));
            lineName = ParseDataUtil.parseString(line.get("name"));
            lineVoltClass = ParseDataUtil.parseInteger(line.get("voltclass"));
            lineVoltClass$text = ParseDataUtil.parseString(line.get("voltclass$text"));
            lineObjectLabel = ParseDataUtil.parseString(line.get("object_label"));
            statusText = ParseDataUtil.parseString(line.get("status$text"));
            objecttypeText = ParseDataUtil.parseString(line.get("objecttype$text"));
            lineCode = ParseDataUtil.parseString(line.get("code"));
            Map<String, Object> substation = null == line.get("substation_model") ? null : MapUtil.getOneOfListFromMap(line, "substation_model");
            // 变电站
            if (null != substation) {
                substationId = ParseDataUtil.parseLong(substation.get("id"));
                substationName = ParseDataUtil.parseString(substation.get("name"));
                substationVoltClass = ParseDataUtil.parseInteger(substation.get("voltclass"));
                substationVoltClass$text = ParseDataUtil.parseString(substation.get("voltclass$text"));
                //县级市
                Map<String, Object> countyCompany = null == substation.get("countycompany_model") ? null : MapUtil.getOneOfListFromMap(substation, "countycompany_model");
                Map<String, Object> cityCompany = null;
                if (null != countyCompany) {
                    countyCompanyId = ParseDataUtil.parseLong(countyCompany.get("id"));
                    ;
                    countyCompanyName = ParseDataUtil.parseString(countyCompany.get("name"));
                    countyCompanyFullName = ParseDataUtil.parseString(countyCompany.get(ColumnName.FULLNAME));
                    cityCompany = null == countyCompany.get(ColumnName.CITYCOMPANY_MODEL) ? null : MapUtil.getOneOfListFromMap(countyCompany, ColumnName.CITYCOMPANY_MODEL);
                } else {
                    cityCompany = null == substation.get(ColumnName.CITYCOMPANY_MODEL) ? null : MapUtil.getOneOfListFromMap(substation, ColumnName.CITYCOMPANY_MODEL);
                }
                if (null != cityCompany) {
                    cityCompanyId = ParseDataUtil.parseLong(cityCompany.get("id"));
                    cityCompanyName = ParseDataUtil.parseString(cityCompany.get("name"));
                    cityCompanyFullName = ParseDataUtil.parseString(cityCompany.get(ColumnName.FULLNAME));
                    // 省级单位
                    Map<String, Object> provinceCompany = null == cityCompany.get("provincecompany_model") ? null : MapUtil.getOneOfListFromMap(cityCompany, "provincecompany_model");
                    if (null != provinceCompany) {
                        provinceCompanyId = ParseDataUtil.parseLong(provinceCompany.get("id"));
                        provinceCompanyName = ParseDataUtil.parseString(provinceCompany.get("name"));
                    }
                }
            }
        }
        return new LineAndCompanyProperty(provinceCompanyId, provinceCompanyName, cityCompanyId, cityCompanyName, cityCompanyFullName, countyCompanyId, countyCompanyName, countyCompanyFullName,
                substationId, substationName, substationVoltClass, substationVoltClass$text, lineId, lineName, lineVoltClass, lineVoltClass$text, lineObjectLabel, statusText, objecttypeText, lineCode);
    }

    /**
     * 事件查询过滤条件处理
     */
    private ResultWithTotal<List<PqVariationEventResult>> getVariationEventList(
            PqVariationEventParams pqVariationEventParams,
            Page page) {
        ResultWithTotal resultWithTotal = new ResultWithTotal();
        if (CollectionUtils.isEmpty(pqVariationEventParams.getMonitoredId())) {
            resultWithTotal.setData(new ArrayList<>());
            return resultWithTotal;
        }
        List<ConditionBlock> filters = new ArrayList<>();
        filters.add(new ConditionBlock(ColumnName.MONITOREDID, ConditionBlock.OPERATOR_IN, pqVariationEventParams.getMonitoredId()));
        if (!Objects.isNull(pqVariationEventParams.getStartTime())) {
            filters.add(new ConditionBlock(ColumnName.EVENT_TIME, ConditionBlock.OPERATOR_GE, pqVariationEventParams.getStartTime()));
        }
        if (!Objects.isNull(pqVariationEventParams.getEndTime())) {
            filters.add(new ConditionBlock(ColumnName.EVENT_TIME, ConditionBlock.OPERATOR_LT, pqVariationEventParams.getEndTime()));
        }
        if (Boolean.TRUE.equals(pqVariationEventParams.getIsMinuteExtreme())) {
            filters.add(new ConditionBlock(ColumnName.IS_MINUTE_EXTREME, ConditionBlock.OPERATOR_NE, Boolean.FALSE));
        }else{
            filters.add(new ConditionBlock(ColumnName.IS_MINUTE_EXTREME, ConditionBlock.OPERATOR_NE, Boolean.TRUE));
        }
        if (!Objects.isNull(pqVariationEventParams.getMaxMagnitude())) {
            filters.add(new ConditionBlock(ColumnName.MAGNITUDE, ConditionBlock.OPERATOR_LT, pqVariationEventParams.getMaxMagnitude()));
        }
        if (!Objects.isNull(pqVariationEventParams.getMinMagnitude())) {
            filters.add(new ConditionBlock(ColumnName.MAGNITUDE, ConditionBlock.OPERATOR_GE, pqVariationEventParams.getMinMagnitude()));
        }
        if (CollectionUtils.isNotEmpty(pqVariationEventParams.getPqVariationEventType())) {
            filters.add(new ConditionBlock(ColumnName.PQ_VARIATION_EVENT_TYPE, ConditionBlock.OPERATOR_IN, pqVariationEventParams.getPqVariationEventType()));
        }
        if (!Objects.isNull(pqVariationEventParams.getMaxDuration())) {
            filters.add(new ConditionBlock(ColumnName.DURATION, ConditionBlock.OPERATOR_LT, ONE_THOUSAND * pqVariationEventParams.getMaxDuration()));
        }
        if (!Objects.isNull(pqVariationEventParams.getMinDuration())) {
            filters.add(new ConditionBlock(ColumnName.DURATION, ConditionBlock.OPERATOR_GE, ONE_THOUSAND * pqVariationEventParams.getMinDuration()));
        }

        List<Order> orderList = eventSort(pqVariationEventParams);
        ResultWithTotal<List<Map<String, Object>>> result = ModelServiceUtils.getSingleModelByGroupAndPage(null, TableName.PQ_VARIATION_EVENT, filters, orderList, page);
        resultWithTotal.setTotal(result.getTotal());
        resultWithTotal.setData(JsonTransferUtils.transferList(result.getData(), PqVariationEventResult.class));
        return resultWithTotal;
    }

    /**
     * 获取监测点数据
     **/
    public List<Map<String, Object>> getLineData(String modelName, Long modelId, Integer monitorType, Integer monitorSort, String lineNameLike,
                                                 Integer lineVoltage) {
        List<SingleModelConditionDTO> subLayerConditions = Arrays.asList(
                TreeUtils.getPlainSingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.NAME, ColumnName.VOLTCLASS), null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.COUNTYCOMPANY, null, null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.FULLNAME), null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.PROVINCECOMPANY, Arrays.asList(ColumnName.FULLNAME), null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.PECDEVICEEXTEND, null, null),
                TreeUtils.getPlainSingleModelConditionDTO(TableName.POWERDISTRIBUTIONAREA, Arrays.asList(ColumnName.LINENAME), null)
        );
        // 监测点筛选条件
        List<ConditionBlock> filters = new ArrayList<>();
        if (Objects.nonNull(monitorType) && !Objects.equals(0, monitorType)) {
            filters.add(new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_EQ, monitorType));
        }
        if (Objects.nonNull(lineVoltage)) {
            filters.add(new ConditionBlock(ColumnName.VOLTCLASS, ConditionBlock.OPERATOR_EQ, lineVoltage));
        }
        if (StringUtils.isNotEmpty(lineNameLike)) {
            filters.add(new ConditionBlock(ColumnName.NAME, ConditionBlock.OPERATOR_LIKE, lineNameLike));
        }
        QueryCondition queryCondition = MonitorUtil.getQueryCondition(modelId, modelName, filters, null, Arrays.asList(new Order(ColumnName.ID,
                        CommonConstant.ASC, 1)), false, subLayerConditions, TableName.LINE,
                Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.MONITORSORT, ColumnName.MONITORTYPE, ColumnName.VOLTCLASS));
        ResultWithTotal<List<Map<String, Object>>> queryResult = ModelServiceUtils.getSingleModelByGroupAndPage(queryCondition);

        List<Map<String, Object>> lineList = queryResult.getData();
        if (CollectionUtils.isNotEmpty(lineList)) {
            //结果过滤
            ResultFilterUtils.lineOrTerminalFilterByCompany(lineList, modelName, modelId);
        }
        return lineList;
    }

}

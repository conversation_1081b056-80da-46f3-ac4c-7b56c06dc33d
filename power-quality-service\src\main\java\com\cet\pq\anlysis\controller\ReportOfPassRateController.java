package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.excel.ExportReportOfPassRateCountParms;
import com.cet.pq.anlysis.model.excel.ExportReportOfPassRateDetailedParms;
import com.cet.pq.anlysis.model.reportofpassrate.*;
import com.cet.pq.anlysis.service.ReportOfPassRateService;
import com.cet.pq.common.model.IdTextPair;
import com.cet.pq.common.model.PageResult;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.ResultWithTotal;
import com.cet.pq.pqservice.model.TimeNodePageExportParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Description: 稳态指标合格率报表
 * @Author:      zhangshixi
 * @Date:     2020/10/13 9:52
 */
@Api(value = "/pq/v1/report/passRate", tags = "稳态指标 - 合格率统计")
@RestController
@RequestMapping(value = "/pq/v1/report/passRate")
public class ReportOfPassRateController {

    @Autowired
    private ReportOfPassRateService reportOfPassRateService;

    @ApiOperation(value = "按管理单位统计-主配网")
    @PostMapping(value = "/statisticalReportByCompany")
    public PageResult<List<ReportOfPassRateCount>> reportOfOverStandardCountByCompany(@RequestBody ReportOfPassRateCountParms reportOfPassRateCountParms) {
        return reportOfPassRateService.reportOfPassRateCountByCompany(reportOfPassRateCountParms);
    }

    @ApiOperation(value = "导出按管理单位统计-主配网")
    @PostMapping(value = "/exportStatisticalReportByCompany")
    public void exportReportOfOverStandardCountByCompany(@RequestBody ExportReportOfPassRateCountParms exportReportOfPassRateCountParms, HttpServletResponse response) {
        reportOfPassRateService.exportReportOfPassRateCountByCompany(exportReportOfPassRateCountParms, response);
    }


    @ApiOperation(value = "按变电站统计-主网")
    @PostMapping(value = "/statisticalReportBySubstation")
    public PageResult<List<ReportOfPassRateCountBySubstation>> reportOfOverStandardCountBySubstation(@RequestBody ReportOfPassRateCountParms reportOfPassRateCountParms) {
        return reportOfPassRateService.reportOfPassRateCountByStutation(reportOfPassRateCountParms);
    }

    @ApiOperation(value = "导出按变电站统计-主网")
    @PostMapping(value = "/exportStatisticalReportBySubstation")
    public void exportReportOfOverStandardCountBySubstation(@RequestBody ExportReportOfPassRateCountParms exportReportOfPassRateCountParms, HttpServletResponse response) {
        reportOfPassRateService.exportReportOfPassRateCountByStutation(exportReportOfPassRateCountParms, response);
    }

    @ApiOperation(value = "按监测对象统计-主网")
    @PostMapping(value = "/statisticalReportByObject")
    public ResultWithTotal<List<ReportOfPassRateCount>> reportOfOverStandardCountByObject(@RequestBody ReportOfPassRateCountParms reportOfPassRateCountParms) {
        List<ReportOfPassRateCount> result = reportOfPassRateService.reportOfPassRateCountByObject(reportOfPassRateCountParms);
        return ResultWithTotal.success(result,result.size());
    }

    @ApiOperation(value = "导出按监测对象统计-主网")
    @PostMapping(value = "/exportStatisticalReportByObject")
    public void exportReportOfOverStandardCountByObject(@RequestBody ExportReportOfPassRateCountParms exportReportOfPassRateCountParms, HttpServletResponse response) {
        reportOfPassRateService.exportReportOfPassRateCountByObject(exportReportOfPassRateCountParms, response);
    }

    @ApiOperation(value = "稳态指标合格率明细-主配网")
    @PostMapping(value = "/statisticalDetail")
    public PageResult<List<ReportOfPassRateDetailed>> reportOfOverStandardCount(@RequestBody ReportOfPassRateDetailedParms reportOfPassRateDetailedParms) {
        return reportOfPassRateService.reportOfPassRateDetailed(reportOfPassRateDetailedParms);
    }

    @ApiOperation(value = "导出稳态指标合格率明细-主配网")
    @PostMapping(value = "/exportStatisticalDetail")
    public void exportReportOfOverStandardCount(@RequestBody ExportReportOfPassRateDetailedParms exportReportOfPassRateDetailedParms, HttpServletResponse response) {
        reportOfPassRateService.exportReportOfPassRateDetailed(exportReportOfPassRateDetailedParms, response);
    }

    @ApiOperation(value = "稳态指标合格率监测点过滤条件枚举")
    @GetMapping(value = "/lineFilterEnums")
    public Result<List<IdTextPair>> getLineFilterEnums(@RequestParam String filter) {
        List<IdTextPair> result = reportOfPassRateService.getLineFilterEnums(filter);
        return ResultWithTotal.success(result,result.size());
    }

    @ApiOperation(value = "按监测点类别统计-配网")
    @PostMapping(value = "/statisticalReportByMonitorSort")
    public Result<List<ReportOfPassRateCount>> reportOfOverStandardCountByMonitorSort(@RequestBody PassRateByMonitorSortParam reportOfPassRateCountParms) {
        return reportOfPassRateService.reportOfOverStandardCountByMonitorSort(reportOfPassRateCountParms);
    }

    @ApiOperation(value = "导出按监测点类别统计-配网")
    @PostMapping(value = "/exportStatisticalReportByMonitorSort")
    public void exportReportOfOverStandardCountByMonitorType(@RequestBody ExportPassRateByMonitorSortParam exportReportOfPassRateCountParms, HttpServletResponse response) {
        reportOfPassRateService.exportReportOfPassRateCountByMonitorType(exportReportOfPassRateCountParms, response);
    }

    @ApiOperation(value = "合格率趋势统计-主配网")
    @PostMapping(value = "/reportOfOverStandardCountByMonth")
    public Result<List<ReportOfPassRateMonth>> reportOfOverStandardCountByMonth(@RequestBody PassRateCountParms reportOfPassRateCountParms) {
        return Result.success(reportOfPassRateService.reportOfOverStandardCountByMonth(reportOfPassRateCountParms));
    }


    @ApiOperation(value = "导出合格率趋势统计-主配网")
    @PostMapping(value = "/exportOverStandardCountByMonth")
    public void exportOverStandardCountByMonth(@RequestBody ExportReportOfPassRateCountParms exportReportOfPassRateCountParms, HttpServletResponse response) {
        reportOfPassRateService.exportOverStandardCountByMonth(exportReportOfPassRateCountParms, response);
    }

    @ApiOperation(value = "稳态指标合格率统计查询按电压等级统计")
    @PostMapping(value = "/statisticalReportByVoltage")
    public PageResult<List<ReportOfPassRateCountByVoltage>> statisticalReportByVoltage(@RequestBody ReportOfPassRateCountParms reportOfPassRateCountParms) {
        PageResult<List<ReportOfPassRateCountByVoltage>> result = reportOfPassRateService.reportOfPassRateCountByVoltage(reportOfPassRateCountParms);
        return result;
    }

    @ApiOperation(value = "导出稳态指标合格率统计查询按电压等级统计")
    @PostMapping(value = "exportStatisticalReportByVoltage")
    public void exportReportOfOverStandardCountByVoltage(@RequestBody ExportReportOfPassRateCountParms exportReportOfPassRateCountParms, HttpServletResponse response) {
        reportOfPassRateService.exportReportOfOverStandardCountByVoltage(exportReportOfPassRateCountParms, response);
    }

}

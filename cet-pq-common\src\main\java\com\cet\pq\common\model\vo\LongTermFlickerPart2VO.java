package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/06/08 16:33
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LongTermFlickerPart2VO {

    /**
     * 次数
     */
    private Integer count;

    /**
     * 长时闪变值
     */
    private Double value;

    /**
     * 相别类型: 0：A，1：B，2：C
     */
    private Integer phaseType;

    /**
     * 长时闪变值的上四分位
     */
    private Double q2;

    /**
     * 长时闪变值的下四分位
     */
    private Double q1;

    /**
     * 长时闪变值偏离值
     */
    private Double s;



}

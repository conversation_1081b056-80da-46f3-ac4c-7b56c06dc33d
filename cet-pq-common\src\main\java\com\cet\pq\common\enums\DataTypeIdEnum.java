package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/6/8 18:04
 * @Description
 */
public enum DataTypeIdEnum {
    //  dataTypeId(常用类型): 1-实时值，2-最大值，3-最小值，4-平均值，5-95概率值,

    REAL_TIME("实时值",1),
    //电站
    MAX("最大值",2),
    //台区
    MIN("最小值",3),
    //线路
    AVG("平均值",4),

    CP95("CP95值",5)
    ;



    DataTypeIdEnum(String text, Integer id) {
        this.text = text;
        this.id = id;
    }

    private String text;
    private Integer id;

    public String getText() {
        return text;
    }

    public Integer getId() {
        return id;
    }


    public static String getDataTypeIdEnumText(Integer id) {
        if(id == null){
            return "";
        }
        for (DataTypeIdEnum dataTypeIdEnum : values()) {
            if (id.equals(dataTypeIdEnum.getId())) {
                return dataTypeIdEnum.getText();
            }
        }
        return "";
    }

}

package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum RegionTypeEnum {
    //区域等级
    citycentre("市区", 1),
    city("县城区", 2),
    suburb("城镇", 3),
    county("乡镇", 4),
    village("农村", 5);

    private String name;
    private Integer value;

    private RegionTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static Integer getRegionType(String name) {
        for (RegionTypeEnum regionTypeEnum : RegionTypeEnum.values()) {
            if (name.equals(regionTypeEnum.getName())) {
                return regionTypeEnum.getValue();
            }
        }
        throw new CommonManagerException("地区特征值无效");
    }

    public static Integer getRegionTypeNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (RegionTypeEnum regionTypeEnum : RegionTypeEnum.values()) {
            if (regionTypeEnum.getName().equals(name)) {
                return regionTypeEnum.getValue();
            }
        }
        return 0;
    }

    public static String getNameByValueNoThrows(Integer value) {
        for (RegionTypeEnum regionTypeEnum : RegionTypeEnum.values()) {
            if (regionTypeEnum.getValue().equals(value)) {
                return regionTypeEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }
}


说明：Java Springboot项目对日志输出、代码注释、POJO实体类注释。

**POJO定义规范**
说明：
Java项目的POJO实体类规范。

1. **类定义**
  - 命名：大驼峰式（`UserDTO`）
  - **必须有无参构造器**（框架依赖）
  - 推荐使用 `@Getter`和`@Setter`（Lombok 自动生成 getter/setter method）
```java

	// Lombok最佳实践
	@Getter 
	@Setter
	public class UserDTO {
		@NonNull private Integer id;
		private String email;
		// 无业务逻辑方法！import lombok.NonNull;
	}
```


2. **字段定义**
 - 命名：
 - 小驼峰式（`userName`）
 - 禁止用is开头（POJO 类中的任何布尔类型的变量，都不要加 is 前缀，否则部分框架解析会引起序列化错误。）
 - 强制 `private` 修饰符
 - 优先包装类型（避免 `int` 默认值 `0` 引发歧义）
 - 空值字段用 `@NotNull` 显式标记

 
3. **方法规范**
 - Getter 命名：`getXxx()` 
 - Setter 命名：`setXxx()`
 - **禁止在 getter/setter 中添加业务逻辑**
 - 可重写 `toString()`/`equals()`/`hashCode()`

 4.  **序列化要求**

```java
    
    public class UserPOJO implements Serializable {
		// 明确版本ID
        private static final long serialVersionUID = 1L; 
        // ...字段定义
    }

```

5. 各POJO对象特殊要求
**VO对象**
  - 用于 **展示层**（如Controller接口返回的数据封装）
  - 必须添加 Swagger注解（`@ApiModel`和`@ApiModelProperty`），必须包含注释

```java
  /**
  * 用户信息VO
  */
  @ApiModel
  public class UserVO {
      @ApiModelProperty(value = "项目id", required = true, example = "1")  
	  /**
	  * 项目id
	  **/
	  private Long projectId;
      // ...
  }
  
```


**DO对象**
 - 领域对象，对应于数据库表对应的数据
 -  使用 模型服务`ModelServiceUtils` 时DO实体类应遵循：
  - 成员变量必须使用 **小驼峰命名法**（lowerCamelcase）
  - 必须添加 `@JsonProperty("全小写字段名")` 注解
  - 必须添加 modelLabel定义
```java
    @JsonProperty("projectid")
    private Long projectId;

	private String modelLabel = TableName.CITY_COMPANY;
	```



**日志规范**
说明：
Java项目的日志规范。
框架：logstash

异常打点
针对重要操作进行输出

记录异常上下文信息​
```java
@Slf4j
@Service
public class OrderService {
    public void updateOrder(Order order) {
        try {
            // 更新订单逻辑
        } catch (InventoryException e) {
            log.error("库存不足 - 订单ID: {}, 商品: {}", 
                      order.getId(), order.getProduct(), e); // 关键业务参数[6](@ref)
        }
    }
}
```

异常日志记录的使用
```java
@Slf4j
@Service
public class RegistrationService {
    public void registerUser(User user) {
        try {
            // 业务逻辑（如数据库操作）
        } catch (DatabaseException e) {
            log.error("用户注册失败 - 用户名: {}", user.getUsername(), e); // 输出异常堆栈
            
        }
    }
}

```
package com.cet.pq.common.time;

import com.cet.pq.common.constant.QueryType;
import com.cet.pq.common.model.objective.physicalquantity.AggregationCycle;
import org.joda.time.DateTime;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @ClassName TimeUtils
 * @Description 时间工具类，与业务相关
 * <AUTHOR>
 * @Date 2020/3/2 17:45
 */
public class TimeUtils {
	/**
	 * 天的时间戳
	 */
	public static final Long DAY = 24L * 3600 * 1000L;
	/**
	 * 年的时间戳
	 */
	public static final Long YEAR = 365L * 24 * 3600 * 1000L;
	/**
	 * 分钟的时间戳
	 */
	public static final Long MINUTE = 60 * 1000L;
	/**
	 * 小时的时间戳
	 */
	public static final Long HOUR = 3600L * 1000L;

	public static final int FEBRUARY = 2;
	/**
	 * 第一天的日期
	 */
	public static final int FIRST_DAY = 1;

	/**
	 * 加减时间
	 *
	 * @param oriTime
	 * @return
	 */
	public static long add(Date oriTime, int field, int value) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(oriTime);
		calendar.add(field, value);
		return calendar.getTimeInMillis();
	}

	/**
	 * 增减时间
	 *
	 * @param dt
	 * @param cycle
	 * @param step
	 * @return
	 */
	public static Date addCycle(Date dt, int cycle, int step) {
		switch (cycle) {
		case AggregationCycle.ONE_DAY:
			return DateUtils.dateCalc(dt, Calendar.DAY_OF_MONTH, step);
		case AggregationCycle.ONE_MONTH:
			return DateUtils.dateCalc(dt, Calendar.MONTH, step);
		case AggregationCycle.ONE_YEAR:
			return DateUtils.dateCalc(dt, Calendar.YEAR, step);
		case AggregationCycle.ONE_HOUR:
			return DateUtils.dateCalc(dt, Calendar.HOUR_OF_DAY, step);
		default:
			break;
		}
		return dt;
	}

	/**
	 * 增减时间
	 *
	 * @param dt
	 * @param cycle
	 * @param step
	 * @return
	 */
	public static long addCycle(long dt, int cycle, int step) {
		Date date = addCycle(DateUtils.timestampToDate(dt), cycle, step);
		return DateUtils.dateToTimeStamp(date);
	}

	/**
	 * 获取一个指定时间段内的时间列表，前闭后开
	 *
	 * @param st    开始时间
	 * @param et    结束时间
	 * @param cycle 聚合周期
	 * @return 时间列表
	 */
	public static List<Long> getTimeRange(long st, long et, int cycle) {
		List<Long> result = new ArrayList<>();
		for (long time = st; time < et; time++) {
			result.add(time);
			time = addCycle(time, cycle, 1);
		}

		return result;
	}

	/**
	 * 计算位移天数0点的时间戳
	 * 
	 * @param off
	 * @return
	 */
	public static long getLastDateZero(int off, Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH) - off, 0, 0, 0);
		return calendar.getTime().getTime() / 1000 * 1000;
	}

	/**
	 * 计算位移天23:59:59 秒的时间戳
	 * 
	 * @param off
	 * @return
	 */
	public static long getLastDateEndTime(int off, Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH) - off, 23, 59, 59);
		return calendar.getTime().getTime();
	}

	/**
	 * 计算位移月0点的时间戳
	 * 
	 * @param off
	 * @return
	 */
	public static long getLastMonthStartTime(int off, Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) - off, calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		return calendar.getTime().getTime() / 1000 * 1000;
	}

	/**
	 * 按周期偏移时间
	 * 
	 * @param cycle
	 * @param off
	 * @param date
	 * @return
	 */
	public static long getLastMonthStartTimeByCycle(int cycle, int off, Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		if (AggregationCycle.ONE_YEAR == cycle) {
			calendar.set(calendar.get(Calendar.YEAR) - off, calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
		} else if (AggregationCycle.ONE_MONTH == cycle) {
			calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) - off, calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
		} else if (AggregationCycle.THREE_MONTHS == cycle) {
			calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) - 3 * off, calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
		}
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		return calendar.getTime().getTime() / 1000 * 1000;
	}

	/**
	 * 计算位移月0点的时间戳
	 * 
	 * @param off
	 * @return
	 */
	public static long getLastMonthEndTime(int off, Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) - off, calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return calendar.getTime().getTime();
	}

	/**
	 * @param timestamp 需要转换的时间戳
	 * @param cycle     时间类型
	 * @param querytype 查询类型 1 同比 2环比
	 * @return
	 */
	public static long convertTime(long timestamp, Integer cycle, Integer querytype) {
		if (querytype == QueryType.CURRENT) {
			return timestamp;
		}
		long result = 0;
		switch (cycle) {
		case AggregationCycle.ONE_YEAR:
			result = new DateTime(new Date(timestamp)).minusYears(1).toDate().getTime();
			break;
		case AggregationCycle.ONE_MONTH:
			if (querytype == QueryType.YEAR_ON_YEAR) {
				result = new DateTime(new Date(timestamp)).minusYears(1).toDate().getTime();
			} else if (querytype == QueryType.MONTH_ON_MONTH) {
				result = new DateTime(new Date(timestamp)).minusMonths(1).toDate().getTime();
			}
			break;
		case AggregationCycle.ONE_DAY:
			if (querytype == QueryType.YEAR_ON_YEAR) {
				result = new DateTime(new Date(timestamp)).minusMonths(1).toDate().getTime();
			} else if (querytype == QueryType.MONTH_ON_MONTH) {
				result = new DateTime(new Date(timestamp)).minusDays(1).toDate().getTime();
			}
			break;
		case AggregationCycle.ONE_HOUR:
			if (querytype == QueryType.YEAR_ON_YEAR) {
				result = new DateTime(new Date(timestamp)).minusYears(1).toDate().getTime();
			} else if (querytype == QueryType.MONTH_ON_MONTH) {
				result = new DateTime(new Date(timestamp)).minusHours(1).toDate().getTime();
			}
			break;
		default:
			break;
		}
		return result;
	}

	public static String timestampToDate(long timestamp) {
		return formatDate(timestamp, "yyyy-MM-dd HH:mm:ss");
	}

	public static String timestampToDateOnly(long timestamp) {
		return formatDate(timestamp, "yyyy-MM-dd");
	}

	public static String timestampToMonth(long timestamp) {
		return formatDate(timestamp, "yyyy-MM");
	}

	public static String timestampToYear(long timestamp) {
		return formatDate(timestamp, "yyyy");
	}

	private static String formatDate(long timestamp, String pattern) {
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		return sdf.format(new Date(timestamp));
	}
}

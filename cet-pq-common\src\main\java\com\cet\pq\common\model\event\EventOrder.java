package com.cet.pq.common.model.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019年10月23日
 * 
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventOrder implements Comparable<EventOrder> {

	private String fieldName;

	/**
	 * orderType：asc、desc
	 */
	private String orderType;

	/**
	 * 优先级，数值越小优先级越高
	 */
	private Integer priority;

	@Override
	public int compareTo(EventOrder o) {
		if (null == this.priority) {
			return -1;
		}
		if (null == o.priority) {
			return 1;
		}
		return this.priority.compareTo(o.priority);
	}
	
}

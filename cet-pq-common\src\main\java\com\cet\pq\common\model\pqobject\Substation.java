package com.cet.pq.common.model.pqobject;

import java.util.List;

import com.cet.pq.common.model.realtime.Line;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Substation {

	private String modelLabel = "substation";
	private Long id;
	private String name;
	private Integer voltclass;
	private String address;
	private Long commissioningdate;
	private Integer convertstationtype;
	private String dcsystem;
	private Double dctransmissioncapacity;
	private Double gislatitude;
	private Double gislongitude;
	private Integer importancetype;
	private Boolean iscenterstation;
	private Boolean isintelligentstation;
	private Boolean islocked;
	private String maintenanceteam;
	private String maintenanceteamname;
	private String objid;
	private String powerstationname;
	private Integer powerstationtype;
	private Integer regiontype;
	private Long runid;
	private String stationcode;
	private Long updatetime;
	
	private List<Line> line_model;
	
	private List<PQTerminal> pqterminal_model;

	@JsonProperty("zhikanname")
	private String zhiKanName;
	@JsonProperty("zhikanid")
	private String zhiKanId;
	
}

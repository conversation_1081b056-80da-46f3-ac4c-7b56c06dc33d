package com.cet.pq.common.model.pecnode;

import lombok.Data;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/7/3 15:47
 */
@Data
public class Device {
    private String nodeName;
    private Long nodeId;
    private Long nodeType;
    private Long meterTypeId;
    private String meterTypeName;
    private String ip;
    private Integer port;
    private Long communicationId;

    private Long parentNodeId;
    private Long parentNodeType;
    private String modelLabel = "Device";
    private List<CustomInfParamList> infValueList;
}

package com.cet.pq.common.cca;

import lombok.extern.slf4j.Slf4j;

import java.text.DecimalFormat;


/**
 * 实现矩阵的运算
 *
 * <AUTHOR>
 * class Matrix：
 * - height(): return no. of rows(行数)
 * - width(): return no. of columns(列数)
 * - add(Matrix target), multiply(double target): Linear Operations(线性运算)
 * - multiply(Matrix target): Multiplication(乘法运算)
 * - transpose(Matrix target)(): Transposed(转置)
 * - Solve equatino: solve(Matrix target), GaussElimination(Matrix target), setNum(int num): Equation System(解方程组)
 * - print()
 * test:
 * - output: origin & transpose & add & multiply & solve equation
 */
class Matrix {
    private int row;
    private int column;
    private double value[][];
    private int num;

    Matrix(double[][] matrix) {
    }

    Matrix(int row, int column, double[][] value) {
        this.row = row;
        this.column = column;
        this.value = value;
    }

    int height() {
        return column;
    }

    int width() {
        return row;
    }

    Matrix add(Matrix target) throws Exception {
        if (this.height() != target.height() || this.width() != target.width()) {
            throw new Exception("The two matrices must be identical in addition and subtraction! " +
                    "(加减法运算时两个矩阵必须是同型矩阵！)");
        } else {
            double result[][] = new double[this.row][this.column];
            for (int i = 0; i < this.row; i++) {
                for (int j = 0; j < this.column; j++) {
                    result[i][j] = this.value[i][j] + target.value[i][j];
                }
            }
            Matrix addition = new Matrix(this.row, this.column, result);
            return addition;
        }
    }

    Matrix multiply(double d) {
        double result[][] = new double[this.row][this.column];
        for (int i = 0; i < this.row; i++) {
            //System.out.println(i+"row = "+this.row+" 。 ");
           // System.out.println("col = "+this.column+" ! ");
            for (int j = 0; j < this.column; j++) {
                //System.out.print(j+"col = "+this.column+" ? ");
                result[i][j] = d * this.value[i][j];
            }
        }
        Matrix multiplication2 = new Matrix(this.row, this.column, result);
        return multiplication2;
    }

    Matrix multiply(Matrix target) throws Exception {
        if (this.column != target.row) {
            throw new Exception("The number of columns in the left matrix must equal to the number of rows in the right matrix! " +
                    "(乘法运算时左边矩阵的列数必须等于右边矩阵的行数!)");
        } else {
            double result[][] = new double[this.row][this.column];
            double c = 0;
            for (int i = 0; i < this.row; i++) {
                for (int j = 0; j < target.column; j++) {
                    //求C的元素值
                    for (int k = 0; k < this.column; k++) {
                        c += this.value[i][k] * target.value[k][j];
                    }
                    result[i][j] = c;
                    c = 0;
                }
            }
            Matrix multiplication1 = new Matrix(this.row, target.column, result);
            return multiplication1;
        }
    }

    double[][] multiply_T(Matrix target) throws Exception {
        if (this.column != target.row) {
            throw new Exception("The number of columns in the left matrix must equal to the number of rows in the right matrix! " +
                    "(乘法运算时左边矩阵的列数必须等于右边矩阵的行数!)");
        } else {
            double result[][] = new double[this.row][this.column];
            double c = 0;
            for (int i = 0; i < this.row; i++) {
                for (int j = 0; j < target.column; j++) {
                    //求C的元素值
                    for (int k = 0; k < this.column; k++) {
                        c += this.value[i][k] * target.value[k][j];
                    }
                    result[i][j] = c;
                    c = 0;
                }
            }
            return result;
        }
    }

    void setNum(int num) {
        this.num = num;
    }



    Matrix transpose() {
        double result[][] = new double[this.column][this.row];
        for (int i = 0; i < this.row; i++) {
            for (int j = 0; j < this.column; j++) {
                result[j][i] = this.value[i][j];
            }
        }
        Matrix transposed = new Matrix(this.column, this.row, result);
        return transposed;
    }

    double[][] transpose_T() {
        double result[][] = new double[this.column][this.row];
        for (int i = 0; i < this.row; i++) {
            for (int j = 0; j < this.column; j++) {
                result[j][i] = this.value[i][j];
            }
        }
        return result;
    }


    Matrix qujunzhi() {
        double result[][] = new double[this.row][this.column];
        double[] matrix_mean = new double [this.column];
        for (int j = 0; j < this.column; j++) {
            matrix_mean[j] = 0;
            for (int i = 0; i < this.row; i++) {
                matrix_mean[j] = matrix_mean[j] + this.value[i][j];
                //result[j][i] = this.value[i][j];
            }
            matrix_mean[j] = matrix_mean[j] / (this.row);
            //System.out.println("row= "+this.row);
            //System.out.println("matrix = "+matrix_mean[j]+"i= "+i);
            for(int i=0;i<this.row;i++)
            {
                result[i][j] = this.value[i][j] -  matrix_mean[j];
                //System.out.println("matrix_X "+i+","+j+"= "+ result[i][j]);
            }
        }

        Matrix qujunzhied = new Matrix(this.row, this.column, result);
        return qujunzhied;
    }

    Matrix cov() {
        //按各列分别去均值
        double cov_qujunzhi[][] = new double[this.row][this.column];
        double[] matrix_mean = new double [this.column];
        for (int j = 0; j < this.column; j++) {
            matrix_mean[j] = 0;
            for (int i = 0; i < this.row; i++) {
                matrix_mean[j] = matrix_mean[j] + this.value[i][j];
                //result[j][i] = this.value[i][j];
            }
            matrix_mean[j] = matrix_mean[j] / (this.row);
            //System.out.println("row= "+this.row);
            //System.out.println("matrix = "+matrix_mean[j]+"i= "+i);
            for(int i=0;i<this.row;i++)
            {
                cov_qujunzhi[i][j] = this.value[i][j] -  matrix_mean[j];
                //System.out.println("matrix_X "+i+","+j+"= "+ result[i][j]);
            }
        }

        double cov_transpos[][] = new double[this.column][this.row];
        for (int i = 0; i < this.row; i++) {
            for (int j = 0; j < this.column; j++) {
                cov_transpos[j][i] = cov_qujunzhi[i][j];
            }
        }

        double cov_multiplication[][] = new double[this.column][this.column];
        double c = 0;
        for (int i = 0; i < this.column; i++) {
            //for (int j = 0; j < target.column; j++)
            for (int j = 0; j < this.column; j++){
                //求C的元素值
                for (int k = 0; k < this.row; k++) {
                    c += cov_transpos[i][k]*cov_qujunzhi[k][j];
                }
                //cov_multiplication[i][j] = c;
                cov_multiplication[i][j] = ((float)c/(this.row-1));
                c = 0;
            }
        }



        Matrix coved = new Matrix(this.column, this.column, cov_multiplication);
        //Matrix coved = new Matrix(this.column, this.row, cov_transpos);
        return coved;

        //return cov_multiplication;
    }


    double [][]change_value(){
        double result[][] ;
        result = this.value;
        return result;
    }
    int change_row(){
        int result ;
        result = this.row;
        return result;
    }
    int change_column(){
        int result ;
        result = this.column;
        return result;
    }


    String solve(Matrix target) {
        //Augmented Matrix
        double aug[][] = new double[this.row][this.column + 1];
        for (int i = 0; i < this.row; i++) {
            for (int j = 0; j < this.column + 1; j++) {
                if (j == this.column) {
                    aug[i][j] = target.value[i][0];
                } else {
                    aug[i][j] = this.value[i][j];
                }
            }
        }
        Matrix augmented = new Matrix(this.row, this.column + 1, aug);
        Matrix solution = GaussElimination(augmented);
        StringBuilder sb = new StringBuilder();
        sb.append("\r\n");
        switch (solution.num) {
            case 0:
                sb.append("AX = B has no solution(方程无解).");
                break;
            case 1:
                sb.append("AX = B has one unique solution(方程有解),\r\n");
                sb.append("X =");
                sb.append(solution.print());
                break;
            default:
                sb.append("AX = B has infinite many solutions(方程有无穷多个解).");
                break;
        }
        return sb.toString();
    }

    Matrix GaussElimination(Matrix augmented) {
        double[][] sol = new double[augmented.row][1];
        //TODO: 1. calculate arbitrary Equation System; 2. catch exception
        //r2-r3,r3*(1/2),r1-r3
        for (int j = 0; j < augmented.column; j++) {
            augmented.value[1][j] -= augmented.value[2][j];
            augmented.value[2][j] *= 0.5;
            augmented.value[0][j] -= augmented.value[2][j];
        }
        //r2*(1/3),r1-r2
        for (int j = 0; j < augmented.column; j++) {
            augmented.value[1][j] /= 3;
            augmented.value[0][j] -= augmented.value[1][j];
        }
        //r3-r1
        for (int j = 0; j < augmented.column; j++) {
            augmented.value[2][j] -= augmented.value[0][j];
        }
        //switch lines: r3->r1, r2->r3, r1->r2
        for (int j = 0; j < augmented.column; j++) {
            double temp0 = augmented.value[0][j];
            double temp1 = augmented.value[1][j];
            double temp2 = augmented.value[2][j];
            augmented.value[0][j] = temp2;
            augmented.value[1][j] = temp0;
            augmented.value[2][j] = temp1;
        }
        //output X
        for (int i = 0; i < augmented.row; i++) {
            sol[i][0] = augmented.value[i][3];
        }
        Matrix solution = new Matrix(augmented.row, 1, sol);
        //set no. of solutions
        solution.setNum(1);
        return solution;
    }

    String print() {
        DecimalFormat df = new DecimalFormat("0.##");
        StringBuilder sb = new StringBuilder();
        sb.append("\r\n");
        for (int i = 0; i < this.row; i++) {
            for (int j = 0; j < this.column; j++) {
                if (j == this.column - 1) {
                    sb.append(df.format(this.value[i][j]) + "\r\n");
                } else {
                    sb.append(df.format(this.value[i][j]) + " ");
                }
            }
        }
        sb.append("------");
        return sb.toString();
    }
}


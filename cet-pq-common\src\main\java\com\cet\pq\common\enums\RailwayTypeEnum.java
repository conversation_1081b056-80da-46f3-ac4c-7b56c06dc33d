package com.cet.pq.common.enums;

import com.cet.pq.common.model.IdTextPair;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 铁路类型
 * @date 2021/2/2 15:18
 */

public enum RailwayTypeEnum {
    //铁路类型
    highspeedrailway(2, "高速铁路"), commonelectriciron(1, "普通电铁");

    private Integer id;
    private String text;

    RailwayTypeEnum(Integer id, String name) {
        this.id = id;
        this.text = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return text;
    }

    public static List<IdTextPair> toList() {
        List<IdTextPair> result = new ArrayList<>();
        for (int i = 0; i < RailwayTypeEnum.values().length; i++) {
            RailwayTypeEnum railwayTypeEnum = RailwayTypeEnum.values()[i];
            result.add(new IdTextPair(railwayTypeEnum.id, railwayTypeEnum.text));
        }
        return result;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (RailwayTypeEnum railwayTypeEnum : RailwayTypeEnum.values()) {
            if (railwayTypeEnum.id.equals(id)) {
                return railwayTypeEnum.text;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (RailwayTypeEnum railwayTypeEnum : RailwayTypeEnum.values()) {
            if (railwayTypeEnum.getName().trim().equalsIgnoreCase(name)) {
                return railwayTypeEnum.getId();
            }
        }
        return null;
    }
}

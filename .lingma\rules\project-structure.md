# 项目结构说明

## 一、目录结构与分层职责

- config：初始化数据相关的类、系统参数、启动配置等。例如：数据初始化脚本、系统常量加载、启动监听器等。
- configuration：配置文件相关类（如数据源、缓存、消息队列等配置），如RedisConfiguration、DataSourceConfiguration等。
- constant：常量定义，包含全局常量、枚举、错误码等。
- controller：控制层，负责接口请求、参数校验、调用Service、返回封装结果。应包含API接口、参数校验、统一异常处理等。
- service：业务逻辑层，接口与实现分离，处理核心业务。应包含业务接口、实现类、事务管理、业务聚合等。
- dao：数据库操作层，负责数据持久化。应包含Mapper接口、XML映射、Repository等。
- model：数据库实体类（PO/DO），与表结构一一对应。仅放数据库映射实体。
- entity：业务实体类（DTO/VO/BO等），用于数据传输和视图展示。仅放DTO、VO、BO等非数据库实体。
- utils：工具类，通用方法、辅助功能。应包含字符串、日期、集合、加解密等通用工具。
- test：单元测试。应包含各层单元测试、集成测试、Mock测试等。

## 二、结构化说明
- 严格分层架构：控制层、服务层、数据访问层、实体层、数据模型层、公共层、配置层、异常处理层。
- 各层职责单一，命名规范，注解使用标准。
- DTO/VO/Model等数据模型类统一放在model文件夹下，实体类统一放在entity包下。
- 按功能模块和类型分包，结构清晰。
- 配置文件分环境管理，命名规范。
- 目录结构、命名、分层应清晰、易维护。

## 三、pom.xml初始化配置说明
- 项目根pom.xml应统一继承公司基础父工程，如：
  ```xml
  <parent>
    <groupId>com.cet.matterhorn</groupId>
    <artifactId>matterhorn-basic-service-parent</artifactId>
    <version>0.0.13</version>
  </parent>
  ```
- 依赖管理：
  - 统一依赖版本由父pom管理，子模块无需重复声明版本。
  - 常用依赖如spring-boot-starter、mybatis-plus-boot-starter、lombok、slf4j、swagger、hutool等。
  - 推荐使用dependencyManagement集中管理依赖版本。
- 插件配置：
  - maven-compiler-plugin、spring-boot-maven-plugin等。
- 版本管理：
  - 统一使用UTF-8编码，Java 1.8。
  - 依赖、插件、构建参数等应与父工程保持一致。
- 其他：
  - 各模块pom.xml需声明<module>，保证多模块聚合构建。
  - 依赖范围（scope）合理设置，避免冗余依赖。

## 目录结构举例
```
project-root/
  ├─ config/
  ├─ configuration/
  ├─ constant/
  ├─ controller/
  ├─ service/
  ├─ dao/
  ├─ model/
  ├─ entity/
  ├─ utils/
  └─ test/
```



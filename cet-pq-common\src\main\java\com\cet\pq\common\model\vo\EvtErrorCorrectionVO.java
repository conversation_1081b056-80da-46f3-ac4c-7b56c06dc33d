package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/05/18 10:00
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class EvtErrorCorrectionVO {

    /**
     * 测量零序误差识别上部分
     */
    private List<EvtErrorCorrectionPart1VO> part1;

    /**
     * 测量零序误差识别中间部分
     */
    private List<EvtErrorCorrectionPart2VO> part2;

    /**
     * 测量零序误差识别中间判断结果部分
     */
    private String result;

    /**
     * 是否显示修正结果：0：不显示，1：显示
     */
    private Integer exh;

    /**
     * 测量零序误差识别下部分
     */
    private List<EvtErrorCorrectionPart3VO> part3;


}

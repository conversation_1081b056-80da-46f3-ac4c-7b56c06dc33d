package com.cet.pq.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Description: 自定义数据源配置 如果没有配置compatibleTablePD_TB_PQ_05为true 则该数据源不会配置
 * @date 2021/3/26 13:57
 */
@Configuration
@Component("dataSourceConfig")
@ConditionalOnProperty(prefix = "compatibleTablePD-TB-PQ-05", name = "enable", havingValue = "true")
public class DataSourceConfig {

    @Bean(name = "aggdata")
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource businessDbDataSource() {
        return DataSourceBuilder.create().build();
    }


}

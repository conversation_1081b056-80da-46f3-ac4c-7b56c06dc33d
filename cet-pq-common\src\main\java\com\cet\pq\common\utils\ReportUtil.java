package com.cet.pq.common.utils;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.ExcelConstant;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.*;
import com.cet.pq.common.model.excel.ExportParameter;
import com.cet.pq.common.model.excel.InsertData;
import com.cet.pq.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.pq.common.model.report.CompanyNameInfo;
import com.cet.pq.common.model.report.LowerCompanyDTO;
import com.cet.pq.common.model.report.NodeByZhiKanInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cet.pq.common.utils.ParseDataUtil.*;
/**
 * <AUTHOR>
 * @Date 2024/8/22 10:54
 * @Description
 */
public class ReportUtil {

    private static final String ZHI_KAN_SUBSTATION_TYPE = "110";
    private static final String ZHI_KAN_LINE_TYPE = "100";
    private static final List<String> ZHI_KAN_TRANSFORMER_TYPE = Arrays.asList("333", "334", "335");
    // 智瞰数据，5，6
    private static final List<Integer> ZK_DATATYPE = Arrays.asList(5, 6);
    private static final List<Integer> ZK_DATATYPE_TRANSFORMER = Arrays.asList(5, 6);


    public static LowerCompanyDTO setLowerCompany(String modelLabel, Long modelId) {
        List<String> props = Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME, ColumnName.REGIONALCODE, ColumnName.ZHI_KAN_NAME);
        SingleModelConditionDTO cityCompanyDTO = new SingleModelConditionDTO(TableName.CITYCOMPANY, props);
        SingleModelConditionDTO provinceCompanyDTO = new SingleModelConditionDTO(TableName.PROVINCECOMPANY, props);
        SingleModelConditionDTO countyCompanyDTO = new SingleModelConditionDTO(TableName.COUNTYCOMPANY, props);
        List<SingleModelConditionDTO> subConditions = new ArrayList<>();
        List<String> companyList = null;
        String lowCompanyModel = null;
        if (TableName.PROVINCECOMPANY.equals(modelLabel)) {
            subConditions.add(provinceCompanyDTO);
            subConditions.add(cityCompanyDTO);
            lowCompanyModel = TableName.CITYCOMPANY;
            companyList = Arrays.asList(TableName.PROVINCECOMPANY, TableName.CITYCOMPANY);
        } else if (TableName.CITYCOMPANY.equals(modelLabel)) {
            subConditions.add(cityCompanyDTO);
            cityCompanyDTO.setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            subConditions.add(countyCompanyDTO);
            lowCompanyModel = TableName.COUNTYCOMPANY;
            companyList = Arrays.asList(TableName.CITYCOMPANY, TableName.COUNTYCOMPANY);
        } else if (TableName.COUNTYCOMPANY.equals(modelLabel)) {
            subConditions.add(cityCompanyDTO);
            countyCompanyDTO.setFilter(new ConditionBlockCompose(Collections.singletonList(new ConditionBlock(ColumnName.ID, ConditionBlock.OPERATOR_EQ, modelId))));
            subConditions.add(countyCompanyDTO);
            lowCompanyModel = null;
            companyList = Collections.singletonList(TableName.COUNTYCOMPANY);
        } else {
            throw new CommonManagerException("modelLabel异常！");
        }
        LowerCompanyDTO lowerCompanyDTO = new LowerCompanyDTO();
        lowerCompanyDTO.setSubConditions(subConditions);
        lowerCompanyDTO.setCompanyList(companyList);
        lowerCompanyDTO.setLowCompanyModel(lowCompanyModel);
        return lowerCompanyDTO;
    }

    /**
     * @Description: 获取单位信息
     **/
    public static List<Map<String, Object>> getUnit(Long modelId, String modelLabel, Boolean isUpLoad, Integer monitorType, List<SingleModelConditionDTO> tempSubConditions) {
        List<SingleModelConditionDTO> subConditions = new ArrayList<>(tempSubConditions);
        SingleModelConditionDTO lineDTO = new SingleModelConditionDTO(TableName.LINE, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.MONITORSORT,
                ColumnName.MONITORMODE, ColumnName.MONITORTYPE, ColumnName.PQMONITORTYPE, ColumnName.ISUPLOAD));
        List<ConditionBlock> filter = new ArrayList<>();
        if (Boolean.TRUE.equals(isUpLoad)) {
            filter.add(new ConditionBlock(ColumnName.ISUPLOAD, ConditionBlock.OPERATOR_EQ, Boolean.TRUE));
        }
        if (parseInteger(monitorType) > 0){
            filter.add(new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_EQ, monitorType));
        }
        if (CollectionUtils.isNotEmpty(filter)){
            ConditionBlockCompose conditionBlockCompose = new ConditionBlockCompose(filter);
            lineDTO.setFilter(conditionBlockCompose);
        }
        subConditions.add(lineDTO);
//        subConditions.add(new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.ID, ColumnName.NAME)));
        ModelIdPairDTO treeNode = new ModelIdPairDTO(modelId, modelLabel);
        Long id = null;
        if (TableName.PROVINCECOMPANY.equals(modelLabel)){
            treeNode = null;
            id = modelId;
        }
        return ModelServiceUtils.getInterRelations(Collections.singletonList(id), TableName.PROVINCECOMPANY, null, treeNode, subConditions, null);
    }

    /**
     * @Description: 获取单位信息
     **/
    public static List<Map<String, Object>> getUnitByModel(Long modelId, String modelLabel, Boolean isUpLoad, Integer monitorType, List<SingleModelConditionDTO> tempSubConditions) {
        List<SingleModelConditionDTO> subConditions = new ArrayList<>(tempSubConditions);
        SingleModelConditionDTO lineDTO = new SingleModelConditionDTO(TableName.LINE, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.MONITORSORT,
                ColumnName.MONITORMODE, ColumnName.MONITORTYPE, ColumnName.PQMONITORTYPE, ColumnName.ISUPLOAD));
        List<ConditionBlock> filter = new ArrayList<>();
        if (Boolean.TRUE.equals(isUpLoad)) {
            filter.add(new ConditionBlock(ColumnName.ISUPLOAD, ConditionBlock.OPERATOR_EQ, Boolean.TRUE));
        }
        if (parseInteger(monitorType) > 0){
            filter.add(new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_EQ, monitorType));
        }
        if (CollectionUtils.isNotEmpty(filter)){
            ConditionBlockCompose conditionBlockCompose = new ConditionBlockCompose(filter);
            lineDTO.setFilter(conditionBlockCompose);
        }
        subConditions.add(lineDTO);
        return ModelServiceUtils.getInterRelations(Collections.singletonList(modelId), modelLabel, null, null, subConditions, null);
    }

    public static List<Long> runLineIdList(Long startTime, Long endTime, Integer aggregationCycle, List<Integer> status) {
        // 设置查询条件
        List<ConditionBlock> filter = new ArrayList<>();
        filter.add(new ConditionBlock(ColumnName.LOGTIME, ConditionBlock.OPERATOR_GE, startTime));
        filter.add(new ConditionBlock(ColumnName.LOGTIME, ConditionBlock.OPERATOR_LT, endTime));
        filter.add(new ConditionBlock(ColumnName.AGGREGATIONCYCLE, ConditionBlock.OPERATOR_EQ, aggregationCycle));
        if (CollectionUtils.isNotEmpty(status)) {
            filter.add(new ConditionBlock(ColumnName.MONITORSTATUS, ConditionBlock.OPERATOR_IN, status));
        }
        List<PQHistoryStatus> lineHis = ModelServiceUtils.getSingleModelByGroup(null, TableName.PQHISTORYSTATUS, filter, null, null, PQHistoryStatus.class);
        return lineHis.stream().map(PQHistoryStatus::getLineId).collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @Description 获取单位、变电站信息
     */
    public static CompanyNameInfo getCompanyNameInfoByLine(Map<String, Object> dataMap) {
        CompanyNameInfo companyNameInfo = new CompanyNameInfo();
        // 获取变电站信息
        List<Map<String, Object>> substations = parseList(dataMap.get(ColumnName.SUBSTATION_MODEL));
        if (CollectionUtils.isEmpty(substations)){
            substations = Collections.singletonList(dataMap);
        }
        companyNameInfo.setSubstationName(parseString(substations.get(0).get(ColumnName.NAME)));
        // 获取市级、县级信息
        List<Map<String, Object>> cityCompany = new ArrayList<>();
        List<Map<String, Object>> countyCompany = parseList(substations.get(0).get(ColumnName.COUNTYCOMPANY_MODEL));
        if (CollectionUtils.isEmpty(countyCompany)){
            cityCompany = parseList(substations.get(0).get(ColumnName.CITYCOMPANY_MODEL));
        } else {
            cityCompany = parseList(countyCompany.get(0).get(ColumnName.CITYCOMPANY_MODEL));
            companyNameInfo.setCountryCompanyName(parseString(countyCompany.get(0).get(ColumnName.FULLNAME)));
        }
        if (CollectionUtils.isEmpty(cityCompany)){
            return companyNameInfo;
        }
        companyNameInfo.setCityCompanyName(parseString(cityCompany.get(0).get(ColumnName.FULLNAME)));
        // 获取省级信息
        List<Map<String, Object>> provinceCompany = parseList(cityCompany.get(0).get(ColumnName.PROVINCECOMPANY_MODEL));
        if (CollectionUtils.isNotEmpty(provinceCompany)){
            companyNameInfo.setProvinceCompanyName(parseString(provinceCompany.get(0).get(ColumnName.FULLNAME)));
        }
        return companyNameInfo;
    }

    public static List<Map<String, Object>> getSubstation(String modelLabel, Long modelId, Page page){
        ModelIdPairDTO treeNode = new ModelIdPairDTO(modelId, modelLabel);
        List<Long> ids = null;
        if (TableName.SUBSTATION.equals(modelLabel)) {
            treeNode = null;
            ids = Collections.singletonList(modelId);
        }
        // 获取变电站信息
        List<SingleModelConditionDTO> subLayerConditions = Stream.of(
                new SingleModelConditionDTO(TableName.LINE, Arrays.asList(ColumnName.ID, ColumnName.MONITORTYPE)),
                new SingleModelConditionDTO(TableName.COUNTYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(TableName.PROVINCECOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME))
        ).collect(Collectors.toList());
        ResultWithTotal<List<Map<String, Object>>> pageData = ModelServiceUtils.getInterRelations(ids, TableName.SUBSTATION, null, treeNode, subLayerConditions, null, null, page);
        return pageData.getData();
    }

    public static ResultWithTotal<List<Map<String, Object>>> getSubstation(String modelLabel, Long modelId, Page page, Integer monitorType, Boolean isUpload){
        // 处理节点信息
        ModelIdPairDTO treeNode = new ModelIdPairDTO(modelId, modelLabel);
        List<Long> ids = null;
        if (TableName.SUBSTATION.equals(modelLabel)) {
            treeNode = null;
            ids = Collections.singletonList(modelId);
        }
        // 处理监测点信息
        SingleModelConditionDTO lineDTO = new SingleModelConditionDTO(TableName.LINE, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.MONITORSORT,
                ColumnName.MONITORMODE, ColumnName.MONITORTYPE, ColumnName.PQMONITORTYPE, ColumnName.ISUPLOAD));
        List<ConditionBlock> filter = new ArrayList<>();
        if (Boolean.TRUE.equals(isUpload)) {
            filter.add(new ConditionBlock(ColumnName.ISUPLOAD, ConditionBlock.OPERATOR_EQ, Boolean.TRUE));
        }
        if (parseInteger(monitorType) > 0){
            filter.add(new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_EQ, monitorType));
        }
        if (CollectionUtils.isNotEmpty(filter)){
            ConditionBlockCompose conditionBlockCompose = new ConditionBlockCompose(filter);
            lineDTO.setFilter(conditionBlockCompose);
        }
        // 获取变电站信息
        List<SingleModelConditionDTO> subLayerConditions = Stream.of(
                new SingleModelConditionDTO(TableName.COUNTYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(TableName.PROVINCECOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME))
        ).collect(Collectors.toList());
        subLayerConditions.add(lineDTO);
        return ModelServiceUtils.getInterRelations(ids, TableName.SUBSTATION, null, treeNode, subLayerConditions, null, null, page);
    }

    public static void setExcelHeaders(ExportParameter exportParameter, Integer cloNum, Integer aggregationCycle) {
        if (aggregationCycle == AggregationCycle.ONE_MONTH){
            return;
        }
        exportParameter.getSheets().stream().peek(sheets -> {
            LinkedList<Object> row1 = new LinkedList<>();
            if (aggregationCycle == AggregationCycle.THREE_MONTHS){
                row1.add(ExcelConstant.ONLINE_LINE_NUM_QUARTER);
            }else {
                row1.add(ExcelConstant.ONLINE_LINE_NUM_YEAR);
            }
            LinkedList<Object> row2 = new LinkedList<>();
            for (int i=0;i<5;i++){
                if (aggregationCycle == AggregationCycle.THREE_MONTHS){
                    row2.add(ExcelConstant.NOW_QUARTER);
                    row2.add(ExcelConstant.NOW_YEAR_ACCUMULATION);
                }else {
                    row2.add(ExcelConstant.NOW_YEAR);
                    row2.add(ExcelConstant.LAST_YEAR);
                }
            }
            LinkedList<InsertData> insertData = new LinkedList<>();
            if (CollectionUtils.isNotEmpty(sheets.getInsertData())){
                insertData.addAll(sheets.getInsertData());
            }
            insertData.add(new InsertData(1, cloNum, row1));
            insertData.add(new InsertData(2, cloNum+1, row2));
            sheets.setInsertData(insertData);
        }).collect(Collectors.toList());
    }

    public static ResultWithTotal<List<Map<String, Object>>> getLineList(String modelLabel, Long modelId, Page page){
        ModelIdPairDTO treeNode = new ModelIdPairDTO(modelId, modelLabel);
        List<Long> ids = null;
        if (TableName.LINE.equals(modelLabel)) {
            treeNode = null;
            ids = Collections.singletonList(modelId);
        }
        // 获取变电站信息
        List<SingleModelConditionDTO> subLayerConditions = Stream.of(
                new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
                new SingleModelConditionDTO(TableName.COUNTYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME)),
                new SingleModelConditionDTO(TableName.PROVINCECOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.FULLNAME))
        ).collect(Collectors.toList());
        List<String> props = Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.VOLTCLASS, ColumnName.VOLTCLASS_TEXT);
        return ModelServiceUtils.getInterRelations(ids, TableName.LINE, null, treeNode, subLayerConditions, null, null, page, props);
    }


    @Data
    private static class PQHistoryStatus {
        private Long id;
        @JsonProperty("pqmonitor_id")
        private Long lineId;
    }

    public static NodeByZhiKanInfo getNodeByZhiKan(String modelType, String modelName) {
        NodeByZhiKanInfo result = new NodeByZhiKanInfo();
        if (ZHI_KAN_SUBSTATION_TYPE.equals(modelType)) {
            // 通过名称获取变电站信息
            List<ConditionBlock> modelFilters = Arrays.asList(
                    new ConditionBlock(ColumnName.ZHI_KAN_NAME, ConditionBlock.OPERATOR_EQ, modelName)
            );
            List<Map<String, Object>> modelList = ModelServiceUtils.getRelations(null, TableName.SUBSTATION, modelFilters, null, null, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.VOLTCLASS_TEXT));
            if (CollectionUtils.isEmpty(modelList)) {
                return result;
            }
            result.setModelId(parseLong(modelList.get(0).get(ColumnName.ID)));
            result.setModelLabel(TableName.SUBSTATION);
            result.setModelName(parseString(modelList.get(0).get(ColumnName.NAME)));
            result.setVoltageLevel(parseString(modelList.get(0).get(ColumnName.VOLTCLASS_TEXT)));
        } else if (ZHI_KAN_LINE_TYPE.equals(modelType)) {
            // 通过名称获取智瞰测点信息
            List<ConditionBlock> modelFilters = Arrays.asList(
                    new ConditionBlock(ColumnName.NAME, ConditionBlock.OPERATOR_EQ, modelName),
                    new ConditionBlock(ColumnName.DATATYPE, ConditionBlock.OPERATOR_IN, ZK_DATATYPE)
            );
            List<Map<String, Object>> modelList = ModelServiceUtils.getRelations(null, TableName.ZHIKANDATA, modelFilters, null, null, Arrays.asList(ColumnName.ID, ColumnName.NAME));
            if (CollectionUtils.isEmpty(modelList)) {
                return result;
            }
            Long zhiKanId = ParseDataUtil.parseLong(modelList.get(0).get(ColumnName.ID));
            // 通过智瞰id获取监测点信息集合
            List<ConditionBlock> lineFilters = Arrays.asList(
                    new ConditionBlock(ColumnName.RELATED_MODEL_LABEL, ConditionBlock.OPERATOR_EQ, TableName.ZHIKANDATA),
                    new ConditionBlock(ColumnName.RELATED_MODEL_ID, ConditionBlock.OPERATOR_EQ, zhiKanId)
            );
            List<Map<String, Object>> lineList = ModelServiceUtils.getRelations(null, TableName.LINEFUSION, lineFilters, null, null, Arrays.asList(ColumnName.ID, ColumnName.LINEID));
            if (CollectionUtils.isEmpty(lineList)) {
                return result;
            }
            List<Long> lineIds = lineList.stream().map(map -> ParseDataUtil.parseLong(map.get(ColumnName.LINEID))).collect(Collectors.toList());
            result.setModelIds(lineIds);
            result.setModelId(parseLong(lineList.get(0).get(ColumnName.LINEID)));
            result.setModelLabel(TableName.LINE);
        } else if (ZHI_KAN_TRANSFORMER_TYPE.contains(modelType)) {
            // 通过名称获取智瞰测点信息
            List<ConditionBlock> modelFilters = Arrays.asList(
                    new ConditionBlock(ColumnName.DISTRIBUTION_TRANSFORMER, ConditionBlock.OPERATOR_EQ, modelName),
                    new ConditionBlock(ColumnName.LINETYPE, ConditionBlock.OPERATOR_EQ, 9)
            );
            List<Map<String, Object>> modelList = ModelServiceUtils.getRelations(null, TableName.ZHIKANDATA, modelFilters, null, null, Arrays.asList(ColumnName.ID, ColumnName.NAME));
            if (CollectionUtils.isEmpty(modelList)) {
                return result;
            }
            List<Long> zhiKanIds = modelList.stream().map(map -> ParseDataUtil.parseLong(map.get(ColumnName.ID))).collect(Collectors.toList());
//            Long zhiKanId = ParseDataUtil.parseLong(modelList.get(0).get(ColumnName.ID));
            // 通过智瞰id获取监测点信息集合
            List<ConditionBlock> lineFilters = Arrays.asList(
                    new ConditionBlock(ColumnName.RELATED_MODEL_LABEL, ConditionBlock.OPERATOR_EQ, TableName.ZHIKANDATA),
                    new ConditionBlock(ColumnName.RELATED_MODEL_ID, ConditionBlock.OPERATOR_IN, zhiKanIds)
            );
            List<Map<String, Object>> lineList = ModelServiceUtils.getRelations(null, TableName.LINEFUSION, lineFilters, null, null, Arrays.asList(ColumnName.ID, ColumnName.LINEID));
            if (CollectionUtils.isEmpty(lineList)) {
                return result;
            }
            List<Long> lineIds = lineList.stream().map(map -> ParseDataUtil.parseLong(map.get(ColumnName.LINEID))).collect(Collectors.toList());
            result.setModelIds(lineIds);
            result.setModelId(parseLong(lineList.get(0).get(ColumnName.LINEID)));
            result.setModelLabel(TableName.LINE);
        }
        return result;
    }

}

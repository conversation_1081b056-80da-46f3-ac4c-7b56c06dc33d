package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 	定检周期枚举类
 * <AUTHOR>
 *
 */
public enum CheckcycleEnum {
    //定检周期
	checkcycle_0(4D,"0.5年"),
	checkcycle_1(1D,"1年"),
	checkcycle_2(2D,"2年"),
	checkcycle_3(3D,"3年");

	private Double key;
	private String value;

	private CheckcycleEnum(Double key, String value) {
		this.key = key;
		this.value = value;
	}
	
	public static Double getCheckcycleNoThrows(String levelStr) {
		if (StringUtils.isEmpty(levelStr)) {
			return null;
		}
		for (CheckcycleEnum checkcycleEnum : CheckcycleEnum.values()) {
			if(checkcycleEnum.value.equals(levelStr)) {
				return checkcycleEnum.key;
			}
		}
		return 0D;
	}

	public static String getValueByKeyNoThrows(Double key) {
		for (CheckcycleEnum checkcycleEnum : CheckcycleEnum.values()) {
		    if (checkcycleEnum.key.equals(key)) {
		    	return checkcycleEnum.value;
			}
		}
		return StringUtils.EMPTY;
	}

	public Double getKey() {
		return key;
	}

	public void setKey(Double key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
}

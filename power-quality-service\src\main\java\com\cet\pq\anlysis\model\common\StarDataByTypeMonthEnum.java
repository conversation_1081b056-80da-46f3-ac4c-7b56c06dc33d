package com.cet.pq.anlysis.model.common;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/8/28 11:17
 * @Description 星形接线根据类型的dataid集合(月报表)
 */
public enum StarDataByTypeMonthEnum {
    VOLTAGE_EFFECTIVE_VALUE_PHASE(1, "相电压有效值(kV)", Arrays.asList(2L, 3L, 4L)),
    VOLTAGE_EFFECTIVE_VALUE_LINE(1, "线电压有效值(kV)", Arrays.asList(5L, 6L, 7L)),
    CURRENT_EFFECTIVE_VALUE(1, "电流总有效值(A)", Arrays.asList(1000001L, 1000002L, 1000003L)),
    VOLTAGE_DEVIATION(1, "电压偏差(%)", Arrays.asList(94L, 95L, 96L)),
    VOLTAGE_THD(1, "电压总谐波畸变率(%)", Arrays.asList(32L, 33L, 34L)),
    CURRENT_THD(1, "电流总谐波畸变率(%)", Arrays.asList(1000016L, 1000017L, 1000018L)),
    LONG_FLASH(1, "长时闪变", Arrays.asList(82L, 83L, 84L)),
    SHORT_FLASH(1, "短时闪变", Arrays.asList(76L, 77L, 78L)),
    PHASE_POWER(1, "分相功率(kW)", Arrays.asList(2000001L, 2000002L, 2000003L)),

    FREQUENCY(2, "频率(Hz)", Arrays.asList(1L)),
    FREQUENCY_DEVIATION(2, "频率偏差(Hz)", Arrays.asList(97L)),
    POWER_FACTOR(2, "功率因数", Arrays.asList(2000016L)),
    TOTAL_ACTIVE_POWER(2, "总有功功率(kW)", Arrays.asList(2000004L)),
    TOTAL_REACTIVE_POWER(2, "总无功功率(kVar)", Arrays.asList(2000008L)),
    TOTAL_APPARENT_POWER(2, "总视在功率(kVA)", Arrays.asList(2000012L)),

    // 电压、电流不平衡
    VOLTAGE_UNBALANCE(3, "电压", Arrays.asList(70L, 71L)),
    VOLTAGE_UNBALANCE2(3, "电压不平衡", Arrays.asList(69L)),
    CURRENT_UNBALANCE(3, "电流", Arrays.asList(1000036L, 1000037L)),
    CURRENT_UNBALANCE2(3, "电流不平衡", Arrays.asList(1000035L)),

    FUNDAMENTAL_VOLTAGE(4, "基波电压(kV)", Arrays.asList(20L, 21L, 22L)),
    HARMONIC_VOLTAGE_2ND(4, "2次谐波电压(%)", Arrays.asList(10002L, 20002L, 30002L)),
    HARMONIC_VOLTAGE_3RD(4, "3次谐波电压(%)", Arrays.asList(10003L, 20003L, 30003L)),
    HARMONIC_VOLTAGE_4RD(4, "4次谐波电压(%)", Arrays.asList(10004L, 20004L, 30004L)),
    HARMONIC_VOLTAGE_5RD(4, "5次谐波电压(%)", Arrays.asList(10005L, 20005L, 30005L)),
    HARMONIC_VOLTAGE_6RD(4, "6次谐波电压(%)", Arrays.asList(10006L, 20006L, 30006L)),
    HARMONIC_VOLTAGE_7RD(4, "7次谐波电压(%)", Arrays.asList(10007L, 20007L, 30007L)),
    HARMONIC_VOLTAGE_8RD(4, "8次谐波电压(%)", Arrays.asList(10008L, 20008L, 30008L)),
    HARMONIC_VOLTAGE_9RD(4, "9次谐波电压(%)", Arrays.asList(10009L, 20009L, 30009L)),
    HARMONIC_VOLTAGE_10RD(4, "10次谐波电压(%)", Arrays.asList(10010L, 20010L, 30010L)),
    HARMONIC_VOLTAGE_11RD(4, "11次谐波电压(%)", Arrays.asList(10011L, 20011L, 30011L)),
    HARMONIC_VOLTAGE_12RD(4, "12次谐波电压(%)", Arrays.asList(10012L, 20012L, 30012L)),
    HARMONIC_VOLTAGE_13RD(4, "13次谐波电压(%)", Arrays.asList(10013L, 20013L, 30013L)),
    HARMONIC_VOLTAGE_14RD(4, "14次谐波电压(%)", Arrays.asList(10014L, 20014L, 30014L)),
    HARMONIC_VOLTAGE_15RD(4, "15次谐波电压(%)", Arrays.asList(10015L, 20015L, 30015L)),
    HARMONIC_VOLTAGE_16RD(4, "16次谐波电压(%)", Arrays.asList(10016L, 20016L, 30016L)),
    HARMONIC_VOLTAGE_17RD(4, "17次谐波电压(%)", Arrays.asList(10017L, 20017L, 30017L)),
    HARMONIC_VOLTAGE_18RD(4, "18次谐波电压(%)", Arrays.asList(10018L, 20018L, 30018L)),
    HARMONIC_VOLTAGE_19RD(4, "19次谐波电压(%)", Arrays.asList(10019L, 20019L, 30019L)),
    HARMONIC_VOLTAGE_20RD(4, "20次谐波电压(%)", Arrays.asList(10020L, 20020L, 30020L)),
    HARMONIC_VOLTAGE_21RD(4, "21次谐波电压(%)", Arrays.asList(10021L, 20021L, 30021L)),
    HARMONIC_VOLTAGE_22RD(4, "22次谐波电压(%)", Arrays.asList(10022L, 20022L, 30022L)),
    HARMONIC_VOLTAGE_23RD(4, "23次谐波电压(%)", Arrays.asList(10023L, 20023L, 30023L)),
    HARMONIC_VOLTAGE_24RD(4, "24次谐波电压(%)", Arrays.asList(10024L, 20024L, 30024L)),
    HARMONIC_VOLTAGE_25RD(4, "25次谐波电压(%)", Arrays.asList(10025L, 20025L, 30025L)),
    HARMONIC_VOLTAGE_26RD(4, "26次谐波电压(%)", Arrays.asList(10026L, 20026L, 30026L)),
    HARMONIC_VOLTAGE_27RD(4, "27次谐波电压(%)", Arrays.asList(10027L, 20027L, 30027L)),
    HARMONIC_VOLTAGE_28RD(4, "28次谐波电压(%)", Arrays.asList(10028L, 20028L, 30028L)),
    HARMONIC_VOLTAGE_29RD(4, "29次谐波电压(%)", Arrays.asList(10029L, 20029L, 30029L)),
    HARMONIC_VOLTAGE_30RD(4, "30次谐波电压(%)", Arrays.asList(10030L, 20030L, 30030L)),
    HARMONIC_VOLTAGE_31RD(4, "31次谐波电压(%)", Arrays.asList(10031L, 20031L, 30031L)),
    HARMONIC_VOLTAGE_32RD(4, "32次谐波电压(%)", Arrays.asList(10032L, 20032L, 30032L)),
    HARMONIC_VOLTAGE_33RD(4, "33次谐波电压(%)", Arrays.asList(10033L, 20033L, 30033L)),
    HARMONIC_VOLTAGE_34RD(4, "34次谐波电压(%)", Arrays.asList(10034L, 20034L, 30034L)),
    HARMONIC_VOLTAGE_35RD(4, "35次谐波电压(%)", Arrays.asList(10035L, 20035L, 30035L)),
    HARMONIC_VOLTAGE_36RD(4, "36次谐波电压(%)", Arrays.asList(10036L, 20036L, 30036L)),
    HARMONIC_VOLTAGE_37RD(4, "37次谐波电压(%)", Arrays.asList(10037L, 20037L, 30037L)),
    HARMONIC_VOLTAGE_38RD(4, "38次谐波电压(%)", Arrays.asList(10038L, 20038L, 30038L)),
    HARMONIC_VOLTAGE_39RD(4, "39次谐波电压(%)", Arrays.asList(10039L, 20039L, 30039L)),
    HARMONIC_VOLTAGE_40RD(4, "40次谐波电压(%)", Arrays.asList(10040L, 20040L, 30040L)),
    HARMONIC_VOLTAGE_41RD(4, "41次谐波电压(%)", Arrays.asList(10041L, 20041L, 30041L)),
    HARMONIC_VOLTAGE_42RD(4, "42次谐波电压(%)", Arrays.asList(10042L, 20042L, 30042L)),
    HARMONIC_VOLTAGE_43RD(4, "43次谐波电压(%)", Arrays.asList(10043L, 20043L, 30043L)),
    HARMONIC_VOLTAGE_44RD(4, "44次谐波电压(%)", Arrays.asList(10044L, 20044L, 30044L)),
    HARMONIC_VOLTAGE_45RD(4, "45次谐波电压(%)", Arrays.asList(10045L, 20045L, 30045L)),
    HARMONIC_VOLTAGE_46RD(4, "46次谐波电压(%)", Arrays.asList(10046L, 20046L, 30046L)),
    HARMONIC_VOLTAGE_47RD(4, "47次谐波电压(%)", Arrays.asList(10047L, 20047L, 30047L)),
    HARMONIC_VOLTAGE_48RD(4, "48次谐波电压(%)", Arrays.asList(10048L, 20048L, 30048L)),
    HARMONIC_VOLTAGE_49RD(4, "49次谐波电压(%)", Arrays.asList(10049L, 20049L, 30049L)),
    HARMONIC_VOLTAGE_50RD(4, "50次谐波电压(%)", Arrays.asList(10050L, 20050L, 30050L)),

    FUNDAMENTAL_CURRENT(5, "基波电流(A)", Arrays.asList(1000010L, 1000011L, 1000012L)),
    HARMONIC_CURRENT_2ND(5, "2次谐波电流(A)", Arrays.asList(1040002L, 1050002L, 1060002L)),
    HARMONIC_CURRENT_3ND(5, "3次谐波电流(A)", Arrays.asList(1040003L, 1050003L, 1060003L)),
    HARMONIC_CURRENT_4ND(5, "4次谐波电流(A)", Arrays.asList(1040004L, 1050004L, 1060004L)),
    HARMONIC_CURRENT_5ND(5, "5次谐波电流(A)", Arrays.asList(1040005L, 1050005L, 1060005L)),
    HARMONIC_CURRENT_6ND(5, "6次谐波电流(A)", Arrays.asList(1040006L, 1050006L, 1060006L)),
    HARMONIC_CURRENT_7ND(5, "7次谐波电流(A)", Arrays.asList(1040007L, 1050007L, 1060007L)),
    HARMONIC_CURRENT_8ND(5, "8次谐波电流(A)", Arrays.asList(1040008L, 1050008L, 1060008L)),
    HARMONIC_CURRENT_9ND(5, "9次谐波电流(A)", Arrays.asList(1040009L, 1050009L, 1060009L)),
    HARMONIC_CURRENT_10ND(5, "10次谐波电流(A)", Arrays.asList(1040010L, 1050010L, 1060010L)),
    HARMONIC_CURRENT_11ND(5, "11次谐波电流(A)", Arrays.asList(1040011L, 1050011L, 1060011L)),
    HARMONIC_CURRENT_12ND(5, "12次谐波电流(A)", Arrays.asList(1040012L, 1050012L, 1060012L)),
    HARMONIC_CURRENT_13ND(5, "13次谐波电流(A)", Arrays.asList(1040013L, 1050013L, 1060013L)),
    HARMONIC_CURRENT_14ND(5, "14次谐波电流(A)", Arrays.asList(1040014L, 1050014L, 1060014L)),
    HARMONIC_CURRENT_15ND(5, "15次谐波电流(A)", Arrays.asList(1040015L, 1050015L, 1060015L)),
    HARMONIC_CURRENT_16ND(5, "16次谐波电流(A)", Arrays.asList(1040016L, 1050016L, 1060016L)),
    HARMONIC_CURRENT_17ND(5, "17次谐波电流(A)", Arrays.asList(1040017L, 1050017L, 1060017L)),
    HARMONIC_CURRENT_18ND(5, "18次谐波电流(A)", Arrays.asList(1040018L, 1050018L, 1060018L)),
    HARMONIC_CURRENT_19ND(5, "19次谐波电流(A)", Arrays.asList(1040019L, 1050019L, 1060019L)),
    HARMONIC_CURRENT_20ND(5, "20次谐波电流(A)", Arrays.asList(1040020L, 1050020L, 1060020L)),
    HARMONIC_CURRENT_21ND(5, "21次谐波电流(A)", Arrays.asList(1040021L, 1050021L, 1060021L)),
    HARMONIC_CURRENT_22ND(5, "22次谐波电流(A)", Arrays.asList(1040022L, 1050022L, 1060022L)),
    HARMONIC_CURRENT_23ND(5, "23次谐波电流(A)", Arrays.asList(1040023L, 1050023L, 1060023L)),
    HARMONIC_CURRENT_24ND(5, "24次谐波电流(A)", Arrays.asList(1040024L, 1050024L, 1060024L)),
    HARMONIC_CURRENT_25ND(5, "25次谐波电流(A)", Arrays.asList(1040025L, 1050025L, 1060025L)),
    HARMONIC_CURRENT_26ND(5, "26次谐波电流(A)", Arrays.asList(1040026L, 1050026L, 1060026L)),
    HARMONIC_CURRENT_27ND(5, "27次谐波电流(A)", Arrays.asList(1040027L, 1050027L, 1060027L)),
    HARMONIC_CURRENT_28ND(5, "28次谐波电流(A)", Arrays.asList(1040028L, 1050028L, 1060028L)),
    HARMONIC_CURRENT_29ND(5, "29次谐波电流(A)", Arrays.asList(1040029L, 1050029L, 1060029L)),
    HARMONIC_CURRENT_30ND(5, "30次谐波电流(A)", Arrays.asList(1040030L, 1050030L, 1060030L)),
    HARMONIC_CURRENT_31ND(5, "31次谐波电流(A)", Arrays.asList(1040031L, 1050031L, 1060031L)),
    HARMONIC_CURRENT_32ND(5, "32次谐波电流(A)", Arrays.asList(1040032L, 1050032L, 1060032L)),
    HARMONIC_CURRENT_33ND(5, "33次谐波电流(A)", Arrays.asList(1040033L, 1050033L, 1060033L)),
    HARMONIC_CURRENT_34ND(5, "34次谐波电流(A)", Arrays.asList(1040034L, 1050034L, 1060034L)),
    HARMONIC_CURRENT_35ND(5, "35次谐波电流(A)", Arrays.asList(1040035L, 1050035L, 1060035L)),
    HARMONIC_CURRENT_36ND(5, "36次谐波电流(A)", Arrays.asList(1040036L, 1050036L, 1060036L)),
    HARMONIC_CURRENT_37ND(5, "37次谐波电流(A)", Arrays.asList(1040037L, 1050037L, 1060037L)),
    HARMONIC_CURRENT_38ND(5, "38次谐波电流(A)", Arrays.asList(1040038L, 1050038L, 1060038L)),
    HARMONIC_CURRENT_39ND(5, "39次谐波电流(A)", Arrays.asList(1040039L, 1050039L, 1060039L)),
    HARMONIC_CURRENT_40ND(5, "40次谐波电流(A)", Arrays.asList(1040040L, 1050040L, 1060040L)),
    HARMONIC_CURRENT_41ND(5, "41次谐波电流(A)", Arrays.asList(1040041L, 1050041L, 1060041L)),
    HARMONIC_CURRENT_42ND(5, "42次谐波电流(A)", Arrays.asList(1040042L, 1050042L, 1060042L)),
    HARMONIC_CURRENT_43ND(5, "43次谐波电流(A)", Arrays.asList(1040043L, 1050043L, 1060043L)),
    HARMONIC_CURRENT_44ND(5, "44次谐波电流(A)", Arrays.asList(1040044L, 1050044L, 1060044L)),
    HARMONIC_CURRENT_45ND(5, "45次谐波电流(A)", Arrays.asList(1040045L, 1050045L, 1060045L)),
    HARMONIC_CURRENT_46ND(5, "46次谐波电流(A)", Arrays.asList(1040046L, 1050046L, 1060046L)),
    HARMONIC_CURRENT_47ND(5, "47次谐波电流(A)", Arrays.asList(1040047L, 1050047L, 1060047L)),
    HARMONIC_CURRENT_48ND(5, "48次谐波电流(A)", Arrays.asList(1040048L, 1050048L, 1060048L)),
    HARMONIC_CURRENT_49ND(5, "49次谐波电流(A)", Arrays.asList(1040049L, 1050049L, 1060049L)),
    HARMONIC_CURRENT_50ND(5, "50次谐波电流(A)", Arrays.asList(1040050L, 1050050L, 1060050L)),

    INTERHARMONIC_VOLTAGE_0(6, "0.5次间谐波电压(%)", Arrays.asList(190000L, 200000L, 210000L)),
    INTERHARMONIC_VOLTAGE_1(6, "1.5次间谐波电压(%)", Arrays.asList(190001L, 200001L, 210001L)),
    INTERHARMONIC_VOLTAGE_2(6, "2.5次间谐波电压(%)", Arrays.asList(190002L, 200002L, 210002L)),
    INTERHARMONIC_VOLTAGE_3(6, "3.5次间谐波电压(%)", Arrays.asList(190003L, 200003L, 210003L)),
    INTERHARMONIC_VOLTAGE_4(6, "4.5次间谐波电压(%)", Arrays.asList(190004L, 200004L, 210004L)),
    INTERHARMONIC_VOLTAGE_5(6, "5.5次间谐波电压(%)", Arrays.asList(190005L, 200005L, 210005L)),
    INTERHARMONIC_VOLTAGE_6(6, "6.5次间谐波电压(%)", Arrays.asList(190006L, 200006L, 210006L)),
    INTERHARMONIC_VOLTAGE_7(6, "7.5次间谐波电压(%)", Arrays.asList(190007L, 200007L, 210007L)),
    INTERHARMONIC_VOLTAGE_8(6, "8.5次间谐波电压(%)", Arrays.asList(190008L, 200008L, 210008L)),
    INTERHARMONIC_VOLTAGE_9(6, "9.5次间谐波电压(%)", Arrays.asList(190009L, 200009L, 210009L)),
    INTERHARMONIC_VOLTAGE_10(6, "10.5次间谐波电压(%)", Arrays.asList(190010L, 200010L, 210010L)),
    INTERHARMONIC_VOLTAGE_11(6, "11.5次间谐波电压(%)", Arrays.asList(190011L, 200011L, 210011L)),
    INTERHARMONIC_VOLTAGE_12(6, "12.5次间谐波电压(%)", Arrays.asList(190012L, 200012L, 210012L)),
    INTERHARMONIC_VOLTAGE_13(6, "13.5次间谐波电压(%)", Arrays.asList(190013L, 200013L, 210013L)),
    INTERHARMONIC_VOLTAGE_14(6, "14.5次间谐波电压(%)", Arrays.asList(190014L, 200014L, 210014L)),
    INTERHARMONIC_VOLTAGE_15(6, "15.5次间谐波电压(%)", Arrays.asList(190015L, 200015L, 210015L)),
    INTERHARMONIC_VOLTAGE_16(6, "16.5次间谐波电压(%)", Arrays.asList(190016L, 200016L, 210016L)),
    INTERHARMONIC_VOLTAGE_17(6, "17.5次间谐波电压(%)", Arrays.asList(190017L, 200017L, 210017L)),
    INTERHARMONIC_VOLTAGE_18(6, "18.5次间谐波电压(%)", Arrays.asList(190018L, 200018L, 210018L)),
    INTERHARMONIC_VOLTAGE_19(6, "19.5次间谐波电压(%)", Arrays.asList(190019L, 200019L, 210019L)),
    INTERHARMONIC_VOLTAGE_20(6, "20.5次间谐波电压(%)", Arrays.asList(190020L, 200020L, 210020L)),
    INTERHARMONIC_VOLTAGE_21(6, "21.5次间谐波电压(%)", Arrays.asList(190021L, 200021L, 210021L)),
    INTERHARMONIC_VOLTAGE_22(6, "22.5次间谐波电压(%)", Arrays.asList(190022L, 200022L, 210022L)),
    INTERHARMONIC_VOLTAGE_23(6, "23.5次间谐波电压(%)", Arrays.asList(190023L, 200023L, 210023L)),
    INTERHARMONIC_VOLTAGE_24(6, "24.5次间谐波电压(%)", Arrays.asList(190024L, 200024L, 210024L)),
    INTERHARMONIC_VOLTAGE_25(6, "25.5次间谐波电压(%)", Arrays.asList(190025L, 200025L, 210025L)),
    INTERHARMONIC_VOLTAGE_26(6, "26.5次间谐波电压(%)", Arrays.asList(190026L, 200026L, 210026L)),
    INTERHARMONIC_VOLTAGE_27(6, "27.5次间谐波电压(%)", Arrays.asList(190027L, 200027L, 210027L)),
    INTERHARMONIC_VOLTAGE_28(6, "28.5次间谐波电压(%)", Arrays.asList(190028L, 200028L, 210028L)),
    INTERHARMONIC_VOLTAGE_29(6, "29.5次间谐波电压(%)", Arrays.asList(190029L, 200029L, 210029L)),
    INTERHARMONIC_VOLTAGE_30(6, "30.5次间谐波电压(%)", Arrays.asList(190030L, 200030L, 210030L)),
    INTERHARMONIC_VOLTAGE_31(6, "31.5次间谐波电压(%)", Arrays.asList(190031L, 200031L, 210031L)),
    INTERHARMONIC_VOLTAGE_32(6, "32.5次间谐波电压(%)", Arrays.asList(190032L, 200032L, 210032L)),
    INTERHARMONIC_VOLTAGE_33(6, "33.5次间谐波电压(%)", Arrays.asList(190033L, 200033L, 210033L)),
    INTERHARMONIC_VOLTAGE_34(6, "34.5次间谐波电压(%)", Arrays.asList(190034L, 200034L, 210034L)),
    INTERHARMONIC_VOLTAGE_35(6, "35.5次间谐波电压(%)", Arrays.asList(190035L, 200035L, 210035L)),
    INTERHARMONIC_VOLTAGE_36(6, "36.5次间谐波电压(%)", Arrays.asList(190036L, 200036L, 210036L)),
    INTERHARMONIC_VOLTAGE_37(6, "37.5次间谐波电压(%)", Arrays.asList(190037L, 200037L, 210037L)),
    INTERHARMONIC_VOLTAGE_38(6, "38.5次间谐波电压(%)", Arrays.asList(190038L, 200038L, 210038L)),
    INTERHARMONIC_VOLTAGE_39(6, "39.5次间谐波电压(%)", Arrays.asList(190039L, 200039L, 210039L)),
    INTERHARMONIC_VOLTAGE_40(6, "40.5次间谐波电压(%)", Arrays.asList(190040L, 200040L, 210040L)),
    INTERHARMONIC_VOLTAGE_41(6, "41.5次间谐波电压(%)", Arrays.asList(190041L, 200041L, 210041L)),
    INTERHARMONIC_VOLTAGE_42(6, "42.5次间谐波电压(%)", Arrays.asList(190042L, 200042L, 210042L)),
    INTERHARMONIC_VOLTAGE_43(6, "43.5次间谐波电压(%)", Arrays.asList(190043L, 200043L, 210043L)),
    INTERHARMONIC_VOLTAGE_44(6, "44.5次间谐波电压(%)", Arrays.asList(190044L, 200044L, 210044L)),
    INTERHARMONIC_VOLTAGE_45(6, "45.5次间谐波电压(%)", Arrays.asList(190045L, 200045L, 210045L)),
    INTERHARMONIC_VOLTAGE_46(6, "46.5次间谐波电压(%)", Arrays.asList(190046L, 200046L, 210046L)),
    INTERHARMONIC_VOLTAGE_47(6, "47.5次间谐波电压(%)", Arrays.asList(190047L, 200047L, 210047L)),
    INTERHARMONIC_VOLTAGE_48(6, "48.5次间谐波电压(%)", Arrays.asList(190048L, 200048L, 210048L)),
    INTERHARMONIC_VOLTAGE_49(6, "49.5次间谐波电压(%)", Arrays.asList(190049L, 200049L, 210049L)),

    INTERHARMONIC_CURRENT_0(7, "0.5次间谐波电流(A)", Arrays.asList(1130000L, 1140000L, 1150000L)),
    INTERHARMONIC_CURRENT_1(7, "1.5次间谐波电流(A)", Arrays.asList(1130001L, 1140001L, 1150001L)),
    INTERHARMONIC_CURRENT_2(7, "2.5次间谐波电流(A)", Arrays.asList(1130002L, 1140002L, 1150002L)),
    INTERHARMONIC_CURRENT_3(7, "3.5次间谐波电流(A)", Arrays.asList(1130003L, 1140003L, 1150003L)),
    INTERHARMONIC_CURRENT_4(7, "4.5次间谐波电流(A)", Arrays.asList(1130004L, 1140004L, 1150004L)),
    INTERHARMONIC_CURRENT_5(7, "5.5次间谐波电流(A)", Arrays.asList(1130005L, 1140005L, 1150005L)),
    INTERHARMONIC_CURRENT_6(7, "6.5次间谐波电流(A)", Arrays.asList(1130006L, 1140006L, 1150006L)),
    INTERHARMONIC_CURRENT_7(7, "7.5次间谐波电流(A)", Arrays.asList(1130007L, 1140007L, 1150007L)),
    INTERHARMONIC_CURRENT_8(7, "8.5次间谐波电流(A)", Arrays.asList(1130008L, 1140008L, 1150008L)),
    INTERHARMONIC_CURRENT_9(7, "9.5次间谐波电流(A)", Arrays.asList(1130009L, 1140009L, 1150009L)),
    INTERHARMONIC_CURRENT_10(7, "10.5次间谐波电流(A)", Arrays.asList(1130010L, 1140010L, 1150010L)),
    INTERHARMONIC_CURRENT_11(7, "11.5次间谐波电流(A)", Arrays.asList(1130011L, 1140011L, 1150011L)),
    INTERHARMONIC_CURRENT_12(7, "12.5次间谐波电流(A)", Arrays.asList(1130012L, 1140012L, 1150012L)),
    INTERHARMONIC_CURRENT_13(7, "13.5次间谐波电流(A)", Arrays.asList(1130013L, 1140013L, 1150013L)),
    INTERHARMONIC_CURRENT_14(7, "14.5次间谐波电流(A)", Arrays.asList(1130014L, 1140014L, 1150014L)),
    INTERHARMONIC_CURRENT_15(7, "15.5次间谐波电流(A)", Arrays.asList(1130015L, 1140015L, 1150015L)),
    INTERHARMONIC_CURRENT_16(7, "16.5次间谐波电流(A)", Arrays.asList(1130016L, 1140016L, 1150016L)),
    INTERHARMONIC_CURRENT_17(7, "17.5次间谐波电流(A)", Arrays.asList(1130017L, 1140017L, 1150017L)),
    INTERHARMONIC_CURRENT_18(7, "18.5次间谐波电流(A)", Arrays.asList(1130018L, 1140018L, 1150018L)),
    INTERHARMONIC_CURRENT_19(7, "19.5次间谐波电流(A)", Arrays.asList(1130019L, 1140019L, 1150019L)),
    INTERHARMONIC_CURRENT_20(7, "20.5次间谐波电流(A)", Arrays.asList(1130020L, 1140020L, 1150020L)),
    INTERHARMONIC_CURRENT_21(7, "21.5次间谐波电流(A)", Arrays.asList(1130021L, 1140021L, 1150021L)),
    INTERHARMONIC_CURRENT_22(7, "22.5次间谐波电流(A)", Arrays.asList(1130022L, 1140022L, 1150022L)),
    INTERHARMONIC_CURRENT_23(7, "23.5次间谐波电流(A)", Arrays.asList(1130023L, 1140023L, 1150023L)),
    INTERHARMONIC_CURRENT_24(7, "24.5次间谐波电流(A)", Arrays.asList(1130024L, 1140024L, 1150024L)),
    INTERHARMONIC_CURRENT_25(7, "25.5次间谐波电流(A)", Arrays.asList(1130025L, 1140025L, 1150025L)),
    INTERHARMONIC_CURRENT_26(7, "26.5次间谐波电流(A)", Arrays.asList(1130026L, 1140026L, 1150026L)),
    INTERHARMONIC_CURRENT_27(7, "27.5次间谐波电流(A)", Arrays.asList(1130027L, 1140027L, 1150027L)),
    INTERHARMONIC_CURRENT_28(7, "28.5次间谐波电流(A)", Arrays.asList(1130028L, 1140028L, 1150028L)),
    INTERHARMONIC_CURRENT_29(7, "29.5次间谐波电流(A)", Arrays.asList(1130029L, 1140029L, 1150029L)),
    INTERHARMONIC_CURRENT_30(7, "30.5次间谐波电流(A)", Arrays.asList(1130030L, 1140030L, 1150030L)),
    INTERHARMONIC_CURRENT_31(7, "31.5次间谐波电流(A)", Arrays.asList(1130031L, 1140031L, 1150031L)),
    INTERHARMONIC_CURRENT_32(7, "32.5次间谐波电流(A)", Arrays.asList(1130032L, 1140032L, 1150032L)),
    INTERHARMONIC_CURRENT_33(7, "33.5次间谐波电流(A)", Arrays.asList(1130033L, 1140033L, 1150033L)),
    INTERHARMONIC_CURRENT_34(7, "34.5次间谐波电流(A)", Arrays.asList(1130034L, 1140034L, 1150034L)),
    INTERHARMONIC_CURRENT_35(7, "35.5次间谐波电流(A)", Arrays.asList(1130035L, 1140035L, 1150035L)),
    INTERHARMONIC_CURRENT_36(7, "36.5次间谐波电流(A)", Arrays.asList(1130036L, 1140036L, 1150036L)),
    INTERHARMONIC_CURRENT_37(7, "37.5次间谐波电流(A)", Arrays.asList(1130037L, 1140037L, 1150037L)),
    INTERHARMONIC_CURRENT_38(7, "38.5次间谐波电流(A)", Arrays.asList(1130038L, 1140038L, 1150038L)),
    INTERHARMONIC_CURRENT_39(7, "39.5次间谐波电流(A)", Arrays.asList(1130039L, 1140039L, 1150039L)),
    INTERHARMONIC_CURRENT_40(7, "40.5次间谐波电流(A)", Arrays.asList(1130040L, 1140040L, 1150040L)),
    INTERHARMONIC_CURRENT_41(7, "41.5次间谐波电流(A)", Arrays.asList(1130041L, 1140041L, 1150041L)),
    INTERHARMONIC_CURRENT_42(7, "42.5次间谐波电流(A)", Arrays.asList(1130042L, 1140042L, 1150042L)),
    INTERHARMONIC_CURRENT_43(7, "43.5次间谐波电流(A)", Arrays.asList(1130043L, 1140043L, 1150043L)),
    INTERHARMONIC_CURRENT_44(7, "44.5次间谐波电流(A)", Arrays.asList(1130044L, 1140044L, 1150044L)),
    INTERHARMONIC_CURRENT_45(7, "45.5次间谐波电流(A)", Arrays.asList(1130045L, 1140045L, 1150045L)),
    INTERHARMONIC_CURRENT_46(7, "46.5次间谐波电流(A)", Arrays.asList(1130046L, 1140046L, 1150046L)),
    INTERHARMONIC_CURRENT_47(7, "47.5次间谐波电流(A)", Arrays.asList(1130047L, 1140047L, 1150047L)),
    INTERHARMONIC_CURRENT_48(7, "48.5次间谐波电流(A)", Arrays.asList(1130048L, 1140048L, 1150048L)),
    INTERHARMONIC_CURRENT_49(7, "49.5次间谐波电流(A)", Arrays.asList(1130049L, 1140049L, 1150049L)),

    ;


    private Integer dataType;
    private String name;
    private List<Long> dataIds;

    StarDataByTypeMonthEnum(Integer dataType, String name, List<Long> dataIds) {
        this.dataType = dataType;
        this.dataIds = dataIds;
        this.name = name;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Long> getDataIds() {
        return dataIds;
    }

    public void setDataIds(List<Long> dataIds) {
        this.dataIds = dataIds;
    }

    public static List<DataReportParam.QuantityTemplate> getDataIdsByDataType(Integer dataType) {
        List<DataReportParam.QuantityTemplate> quantityTemplates = new ArrayList<>();
        if (dataType == null) {
            return null;
        }
        for (StarDataByTypeMonthEnum oneEnum : values()) {
            if (dataType.equals(oneEnum.getDataType())) {
                DataReportParam.QuantityTemplate data = new DataReportParam.QuantityTemplate();
                data.setName(oneEnum.getName());
                data.setDataIds(oneEnum.getDataIds());
                quantityTemplates.add(data);
            }
        }
        return quantityTemplates;
    }

    public static List<List<DataReportParam.QuantityTemplate>> getAllDataIds() {
        List<List<DataReportParam.QuantityTemplate>> result = new ArrayList<>();
        List<DataReportParam.QuantityTemplate> quantityTemplates1 = new ArrayList<>();
        List<DataReportParam.QuantityTemplate> quantityTemplates2 = new ArrayList<>();
        List<DataReportParam.QuantityTemplate> quantityTemplates3 = new LinkedList<>();
        List<DataReportParam.QuantityTemplate> quantityTemplates4 = new ArrayList<>();
        List<DataReportParam.QuantityTemplate> quantityTemplates5 = new ArrayList<>();
        List<DataReportParam.QuantityTemplate> quantityTemplates6 = new ArrayList<>();
        List<DataReportParam.QuantityTemplate> quantityTemplates7 = new ArrayList<>();
        for (StarDataByTypeMonthEnum oneEnum : values()) {
            DataReportParam.QuantityTemplate data = new DataReportParam.QuantityTemplate();
            data.setDataIds(oneEnum.getDataIds());
            data.setName(oneEnum.getName());
            switch (oneEnum.getDataType()) {
                case 1:
                    quantityTemplates1.add(data);
                    break;
                case 2:
                    quantityTemplates2.add(data);
                    break;
                case 3:
                    quantityTemplates3.add(data);
                    break;
                case 4:
                    quantityTemplates4.add(data);
                    break;
                case 5:
                    quantityTemplates5.add(data);
                    break;
                case 6:
                    quantityTemplates6.add(data);
                    break;
                case 7:
                    quantityTemplates7.add(data);
                    break;

            }
        }
        result.add(quantityTemplates1);
        result.add(quantityTemplates2);
        result.add(quantityTemplates3);
        result.add(quantityTemplates4);
        result.add(quantityTemplates5);
        result.add(quantityTemplates6);
        result.add(quantityTemplates7);
        return result;
    }

}

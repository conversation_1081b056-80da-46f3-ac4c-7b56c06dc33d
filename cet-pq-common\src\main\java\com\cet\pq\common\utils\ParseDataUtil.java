package com.cet.pq.common.utils;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @ClassName AlarmManagerTest
 * @Description unknown
 * @Date 2020/11/12
 */
public class ParseDataUtil {

    private ParseDataUtil() {
    }

    /**
     * 对象转Integer
     *
     * @param obj
     * @return
     */
    public static Integer parseInteger(Object obj) {
        if (null == obj || "".equals(obj)) {
            return 0;
        } else {
            // 数字带.0 转换
            if (String.valueOf(obj).trim().contains(".0")) {
                return Integer.parseInt(String.valueOf(obj).trim().replace(".0", ""));
            }
            return Integer.parseInt(String.valueOf(obj).trim());
        }
    }

    public static Integer parseIntegerNull(Object obj) {
        if (null == obj || "".equals(obj)) {
            return null;
        } else {
            // 数字带.0 转换
            if (String.valueOf(obj).trim().contains(".0")) {
                return Integer.parseInt(String.valueOf(obj).trim().replace(".0", ""));
            }
            return Integer.parseInt(String.valueOf(obj).trim());
        }
    }

    /**
     * 对象转Double
     *
     * @param obj
     * @return
     */
    public static Double parseDouble(Object obj) {
        if (null == obj) {
            return 0D;
        } else {
            return Double.parseDouble(String.valueOf(obj).trim());
        }
    }

    public static Double parseDoubleNull(Object obj) {
        if (null == obj) {
            return null;
        } else {
            return Double.parseDouble(String.valueOf(obj).trim());
        }
    }

    /**
     * 对象转Long
     *
     * @param obj
     * @return
     */
    public static Long parseLong(Object obj) {
        if (null == obj) {
            return 0L;
        } else {
            return Long.parseLong(String.valueOf(obj).trim());
        }
    }

    /**
     * 对象转String
     *
     * @param obj
     * @return
     */
    public static String parseString(Object obj) {
        if (null == obj) {
            return "";
        } else {
            return String.valueOf(obj).trim();
        }
    }

    /**
     * 对象转Boolean
     *
     * @param obj
     * @return
     */
    public static Boolean parseBoolean(Object obj) {
        if (null == obj) {
            return Boolean.FALSE;
        } else {
            return Boolean.parseBoolean(String.valueOf(obj).trim());
        }
    }

    /**
     * 对象转List
     *
     * @param obj
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> parseList(Object obj) {
        if (null == obj) {
            return new ArrayList<>();
        } else {
            return (List<T>) obj;
        }
    }

    /**
     * 对象转Map
     *
     * @param obj
     * @return
     */
    @SuppressWarnings("rawtypes")
    public static Map parseMap(Object obj) {
        if (null == obj) {
            return new ConcurrentHashMap(1);
        } else {
            return (Map) obj;
        }
    }

    /**
     * 保留2位小数
     *
     * @param rate
     * @return
     */
    public static Double round(Double rate) {
        //不为null才进行格式化,否则返回null
        if (Objects.nonNull(rate)) {
            DecimalFormat df = new DecimalFormat("#.00");
            df.setRoundingMode(RoundingMode.HALF_UP);
            rate = Double.valueOf(df.format(rate));
        }
        return rate;
    }

    /**
     * 对象转Boolean
     *
     * @param obj
     * @return
     */
    public static String transformBooleanToStr(Object obj) {
        if (obj == null) {
            return "--";
        }
        if (ParseDataUtil.parseBoolean(obj)) {
            return "是";
        } else {
            return "否";
        }
    }

    public static String parseStringNull(Object obj) {
        return Objects.isNull(obj) ? null : String.valueOf(obj).trim();
    }

    public static Long parseLongNull(Object obj) {
        return Objects.isNull(obj) ? null : Long.parseLong(String.valueOf(obj).trim());
    }
}

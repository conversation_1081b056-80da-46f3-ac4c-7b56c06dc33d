package com.cet.pq.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 聚合值
 * @date 2021/2/2 17:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PQQuantityAggData {
    private Integer aggregationcycle;
    private Integer aggregationtype;
    private Integer dataid;
    private Long line_id;
    private Long logtime;
    private Integer quantitymaptemplate_id;
    private Double value;
}

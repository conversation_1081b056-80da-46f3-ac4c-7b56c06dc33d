package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.common.enums.offlinetest.ModuleEnum;
import com.cet.pq.anlysis.model.datareport.ExportTestReportDetailParam;
import com.cet.pq.anlysis.model.offlinetest.*;
import com.cet.pq.anlysis.service.OffLinePqdifService;
import com.cet.pq.anlysis.service.OffLineTestExcelService;
import com.cet.pq.anlysis.service.OffLineTestService;
import com.cet.pq.common.constant.ErrorCode;
import com.cet.pq.common.model.PageData;
import com.cet.pq.common.model.PageResult;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.datalog.TrendDataVo;
import com.cet.pq.common.model.datalog.TrendSearchListVo;
import com.cet.pq.common.model.offlinetest.DataReport;
import com.cet.pq.common.model.wave.Wave;
import com.cet.pq.common.utils.FileUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@RestController
@RequestMapping("/pq/v1/test")
@SuppressWarnings({ "rawtypes", "unchecked" })
@Api(value = "OffLineTestController", tags = { "运维管理-现场测试数据管理" })
public class OffLineTestController {

	@Autowired
	private OffLinePqdifService offLinePqdifService;

	@Autowired
	private OffLineTestExcelService offLineTestExcelService;

	@Autowired
	private OffLineTestService offlineTestService;

	private List<String> fileTypes = Lists.newArrayList("docx", "doc", "xls", "xlsx", "pdif");

	@ApiOperation("现场测试数据导入")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "offlineTestId", value = "测点ID", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "importer", value = "操作人", required = true, dataType = "String"),
			@ApiImplicitParam(name = "fileType", value = "文件类型：1：PQDif,2：Comtrade；3:Excel", required = true, dataType = "int"),
			@ApiImplicitParam(name = "moduleValue", value = "Excel模板值，只有导入类型为Excel才需要传", required = false, dataType = "int"),
	})
	@PostMapping("/import")
	public Result importExcelData(@RequestParam Long offlineTestId, @RequestParam String importer, @RequestParam Integer fileType,
			@RequestParam Integer moduleValue, @RequestParam MultipartFile file, @RequestParam(defaultValue = "0") Long generalplanId) {
		//检查文件类型和大小
		if (!FileUtil.checkFile(file, fileTypes)) {
			return Result.error(ErrorCode.ERROR_FILE);
		}
		Object obj = null;
		switch (fileType) {
		case 1:
			obj = offLinePqdifService.importPqdif(offlineTestId, importer, file);
			break;
		case 2:
			break;
		case 3:
//			obj = offLineTestExcelService.importExcelData(offlineTestId, importer, moduleValue, file);
			if (generalplanId != 0){
				obj = offLineTestExcelService.importExcelData(offlineTestId, importer, moduleValue, file, generalplanId);
			}else {
				obj = offLineTestExcelService.importExcelData(offlineTestId, importer, moduleValue, file);
			}
			break;
		default:
			break;

		}
		return Result.success(obj);
	}

	@ApiOperation("母线测试数据文件导入")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "busBarSectionId", value = "母线ID", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "importer", value = "操作人", required = true, dataType = "String"),
			@ApiImplicitParam(name = "moduleValue", value = "Excel模板值，只有导入类型为Excel才需要传", required = false, dataType = "int"),
	})
	@PostMapping("/importBusBarSectionData")
	public Result importBusBarSectionData(@RequestParam Long busBarSectionId, @RequestParam String importer,
			@RequestParam Integer moduleValue, @RequestParam MultipartFile file, @RequestParam(defaultValue = "0") Long generalplanId) {
		Object obj = null;
		obj = offLineTestExcelService.importBusBarSectionData(busBarSectionId, importer, moduleValue, file, generalplanId);
		return Result.success(obj);
	}

	@ApiOperation("查询文件的导入记录")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "pageSize", value = "单页数据条数", required = true, dataType = "int"),
			@ApiImplicitParam(name = "pageNum", value = "页数", required = true, dataType = "String"),
			@ApiImplicitParam(name = "fileType", value = "文件类型：1：PQDif,2:Comtrade,3:Excel ", required = false, dataType = "int"),
	})
	@GetMapping("/file/record")
	public Result<PageData<OffLineImportDataRes>> getImportRecord(@RequestParam Integer pageSize, @RequestParam Integer pageNum, Long id, String modelLabel, Long startTime, Long endTime,
                                                                  Integer fileType) {
		PageData<OffLineImportDataRes> importRecord = offlineTestService.getImportRecord(pageSize, pageNum, id, modelLabel, startTime, endTime, fileType);
		return Result.success(importRecord);
	}

	@ApiOperation("根据导入记录id查询导入数据-只用于删除前查询是否有导入数据")
	@GetMapping("/file/data")
	public Result<List> getDataByRecordId(@RequestParam Integer fileType, @RequestParam Long recoredId) {
		List dataList = new ArrayList<>();
		switch (fileType) {
		case 1:
			break;
		case 2:
			break;
		case 3:
			dataList = offLineTestExcelService.getExcelDataByByRecordId(recoredId);
			break;
		default:
			break;
		}
		return Result.success(dataList);
	}

	@ApiOperation("删除导入记录,会同时删除导入的数据")
	@PostMapping("/importrecored")
	public Result deleteFile(@RequestParam Long id) {
		offlineTestService.deleteImportRecord(id);
		return Result.success();
	}

	/**
	 * 下载数据文件
	 */
	@ApiOperation("下载导入的文件")
	@PostMapping("/download")
	public Result downloadFile(HttpServletResponse response, Integer fileType, @RequestParam long recoredId) {
		offlineTestService.download(response, recoredId);
		return Result.success();
	}

	@ApiOperation("获取Excel模板列表")
	@GetMapping("/excel/modulelist")
	public Result<List<ExcelModule>> getExcelModuleList() {
		return Result.success(ModuleEnum.getAllModule());
	}

	@PostMapping("/detail")
	@ApiOperation("获取离线数据报表明细")
	public Result<List<DataReport>> getOffLineDataReportList(@RequestParam Long offlineTestId, @RequestParam Long starttime, @RequestParam Long endtime,
															 @RequestParam Integer aggregationcycle) {
		List<DataReport> dataDetail = offLineTestExcelService.getOffLineDataReportList(offlineTestId, starttime, endtime, aggregationcycle);
		return Result.success(dataDetail);
	}

	@ApiOperation("获取暂态事件列表")
	@GetMapping("/variationevent")
	public PageResult<List<VariationEventReport>> getOffLineVariationEvent(Integer pageNum, Integer pageSize, Long modelId, String modelLabel, Long startTime, Long endTime) {
		return offLinePqdifService.getOffLineVariationEvent(pageNum, pageSize, modelId, modelLabel, startTime, endTime);
	}

	@ApiOperation("获取波形数据")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "offlineTestId", value = "测点id", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "waveformlogtime", value = "触发录波时间", required = true, dataType = "Long"),
	})
	@GetMapping("/wava")
	public Result<Wave> getWava(HttpServletResponse response,@RequestParam Long offlineTestId, @RequestParam Long waveformlogtime) {
		Wave wave = offLinePqdifService.getWava(offlineTestId, waveformlogtime);
		if (null != wave) {
			return Result.success(wave);
		}
		return Result.error("获取波形数据失败，可能是不存在波形数据");
	}
	
	@ApiOperation("获取定时记录")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "fill", value = "是否对缺失数据进行填充，true-填充，false-不进行填充。按照interval进行填充，interval为0或者null时，则自动计算间隔进行填充", required = false, dataType = "Boolean")
	})
	@GetMapping("/trendcurve")
	public Result<List<TrendDataVo>> queryTrendCurveData(@RequestBody TrendSearchListVo searchVo, Boolean fill) {
		List<TrendDataVo> trendDataList = offLinePqdifService.queryTrendCurveData(searchVo, fill);
		if (CollectionUtils.isNotEmpty(trendDataList)) {
			return Result.success(trendDataList);
		}
		return Result.error("定时记录不存在");
	}

	@GetMapping("/overlimit/detail")
	@ApiOperation("超标指标明细")
	public PageResult<OverLimitData> getOverLimitDetail(Integer pageNum, Integer pageSize, Long starttime, Long endtime, String modelLabel, Long modelId) {
		PageResult<OverLimitData> overlimitDataRes = offLineTestExcelService.getOverLimitDetail(pageNum, pageSize, starttime, endtime, modelLabel, modelId);
		return overlimitDataRes;
	}

	@ApiOperation(value = "导出测试数据报表")
	@PostMapping(value = "/exportDetailReport")
	public void exportDetailReport(@RequestBody @Valid @ApiParam(name = "ExportTestReportDetailParam", value = "参数", required = true) ExportTestReportDetailParam exportTestReportDetailParam, HttpServletResponse response) {
		offLineTestExcelService.exportOffLineDataReportList(exportTestReportDetailParam, response);
	}

	@ApiOperation("获取普测模板")
	@GetMapping("/getTemplate")
	public Result getTemplate() {
		return offLineTestExcelService.getTemplate();
	}

	@ApiOperation("根据不同模板类型导入普测数据")
	@PostMapping("/importTestData")
	public Result importTestData(@RequestParam Long generalPlanId, @RequestParam Long offlineTestId, @RequestParam Long testDataMappingId,
								 @RequestParam String importer, @RequestParam MultipartFile file) {
		Object obj = offLineTestExcelService.importTestData(generalPlanId, offlineTestId, testDataMappingId, file, importer);
		return Result.success(obj);
	}
}

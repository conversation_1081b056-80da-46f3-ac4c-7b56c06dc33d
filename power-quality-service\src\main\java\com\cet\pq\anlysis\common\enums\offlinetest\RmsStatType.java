package com.cet.pq.anlysis.common.enums.offlinetest;

/**
 * <AUTHOR>
 * @ClassName RmsStatType
 * @Description TODO
 * @Date 2020/10/22
 */
public enum RmsStatType {
    //暂态类型
    RMS_STAT_TYPE_ALL("暂态事件统计类型", 8),
    RMS_STAT_TYPE_DROP("暂态暂降统计类型", 2),
    RMS_STAT_TYPE_RISE("暂态暂升统计类型", 4),
    RMS_STAT_TYPE_INTERRUPT("暂态中断统计类型", 1);

    private String name;

    private Integer value;
    private RmsStatType(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }
}

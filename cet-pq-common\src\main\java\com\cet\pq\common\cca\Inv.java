package com.cet.pq.common.cca;

public class Inv {
    /**
     * 1
     * 求解代数余子式 输入：原始矩阵+行+列 现实中真正的行和列数目
     */

    public static double[][] getDY(double[][] data, int h, int v) {
        int H = data.length;
        int V = data[0].length;
        double[][] newData = new double[H - 1][V - 1];

        for (int i = 0; i < newData.length; i++) {

            if (i < h - 1) {
                for (int j = 0; j < newData[i].length; j++) {
                    if (j < v - 1) {
                        newData[i][j] = data[i][j];
                    } else {
                        newData[i][j] = data[i][j + 1];
                    }
                }
            } else {
                for (int j = 0; j < newData[i].length; j++) {
                    if (j < v - 1) {
                        newData[i][j] = data[i + 1][j];
                    } else {
                        newData[i][j] = data[i + 1][j + 1];
                    }
                }

            }
        }
        // System.out.println("---------------------代数余子式测试.---------------------------------");
        // for(int i=0;i<newData.length;i++){
        // for(int j=0;j<newData[i].length;j++){
        // System.out.print("newData["+i+"]"+"["+j+"]="+newData[i][j]+"   ");
        // }
        //
        // System.out.println();
        // }

        return newData;
    }


    /**
     * 2
     * 求2阶行列式的数值
     * @param data
     * @return
     */
    public static double getHL2(double[][] data) {
        // data 必须是2*2 的数组
        double num1 = data[0][0] * data[1][1];
        double num2 = -data[0][1] * data[1][0];
        return num1 + num2;
    }


    /**
     * 求3阶行列式的数值
     *
     * @param data
     * @return
     */
    public static double getHL3(double[][] data) {
        double num1 = data[0][0] * getHL2(getDY(data, 1, 1));
        double num2 = -data[0][1] * getHL2(getDY(data, 1, 2));
        double num3 = data[0][2] * getHL2(getDY(data, 1, 3));
        // System.out.println("---->"+num1);
        // System.out.println("---->"+num2);
        // System.out.println("---->"+num3);
        System.out.println("3阶行列式的数值是：----->" + (num1 + num2 + num3));
        return num1 + num2 + num3;
    }

    /**
     * 求4阶行列式的数值
     *
     * @param data
     * @return
     */
    public static double getHL4(double[][] data) {
        double num1 = data[0][0] * getHL3(getDY(data, 1, 1));
        double num2 = -data[0][1] * getHL3(getDY(data, 1, 2));
        double num3 = data[0][2] * getHL3(getDY(data, 1, 3));
        double num4 = -data[0][3] * getHL3(getDY(data, 1, 4));
        // System.out.println("--------->"+num1);
        // System.out.println("--------->"+num2);
        // System.out.println("--------->"+num3);
        // System.out.println("--------->"+num4);
        // System.out.println("4阶行列式的数值------->"+(num1+num2+num3+num4));

        return num1 + num2 + num3 + num4;
    }


    /**
     * 求5阶行列式的数值
     */
    public static double getHL5(double[][] data) {

        double num1 = data[0][0] * getHL4(getDY(data, 1, 1));
        double num2 = -data[0][1] * getHL4(getDY(data, 1, 2));
        double num3 = data[0][2] * getHL4(getDY(data, 1, 3));
        double num4 = -data[0][3] * getHL4(getDY(data, 1, 4));
        double num5 = data[0][4] * getHL4(getDY(data, 1, 5));

        System.out.println("5 阶行列式的数值是：  ------->"
                + (num1 + num2 + num3 + num4 + num5));
        return num1 + num2 + num3 + num4 + num5;

    }

    /**
     * 求解行列式的模----------->最终的总结归纳!!!!!!!!!!!!!
     *
     * @param data
     * @return
     */
    public static double getHL(double[][] data) {

        // 终止条件
        if (data.length == 2) {
            return data[0][0] * data[1][1] - data[0][1] * data[1][0];
        }

        double total = 0;
        // 根据data 得到行列式的行数和列数
        int num = data.length;
        // 创建一个大小为num 的数组存放对应的展开行中元素求的的值
        double[] nums = new double[num];

        for (int i = 0; i < num; i++) {
            if (i % 2 == 0) {
                nums[i] = data[0][i] * getHL(getDY(data, 1, i + 1));
            } else {
                nums[i] = -data[0][i] * getHL(getDY(data, 1, i + 1));
            }
        }
        for (int i = 0; i < num; i++) {
            total += nums[i];
        }
        //System.out.println("total=" + total);
        return total;
    }

    public static double getHLFix(double[][] data) {
        if (data.length <= 2 || data[0].length <= 2) {
            return getHL(getDY(data, 1, 1));
        }
        // 终止条件
        if (data.length == 2) {
            return data[0][0] * data[1][1] - data[0][1] * data[1][0];
        }
        int m = data.length;
        int n = data[0].length;
        double total = data[m-2][n-2] * data[m-1][n-1] - data[m-2][n-1] * data[m-1][n-2];
        for (int i = m-3; i >= 0; i--) {
            double temp = 0d;
            for (int j = n-1; j >= i; j--) {
                if (i % 2 == 0) {
                    temp += data[i][j] * total;
                } else {
                    temp += -data[i][j] * total;
                }
            }
            total = temp;
        }
        // 根据data 得到行列式的行数和列数
//        int num = data.length;
//        // 创建一个大小为num 的数组存放对应的展开行中元素求的的值
//        double[] nums = new double[num];
//
//        for (int i = 0; i < num; i++) {
//            if (i % 2 == 0) {
//                nums[i] = data[0][i] * getHL(getDY(data, 1, i + 1));
//            } else {
//                nums[i] = -data[0][i] * getHL(getDY(data, 1, i + 1));
//            }
//        }
//        for (int i = 0; i < num; i++) {
//            total += nums[i];
//        }
        //System.out.println("total=" + total);
        return total;
    }




    /**
     * 求解3阶矩阵的逆矩阵
     * @param data
     * @return
     */
    public static double[][] getN3(double[][] data) {
        // 先是求出整个行列式的数值|A|
        double A = getHL3(data);
        double[][] newData = new double[3][3];
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                double num;
                if ((i + j) % 2 == 0) {// i+j 是偶数 实际是(i+1)+(j+1)
                    num = getHL2(getDY(data, i + 1, j + 1));
                } else {
                    num = -getHL2(getDY(data, i + 1, j + 1));
                }
                System.out.println("num=" + num);
                newData[i][j] = num / A;
            }
        }

        // 再转制
        newData = getA_T(newData);

        // 打印
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                System.out.print("newData[" + i + "][" + j + "]= "
                        + newData[i][j] + "   ");
            }

            System.out.println();
        }

        return newData;
    }

    /**
     * 求解逆矩阵------>z最后的总结和归纳
     *
     * @param data
     * @return
     */
    public static double[][] getN(double[][] data) {
        // 先是求出行列式的模|data|
        double A = getHLFix(data);
        // 创建一个等容量的逆矩阵
        double[][] newData = new double[data.length][data.length];

        for (int i = 0; i < data.length; i++) {
            for (int j = 0; j < data.length; j++) {
                double num;
                if ((i + j) % 2 == 0) {
                    num = getHLFix(getDY(data, i + 1, j + 1));
                } else {
                    num = -getHLFix(getDY(data, i + 1, j + 1));
                }

                newData[i][j] = A==0? Double.NaN: num / A;
            }
        }

        // 转置 代数余子式转制
        newData = getA_T(newData);
        /*// 打印
        for (int i = 0; i < data.length; i++) {
            for (int j = 0; j < data.length; j++) {
                System.out.print("newData[" + i + "][" + j + "]= "
                        + newData[i][j] + "   ");
            }

            System.out.println();
        }*/

        return newData;
    }



    /**
     * @param  args
     *  测试
     */
/*    public static void main(String[] args) {
        //MartrixTest t = new MartrixTest();
        //t.getDY(data6, 2, 3); //代数余子式
        // getHL2(data3);
        //getHL3(data3);
        // getHL4(data4);
        // getHL5(data5);
        // getN3(data3);

        //getHL(data3);     //行列式的模
        //getN(data3);      //矩阵的逆
    }*/

    static double[][] data6 =
            { { 1, 2, 3, 4, 5, 6 },
            { 1, 2, 3, 4, 5, 6 },
            { 3, 4, 3, 2, 2, 1 },
            { 1, 2, 3, 4, 5, 6 },
            { 1, 2, 3, 4, 5, 6 },
            { 1, 2, 3, 4, 5, 6 },};

    static double[][] data5 =
            { { 1, 2, 3, 4, 5 },
            { 2, 3, 4, 5, 1 },
            { 3, 4, 5, 1, 2 },
            { 4, 5, 1, 2, 3 },
            { 5, 1, 2, 3, 4 },};

    static double[][] data4 =
            { { 1, 0, -1, 2 },
            { -2, 1, 3, 1 },
            { 0, 2, 0, -2 },
            { 1, 3, 4, -2 },};

    static double[][] data3 =
            { {1,2,-1 },
            {3,1,0 },
            {-1,-1,-2 },};



    /**
     * 取得转置矩阵
     * @param A
     * @return
     */
    public static double[][] getA_T(double[][] A) {
        int h = A.length;
        int v = A[0].length;
        // 创建和A行和列相反的转置矩阵
        double[][] A_T = new double[v][h];
        // 根据A取得转置矩阵A_T
        for (int i = 0; i < v; i++) {
            for (int j = 0; j < h; j++) {
                A_T[j][i] = A[i][j];
            }
        }
       // System.out.println("取得转置矩阵  wanbi........");
        return A_T;
    }



}

package com.cet.pq.common.utils;

import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.ExcelConstant;
import com.cet.pq.common.enums.MonitorTypeEnum;
import com.cet.pq.common.enums.TemplateDataEnum;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.exception.ExcelException;
import com.cet.pq.common.model.ErrorExcelMsg;
import com.cet.pq.common.model.excel.*;
import com.cet.pq.common.time.DateUtils;
import com.cet.pq.common.time.TimeFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Excel解析工具
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtil {

	private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);
	public static final String MISMATCH_CITYCOMPANY = "所属市级单位匹配错误.";
	public static final String READ_TEMPLATE_ERROR = "读取模板失败";
	public static final Integer ROW_INDEX = 3;

	public static final String REPORT_EXPORT_TEMPLATE = "templates/export/报表管理导出模板.xlsx";
	private static final String LONG_TYPE = "java.lang.Long";
	private static final String BOOLEAN_TYPE = "java.lang.Boolean";
	private static final String STRING_TYPE = "java.lang.String";
	private static final String INTEGER_TYPE = "java.lang.Integer";
	private static final String DOUBLE_TYPE = "java.lang.Double";
	private static final String FLOAT_TYPE = "java.lang.Float";
	public static final String TEMPLATE_EXPORT_DATA_REPORT_PATH = "templates/export/datareport/";

	private static final List<String> specialIdList =  Arrays.asList(
			"realGraph","realData");


	/**
	 * 获取Excel文件的所有数据
	 *
	 * @return
	 */
	@SuppressWarnings("resource")
	public static List<ExcelData> getAllData(MultipartFile excel) {
		// 准备返回值
		List<ExcelData> dataList = new ArrayList<>();
		InputStream fis = null;
		Workbook workBook = null;
		String fileType = null;
		if(StringUtils.isNotEmpty(excel.getOriginalFilename())){
			fileType = excel.getOriginalFilename().split("\\.")[1];
		}
		try {
			fis = excel.getInputStream();
			if (ExcelConstant.EXCEL_TYPE_XLSX.equals(fileType)) {
				workBook = new XSSFWorkbook(fis);
			} else {
				workBook = new HSSFWorkbook(fis);
			}
			Integer sheetNum = workBook.getNumberOfSheets();
			for (int i = 0; i < sheetNum; i++) {
				Sheet sheet = workBook.getSheetAt(i);
				List<ExcelData> sheetDataList = getAllDataBySheet(sheet);
				dataList.addAll(sheetDataList);
			}
		} catch (IOException e) {
			logger.debug(CommonConstant.CONTEXT, e);
			throw new CommonManagerException("Excel获取失败", e);
		}
		return dataList;
	}

	/**
	 * 获取Excel文件中一个sheet的所有数据
	 *
	 * @return
	 */
	private static List<ExcelData> getAllDataBySheet(Sheet sheet) {
		// 准备返回值
		List<ExcelData> sheetDataList = new ArrayList<>();
		String sheetName = sheet.getSheetName();
		String row;
		String col;
		// 获取sheet的行数
		Integer rowNum = sheet.getLastRowNum();
		for (int i = 0; i < rowNum; i++) {
			Row rowIns = sheet.getRow(i);
			if (null == rowIns) {
				continue;
			}
			row = ParseDataUtil.parseString(i + 1);
			short colNum = (short) (rowIns.getLastCellNum() - 1);
			for (int j = 0; j < colNum; j++) {
				Cell cell = rowIns.getCell(j);
				if (null == cell) {
					continue;
				}
				col = getExcelColName(j);
				CellType cellType = cell.getCellTypeEnum();
				ExcelData data;
				switch (cellType) {
					case NUMERIC:
						Double numValue = cell.getNumericCellValue();
						data = new ExcelData(sheetName, row, col, numValue);
						break;
					case BOOLEAN:
						Boolean boolValue = cell.getBooleanCellValue();
						data = new ExcelData(sheetName, row, col, boolValue);
						break;
					default:
						if (null == cell.getStringCellValue()) {
							data = new ExcelData(sheetName, row, col, null);
						} else {
							String strValue = cell.getStringCellValue();
							data = new ExcelData(sheetName, row, col, strValue);
						}
						break;
				}
				sheetDataList.add(data);
			}
		}
		return sheetDataList;
	}

	/**
	 * 根据列号获取列名
	 *
	 * @param col
	 * @return
	 */
	public static String getExcelColName(int col) {
		StringBuilder str = new StringBuilder(2);
		calcExcelColumnName(str, col);
		return str.toString();
	}

	/**
	 * 计算列名
	 *
	 * @param str
	 * @param col
	 */
	private static void calcExcelColumnName(StringBuilder str, int col) {
		int tmp = col / 26;
		if (tmp > 26) {
			calcExcelColumnName(str, tmp - 1);
		} else if (tmp > 0) {
			str.append((char) (tmp + 64));
		}
		str.append((char) (col % 26 + 65));
	}

	/**
	 * 读取模板坐标单元格的文本
	 */
	public static String readCol(Workbook template, String id, int rowNum, int colNum) {
		Sheet templateSheet = template.getSheet(TemplateDataEnum.getTemplateNameById(id));
		Row row = templateSheet.getRow(rowNum);
		Cell cell = row.getCell(colNum);
		return cell.getStringCellValue();
	}

	/**
	 * 读取模板
	 */
	/*public static Workbook getTemplate() {
		InputStream templateIn = null;
		Workbook template;
		try {
			templateIn = Thread.currentThread().getContextClassLoader().getResourceAsStream("templates/export/" + "图表导出模板.xlsx");
			template = new XSSFWorkbook(templateIn);
		} catch (IOException e) {
			throw new ExcelException(READ_TEMPLATE_ERROR);
		} finally {
			try {
				if(null != templateIn){
					templateIn.close();
				}
			} catch (IOException e) {
			}
		}
		return template;
	}*/

	/**
	 * 导出图表方法
	 */
	public static void commonExport(HttpServletResponse response, ExportParameter exportParameter) throws ExcelException {
		commonExport(response, exportParameter, "templates/export/图表导出模板.xlsx");
	}

	/**
	 * 导出图表方法
	 */
	public static void commonExport(HttpServletResponse response, ExportParameter exportParameter, String exportTemplatePath) throws ExcelException {
		commonExport(response, exportParameter, null, exportTemplatePath);
	}

	/**
	 * @description: 稳态单点自定义报表的导出，其他方法不用
	 **/
	public static void commonExport(String filePath, ExportParameter exportParameter, String exportTemplatePath) throws ExcelException {
		Workbook workbook = new XSSFWorkbook();
		setSingleCustomizeSheets(exportParameter, workbook, exportTemplatePath);
		String fileName = exportParameter.getFileName();
		String wholePath = filePath + File.separator + fileName;

		try {
			FileOutputStream fileOutputStream = new FileOutputStream(wholePath);
			Throwable var7 = null;

			try {
				workbook.write(fileOutputStream);
			} catch (Throwable var17) {
				var7 = var17;
				throw var17;
			} finally {
				if (fileOutputStream != null) {
					if (var7 != null) {
						try {
							fileOutputStream.close();
						} catch (Throwable var16) {
							var7.addSuppressed(var16);
						}
					} else {
						fileOutputStream.close();
					}
				}

			}

		} catch (IOException var19) {
			throw new ExcelException("文件输出失败");
		}
	}


	/**
	 *
	 * @param response
	 * ExportParameter.sheets解释，data只支持白底黑字单元格格式，redData只支持红底白字单元格格式，insertData只支持白底黑字单元格格式，picture只支持图片格式
	 * 导出公共方法不允许添加关于超标，特定excel模板相关的特殊逻辑，参考在业务方法对超标结论放进redData
	 *
	 * @param exportParameter
	 * @throws ExcelException
	 */
	public static void commonExport(HttpServletResponse response, ExportParameter exportParameter,
									Map<String, List<CellRangeAddress>> sheetMergeList, String exportTemplatePath) throws ExcelException {
		// 创建文件
		Workbook workbook = new XSSFWorkbook();
		for (Sheets sheets : exportParameter.getSheets()) {
			Sheet sheet;
			String id = sheets.getId();
			// id为空时创建空白sheet；id不为空时复制模板到新建sheet
			if (StringUtils.isBlank(id) && CollectionUtils.isNotEmpty(sheets.getHead())) {
				sheet = workbook.createSheet(sheets.getSheetName());
				// 写表头
				List<String> head = sheets.getHead();
				Row headRow = sheet.createRow(sheets.getWriteRow() - 2);
				for (int i = 0; i < head.size(); i++) {
					Cell headCell = headRow.createCell(i);
					headCell.setCellStyle(getHeadStyle(workbook));
					headCell.setCellValue(head.get(i));
				}
			} else {
				if(Objects.isNull(sheets.getSheetName())){
					sheets.setSheetName(TemplateDataEnum.getTemplateNameById(id));
				}
				// 创建要写数据的目标工作表
				sheet = workbook.createSheet(sheets.getSheetName());
				// 设置该sheet的合并单元格
				setSheetMergeList(sheetMergeList, id, sheet);

				// 读取模板
				copyTemplate2WorkBook(exportTemplatePath, id, sheet, workbook);
			}
			// 图片导出
			int lastCol = sheets.getCol();
			int lastRow = sheets.getRow();
			writePicture(workbook, sheets, lastCol, lastRow, sheet);

			List<LinkedHashMap<String, Object>> data = sheets.getData();
			// 如果表数据为空，返回模板空表
			if (CollectionUtils.isEmpty(data) && CollectionUtils.isEmpty(sheets.getInsertData()) && CollectionUtils.isEmpty(sheets.getLeftHeaderData())) {
				continue;
			}
			// 行数据列表
			List<List> valueList = new ArrayList();
			Class clazz = null;
			if(StringUtils.isNotEmpty(id)){
				clazz = TemplateDataEnum.getClazzById(id);
			}
			// 普通单元格格式
			//CellStyle comm = getPlainCellLeftStyle(workbook);
			// 普通单元格格式 居左
			CellStyle commLeft = getPlainCellLeftStyle(workbook);
			//表头样式
			CellStyle headStyle = getHeadStyle(workbook);
			//表头样式
			CellStyle titleStyle = getTitleStyle(workbook);
			// 红色单元格样式
			CellStyle redStyle = getRedCellStyle(workbook);
			// 红色粗体单元格样式
			CellStyle redStyleBold = getRedCellStyleBold(workbook);

			// 逐行写入数据
			setSheetPlainData(sheets, data, clazz, valueList, sheet, commLeft);
			// 特定行写入数据
			setSheetSpecialData(sheets, id, sheet, redStyle, redStyleBold, commLeft);
			// 特定行写入数据
			setSheetSpecialData(sheets, id, sheet, redStyle, redStyleBold, headStyle,titleStyle);
			//处理标题
			setSheetTitleData(sheets, sheet,titleStyle);
			//设置单元格背景数据
			setSheetBgData(sheets, sheet, headStyle);
			// 默认列宽
			sheet.setDefaultColumnWidth(15);
			//设置标红单元格
			setSheetRedData(sheets, sheet, redStyle);

			//合并单元格
			if (CollectionUtils.isNotEmpty(sheets.getMergeData())) {
				for (MergeData mergeData : sheets.getMergeData()) {
					ExcelUtil.addMergeRegion(sheet, mergeData.getFirstRow(), mergeData.getLastRow(), mergeData.getFirstCol(), mergeData.getLastCol());
				}
			}
		}
		// 返回文件
		String fileName = exportParameter.getFileName();
		response.reset();
		response.setHeader("Content-disposition", "attachment; filename=" + fileName);
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding(StandardCharsets.UTF_8.name());
		try {
			OutputStream outputStream = response.getOutputStream();
			workbook.write(outputStream);
			outputStream.close();
		} catch (IOException e) {
			throw new ExcelException("文件输出失败");
		}
	}

	private static void setSheetTitleData(Sheets sheets, Sheet sheet, CellStyle titleStyle) {
		if (sheets.getTitleData() == null){
			return;
		}
		//处理标题
		Row row = sheet.getRow(sheets.getTitleData().getRow());
		if (row == null) {
			row = sheet.createRow(sheets.getTitleData().getRow());
			row.setHeight(sheet.getDefaultRowHeight());
		}
		//默认合并1行
		int titleWidth = 1;
		if(CollectionUtils.isNotEmpty(sheets.getData())){
			titleWidth = sheets.getData().get(0).size();
		}
		//合并单元格
		//FileUtil.writeMergeData(sheets,sheets.getTitleData().getRow(),sheets.getTitleData().getRow(),0,titleWidth-1);
		String titleName = StringUtils.EMPTY;
		if(CollectionUtils.isNotEmpty(sheets.getTitleData().getData())){
			titleName = ParseDataUtil.parseString(sheets.getTitleData().getData().get(0));
		}
		for (int i = 0; i < titleWidth; i++) {
			Cell cell = row.getCell(sheets.getTitleData().getCol() + i);
			if (null == cell) {
				cell = row.createCell(sheets.getTitleData().getCol() + i);
			}
			cell.setCellStyle(titleStyle);
			setCellValue(cell, titleName);
		}
	}

	private static CellStyle getHeadStyle(Workbook workbook) {
		XSSFCellStyle cellStyle = (XSSFCellStyle) workbook.createCellStyle();
		// 水平居左
		cellStyle.setAlignment(HorizontalAlignment.LEFT);
		// 垂直居中
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 背景色RGB灰色
		cellStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(228,230,234)));
		cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		// 单元格边框
		cellStyle.setAlignment(HorizontalAlignment.LEFT);
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setBorderTop(BorderStyle.THIN);
		// 创建并设置字体
		Font font = workbook.createFont();
		font.setFontHeightInPoints((short) 11);
		font.setBold(true);
		font.setFontName("宋体");
		cellStyle.setFont(font);
		return cellStyle;
	}


	private static void setSheetMergeList(Map<String, List<CellRangeAddress>> sheetMergeList, String id, Sheet sheet) {
		if(Objects.isNull(sheetMergeList)) {
			return;
		}
		List<CellRangeAddress> mergeList = sheetMergeList.get(id);
		if(CollectionUtils.isNotEmpty(mergeList)) {
			for (CellRangeAddress x : mergeList) {
				sheet.addMergedRegion(x);
			}
		}
	}

	private static void copyTemplate2WorkBook(String exportTemplatePath, String id, Sheet sheet, Workbook workbook) {
		try(InputStream templateIn = Thread.currentThread().getContextClassLoader().getResourceAsStream(exportTemplatePath);) {
			try(Workbook template  = new XSSFWorkbook(templateIn);){
				// 获取模板sheet
				String templateSheetName = TemplateDataEnum.getTemplateNameById(id);
				Sheet templateSheet = template.getSheet(templateSheetName);
				// 复制模板工作表到目标工作表
				CopySheetUtil.copySheet(templateSheet, sheet, CopySheetUtil.copyCellStyle(template, workbook));
			}catch (IOException e) {
				throw new ExcelException(READ_TEMPLATE_ERROR);
			}
		} catch (IOException e) {
			throw new ExcelException(READ_TEMPLATE_ERROR);
		}
	}

	private static void setSheetSpecialData(Sheets sheets, String id, Sheet sheet, CellStyle redStyle, CellStyle redStyleBold, CellStyle comm, CellStyle titleStyle) {
		if (CollectionUtils.isEmpty(sheets.getLeftHeaderData())) {
			return;
		}
		String templateSheetName = TemplateDataEnum.getTemplateNameById(id);
		for (InsertData insertData : sheets.getLeftHeaderData()) {
			Row row = sheet.getRow(insertData.getRow());
			if (row == null) {
				row = sheet.createRow(insertData.getRow());
				row.setHeight(sheet.getDefaultRowHeight());
			}
			for (int i = 0; i < insertData.getData().size(); i++) {
				Cell cell = row.getCell(insertData.getCol() + i);
				if(null == cell){
					cell = row.createCell(insertData.getCol() + i);
				}
				Object value = insertData.getData().get(i);
				if (value == null) {
					value = "--";
				}
				int columnIndex = cell.getColumnIndex();
				int rowIndex = cell.getRowIndex();
				if (ParseDataUtil.parseString(value).equals(ExcelConstant.IS_OVER_LIMIT)) {
					cell.setCellStyle(redStyle);
				} else if(ExcelConstant.BEARING_CAPACITY_EVALUATION.equals(templateSheetName) && columnIndex == 0 && rowIndex == 0){
					cell.setCellStyle(redStyleBold);
				}else {
					cell.setCellStyle(comm);
				}
				setCellValue(cell, value);
			}
		}
	}

	private static void setSheetSpecialData(Sheets sheets, String id, Sheet sheet, CellStyle redStyle, CellStyle redStyleBold, CellStyle comm) {
		if (CollectionUtils.isEmpty(sheets.getInsertData())) {
			return;
		}
		String templateSheetName = TemplateDataEnum.getTemplateNameById(id);
		for (InsertData insertData : sheets.getInsertData()) {
			Row row = sheet.getRow(insertData.getRow());
			if (row == null) {
				row = sheet.createRow(insertData.getRow());
				row.setHeight(sheet.getDefaultRowHeight());
			}
			for (int i = 0; i < insertData.getData().size(); i++) {
				Cell cell = row.getCell(insertData.getCol() + i);
				if(null == cell){
					cell = row.createCell(insertData.getCol() + i);
				}
				Object value = insertData.getData().get(i);
				if (value == null) {
					value = "--";
				}
				int columnIndex = cell.getColumnIndex();
				int rowIndex = cell.getRowIndex();
				if (ParseDataUtil.parseString(value).equals(ExcelConstant.IS_OVER_LIMIT)) {
					cell.setCellStyle(redStyle);
				} else if(ExcelConstant.BEARING_CAPACITY_EVALUATION.equals(templateSheetName) && columnIndex == 0 && rowIndex == 0){
					cell.setCellStyle(redStyleBold);
				}else {
					cell.setCellStyle(comm);
				}
				setCellValue(cell, value);
			}
		}
	}

	private static void setSheetPlainData(Sheets sheets, List<LinkedHashMap<String, Object>> data, Class clazz, List<List> valueList, Sheet sheet, CellStyle commLeft) {
		if (CollectionUtils.isEmpty(data)) {
			return;
		}
		if (clazz == null) {
			// 调用导出方法时数据字段需要按表头顺序排序好
			for (LinkedHashMap<String, Object> map : data) {
				List values = new ArrayList();
				for (Object value: map.values()) {
					if (value == null) {
						value = "--";
					}
					if(value instanceof String && ((String) value).isEmpty()){
						value = "--";
					}
					values.add(value);
				}
				valueList.add(values);
			}
		} else {
			// 通过实体类确定数据顺序
			List sortData = JsonTransferUtils.transferList(data, clazz);
			// 获取要导出的行数据列表
			valueList = getRowData(clazz, sortData, valueList);
		}
		// 顺序写入数据
		writeData(sheet, valueList, sheets.getWriteRow(), commLeft);
	}

	private static void setSheetBgData(Sheets sheets, Sheet sheet, CellStyle grayCellStyleBold) {
		if (CollectionUtils.isNotEmpty(sheets.getAddBgData())) {
			for (InsertData insertData : sheets.getAddBgData()) {
				Row row = sheet.getRow(insertData.getRow());
				if (row != null) {
					// 填充数据和设置cellstyle
					LinkedList<Object> data = insertData.getData();
					for(int j=0; j<data.size(); j+=1) {
						Cell cell = row.getCell(insertData.getCol() + j);
						if (null == cell) {
							cell = row.createCell(insertData.getCol() + j);
						}
						cell.setCellStyle(grayCellStyleBold);
						setCellValue(cell, data.get(j));
					}
				}
			}
		}
	}

	private static void setSheetRedData(Sheets sheets, Sheet sheet, CellStyle redStyle) {
		if (CollectionUtils.isNotEmpty(sheets.getRedData())) {
			for (InsertData insertData : sheets.getRedData()) {
				Row row = sheet.getRow(insertData.getRow());
				if (row != null) {
					Cell cell = row.getCell(insertData.getCol());
					if(cell != null){
						cell.setCellStyle(redStyle);
					}
				}

			}
		}
	}

	/**
	 * 获取无底色单元格样式
	 **/
	private static CellStyle getPlainCellStyle(Workbook workbook) {
		CellStyle comm = workbook.createCellStyle();
		// 水平居中
		comm.setAlignment(HorizontalAlignment.LEFT);
		// 垂直居中
		comm.setVerticalAlignment(VerticalAlignment.CENTER);
		// 单元格边框
		comm.setBorderBottom(BorderStyle.THIN);
		comm.setBorderLeft(BorderStyle.THIN);
		comm.setBorderRight(BorderStyle.THIN);
		comm.setBorderTop(BorderStyle.THIN);
		return comm;
	}

	/**
	 * 获取无底色单元格样式
	 **/
	private static CellStyle getPlainCellLeftStyle(Workbook workbook) {
		CellStyle comm = workbook.createCellStyle();
		// 水平居左
		comm.setAlignment(HorizontalAlignment.LEFT);
		// 垂直居中
		comm.setVerticalAlignment(VerticalAlignment.CENTER);
		// 单元格边框
		comm.setBorderBottom(BorderStyle.THIN);
		comm.setBorderLeft(BorderStyle.THIN);
		comm.setBorderRight(BorderStyle.THIN);
		comm.setBorderTop(BorderStyle.THIN);
		return comm;
	}

	private static CellStyle getTitleStyle(Workbook workbook) {
		CellStyle titleStyle = getHeadStyle(workbook);
		// title水平居中
		titleStyle.setAlignment(HorizontalAlignment.CENTER);
		return titleStyle;
	}

	private static CellStyle getRedCellStyle(Workbook workbook) {
		CellStyle redStyle = getPlainCellStyle(workbook);
		Font font = workbook.createFont();
		font.setColor(Font.COLOR_RED);
		redStyle.setFont(font);
		return redStyle;
	}


	private static CellStyle getRedCellStyleBold(Workbook workbook) {
		XSSFCellStyle cellStyle = (XSSFCellStyle) workbook.createCellStyle();
		// 水平居中
		//cellStyle.setAlignment(HorizontalAlignment.LEFT);
		// 垂直居中
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 背景色RGB灰色 纯色填充 ,new DefaultIndexedColorMap()
		cellStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(228,230,234)));
		cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		// 单元格边框
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setBorderTop(BorderStyle.THIN);
		Font font = workbook.createFont();
		font.setFontHeightInPoints((short) 12);
		font.setBold(true);
		cellStyle.setFont(font);
		return cellStyle;

	}

	private static CellStyle getGrayCellStyleBold(Workbook workbook) {
		XSSFCellStyle cellStyle = (XSSFCellStyle) workbook.createCellStyle();
		// 水平居中
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		// 垂直居中
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 背景色RGB灰色
		cellStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(228,230,234)));
		cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		// 单元格边框
		cellStyle.setAlignment(HorizontalAlignment.LEFT);
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setBorderTop(BorderStyle.THIN);
		// 创建并设置字体
		Font font = workbook.createFont();
		font.setFontHeightInPoints((short) 11); // 设置字号为11号字
		font.setBold(true); // 设置加粗
		font.setFontName("宋体"); // 设置字体为宋体

		cellStyle.setFont(font);
		return cellStyle;

	}


	private static void writeData(Sheet sheet, List<List> valueList, int writeRow, CellStyle commLeft) {
		try {
			for (int i = 0; i < valueList.size(); i++) {
				List dataArray = valueList.get(i);
				Row row = sheet.getRow(i + writeRow - 1);
				if (row == null) {
					row = sheet.createRow(i + writeRow - 1);
					row.setHeight(sheet.getDefaultRowHeight());
				}
				for (int j = 0; j < dataArray.size(); j++) {
					Cell cell = row.getCell(j);
					if (cell == null) {
						cell = row.createCell(j);
					}
					//内容全部居左
					cell.setCellStyle(commLeft);
					Object value = dataArray.get(j);
					setCellValue(cell, value);
				}
			}
		} catch (Exception e) {
			throw new ExcelException("数据导出失败");
		}
	}

	private static void setCellValue(Cell cell, Object value) {
		switch (value.getClass().getName()) {
			case INTEGER_TYPE:
			case DOUBLE_TYPE:
			case FLOAT_TYPE:
			case LONG_TYPE:
				cell.setCellValue(Double.parseDouble(value.toString()));
				break;
			case BOOLEAN_TYPE:
				cell.setCellValue(Boolean.parseBoolean(value.toString()));
				break;
			case STRING_TYPE:
				cell.setCellValue(String.valueOf(value));
				break;
			default:
				break;
		}
	}

	private static List<List> getRowData(Class clazz, List sortData, List<List> valueList) {
		try {
			for (Object entity : sortData) {
				List values = new ArrayList();
				for (Field field : entity.getClass().getDeclaredFields()) {
					if ("serialVersionUID".equals(field.getName())) {
						continue;
					}
					PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
					Method getMethod = pd.getReadMethod();
					Object value = getMethod.invoke(entity);
					values.add(value);
				}
				valueList.add(values);
			}
		} catch (IllegalAccessException | IntrospectionException | InvocationTargetException e) {
			throw new ExcelException("数据排序失败");
		}
		return valueList;
	}

	private static void writePicture(Workbook workbook, Sheets sheets, int lastCol, int lastRow, Sheet sheet) {
		if (CollectionUtils.isNotEmpty(sheets.getPicture())) {
			try {
				for (int j = 0; j < sheets.getPicture().size(); j++) {
					// 图片字符串转为字节数组
					byte[] picture = Base64.decodeBase64(sheets.getPicture().get(j).replace("data:image/png;base64,", ""));
					// 第一张图片右下角坐标
					XSSFDrawing xssfDrawing = (XSSFDrawing) sheet.createDrawingPatriarch();
					// 获取图片的实际宽度
					BufferedImage image = ImageIO.read(new ByteArrayInputStream(picture));
					int widthPixels = image.getWidth();
					//计算图片自适应excel宽度
					int width = widthPixels / 128;
					if(specialIdList.contains( sheets.getId())){
						width = sheets.getCol();
					}
					// 前四个参数是控制图片距离单元格left，top，right，bottom的像素距离,后四个参数表示图片左上角所在的cellNum和rowNum、右下角所在的cellNum和rowNum
					XSSFClientAnchor anchor = new XSSFClientAnchor(0,0,0,0, 0,lastRow*j, width, lastRow*(j + 1));
					xssfDrawing.createPicture(anchor, workbook.addPicture(picture, XSSFWorkbook.PICTURE_TYPE_PNG));
				}
			} catch (Exception e) {
				throw new ExcelException("图片导出失败");
			}
		}
	}

	/**
	 * 设置图片的位置大小、写数据的起始行、sheet名
	 * @param sheets
	 */
	public static void setSheets(Sheets sheets) {
		String id = sheets.getId();
		sheets.setSheetName(TemplateDataEnum.getSheetNameById(id));
		sheets.setCol(TemplateDataEnum.getColById(id));
		sheets.setRow(TemplateDataEnum.getRowById(id));
		sheets.setWriteRow(TemplateDataEnum.getWriteRowById(id));
	}

	/**
	 * @description: 稳态单点自定义报表的导出，其他方法不用
	 **/
	private static void setSingleCustomizeSheets(ExportParameter exportParameter, Workbook workbook, String exportTemplatePath) {
		Iterator var5 = exportParameter.getSheets().iterator();

		while(true) {
			Sheets sheets;
			Sheet sheet;
			CellStyle redStyle;
			Iterator var16;
			InsertData insertData;
			Row row;
			do {
				String id;
				List data;
				do {
					if (!var5.hasNext()) {
						return;
					}

					sheets = (Sheets)var5.next();
					id = sheets.getId();
					sheet = setSingleCustomizeSheets(workbook, exportTemplatePath, id, sheets);

					int lastCol = sheets.getCol();
					int lastRow = sheets.getRow();
					writePicture(workbook, sheets, lastCol, lastRow, sheet);
					data = sheets.getData();
				} while(CollectionUtils.isEmpty(data) && CollectionUtils.isEmpty(sheets.getInsertData()));

				List<List> valueList = new ArrayList();
				Class clazz = TemplateDataEnum.getClazzById(id);
				CellStyle comm = getPlainCellStyle(workbook);
				redStyle = getRedCellStyle(workbook);
				// 红色粗体单元格样式
				CellStyle redStyleBold = getRedCellStyleBold(workbook);
				//表头样式
				CellStyle titleStyle = getTitleStyle(workbook);
				CellStyle headStyle = getHeadStyle(workbook);
				singleCustomizedPlainData(data, clazz, valueList, sheet, sheets, comm);

				setSingleCustomizedData(sheets, sheet, comm, redStyle);
				setSheetSpecialData(sheets, id, sheet, redStyle, redStyleBold, headStyle,titleStyle);

				CellStyle grayCellStyleBold = getGrayCellStyleBold(workbook);
				//设置添加单元格背景数据
				setSheetBgData(sheets, sheet, grayCellStyleBold);
				setSheetTitleData(sheets, sheet,titleStyle);
			} while(!CollectionUtils.isNotEmpty(sheets.getRedData()));

			setSingleCustomizeSheetsRedData(sheets, sheet, redStyle);

		}
	}

	private static Sheet setSingleCustomizeSheets(Workbook workbook, String exportTemplatePath, String id, Sheets sheets) {
		Sheet sheet;
		if (StringUtils.isBlank(id)) {
			sheet = workbook.createSheet(sheets.getSheetName());
			List<String> head = sheets.getHead();
			Row headRow = sheet.createRow(sheets.getWriteRow() + 1);

			for(int i = 0; i < head.size(); ++i) {
				Cell headCell = headRow.createCell(i);
				headCell.setCellValue((String)head.get(i));
			}
		} else {
			XSSFWorkbook template;
			try(InputStream templateIn = Thread.currentThread().getContextClassLoader().getResourceAsStream(exportTemplatePath);) {
				template = new XSSFWorkbook(templateIn);
			} catch (IOException var28) {
				throw new ExcelException(READ_TEMPLATE_ERROR);
			}

			String templateSheetName = TemplateDataEnum.getTemplateNameById(id);
			Sheet templateSheet = template.getSheet(templateSheetName);
			sheet = workbook.createSheet(sheets.getSheetName());
			CopySheetUtil.copySheet(templateSheet, sheet, CopySheetUtil.copyCellStyle(template, workbook));
		}
		return sheet;
	}

	private static void setSingleCustomizeSheetsRedData(Sheets sheets, Sheet sheet, CellStyle redStyle) {
		Iterator var16;
		Row row;
		InsertData insertData;
		var16 = sheets.getRedData().iterator();

		while(var16.hasNext()) {
			insertData = (InsertData)var16.next();
			row = sheet.getRow(insertData.getRow());
			if (row != null) {
				Cell cell = row.getCell(insertData.getCol());
				if (cell != null) {
					cell.setCellStyle(redStyle);
				}
			}
		}
	}

	private static void singleCustomizedPlainData(List data, Class clazz, List<List> valueList, Sheet sheet, Sheets sheets, CellStyle comm) {
		Iterator var16;
		if (CollectionUtils.isNotEmpty(data)) {
			if (clazz == null) {
				var16 = data.iterator();

				while(var16.hasNext()) {
					LinkedHashMap<String, Object> map = (LinkedHashMap)var16.next();
					List values = new ArrayList();

					Object value;
					for(Iterator var19 = map.values().iterator(); var19.hasNext(); values.add(value)) {
						value = var19.next();
						if (value == null) {
							value = "--";
						}
					}

					((List) valueList).add(values);
				}
			} else {
				List sortData = JsonTransferUtils.transferList(data, clazz);
				valueList = getRowData(clazz, sortData, (List) valueList);
			}

			writeData(sheet, (List) valueList, sheets.getWriteRow(), comm);
		}
	}

	private static void setSingleCustomizedData(Sheets sheets, Sheet sheet, CellStyle comm, CellStyle redStyle) {
		Row row;
		InsertData insertData;
		Iterator var16;
		if (CollectionUtils.isEmpty(sheets.getInsertData())) {
			return;
		}
		var16 = sheets.getInsertData().iterator();

		while(var16.hasNext()) {
			insertData = (InsertData)var16.next();
			row = sheet.getRow(insertData.getRow());
			if (row == null) {
				row = sheet.createRow(insertData.getRow());
				row.setHeight(sheet.getDefaultRowHeight());
			}

			for(int i = 0; i < insertData.getData().size(); ++i) {
				Cell cell = row.getCell(insertData.getCol() + i);
				if (null == cell) {
					cell = row.createCell(insertData.getCol() + i);
					cell.setCellStyle(comm);
				}

				CellStyle cs = cell.getCellStyle();
				cs.setVerticalAlignment(VerticalAlignment.CENTER);
				cs.setAlignment(HorizontalAlignment.LEFT);
				Object value = insertData.getData().get(i);
				if (value == null) {
					value = "--";
				}

				if (ParseDataUtil.parseString(value).equals("不合格")) {
					cell.setCellStyle(redStyle);
				} else {
					cell.setCellStyle(comm);
				}

				setCellValue(cell, value);
			}
		}
	}

	/**
	 * 设置图片的位置大小、写数据的起始行、sheet名
	 * @param sheets
	 */
	public static void setSheetsInfoByMonitorType(Sheets sheets,Integer monitorType) {
		String id = sheets.getId();
		String sheetName = "";
		if (ParseDataUtil.parseInteger(monitorType) > 0){
			sheetName = MonitorTypeEnum.getNameByValue(monitorType);
		}
		sheets.setSheetName(sheetName + TemplateDataEnum.getSheetNameById(id));
		sheets.setCol(TemplateDataEnum.getColById(id));
		sheets.setRow(TemplateDataEnum.getRowById(id));
		sheets.setWriteRow(TemplateDataEnum.getWriteRowById(id));
	}

	/**
	 * 设置首行标题数据（在标题中插入查询时间、全网、上送）
	 * @param sheets
	 * @param row 标题行
	 * @param col 标题列
	 */
	public static void setSheetAndTitle(Long startTime, Long endTime, Sheets sheets, int row, int col) {
		setSheets(sheets);
		String title = TemplateDataEnum.getSheetNameById(sheets.getId());
		LinkedList<Object> objects = new LinkedList<>();
		if (startTime != null) {
			title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
		}
		objects.add(title);
		if (CollectionUtils.isEmpty(sheets.getInsertData())) {
			sheets.setInsertData(Collections.singletonList(new InsertData(row, col, objects)));
		} else {
			sheets.getInsertData().add(new InsertData(row, col, objects));
		}
	}

	/**
	 * 设置首行标题数据（在标题中插入查询时间、主配网）
	 * @param sheets
	 * @param row 标题行
	 * @param col 标题列
	 */
	public static void setSheetAndTitleByMonitorType(Long startTime, Long endTime, Sheets sheets,int row, int col,Integer monitorType) {
		setSheetsInfoByMonitorType(sheets,monitorType);
		String sheetName = "";
		if (MonitorTypeEnum.MAIN.getValue().equals(monitorType)) {
			sheetName =  MonitorTypeEnum.MAIN.getName();
		}else if(MonitorTypeEnum.DISTRIBUTION.getValue().equals(monitorType)){
			sheetName =  MonitorTypeEnum.DISTRIBUTION.getName();
		}
		String title = sheetName + TemplateDataEnum.getSheetNameById(sheets.getId());
		LinkedList<Object> objects = new LinkedList<>();
		if (startTime != null) {
			title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
		}
		objects.add(title);
		sheets.setTitleData(new InsertData(row, col, objects));
	}

	/**
	 * 设置首行标题数据（在标题中插入查询时间、主配网）
	 * @param sheets
	 * @param row 标题行
	 * @param col 标题列
	 */
	public static void setSheetTitleByMonitorTypeUpload(Long startTime, Long endTime, Sheets sheets,int row, int col,Integer monitorType, Boolean isUpload) {
		setSheetsInfoByMonitorType(sheets,monitorType);
		String title = TemplateDataEnum.getSheetNameById(sheets.getId());
		if (isUpload != null) {
			if (isUpload) {
				title += ExcelConstant.HEAD_DELIMITER + ExcelConstant.HEAD_UPLOAD_TRUE;
			} else {
				title += ExcelConstant.HEAD_DELIMITER + ExcelConstant.HEAD_UPLOAD_FALSE;
			}
		}
		LinkedList<Object> objects = new LinkedList<>();
		if (startTime != null) {
			title += ExcelConstant.HEAD_DELIMITER + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
		}
		objects.add(title);
		sheets.setTitleData(new InsertData(row, col, objects));
	}

	/**
	 * 设置首行标题数据（在标题中插入查询时间、全网、上送）
	 * @param sheets
	 * @param isUpload
	 * @param row 标题行
	 * @param col 标题列
	 */
	public static void setSheetAndTitle(Long startTime, Long endTime, Sheets sheets,Boolean isUpload, int row, int col) {
		setSheets(sheets);
		String title = TemplateDataEnum.getSheetNameById(sheets.getId());
		if (isUpload != null) {
			if (isUpload) {
				title += " - " + ExcelConstant.HEAD_UPLOAD_TRUE;
			} else {
				title += " - " + ExcelConstant.HEAD_UPLOAD_FALSE;
			}
		}
		LinkedList<Object> objects = new LinkedList<>();
		if (startTime != null) {
			title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
		}
		objects.add(title);
		sheets.setTitleData(new InsertData(row, col, objects));
	}

	/**
	 * 设置首行标题数据（在标题中插入查询时间、全网、上送）
	 * @param sheets
	 * @param isUpload
	 * @param row 标题行
	 * @param col 标题列
	 */
	public static void setSheetAndTitleWithUpload(Long startTime, Long endTime, Sheets sheets,Boolean isUpload, int row, int col) {
		setSheets(sheets);
		String title = TemplateDataEnum.getSheetNameById(sheets.getId());
		if (isUpload != null) {
			if (isUpload) {
				title += " - " + ExcelConstant.HEAD_UPLOAD_TRUE;
			} else {
				title += " - " + ExcelConstant.HEAD_UPLOAD_FALSE;
			}
		}
		LinkedList<Object> objects = new LinkedList<>();
		if (startTime != null) {
			title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
		}
		objects.add(title);
		if (CollectionUtils.isEmpty(sheets.getInsertData())) {
			sheets.setTitleData(new InsertData(row, col, objects));
		} else {
			sheets.getInsertData().add(new InsertData(row, col, objects));
		}
	}


	/**
	 * 设置首行标题数据（在标题中插入查询时间、全网、上送）
	 * @param sheets
	 * @param row 标题行
	 * @param col 标题列
	 */
	public static void setSheetAndTitleWithoutUpload(Long startTime, Long endTime, Sheets sheets, int row, int col) {
		setSheets(sheets);
		String title = TemplateDataEnum.getSheetNameById(sheets.getId());
		LinkedList<Object> objects = new LinkedList<>();
		if (startTime != null) {
			title += " - " + DateUtils.formatDate(startTime, TimeFormat.DATETIMEFORMAT) + "-" + DateUtils.formatDate(endTime, TimeFormat.DATETIMEFORMAT);
		}
		objects.add(title);
		if (CollectionUtils.isEmpty(sheets.getInsertData())) {
			sheets.setInsertData(Collections.singletonList(new InsertData(row, col, objects)));
		} else {
			sheets.getInsertData().add(new InsertData(row, col, objects));
		}
	}


	public static void setSheetAndTitle(Sheets sheets, int row, int col) {
		setSheets(sheets);
		String title = TemplateDataEnum.getSheetNameById(sheets.getId());
		LinkedList<Object> objects = new LinkedList<>();
		objects.add(title);
		sheets.setTitleData(new InsertData(row, col, objects));
	}
	/**
	 * 合并单元格
	 * @param sheet
	 * @param firstRow
	 * @param lastRow
	 * @param firstCol
	 * @param lastCol
	 */
	public static void addMergeRegion(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
		sheet.addMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
	}

	/**
	 * 设置列宽
	 * @param sheet
	 * @param i2
	 * @param width
	 */
	public static void setColumnWidth(Sheet sheet, int i2, int width) {
		sheet.setColumnWidth(i2, width * 256);
	}

	/**
	 *
	 * @param sheet
	 * @param index    行号（从0开始）
	 * @param height   行高
	 * @return
	 */
	public static Row createRowSetRowHeight(Sheet sheet, int index, int height) {
		Row row1 = sheet.createRow(index);
		row1.setHeight((short) (height * 20));
		return row1;
	}

	/**
	 *
	 * @param headerCellStyle 单元格样式
	 * @param row   行
	 * @param index 一行中的列号（从0开始）
	 * @param value 单元格的值
	 */
	public static void addCell(XSSFCellStyle headerCellStyle, Row row, int index, Object value) {
		Cell cell = row.createCell(index);
		if (StringUtils.isEmpty(ParseDataUtil.parseString(value))) {
			value = "--";
		}
		switch (value.getClass().getName()) {
			case INTEGER_TYPE:
			case DOUBLE_TYPE:
			case FLOAT_TYPE:
			case LONG_TYPE:
				cell.setCellValue(Double.parseDouble(value.toString()));
				break;
			case BOOLEAN_TYPE:
				cell.setCellValue(Boolean.parseBoolean(value.toString()));
				break;
			case STRING_TYPE:
				if (String.valueOf(value).endsWith(".0")) {
					cell.setCellValue(String.valueOf(value).replace(".0", ""));
				} else {
					cell.setCellValue(String.valueOf(value));
				}
				break;
			default:
				break;
		}
		cell.setCellStyle(headerCellStyle);
	}

	/**
	 * 不对String类型进行处理
	 * @param headerCellStyle 单元格样式
	 * @param row   行
	 * @param index 一行中的列号（从0开始）
	 * @param value 单元格的值
	 */
	public static void addCellOrigin(XSSFCellStyle headerCellStyle, Row row, int index, Object value) {
		Cell cell = row.createCell(index);
		if (StringUtils.isEmpty(ParseDataUtil.parseString(value))) {
			value = "--";
		}
		switch (value.getClass().getName()) {
			case INTEGER_TYPE:
			case DOUBLE_TYPE:
			case FLOAT_TYPE:
			case LONG_TYPE:
				cell.setCellValue(Double.parseDouble(value.toString()));
				break;
			case BOOLEAN_TYPE:
				cell.setCellValue(Boolean.parseBoolean(value.toString()));
				break;
			case STRING_TYPE:
				cell.setCellValue(String.valueOf(value));
				break;
			default:
				break;
		}
		cell.setCellStyle(headerCellStyle);
	}

	public static ExcelWriter getWriter() {
		return cn.hutool.poi.excel.ExcelUtil.getWriter(true);
	}

	/**
	 * @Description: easyexcel写入，解决小数点自动补0的问题
	 **/
	public static void easyExcelWrite(List<List<Object>> resultData, WriteSheet sheet, com.alibaba.excel.ExcelWriter excelWriter, WriteTable table) {
		List<List<Object>> resultList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(resultData)) {
			resultData.forEach(dataList -> {
				List<Object> objects = new ArrayList<>();
				dataList.forEach(data -> {
					String value = ParseDataUtil.parseString(data);
					if (value.endsWith(".0")) {
						objects.add(value.replace(".0", ""));
					} else {
						objects.add(value);
					}
				});
				if (CollectionUtils.isNotEmpty(objects)) {
					resultList.add(objects);
				}
			});
		}
		if (table == null) {
			excelWriter.write(resultList, sheet);
		} else {
			excelWriter.write(resultList, sheet, table);
		}
	}
	/**
	 *
	 * @param writer
	 * @param index     sheetNo（从0开始）
	 * @param sheetName sheet名
	 * @return
	 */
	public static Sheet getSheetAndSetSheetName(ExcelWriter writer, int index, String sheetName) {
		Sheet sheet = writer.getSheet();
		writer.getWorkbook().setSheetName(index,sheetName);
		return sheet;
	}

	/**
	 * 数据单元格样式 边框黑细
	 * @param writer
	 * @return
	 */
	public static XSSFCellStyle getDataCellStyle(ExcelWriter writer, int row, int col) {
		// 获取当前单元格样式
		XSSFCellStyle cellStyle = (XSSFCellStyle )writer.createCellStyle();
		// 上下居中 左右居中
		cellStyle.setAlignment(HorizontalAlignment.LEFT);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 下边框
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		// 左边框
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		// 右边框
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		// 上边框
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		return cellStyle;
	}

	/**
	 * 数据单元格样式 边框黑细 文字居左
	 * @param writer
	 * @return
	 */
	public static XSSFCellStyle getDataCellStyleLeft(ExcelWriter writer, int row, int col) {
		// 获取当前单元格样式
		XSSFCellStyle cellStyle = (XSSFCellStyle )writer.createCellStyle();
		// 上下居中 左右居中
		cellStyle.setAlignment(HorizontalAlignment.LEFT);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 下边框
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		// 左边框
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		// 右边框
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		// 上边框
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		return cellStyle;
	}

	/**
	 * 数据单元格样式 边框黑细 数据标红
	 * @param writer
	 * @return
	 */
	public static XSSFCellStyle getRedDataCellStyle(ExcelWriter writer) {
		// 获取当前单元格样式
		XSSFCellStyle cellStyle = (XSSFCellStyle )writer.createCellStyle();
		// 上下居中 左右居中
		cellStyle.setAlignment(HorizontalAlignment.LEFT);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 下边框
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		// 左边框
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		// 右边框
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		// 上边框
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		// 字体加粗
		Font font = writer.getWorkbook().createFont();
		font.setColor(Font.COLOR_RED);
		cellStyle.setFont(font);
		return cellStyle;
	}

	/**
	 * 表头样式 黑色边框 灰色背景色 字体加粗
	 * @param writer
	 * @return
	 */
	public static XSSFCellStyle getHeaderCellStyle(ExcelWriter writer) {
		// 获取当前单元格样式
		XSSFCellStyle  cellStyle = (XSSFCellStyle )writer.createCellStyle();
		// 上下居中 左右居中
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 下边框
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		// 左边框
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		// 右边框
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		// 上边框
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		// 背景色RGB灰色 纯色填充 ,new DefaultIndexedColorMap()
		cellStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(228,230,234)));
		cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		// 字体加粗
		Font font = writer.getWorkbook().createFont();
		font.setBold(true);
		cellStyle.setFont(font);
		return cellStyle;
	}

	/**
	 * 表头样式 黑色边框 灰色背景色 字体加粗
	 * @param writer
	 * @return
	 */
	public static XSSFCellStyle getHeaderCellStyleLeft(ExcelWriter writer) {
		// 获取当前单元格样式
		XSSFCellStyle  cellStyle = (XSSFCellStyle )writer.createCellStyle();
		// 上下居中 左右居左
		cellStyle.setAlignment(HorizontalAlignment.LEFT);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 下边框
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		// 左边框
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		// 右边框
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		// 上边框
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		// 背景色RGB灰色 纯色填充 ,new DefaultIndexedColorMap()
		cellStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(228,230,234)));
		cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		// 字体加粗
		Font font = writer.getWorkbook().createFont();
		font.setBold(true);
		cellStyle.setFont(font);
		return cellStyle;
	}

	/**
	 * 输出表格
	 *
	 * @param writer
	 * @param response
	 */
	public static void exportExcel(ExcelWriter writer, HttpServletResponse response, String description) throws IOException {
		Date date = new Date(System.currentTimeMillis());
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd_HHmmss");
		String current = formatter.format(date);
		String excelName = description + current + ".xlsx";
		response.setHeader("Content-disposition", "attachment;filename=" + encodeFileName(excelName));
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8 ");
		writer.flush(response.getOutputStream());
		writer.close();
	}

	public static String encodeFileName(String fileName) throws UnsupportedEncodingException {
		fileName = URLEncoder.encode(fileName, "UTF-8");
		return fileName.replace("+", "%20");
	}


	/**
	 * @Description: 变电站匹配台区单位
	 * @return: void
	 **/
	public static void matchSubAreaUnit(List<ErrorExcelMsg> errorMessageList, String provinceCompanyName,String countyCompanyName, String cityCompanyName, String powerdistributionareaname,
										String rowid, AtomicReference<Long> stationId, List<Map<String, Object>> stationOpt, AtomicReference<String> modelLabel) {
		if (CollectionUtils.isNotEmpty(stationOpt)) {
			stationOpt.forEach(substationObjectMap -> {
				//台区
				List<Map<String, Object>> areaList = ParseDataUtil.parseList(substationObjectMap.get(ColumnName.POWERDISTRIBUTIONAREA_MODEL));
				if (CollectionUtils.isNotEmpty(areaList) && StringUtils.isNotEmpty(powerdistributionareaname)) {
					modelLabel.set(ColumnName.POWERDISTRIBUTIONAREA);
					List<Map<String, Object>> matchAreaList = areaList.stream().filter(t -> ParseDataUtil.parseString(t.get(ColumnName.NAME)).equals(powerdistributionareaname)).collect(Collectors.toList());
					if (CollectionUtils.isNotEmpty(matchAreaList)) {
						//变电站匹配管理单位
						ExcelUtil.matchSubCompanyUnit(errorMessageList, stationId, substationObjectMap, provinceCompanyName, countyCompanyName, cityCompanyName, rowid, ParseDataUtil.parseLong(matchAreaList.get(0).get("id")));
					} else {
						errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 5, "所属台区匹配错误"));
					}
				} else {
					//变电站匹配管理单位
					ExcelUtil.matchSubCompanyUnit(errorMessageList, stationId, substationObjectMap, provinceCompanyName, countyCompanyName, cityCompanyName, rowid, ParseDataUtil.parseLong(substationObjectMap.get("id")));
				}
			});
		} else {
			errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 4, "所属电站匹配错误"));
		}
	}


	/**
	 * @Description: 变电站匹配市级单位
	 * @return: void
	 **/
	public static void matchSubCompanyUnit(List<ErrorExcelMsg> errorMessageList, AtomicReference<Long> stationId, Map<String, Object> substationObjectMap, String provinceCompanyName, String countyCompanyName,
										   String cityCompanyName, String rowid, Long modelId) {
		List<Map<String, Object>> countyList = ParseDataUtil.parseList(substationObjectMap.get(ColumnName.COUNTYCOMPANY_MODEL));
		if(StringUtils.isNotEmpty(countyCompanyName) && CollectionUtils.isEmpty(countyList)) {
			errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + ROW_INDEX, 4, "变电站和所属区县匹配错误"));
		}else
		if (CollectionUtils.isNotEmpty(countyList)) {
			matchSubCompanyUnitWhenCounty(errorMessageList, stationId, provinceCompanyName, countyCompanyName, cityCompanyName, rowid, modelId, countyList.get(0));
		} else {
			// 关联市级
			matchSubCompanyUnitWhenCity(errorMessageList, stationId, substationObjectMap, provinceCompanyName, cityCompanyName, rowid, modelId);
		}
	}

	private static void matchSubCompanyUnitWhenCity(List<ErrorExcelMsg> errorMessageList, AtomicReference<Long> stationId, Map<String, Object> mapList, String provinceCompanyName, 
													String cityCompanyName, String rowid, Long modelId) {
		List<Map<String, Object>> cityList = ParseDataUtil.parseList(mapList.get(ColumnName.CITYCOMPANY_MODEL));
		if (CollectionUtils.isNotEmpty(cityList) && Objects.equals(cityList.get(0).get(ColumnName.NAME), cityCompanyName)) {
			List<Map<String, Object>> provinceList = ParseDataUtil.parseList(cityList.get(0).get(ColumnName.PROVINCECOMPANY_MODEL));
			if (CollectionUtils.isNotEmpty(provinceList) && Objects.equals(provinceList.get(0).get(ColumnName.NAME), provinceCompanyName)) {
				stationId.set(modelId);
			} else {
				errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + ROW_INDEX, 1, "所属省级单位匹配错误"));
			}
		} else {
			errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + ROW_INDEX, 2, MISMATCH_CITYCOMPANY));
		}
	}

	private static void matchSubCompanyUnitWhenCounty(List<ErrorExcelMsg> errorMessageList, AtomicReference<Long> stationId, String provinceCompanyName, 
													  String countyCompanyName, String cityCompanyName, String rowid, Long modelId, Map<String, Object> countyMap) {
		if (countyMap.get(ColumnName.NAME).equals(countyCompanyName)) {
			//县级和市级都需要匹配
			matchSubCompanyUnitWhenCity(errorMessageList, stationId, countyMap, provinceCompanyName, cityCompanyName, rowid, modelId);
		} else {
			errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + ROW_INDEX, 3, "所属县级单位匹配错误"));
		}
	}

	/**
	 * @Description: 变电站匹配市级单位
	 * @return: void
	 **/
	public static void matchSubCompanyUnit(List<ErrorExcelMsg> errorMessageList, AtomicReference<Long> stationId, Map<String, Object> substationObjectMap
										   ,String cityCompanyName, String rowid, Long modelId) {
		List<Map<String, Object>> countyList = ParseDataUtil.parseList(substationObjectMap.get(ColumnName.COUNTYCOMPANY_MODEL));
		List<Map<String, Object>> cityList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(countyList)) {
			cityList = ParseDataUtil.parseList(countyList.get(0).get(ColumnName.CITYCOMPANY_MODEL));
		} else {
			// 关联市级
			cityList = ParseDataUtil.parseList(substationObjectMap.get(ColumnName.CITYCOMPANY_MODEL));
		}
		if (CollectionUtils.isNotEmpty(cityList) && !cityList.get(0).get(ColumnName.NAME).equals(cityCompanyName)) {
			errorMessageList.add(new ErrorExcelMsg(0, ParseDataUtil.parseInteger(rowid) + 4, 1, MISMATCH_CITYCOMPANY));
		}
		stationId.set(modelId);
	}
}

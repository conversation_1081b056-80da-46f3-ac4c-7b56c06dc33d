package com.cet.pq.common.model.peccore;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName Node
 * @Description TODO
 * <AUTHOR>
 * @Date 3/11/2020 5:19 PM
 */
@Data
public class Node {
    private long nodeId;
    private long nodeType;
    private String name;
    private List<Node> children;

    public Node(long nodeId, long nodeType) {
        this.nodeId = nodeId;
        this.nodeType = nodeType;
    }

    public Node(long nodeId, long nodeType, String name) {
        this.nodeId = nodeId;
        this.nodeType = nodeType;
        this.name = name;
        this.children = new ArrayList<>();
    }
}

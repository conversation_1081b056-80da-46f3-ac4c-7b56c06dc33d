package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 14:37
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ClockMonitorDeviceVO {

    /**
     * 监测点编号
     */
    private Long monitorId;

    /**
     * 监测时间
     */
    private Long logtime;

    /**
     * 装置测量值
     */
    private Double deviceMeasureValue;

    /**
     * 系统测量值
     */
    private Double systemMeasureValue;

    /**
     * 时间偏差范围
     */
    private String timeOffset;

    /**
     * 监测区域
     */
    private String cityCompany;


}

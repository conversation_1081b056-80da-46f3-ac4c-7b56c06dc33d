package com.cet.pq.common.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/06/08 17:08
 */
@Data
public class AnomalDeatilResult {
    private Long lineId;
    private String lineCode;
    private String lineName;
    private String monitorType;
    private String monitorName;
    private String substationName;
    private String substationvoltageclass;
    private String voltageclass;
    private String cityCompanyName;
    private String provinceCompanyName;
    private Long logtime;
    private String status;
    //暂态相关
    private String eventType;
    private Integer duration;
    private Double magnitude;
    private String eventByte;
    private Integer eventCount;

    private List<DataValue> values;

    @Data
    public static class DataValue{
        private Integer dataId;
        private String dataName;
        private Object dataValue;
    }

}

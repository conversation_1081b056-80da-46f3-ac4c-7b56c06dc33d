package com.cet.pq.common.model.realtime;

import com.cet.pq.common.annotation.FieldAnnotation;
import com.cet.pq.common.enums.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * @Description 监测点
 * <AUTHOR>
 * @Date 2020/6/30 14:30
 */
@Data
@NoArgsConstructor
public class LineVo {

    private String modelLabel = "line";

    private Long id;
    /**
     * 线路名称
     */
    @FieldAnnotation(name = "监测点名称")
    private String name;
    /**
     * 主配网类型
     */
    @FieldAnnotation(name = "主配网类型")
    @JsonProperty("monitortype")
    private Integer monitorType;
}

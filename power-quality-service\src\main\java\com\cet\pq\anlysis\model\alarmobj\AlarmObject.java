package com.cet.pq.anlysis.model.alarmobj;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-2-22 15:46:54
 * @description 告警对象台账对象
 */
@Data
public class AlarmObject {
    private String modelLabel = "alarmobject";
    private Long id;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String mobile;
    /**
     * 是否反馈
     */
    @ApiModelProperty("是否反馈")
    private Boolean iffeedback;
    /**
     * 审核状态(1:审核未通过;2:审核已通过;)
     */
    @ApiModelProperty("审核状态")
    private Integer alarmstatus;
    /**
     * 处理情况说明
     */
    @ApiModelProperty("处理情况说明")
    private String solveexplain;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long logtime;
}

package com.cet.pq.common.feign;

import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.datalog.TrendDataVo;
import com.cet.pq.common.model.datalog.TrendSearchListVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
* @Description:  3.8设备数据服务
* @Author:      zhangshixi
* @Date:     2021/11/17 9:01
*/
@FeignClient(value = "device-data-service38")
public interface DeviceDataService38 {

	/**
	 * 查询定时记录
	 *
	 * @param searchVo
	 * @return
	 */
	@PostMapping("/api/v1/batch/datalog/span/group")
	public Result<List<TrendDataVo>> queryTrendCurveData2(@RequestBody TrendSearchListVo searchVo, @RequestParam("fill") Boolean fill,@RequestParam("p35") Boolean p35);

}

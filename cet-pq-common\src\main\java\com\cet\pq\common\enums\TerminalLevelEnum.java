package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 	装置级别枚举类
 * <AUTHOR>
 *
 */
public enum TerminalLevelEnum {
	//装置级别枚举类
	level_a(1,"A类"),
	level_b(2,"B类"),
	level_s(3,"S类");
	
	private Integer key;
	private String value;
	
	private TerminalLevelEnum(Integer key, String value) {
		this.key = key;
		this.value = value;
	}
	
	public static Integer getTerminalLevel(String levelStr) {
		for (TerminalLevelEnum terminalLevelEnum : TerminalLevelEnum.values()) {
			if(terminalLevelEnum.value.equals(levelStr)) {
				return terminalLevelEnum.key;
			}
		}
		throw new CommonManagerException("装置级别枚举值转化异常");
	}
	
	public static Integer getTerminalLevelNoThrows(String levelStr) {
		if(StringUtils.isBlank(levelStr)){
			return null;
		}
		for (TerminalLevelEnum terminalLevelEnum : TerminalLevelEnum.values()) {
			if(terminalLevelEnum.value.equals(levelStr)) {
				return terminalLevelEnum.key;
			}
		}
		return 0;
	}

	public static String getValueByKeyNoThrows(Integer key) {
		for (TerminalLevelEnum terminalLevelEnum : TerminalLevelEnum.values()) {
		    if (terminalLevelEnum.key.equals(key)) {
		    	return terminalLevelEnum.value;
			}
		}
		return StringUtils.EMPTY;
	}

	public Integer getKey() {
		return key;
	}

	public void setKey(Integer key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
}

package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;

/**
 * 设备类型枚举值
 * 
 * <AUTHOR>
 *
 */
public enum MeterTypeEnum {
	//设备类型枚举值

	secondary("二次表计", 1), logicalunit("逻辑可编程控制器", 2), system("第三方系统", 3), protectunit("保护", 4), temperature("温控仪", 5),
	ups("UPS", 6), battery("蓄电池", 7), pdu("PDU机柜插座", 8), pq("电能质量", 9), generator("发电机", 10), HVDC("高压直流设备", 11);

	private String name;
	private Integer value;

	private MeterTypeEnum(String name, Integer value) {
		this.name = name;
		this.value = value;
	}

	public String getName() {
		return name;
	}


	public Integer getValue() {
		return value;
	}

	public static Integer getMeterType(String name) {
		for (MeterTypeEnum meterTypeEnum : MeterTypeEnum.values()) {
			if (name.equals(meterTypeEnum.getName())) {
				return meterTypeEnum.getValue();
			}
		}
		throw new CommonManagerException("设备类型值无效");
	}

}

package com.cet.pq.common.cca;

import com.cet.pq.common.model.LineDataDTO;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

/**
 * 谐波相关系数
 * <AUTHOR>
 * @date 2022年10月24日 10:42
 */
public class Harmonic {
    /**
     * 分析窗口时间长度（小时）；默认一天
     */
    protected static int n = 24;

    /**
     * 一个分析窗口的数据量，默认为一分钟一条
     */
    protected static int t = n*60;
    /**
     * 线性插值 根据时间线补全数据
     * @param timeLine 时间线
     * @param list 需要补全的数据
     */
    public static void linearInterpolation(List<LocalTime> timeLine, List<LineDataDTO> list, LocalDate date) {
        int length = timeLine.size();
        // 线性插值
        if (!(list.size() == length)) {
            AtomicReference<LineDataDTO> item = new AtomicReference<>();
            Stream.iterate(0, i -> i+1).limit(length).forEach(i -> {
                LocalTime time = timeLine.get(i);
                if (i >= list.size()) {
                    list.add(new LineDataDTO(date.atTime(time), (list.get(i - 1).getValue() + list.get(i - 2).getValue())/2));
                } else {
                    item.set(list.get(i));
                    // 判断该时间点是否缺值，若缺值则拿上下的平均值插值
                    LocalDateTime dateTime = item.get().getTime();
                    if (dateTime.toLocalTime().compareTo(time) > 0) {
                        Double value = null;
                        if (i == 0) {
                            value = (list.get(i + 1).getValue() + list.get(i + 2).getValue())/2;
                        } else if (i == list.size() - 1) {
                            value = (list.get(i - 1).getValue() + list.get(i - 2).getValue())/2;
                        } else {
                            value = (list.get(i + 1).getValue() + list.get(i - 1).getValue())/2;
                        }
                        list.add(i, new LineDataDTO(dateTime.toLocalDate().atTime(time), value));
                    }
                }

            });
        }
    }

    /**
     * 数据补全
     * @param list
     * @param interval 数据间隔时间 分钟
     * @return
     */
    public static List<LineDataDTO> linearInterpolation(List<LineDataDTO> list, int interval) {
        // 补全后的总长度
        int size = interval * list.size();

        for (int i = 0; i < size; i = i + interval) {
            LineDataDTO item = list.get(i);
            LocalDateTime time = item.getTime();
            if (i < size - interval){
                Double next = list.get(i + 1).getValue();
                Double current = item.getValue();
                for (int j = 1; j < interval; j++) {
                    double value = (((float) j / interval) * (next - current) + current);
//                    BigDecimal val = new BigDecimal(j).multiply(next.subtract(current)).divide(new BigDecimal(interval), 20, BigDecimal.ROUND_HALF_UP).add(current);
                    list.add(i+j, new LineDataDTO(time.plusMinutes(j), value));
                }
            } else {
                for (int j = 1; j < interval; j++) {
                    list.add(i+j, new LineDataDTO(time.plusMinutes(j), item.getValue()));
                }
            }
        }
        return list;
    }

    /**
     * 数据补全
     * @param list
     * @param interval 数据间隔时间 分钟
     * @return
     */
    public static List<OriginData> linearInterpolationOriginData(LinkedList<OriginData> list, int interval) {
        // 补全后的总长度
        int size = interval * list.size();

        for (int i = 0; i < size; i = i + interval) {
            if (i == 1437) {
                System.out.println(1);
            }
            OriginData item = list.get(i);
            Date time = item.getTime();
            if (i < size - interval){
                BigDecimal next = list.get(i + 1).getValue();
                BigDecimal current = item.getValue();
                for (int j = 1; j < interval; j++) {
//                    double value = (((float) j / interval) * (next.doubleValue() - current.doubleValue()) + current.doubleValue());
//                    BigDecimal val = new BigDecimal(value);
                    BigDecimal val = new BigDecimal(j).multiply(next.subtract(current)).divide(new BigDecimal(interval), 20, BigDecimal.ROUND_HALF_UP).add(current);
                    OriginData originData = new OriginData();
                    originData.setTime(DateUtils.addMinutes(time, j));
                    originData.setValue(val);
                    originData.setDataType(item.getDataType());
                    originData.setLineId(item.getLineId());
                    originData.setIndexCode(item.getIndexCode());
                    originData.setMainLineId(item.getMainLineId());
                    list.add(i+j, originData);
                }
            } else {
                for (int j = 1; j < interval; j++) {
                    OriginData originData = new OriginData();
                    originData.setTime(DateUtils.addMinutes(time, j));
                    originData.setValue(item.getValue());
                    originData.setDataType(item.getDataType());
                    originData.setLineId(item.getLineId());
                    originData.setIndexCode(item.getIndexCode());
                    originData.setMainLineId(item.getMainLineId());
                    list.add(i+j, originData);
                }
            }
        }
        return list;
    }

    /**
     * 线性插值
     * @param data
     * @param interval
     * @return
     */
    public static double[] linearInterpolation(double[] data, int interval) {
        //行数
        int r = data.length;
        // 补全后的总长度
        int r1 = interval * r;
        double[] newdata = new double[(r1)];
        for (int i = 0; i < r; i++) {
            if (i < r - 1) {
                newdata[(i * interval)] = data[i];
                for (int j = 1; j < interval; j++) {
                    newdata[(i * interval + j)] = (((float) j / interval) * (data[i + 1] - data[i]) + data[i]);
                }
            } else {
                for (int j = 0; j < interval; j++) {
                    newdata[(i * interval + j)] = data[i];
                }
            }
        }
        return newdata;
    }
}

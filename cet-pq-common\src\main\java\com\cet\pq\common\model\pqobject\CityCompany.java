package com.cet.pq.common.model.pqobject;

import java.util.List;

import com.cet.pq.common.model.realtime.Line;

import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/19 16:38
 */
@Data
public class CityCompany {

    private static final String modelLabel = "citycompany";

    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * ODS编码
     */
    private String code;

    /**
     * 电网代码
     */
    private String flag;

    /**
     * 全称
     */
    private String fullname;

    /**
     * 行政编码
     */
    private Long regionalcode;

    private Integer sort;

    /**
     * 更新时间
     */
    private Long updatetime;
    
    private List<Substation> substation_model;
    
    private List<Line> line_model;
    
    private List<PQTerminal> pqterminal_model;
}

package com.cet.pq.common.cca;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.stream.Stream;


/**
 * 谐波源相关系数计算
 * <AUTHOR>
 * @date 2022年10月20日 11:40
 */
@Slf4j
public class HarmonicSources extends Harmonic {
    /**
     * 计算分析的窗口数 默认一天一个窗口
     */
    protected int winNum;

    /**
     * 输出线路数
     */
    protected int outputLineNum;

    /**
     * 输入线路数
     */
    protected int inputLineNum;

    /**
     * 电压原数据
     */
    protected double[] upccArr;

    /**
     * 输入线路数据
     */
    protected double[][] outputArr;

    /**
     * 输出线路数据
     */
    protected double[][] inputArr;

    /**
     * 窗口下每条输入线路的相关系数
     */
    protected double[][] canonInputArr;

    /**
     * 窗口下每条输出线路的相关系数
     */
    protected double[][] canonOutputArr;

    /**
     * 窗口下输入线路总的相关系数
     */
    protected double[] canonAllInputArr;

    /**
     * 窗口下输出线路总的相关系数
     */
    protected double[] canonAllOutputArr;

    public double[][] getCanonInputArr() {
        return canonInputArr;
    }

    public double[][] getCanonOutputArr() {
        return canonOutputArr;
    }

    public double[] getCanonAllInputArr() {
        return canonAllInputArr;
    }

    public double[] getCanonAllOutputArr() {
        return canonAllOutputArr;
    }

    /**
     * @param upccArr 监测点  电压数据 数据已补全且绝对值
     * @param outputArr 输入数据 数据已补全且绝对值
     * @param inputArr 输出数据 数据已补全
     */
    public HarmonicSources(double[] upccArr, double[][] outputArr, double[][] inputArr) {
        this.upccArr = upccArr;
        this.outputArr = outputArr;
        this.inputArr = inputArr;
        winNum = upccArr.length/t;
        outputLineNum = (outputArr == null || outputArr.length == 0) ? 0 : outputArr[0].length;
        inputLineNum = (inputArr == null || inputArr.length == 0) ? 0 : inputArr[0].length;
    }

    public void sources() throws Exception {
        double[][] canonDataArr = new double[t][2];//计算单挑线路的相关系数所需的数据
        double[][] canonInputDataArr = inputLineNum > 0 ? new double[t][inputLineNum +  1] : null; //计算输入线路的相关系数所需的数据
        double[][] canonOutputDataArr = outputLineNum > 0 ? new double[t][outputLineNum + 1] : null; //计算输出线路的相关系数所需的数据
        canonInputArr = new double[winNum][inputLineNum];
        canonOutputArr = new double[winNum][outputLineNum];
        canonAllInputArr = new double[winNum];
        canonAllOutputArr = new double[winNum];
        //按窗口计算
        Stream.iterate(0, i -> i + 1).limit(winNum).forEach(i ->{
            // 截取窗口内的电压数据
            double[] winUpccArr = Arrays.stream(upccArr).skip(((long) (i * t))).limit(t).map(v -> Math.abs(v)).toArray();

            //计算输入线路
            if (inputArr != null) {
                Stream.iterate(0, j -> j + 1).limit(inputLineNum).forEach(j ->{
                    // 从inputArr的i*t处截取t长度的数组，遍历取二维数组的j位置的值重新组成数据  得到输入线路窗口内第J条线路的数据
                    double[] winInputArr = Arrays.stream(inputArr).skip(((long) (i * t))).limit(t).mapToDouble(item -> Math.abs(item[j])).toArray();
                    Stream.iterate(0, k -> k + 1).limit(t).forEach(k ->{
                        canonDataArr[k][0] = winUpccArr[k];
                        canonDataArr[k][1] = winInputArr[k];

                        canonInputDataArr[k][0] = winUpccArr[k];
                        canonInputDataArr[k][j+1] = winInputArr[k];
                    });
                    // 计算定位源相关系数
                    try {
                        double canoncorr = Canoncorr.canoncorr(canonDataArr, canonDataArr.length, canonDataArr[0].length);
                        canonInputArr[i][j] =  Double.isNaN(canoncorr) ? 0 : canoncorr;
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            }

            //计算输出线路
            if (outputArr != null) {
                Stream.iterate(0, j -> j + 1).limit(outputLineNum).forEach(j ->{
                    double[] winOutputArr = Arrays.stream(outputArr).skip(((long) (i * t))).limit(t).mapToDouble(item -> item[j]).toArray();
                    Stream.iterate(0, k -> k + 1).limit(t).forEach(k ->{
                        canonDataArr[k][0] = winUpccArr[k];
                        canonDataArr[k][1] = winOutputArr[k];

                        canonOutputDataArr[k][0] = winUpccArr[k];
                        canonOutputDataArr[k][j+1] = winOutputArr[k];
                    });
                    // 计算定位源相关系数
                    try {
                        double canoncorr = Canoncorr.canoncorr(canonDataArr, canonDataArr.length, canonDataArr[0].length);
                        canonOutputArr[i][j] = Double.isNaN(canoncorr) ? 0 : canoncorr;
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            try {
                if (canonInputDataArr != null) {
                    double canoncorr = Canoncorr.canoncorr(canonInputDataArr, canonInputDataArr.length, canonInputDataArr[0].length);
                    canonAllInputArr[i] = Double.isNaN(canoncorr) ? 0 : canoncorr;
                }
                if (canonOutputDataArr != null) {
                    double canoncorr = Canoncorr.canoncorr(canonOutputDataArr, canonOutputDataArr.length, canonOutputDataArr[0].length);
                    canonAllOutputArr[i] = Double.isNaN(canoncorr) ? 0 : canoncorr;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
}

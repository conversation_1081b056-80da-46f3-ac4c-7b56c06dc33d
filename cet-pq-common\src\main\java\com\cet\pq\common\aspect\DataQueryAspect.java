package com.cet.pq.common.aspect;

import com.cet.pq.common.constant.AggregationType;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.feign.ModelDataService;
import com.cet.pq.common.mapper.PD_TB_PQ_05Mapper;
import com.cet.pq.common.model.*;
import com.cet.pq.common.time.DateUtils;
import com.cet.pq.common.utils.JsonTransferUtils;
import com.cet.pq.common.utils.ParseDataUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 模型服务查询pqquantityaggdata时切入
 * 当查询时间在系统开始时间点之前则使用3.8数据库直接查询后将数据转换成4.0系统的数据格式
 * 当查询时间在系统开始时间点之后则直接调用模型服务查询
 * 当查询的时间跨度分布在系统开始时间点两侧则同时查询模型服务与3.8数据库，之后将3.8数据库中的数据查出来后转换为4.0系统的数据格式并与模型服务查询的数据进行合并，并去重
 * @date 2021/3/22 18:49
 */
@Aspect
@Component("dataQueryAspect")
@ConditionalOnProperty(prefix = "compatibleTablePD-TB-PQ-05", name = "enable", havingValue = "true")
public class DataQueryAspect {

    @Autowired
    private PD_TB_PQ_05Mapper mapper;
    @Autowired
    private ModelDataService modelDataService;

    private static final Logger logger = LoggerFactory.getLogger(DataQueryAspect.class);
    private static Long timePoint = 0L;

    private static final Double invalid = -2147483648.0;

    private void init() {
        QueryCondition queryCondition = new QueryCondition(TableName.SYSVERSIONUPDATEINFO,new ConditionBlock("version", ConditionBlock.OPERATOR_EQ, "4.0"));
        List<Map<String, Object>> result = modelDataService.query(queryCondition).getData();
        if (CollectionUtils.isNotEmpty(result)) {
            timePoint = ParseDataUtil.parseLong(result.get(0).get("datatime"));
        }
        logger.info("兼容3.8系统数据库表PD_TB_PQ_05_配置已生效,4.0系统数据开始时间：" + timePoint);
    }

    /**
     * 只针对模型服务查询QuantityAggData接口做切点
     */
    @Pointcut("execution(* com.cet.pq.common.feign.ModelDataService.queryQuantityAggData(..))")
    public void queryPointcut() {
    }

    /**
     * 对查询条件进一步判断
     *
     * @param joinPoint 切点
     * @return
     * @throws Throwable
     */
    @Around("queryPointcut()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        QueryCondition queryCondition = (QueryCondition) joinPoint.getArgs()[0];
        return getQuantityAggData(queryCondition, joinPoint);

    }

    /**
     * 进一步判断查询模型服务还是查询3.8数据库
     *
     * @param joinPoint 切点
     * @return 查询结果
     * @throws Throwable
     */
    private Object getQuantityAggData(QueryCondition queryCondition, ProceedingJoinPoint joinPoint) throws Throwable {
        List<ConditionBlock> expressions = queryCondition.getRootCondition().getFilter().getExpressions();
        List<ConditionBlock> conditionBlocks = new ArrayList<>(expressions);
        //得到查询的时间段
        List<Long> time = new ArrayList<>();
        //需要查询的聚合类型
        List<Integer> aggregationtype = new ArrayList<>();
        Iterator<ConditionBlock> iterator = conditionBlocks.iterator();
        while (iterator.hasNext()) {
            ConditionBlock next = iterator.next();
            if ("logtime".equals(next.getProp())) {
                time.addAll(objectToList(next.getLimit()));
            }
            if ("aggregationtype".equals(next.getProp())) {
                aggregationtype.addAll(objectToList(next.getLimit()));
                iterator.remove();
            }
            if ("value".equals(next.getProp())) {
                iterator.remove();
            }
        }
        init();
        Boolean timePointBefore = time.stream().allMatch(t -> t <= timePoint);
        Boolean timePointAfter = time.stream().allMatch(t -> t >= timePoint);
        if (timePointBefore) {
            //时间点之前查询3.8数据库;
            List<PD_TB_PQ_05> pqQuantityAggData = mapper.getPqQuantityAggData(conditionBlocks);
            List<PQQuantityAggData> result = pdToQuantityAggData(pqQuantityAggData);
            result = result.stream().filter(r -> aggregationtype.contains(r.getAggregationtype())).collect(Collectors.toList());
            return new ResultWithTotal<>(0, "", result, result.size());
        }
        if (timePointAfter) {
            //时间点之后查询模型服务
            return joinPoint.proceed();
        }
        //同时查询模型服务与3.8数据库，之后将3.8数据库中的数据查出来后转换为4.0系统的数据格式并与模型服务查询的数据进行合并，并去重
        List<PQQuantityAggData> mapperPqQuantityAggData = pdToQuantityAggData(mapper.getPqQuantityAggData(conditionBlocks));
        ResultWithTotal<List<PQQuantityAggData>> modelDataResult = (ResultWithTotal<List<PQQuantityAggData>>) joinPoint.proceed();
        List<PQQuantityAggData> modelPqQuantityAggData = new ArrayList<>(JsonTransferUtils.transferList(modelDataResult.getData(), PQQuantityAggData.class));
        List<PQQuantityAggData> pqQuantityAggData = pqQuantityAggDataMerge(mapperPqQuantityAggData, modelPqQuantityAggData);
        pqQuantityAggData = pqQuantityAggData.stream().filter(r -> aggregationtype.contains(r.getAggregationtype())).collect(Collectors.toList());
        return new ResultWithTotal<>(0, "", pqQuantityAggData, pqQuantityAggData.size());
    }

    /**
     * 对象转成列表
     * @param object
     * @return
     */
    private <T> List<T> objectToList(Object object){
        if (object instanceof List){
            return new ArrayList<T>((List)object);
        }
        if (object == null){
            return Collections.emptyList();
        }
        return Collections.singletonList((T)object);
    }

    /**
     * 3.8数据库查询结果转换为4.0的数据格式
     *
     * @param dataList 待转换的数据
     * @return
     */
    private List<PQQuantityAggData> pdToQuantityAggData(List<PD_TB_PQ_05> dataList) {
        List<PQQuantityAggData> pqQuantityAggDataList = new ArrayList<>();
        dataList.forEach(data -> {
            Integer aggregationCycle = timeTypeToAgg(data.getTimetype());
            Integer paraId = data.getParaid();
            Long pqNodeId = data.getPqnodeid();
            Long recordTime = null;
            recordTime = DateUtils.dateToTimeStamp(DateUtils.stringToDate(data.getRecordtime(), "yyyy-MM-dd HH:mm:ss"));
            Double avgValue = data.getAvgvalue();
            Double maxvalue = data.getMaxvalue();
            Double minvalue = data.getMinvalue();
            Double pro95value = data.getPro95value();
            if(avgValue>invalid){
                pqQuantityAggDataList.add(new PQQuantityAggData(aggregationCycle, AggregationType.ARITHMETIC_AVG, paraId, pqNodeId, recordTime, paraId, avgValue));
            }
            if (maxvalue>invalid){
                pqQuantityAggDataList.add(new PQQuantityAggData(aggregationCycle, AggregationType.MAX, paraId, pqNodeId, recordTime, paraId, maxvalue));
            }
            if(minvalue>invalid){
                pqQuantityAggDataList.add(new PQQuantityAggData(aggregationCycle, AggregationType.MIN, paraId, pqNodeId, recordTime, paraId, minvalue));
            }
            if (pro95value>invalid){
                pqQuantityAggDataList.add(new PQQuantityAggData(aggregationCycle, AggregationType.PRO_95, paraId, pqNodeId, recordTime, paraId, pro95value));
            }
        });
        return pqQuantityAggDataList;
    }

    /**
     * 3.8数据库与4.0系统聚合周期对应关系，将3.8系统中的timeType转化为4.0系统中的aggregationCycle
     *
     * @param timeType
     * @return
     */
    private Integer timeTypeToAgg(Integer timeType) {
        switch (timeType) {
            case 1:
                return 12;
            case 2:
                return 13;
            case 3:
                return 14;
            case 4:
                return 15;
            case 5:
                return 17;
            default:
                throw new CommonManagerException("timeType无效：" + timeType);
        }
    }

    /**
     * 同时查询模型服务与3.8数据库时考虑到有冗余数据去重，并将查询结果合并
     *
     * @param pqQuantityAggData1
     * @param pqQuantityAggData2
     * @return
     */
    private List<PQQuantityAggData> pqQuantityAggDataMerge(List<PQQuantityAggData> pqQuantityAggData1, List<PQQuantityAggData> pqQuantityAggData2) {
        Set<PQQuantityAggData> pqQuantityAggData = new TreeSet<>((o1, o2) -> {
            if (o1.getAggregationcycle().equals(o2.getAggregationcycle())
                    && o1.getAggregationtype().equals(o2.getAggregationtype())
                    && o1.getDataid().equals(o2.getDataid())
                    && o1.getLine_id().equals(o2.getLine_id())
                    && o1.getLogtime().equals(o2.getLogtime())) {
                return 0;
            } else {
                return 1;
            }
        });
        pqQuantityAggData.addAll(pqQuantityAggData1);
        pqQuantityAggData.addAll(pqQuantityAggData2);
        return new ArrayList<>(pqQuantityAggData);
    }
}

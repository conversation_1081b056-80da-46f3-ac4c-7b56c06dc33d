package com.cet.pq.anlysis.common.task;

import com.cet.pq.anlysis.model.monitordatastatistics.DimensionDataDetail;
import com.cet.pq.anlysis.model.monitordatastatistics.PQDataIntegrity;
import com.cet.pq.anlysis.model.monitordatastatistics.PQHistoryStatus;
import com.cet.pq.anlysis.service.MonitorDataIndexService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 异步任务
 * 
 * <AUTHOR>
 */
@Component
@EnableAsync
@Lazy
public class MonitorDataAsyncTask {

	private static final Logger log = LoggerFactory.getLogger(MonitorDataAsyncTask.class);

	@Autowired
	private MonitorDataIndexService monitorDataIndexService;

	/**
	 * 查询历史状态任务
	 * @param starttime
	 * @param endtime
	 * @param aggregationcycle
	 * @param idList
	 * @return
	 */
	@Async(value = "threadPoolExecutor")
	public CompletableFuture<Object> getAllHistoryStatusList(Long starttime, Long endtime, Integer aggregationcycle, List<Long> idList) {
		Object pqHistoryStatusList = new ArrayList<>();
		try {
			pqHistoryStatusList = monitorDataIndexService.getAllHistoryStatusList(starttime, endtime, aggregationcycle, idList);
			log.debug(Thread.currentThread().getName() + "查询监测点历史数据子线程开始执行.............................................................................");
		} catch (Exception e) {
			return AsyncResult.forExecutionException(e).completable();
		}
		return AsyncResult.forValue(pqHistoryStatusList).completable();
	}
	
	/**
	 * 查询监测数据任务
	 * @param starttime
	 * @param endtime
	 * @param aggregationcycle
	 * @param idList
	 * @return
	 */
	@Async(value = "threadPoolExecutor")
	public CompletableFuture<Object> getMonitorDataDetailByLineIdList(Long id, String dimension, String modelLabel, List<Long> lineIdList, List<PQHistoryStatus> allHistoryStatusList,
                                                                      List<PQDataIntegrity> allDataIntegrityList, List<Integer> onRunStatusValueList, List<Integer> onLineStatusValueList) {
		Object detail = new DimensionDataDetail();
		try {
			detail = monitorDataIndexService.getMonitorDataDetailByLineIdList(id, dimension, modelLabel, lineIdList, allHistoryStatusList, allDataIntegrityList, onRunStatusValueList,onLineStatusValueList);
			log.debug(Thread.currentThread().getName() + "完整率计算子线程开始执行.............................................................................");
		} catch (Exception e) {
			return AsyncResult.forExecutionException(e).completable();
		}
		return AsyncResult.forValue(detail).completable();
	}
	
	/**
	 * 查询监测数据任务
	 * @param starttime
	 * @param endtime
	 * @param aggregationcycle
	 * @param idList
	 * @return
	 */
	@Async(value = "threadPoolExecutor")
	public CompletableFuture<Object> getAllIntegrityList(Long starttime, Long endtime, Integer aggregationcycle, List<Long> idList) {
		Object pqHistoryStatusList = new ArrayList<>();
		try {
			pqHistoryStatusList = monitorDataIndexService.getAllIntegrityList(starttime, endtime, aggregationcycle, idList);
			log.debug(Thread.currentThread().getName() + "查询监测点数据子线程开始执行.............................................................................");
		} catch (Exception e) {
			return AsyncResult.forExecutionException(e).completable();
		}
		return AsyncResult.forValue(pqHistoryStatusList).completable();
	}

}

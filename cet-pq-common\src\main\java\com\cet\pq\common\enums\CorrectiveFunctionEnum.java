package com.cet.pq.common.enums;

/**
 * <AUTHOR>
 * @date 2022/05/26 15:35
 */
public enum CorrectiveFunctionEnum {

    /**
     * 无
     */
    NONE("无", 0),
    /**
     * 有
     */
    HAVE("有", 1);

    private final String key;
    private final Integer id;

    CorrectiveFunctionEnum(String key, int value) {
        this.key = key;
        this.id = value;
    }

    public static String getCorrectiveFunctionEnumKeyByValue(Integer value){
        if (value == null) {
            return NONE.getKey();
        }
        for(CorrectiveFunctionEnum correctiveFunctionEnum : values()){
            if(correctiveFunctionEnum.getId().equals(value)){
                return correctiveFunctionEnum.getKey();
            }
        }
        return "";
    }

    public Integer getId() { return id; }

    public String getKey() {
        return key;
    }

}

package com.cet.pq.common.encode;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date: 2020/4/16 15:48
 */
public class CipherUtil {
	private static Logger logger = LoggerFactory.getLogger(CipherUtil.class);

	private static String key = "";

	private static byte[] vector = new byte[] {};

	private static String login_key = "8924534290ABCDEF1264147890ACAB56";

	private static byte[] login_vector = new byte[] { 0x29, 0x34, 0x57, 0x72, (byte) 0x90, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF,
			0x12, 0x64, 0x14, 0x78, (byte) 0x90, (byte) 0xAC, (byte) 0xAE, 0x75 };

	private static String dataBase_key = "8914534290ABCDEF1264147890ACAB55";

	private static byte[] dataBase_vector = new byte[] { 0x19, 0x34, 0x57, 0x72, (byte) 0x90, (byte) 0xAB, (byte) 0xCD, (byte) 0xEF,
			0x12, 0x64, 0x14, 0x78, (byte) 0x90, (byte) 0xAC, (byte) 0xAE, 0x45 };

	static {
		Security.addProvider(new BouncyCastleProvider());
	}

	public static String encryptLoginInfo(String info){
		return doEncrypt(login_key, login_vector, info);
	}

	public static String doEncrypt(String tKey, byte[] tVector, String pwd){
		try {
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");

			byte[] dataBytes = pwd.getBytes("utf-8");
			SecretKeySpec secretKeySpec = new SecretKeySpec(tKey.getBytes("utf-8"), "AES");
			IvParameterSpec ivParameterSpec = new IvParameterSpec(tVector);
			cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

			byte[] original = cipher.doFinal(dataBytes);
			String base64Str = Base64.getEncoder().encodeToString(original);
			return base64Str.replace("\n", "");
		} catch (Exception e){
			logger.error("encrypt error: e", e.getMessage());
			return null;
		}
	}

	public static String encrypt(String pwd) {
		try {
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");

			byte[] dataBytes = pwd.getBytes("utf-8");
			SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes("utf-8"), "AES");
			IvParameterSpec ivParameterSpec = new IvParameterSpec(vector);
			cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

			byte[] original = cipher.doFinal(dataBytes);
			String base64Str = Base64.getEncoder().encodeToString(original);
			return base64Str.replace("\n", "");
		} catch (Exception e) {
			logger.error("encrypt error: e", e.getMessage());
			return null;
		}
	}

	public static String decrypt(String pwd) {
		return _decrypt(key, vector, pwd);
	}

	public static String decryptLoginInfo(String info) {
		return _decrypt(login_key, login_vector, info);
	}

	public static String _decrypt(String tKey, byte[] tVector, String info) {
		try {
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
			byte[] pwdBytes = Base64.getDecoder().decode(info);
			SecretKeySpec secretKeySpec = new SecretKeySpec(tKey.getBytes("utf-8"), "AES");
			IvParameterSpec ivParameterSpec = new IvParameterSpec(tVector);
			cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
			byte[] original = cipher.doFinal(pwdBytes);
			return new String(original, "UTF-8");
		} catch (Exception e) {
			logger.error("decrypt error: {}", e.getMessage());
			return null;
		}
	}

	public static String md5(String str) {
		if (str == null || str.length() == 0) {
			throw new IllegalArgumentException("String to encript cannot be null or zero length");
		}
		StringBuffer hexString = new StringBuffer();
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(str.getBytes());
			byte[] hash = md.digest();
			for (int i = 0; i < hash.length; i++) {
				if ((0xff & hash[i]) < 0x10) {
					hexString.append("0" + Integer.toHexString((0xFF & hash[i])));
				} else {
					hexString.append(Integer.toHexString(0xFF & hash[i]));
				}
			}
		} catch (NoSuchAlgorithmException e) {
			return null;
		}
		return hexString.toString();
	}

}

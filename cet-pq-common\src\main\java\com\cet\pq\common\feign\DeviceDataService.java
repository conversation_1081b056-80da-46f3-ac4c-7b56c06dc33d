package com.cet.pq.common.feign;

import com.cet.pq.common.model.DatalogMeasItemDTO;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.ResultWithTotal;
import com.cet.pq.common.model.datalog.TrendDataVo;
import com.cet.pq.common.model.datalog.TrendSearchListVo;
import com.cet.pq.common.model.datalog.TrendSearchSecondListVo;
import com.cet.pq.common.model.event.EventCondition;
import com.cet.pq.common.model.event.EventLogVo;
import com.cet.pq.common.model.event.WaveLog;
import com.cet.pq.common.model.peccore.DeviceNodeInfo;
import com.cet.pq.common.model.realtime.RealTimeValue;
import com.cet.pq.common.model.realtime.RltRowDataIDLogicalIndex;
import com.cet.pq.common.model.wave.Wave;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 设备数据服务
 * @Data Created in ${Date}
 */
@FeignClient(value = "device-data-service")
public interface DeviceDataService {
	/**
	 * 获取指定meterId和dataId的实时数据，无效值统一返回null
	 *
	 * @param dataId
	 * @param logicalId
	 * @param deviceIds
	 * @return
	 */
	@PostMapping("/api/v1/realtimedata/{dataId}/{logicalId}")
	Result<Object> realTimeData(@PathVariable("dataId") Integer dataId, @PathVariable("logicalId") Integer logicalId,
			@RequestBody List<Long> deviceIds);

	@PostMapping("/api/v1/highspeed/datalog/span")
	ResultWithTotal<Object> highSpeedData(@RequestBody TrendSearchListVo searchVo);

	/**
	 * 查询系统事件数据
	 *
	 * @param index
	 * @param limit
	 * @param startTime
	 * @param endTime
	 * @param condition
	 * @return
	 */
	@PostMapping("/api/event/v1/data")
	public ResultWithTotal<List<EventLogVo>> queryEventData(@RequestParam("index") Integer index,
			@RequestParam("limit") Integer limit, @RequestParam("startTime") Long startTime,
			@RequestParam("endTime") Long endTime, EventCondition condition);

	/**
	 * 查询系统事件数据
	 *
	 * @param queryParam
	 * @param isPrecise
	 * @param offset
	 * @param index
	 * @param limit
	 * @param startTime
	 * @param endTime
	 * @param maxEventTime
	 * @return
	 */
	@PostMapping("/api/event/v1/data/matchwave")
	ResultWithTotal<List<EventLogVo>> queryEventDataWithWave(@RequestBody EventCondition queryParam,
			@RequestParam("isPrecise") Boolean isPrecise, @RequestParam("offset") Integer offset,
			@RequestParam("index") Integer index, @RequestParam("limit") Integer limit,
			@RequestParam("startTime") Long startTime, @RequestParam("endTime") Long endTime,
			@RequestParam("maxEventTime") Long maxEventTime);

	/**
	 * 统计事件条数
	 *
	 * @param queryParam
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@PostMapping("/api/event/v1/count")
	public Result<Integer> queryEventDataCount(@RequestBody EventCondition queryParam,
			@RequestParam("startTime") Long startTime, @RequestParam("endTime") Long endTime);

	/**
	 * 获取所有的场站信息
	 *
	 * @return
	 */
	@GetMapping("/api/comm/v1/stations")
	public Result<List<DeviceNodeInfo>> queryAllStations();

	/**
	 * 根据场站获取通道信息
	 *
	 * @param stationId
	 * @return
	 */
	@GetMapping("/api/comm/v1/station/channels")
	public Result<List<DeviceNodeInfo>> queryChannels(@RequestParam("stationId") long stationId);

	/**
	 * 根据通道获取设备信息
	 *
	 * @param channelId
	 * @return
	 */
	@GetMapping("/api/comm/v1/channel/devices")
	public Result<List<DeviceNodeInfo>> queryDevices(@RequestParam("channelId") long channelId);

	/**
	 * 根据dataid批量获取数据
	 *
	 * @param deviceId
	 * @param dataIdLogicalIdList
	 * @return
	 */
	@PostMapping("/api/v1/realtimedata/{deviceId}")
	public Result<List<RealTimeValue>> queryRealtimedataBatch(@RequestParam("deviceId") Long deviceId,
			@RequestBody List<RltRowDataIDLogicalIndex> dataIdLogicalIdList);

	/**
	 * 查询定时记录
	 *
	 * @param searchVo
	 * @return
	 */
	@PostMapping("/api/v1/batch/datalog/span/group")
	public Result<List<TrendDataVo>> queryTrendCurveData2(@RequestBody TrendSearchListVo searchVo, @RequestParam("fill") Boolean fill);

	/**
	 * 查询秒级定时记录
	 *
	 * @param searchVo
	 * @return
	 */
	@PostMapping("/api/v1/second/datalog")
	public Result<List<TrendDataVo>> querySecondDataLog(@RequestBody TrendSearchSecondListVo searchVo);


	/**
	 * 查询设备信息
	 *
	 * @param deviceIds
	 * @return
	 */
	@PostMapping("/api/comm/v1/device/infos")
	public Result<List<DeviceNodeInfo>> queryDeviceInfos(@RequestBody List<Long> deviceIds);

	/**
	 * 获取指定meterId和dataId的实时数据
	 *
	 * @param deviceIds
	 * @param dataId
	 * @param logicalId
	 * @return
	 */
	@PostMapping("/api/v1/realtimedata/{dataId}/{logicalId}")
	public ResultWithTotal<List<RealTimeValue>> queryRealtimedata(List<Long> deviceIds,
			@PathVariable(value = "dataId") Long dataId, @PathVariable(value = "logicalId") Integer logicalId);

	/**
	 * 查询指定范围内的事件，仅支持分页查询
	 *
	 * @param startId
	 * @param endId
	 * @param index
	 * @param limit
	 * @param year
	 * @param condition
	 * @return
	 */
	@PostMapping("/api/event/v1/data/idrange/{year}")
	public ResultWithTotal<List<EventLogVo>> queryEventById(@RequestParam("startId") Long startId,
			@RequestParam("endId") Long endId, @RequestParam("index") Integer index,
			@RequestParam("limit") Integer limit, @PathVariable("year") Integer year, EventCondition condition);

	/**
	 * 查询指定范围内的波形，仅支持分页查询
	 *
	 * @param startId
	 * @param endId
	 * @param index
	 * @param limit
	 * @param includeData
	 * @return
	 */
	@GetMapping("/api/wave/v1/data/idrange")
	public ResultWithTotal<List<WaveLog>> queryWaveById(@RequestParam("startId") Long startId,
			@RequestParam("endId") Long endId, @RequestParam("index") Integer index,
			@RequestParam("limit") Integer limit, @RequestParam("includeData") Boolean includeData);

	/**
	 * 查询指定设备id，制定触发时间的波形
	 *
	 * @param deviceId
	 * @param waveTime
	 * @return
	 */
	@GetMapping("/api/wave/v1/data/moment/{deviceId}")
	ResultWithTotal<List<String>> queryWaveByDeviceId(@RequestParam("deviceId") Long deviceId, @RequestParam("waveTime") Long waveTime);

	/**
	 * 查询所有的设备
	 *
	 * @return
	 */
	@GetMapping("/api/comm/v1/devices")
	Result<List<DeviceNodeInfo>> queryAllDevices();

	@GetMapping("/api/wave/v1/comtrade/moment/{deviceId}")
	Result<Wave> getWave(@PathVariable("deviceId") Long deviceId,@RequestParam("waveTime") Long waveTime);

	/**
	 *
	 * 获取设备节点最新数据时标，包含数据库和缓存中的时标。不支持P35下poi的查询
	 *
	 * @return
	 */
	@PostMapping("/api/v1/datalog/pois")
	Result<List<Map<String, Object>>> queryLinePois(List<Long> deviceIdList);

	/**
	 * 根据参数号进行遥控
	 *
	 * @return
	 */
	@PostMapping("/api/v1/remote/control")
	Result<Map<String, Object>> control(Map<String,Object> obj);

	/**
	 * 查询遥控点
	 **/
	@PostMapping("/api/comm/v1/control/points")
	Result<List<Map<String, Object>>> points(Object[] obj);

	/**
	 * 获取所有通道信息
	 */
	@GetMapping("/api/comm/v1/channels")
	Result<List<DeviceNodeInfo>> queryAllChannels();

	/**
	 * 获取设备的定时记录测点列表
	 **/
	@PostMapping("/api/comm/v1/device/datalog/points")
	Result<Map<Long, List<DatalogMeasItemDTO>>> getDeviceDatalogPoints(List<Long> deviceIdList);

}

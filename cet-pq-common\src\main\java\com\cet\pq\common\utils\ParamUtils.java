package com.cet.pq.common.utils;


import com.cet.pq.common.ErrorCode;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.objective.physicalquantity.AggregationCycle;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName ParamUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/3/2 16:04
 */
public class ParamUtils {
    private static final Logger logger = LoggerFactory.getLogger(ParamUtils.class);

    public static Integer getNextCycle(int cycle) {
        if (cycle == AggregationCycle.ONE_YEAR) {
            return AggregationCycle.ONE_MONTH;
        } else if (cycle == AggregationCycle.ONE_MONTH) {
            return AggregationCycle.ONE_DAY;
        } else if (cycle == AggregationCycle.ONE_DAY) {
            return AggregationCycle.ONE_HOUR;
        }

        return 0;
    }

    /**
     * 检查返回结果
     *
     * @param result
     * @throws RuntimeException
     */
    public static <T> void checkResultGeneric(Result<T> result) {
        if (Objects.isNull(result)) {
            throw new CommonManagerException("调用接口异常");
        }
        if (ErrorCode.SUCCESS_CODE != result.getCode()) {
            logger.error(result.getMsg());
            throw new CommonManagerException("调用接口异常");
        }
    }

    /**
     * 检查返回结果
     *
     * @param result
     * @throws RuntimeException
     */
    public static <T, K> void checkResultGeneric(Result<T> result, K obj) {
        if (Objects.isNull(result)) {
            throw new CommonManagerException("调用接口异常");
        }
        if (ErrorCode.SUCCESS_CODE != result.getCode()) {
            logger.error(result.getMsg());
            if (obj != null) {
                logger.error(JsonTransferUtils.toJsonString(obj));
            }
            throw new CommonManagerException("调用接口异常");
        } else {
            logger.debug(JsonTransferUtils.toJsonString(obj));
        }
    }

    /**
     * 是否含有sql注入，返回true表示含有
     * @param obj
     * @return
     */
    public static boolean containsSqlInjection(Object obj){
        Pattern pattern= Pattern.compile("\\b(and|exec|insert|select|drop|grant|alter|delete|update|count|chr|mid|master|truncate|char|declare|or)\\b|(\\*|;|\\+|'|%)");
        Matcher matcher=pattern.matcher(obj.toString().toLowerCase());
        return matcher.find();
    }
}

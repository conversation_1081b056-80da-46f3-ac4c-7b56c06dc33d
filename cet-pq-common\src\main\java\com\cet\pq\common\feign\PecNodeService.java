package com.cet.pq.common.feign;

import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.pecnode.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * 设备节点服务
 * <AUTHOR>
 */
@FeignClient(value = "pec-node-service")
public interface PecNodeService {
     /**
      * 返回所有的变电站设备
      * @return
      */
     @GetMapping(value = {"/api/pecnode/v1/stations"})
     Result<List<StationAndChannel>> getStations();


     /**
      * 返回所有的变电站设备
      * @return
      */
     @GetMapping(value = {"/api/pecnode/v1/stations?includeInf=true"})
     Result<List<StationAndChannel>> getStationsAndInf();


     /**
      *
      * 批量获取通道下设备节点数据
      * @return
      */
     @GetMapping(value = {"/api/pecnode/v1/channel/devices"})
     Result<List<Device>> getDevices(@RequestBody List<Long> channelIds );

     /**
      *
      * 查询所有设备节点id
      * @return
      */
     @GetMapping(value = {"/api/pecnode/v1/device/ids"})
     Result<List<Long>> getDeviceIds();

     /**
      * 新建设备节点
      * @param deviceNode
      * @return
      */
     @PostMapping(value = {"/api/pecnode/v1/access/devices"})
     Result<Object> addDevices(@RequestBody List<DeviceNode> deviceNode);

     /**
      * 新建设备节点
      * @param deviceNode
      * @return
      */
     @PutMapping(value = {"/api/pecnode/v1/access/devices"})
     Result<Object> updateDevices(@RequestBody List<DeviceNode> deviceNode);

     /**
      *
      * 批量更新通道节点
      * @param channelNode
      * @return
      */
     @PutMapping(value = {"/api/pecnode/v1/channels"})
     Result<Object> updateChannels(@RequestBody List<ChannelNode> channelNode);


     /**
      * 添加通道信息
      * @param id
      * @param channelParamList
      * @return
      */
     @PostMapping(value = {"/api/pecnode/v1/station/{id}/channels"})
     Result<Object> addChannels(@PathVariable(value="id") Long id, @RequestBody List<ChannelParam> channelParamList );

     /**
      * 获得指定类型的节点信息和节点
      * @param nodeType 节点类型
      * @param nodeId   节点ID
      * @return
      */
     @GetMapping(value = {"/api/pecnode/v1/{nodeType}/{nodeId}/sysnode"})
     Result<SystemNode> getSpecifiedNode(@PathVariable(value = "nodeType") Long nodeType, @PathVariable(value = "nodeId") Long nodeId);

     /**
      *获取通道下设备节点数据
      * @param id
      * @return
      */
     @GetMapping(value = {"/api/pecnode/v1/channel/{id}/devices"})
     Result<List<Device>> getDevicesByChannelId(@PathVariable(value = "id") Long id);
     /**
      * 删除设备
      * @param ids
      * @return
      */
     @DeleteMapping(value = {"/api/pecnode/v1/devices"})
     Result<Object> deleteDevice(@RequestBody List<Long> ids);
     /**
      * 获取所有的驱动类型
      * @return
      */
     @GetMapping(value={"/api/pecnode/v1/drvtypes"})
     Result<List<SystemNode>> getDrvTypes();

     /**
      * 按照节点id列表获取设备节点,一次最大允许请求200个设备节点数据
      */
     @PostMapping(value = {"/api/pecnode/v1/device/nodes"})
     Result<List<Device>> getNodesByNodeIdList(@RequestBody Collection<Long> deviceIdList);

     /**
      * 获取所有的设备类型
      * @return
      */
     @GetMapping(value={"/api/pecnode/v1/devicetypes"})
     Result<List<Device>> getDeviceTypes();

     /**
      * 获取所有的设备类型
      * @return
      */
     @GetMapping(value={"/api/pecnode/v1/datalogmaps"})
     Result<List<DatalogMap>> getDatalogMaps();

     /**
      * 获取系统所有的厂站节点
      * @return
      */
     @GetMapping(value={"/api/pecnode/v1/stationnodes"})
     Result<List<Device>> getAllStations();


     /**
      * 根据设备ID查询设备INF参数,返回全量inf字符串信息
      * @return
      */
     @PostMapping(value={"/api/pecnode/v1/devices/infString"})
     Result<List<DeviceInf>> getDeviceInfString(@RequestBody List<Long> deviceIdList);

     /**
      * 根据通道ID查询设备INF参数,返回全量inf字符串信息
      * @return
      */
     @PostMapping(value={"/api/pecnode/v1/channel/infString"})
     Result<List<DeviceInf>> getChannelInfString(@RequestBody List<Long> channelIdList);

}

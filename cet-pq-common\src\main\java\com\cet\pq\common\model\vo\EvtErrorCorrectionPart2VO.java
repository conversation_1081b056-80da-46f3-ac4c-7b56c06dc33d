package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 10:01
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class EvtErrorCorrectionPart2VO {

    /**
     * 数值
     */
    private Double value;

    /**
     * 类型：0：A，1：B，2：C，3：负序，4：零序
     */
    private Integer type;

    /**
     * 值类型：0：测量值，1：电流换算值
     */
    private Integer valueType;

    /**
     * 判断结果
     */
    private String result;

}

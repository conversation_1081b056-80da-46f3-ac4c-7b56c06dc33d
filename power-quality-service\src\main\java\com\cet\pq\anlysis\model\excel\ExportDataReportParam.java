package com.cet.pq.anlysis.model.excel;

import com.cet.pq.common.model.excel.ExportParameter;
import com.cet.pq.anlysis.model.common.CommonParam;
import com.cet.pq.anlysis.model.common.DataReportParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/5 10:54
 */
@Data
public class ExportDataReportParam extends CommonParam {
    @NotNull(message = "lineId不能为空")
    private Long lineId;
    private List<DataReportParam.QuantityTemplate> quantityTemplates;
    // 导出相关参数
    private ExportParameter exportParameter;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class QuantityTemplate {
        private List<Long> dataIds;
        private String name;
    }
}

package com.cet.pq.common.model.wave;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 通道详细信息
 * @date 2021/6/17 9:52
 */
@Data
public class ChannelDetailInfo {
    /**
     * 通道类型 A或者D
     */
    private String channelType;

    /**
     * 通道名称 ch_id
     */
    private String channelName;

    /**
     * 通道相别标识 ph
     */
    private String phaseType;

    /**
     * 被监视的电路组件 ccbm
     */
    private String ccbm;

    /**
     * 通道单位 uu  开关量通道不存在
     */
    private String channelUnit;

    /**
     * 通道系数 A 必填；实数；1～32个字符宽度；可使用标准浮点记法
     */
    private float channelCof ;

    /**
     * 通道偏移量 B 必填；实数；1～32个字符宽度；可使用标准浮点数记法 （通道转换公式为ax+b， x取自.DAT文件， 单位就是uu）
     */
    private float channelOffset;

    /**
     * 变比值 primary/secondary
     */
    private float channelTransRatio;

    /**
     * 一次值或二次值标识 PS
     */
    private String channelPs;

    /**
     * 通道在cfg文件的位置
     */
    private int channelIndex;

    /**
     * 通道数据
     */
    private List<KeyValuePair> channelData = new ArrayList<>();
}

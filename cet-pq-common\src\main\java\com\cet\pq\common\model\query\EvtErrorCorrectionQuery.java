package com.cet.pq.common.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/05/23 19:47
 */
@ApiModel(value = "测量零序误差识别界面参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class EvtErrorCorrectionQuery extends CommonQuery{

    @ApiModelProperty(required = true, value = "判据类型", example = "1")
    private Integer distinguish;

}

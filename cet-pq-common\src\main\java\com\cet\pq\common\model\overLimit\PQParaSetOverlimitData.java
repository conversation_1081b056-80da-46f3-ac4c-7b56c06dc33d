package com.cet.pq.common.model.overLimit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 监测点超标信息
 * @date 2020/11/6 14:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PQParaSetOverlimitData {
    private Integer aggregationcycle;
    private Integer continuousdaycnt;
    private Long device_id;
    private Boolean isupload;
    private Long line_id;
    private Long logtime;
    private Long overcnt;
    private Integer overdaycnt;
    private Double overlimitpercent;
    private Long quantityparaset_id;
    private Long updatetime;
    private Integer causetype;
}

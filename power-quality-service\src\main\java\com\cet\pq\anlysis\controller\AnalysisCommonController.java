package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.common.DownloadWaveQueryParams;
import com.cet.pq.anlysis.service.CommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("pq/v1/")
@Api(value="CommonController",tags={"通用接口"})
public class AnalysisCommonController {

	@Autowired
	private CommonService commonService;
	
	@ApiOperation("波形下载")
	/*@ApiImplicitParams({
			@ApiImplicitParam(name = "deviceId", value = "设备Id", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "waveformlogtime", value = "触发录波时间", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "fileName", value = "下载的文件名：最终格式为：fileName_波形时间.zip", required = false, dataType = "String")
	})*/
	@PostMapping("/wave/download")
	public void downloadWaveFile(HttpServletResponse response, @RequestBody DownloadWaveQueryParams downloadWaveQueryParams) {
		commonService.downloadWaveFile(response, downloadWaveQueryParams.getDeviceId(),
				downloadWaveQueryParams.getWaveFormLogTime(), downloadWaveQueryParams.getFileName());
	}
	
	
}

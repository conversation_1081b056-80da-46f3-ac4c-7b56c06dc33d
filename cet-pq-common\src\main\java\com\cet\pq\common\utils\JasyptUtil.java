package com.cet.pq.common.utils;

import org.jasypt.encryption.pbe.StandardPBEByteEncryptor;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.EnvironmentStringPBEConfig;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/8 17:35
 */
public class JasyptUtil {

    /**
     * Jasypt生成加密结果
     * @param password 配置文件中设定的加密密文 jasypt.encryptor.password
     * @param value 待加密值
     * @return 加密后的值
     */
    public static String encryptPwd(String password, String value) {
        EnvironmentStringPBEConfig config = new EnvironmentStringPBEConfig();
        config.setAlgorithm(StandardPBEByteEncryptor.DEFAULT_ALGORITHM);
        config.setPassword(password);
        StandardPBEStringEncryptor enc = new StandardPBEStringEncryptor();
        enc.setConfig(config);
        return enc.encrypt(value);
    }

    /**
     * 解密
     * @param password 配置文件中设定的加密密文 jasypt.encryptor.password
     * @param value 待解密密文
     * @return 解密后的密文
     */
    public static String decyptPwd(String password, String value) {
        EnvironmentStringPBEConfig config = new EnvironmentStringPBEConfig();
        config.setAlgorithm(StandardPBEByteEncryptor.DEFAULT_ALGORITHM);
        config.setPassword(password);
        StandardPBEStringEncryptor enc = new StandardPBEStringEncryptor();
        enc.setConfig(config);
        return enc.decrypt(value);
    }
}

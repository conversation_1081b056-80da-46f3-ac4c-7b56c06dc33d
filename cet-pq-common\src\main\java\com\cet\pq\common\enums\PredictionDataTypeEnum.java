package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/9/10 11:07
 * @Description 预测数据类型
 */
public enum PredictionDataTypeEnum {
    THD(1,"电压总谐波畸变率"),
    VOLTAGE(2,"基波电压有效值"),
    UNBALANCE(3,"电压不平衡度"),
    ;

    private Integer id;
    private String text;

    PredictionDataTypeEnum(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public static String getTextById(Integer id) {
        for (PredictionDataTypeEnum oneEnum : PredictionDataTypeEnum.values()) {
            if (oneEnum.id.equals(id)) {
                return oneEnum.text;
            }
        }
        return StringUtils.EMPTY;
    }
}

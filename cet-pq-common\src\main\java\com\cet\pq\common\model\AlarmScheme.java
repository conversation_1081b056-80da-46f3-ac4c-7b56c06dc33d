package com.cet.pq.common.model;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlarmScheme {

	private String alias;
	private int calcType;
	private CheckTypeBean checkType;
	private int duration;
	private int id;
	private String name;
	private OverLimitTypeBean overLimitType;
	private List<ItemsBean> items;
	private List<TimeZonesBeanX> timeZones;

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public int getCalcType() {
		return calcType;
	}

	public void setCalcType(int calcType) {
		this.calcType = calcType;
	}

	public CheckTypeBean getCheckType() {
		return checkType;
	}

	public void setCheckType(CheckTypeBean checkType) {
		this.checkType = checkType;
	}

	public int getDuration() {
		return duration;
	}

	public void setDuration(int duration) {
		this.duration = duration;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public OverLimitTypeBean getOverLimitType() {
		return overLimitType;
	}

	public void setOverLimitType(OverLimitTypeBean overLimitType) {
		this.overLimitType = overLimitType;
	}

	public List<ItemsBean> getItems() {
		return items;
	}

	public void setItems(List<ItemsBean> items) {
		this.items = items;
	}

	public List<TimeZonesBeanX> getTimeZones() {
		return timeZones;
	}

	public void setTimeZones(List<TimeZonesBeanX> timeZones) {
		this.timeZones = timeZones;
	}

	public static class CheckTypeBean {
		private String test;

		public String getTest() {
			return test;
		}

		public void setTest(String test) {
			this.test = test;
		}
	}

	public static class OverLimitTypeBean {

		private String test;

		public String getTest() {
			return test;
		}

		public void setTest(String test) {
			this.test = test;
		}

	}

	public static class ItemsBean {
		
		private int checkReturn;
		private int id;
		private String itemName;
		private int limitBaseline;
		private double limitValue;
		private int returnBaseline;
		private int returnLimitValue;
		private int useTimeZone;
		private List<TimeZonesBean> timeZones;

		public int getCheckReturn() {
			return checkReturn;
		}

		public void setCheckReturn(int checkReturn) {
			this.checkReturn = checkReturn;
		}

		public int getId() {
			return id;
		}

		public void setId(int id) {
			this.id = id;
		}

		public String getItemName() {
			return itemName;
		}

		public void setItemName(String itemName) {
			this.itemName = itemName;
		}

		public int getLimitBaseline() {
			return limitBaseline;
		}

		public void setLimitBaseline(int limitBaseline) {
			this.limitBaseline = limitBaseline;
		}

		public double getLimitValue() {
			return limitValue;
		}

		public void setLimitValue(double limitValue) {
			this.limitValue = limitValue;
		}

		public int getReturnBaseline() {
			return returnBaseline;
		}

		public void setReturnBaseline(int returnBaseline) {
			this.returnBaseline = returnBaseline;
		}

		public int getReturnLimitValue() {
			return returnLimitValue;
		}

		public void setReturnLimitValue(int returnLimitValue) {
			this.returnLimitValue = returnLimitValue;
		}

		public int getUseTimeZone() {
			return useTimeZone;
		}

		public void setUseTimeZone(int useTimeZone) {
			this.useTimeZone = useTimeZone;
		}

		public List<TimeZonesBean> getTimeZones() {
			return timeZones;
		}

		public void setTimeZones(List<TimeZonesBean> timeZones) {
			this.timeZones = timeZones;
		}

		public static class TimeZonesBean {

			private CronBean cron;
			private int id;
			private int limitBaseline;
			private Double limitValue;
			private int returnBaseline;
			private int returnLimitValue;

			public CronBean getCron() {
				return cron;
			}

			public void setCron(CronBean cron) {
				this.cron = cron;
			}

			public int getId() {
				return id;
			}

			public void setId(int id) {
				this.id = id;
			}

			public int getLimitBaseline() {
				return limitBaseline;
			}

			public void setLimitBaseline(int limitBaseline) {
				this.limitBaseline = limitBaseline;
			}

			public Double getLimitValue() {
				return limitValue;
			}

			public void setLimitValue(Double limitValue) {
				this.limitValue = limitValue;
			}

			public int getReturnBaseline() {
				return returnBaseline;
			}

			public void setReturnBaseline(int returnBaseline) {
				this.returnBaseline = returnBaseline;
			}

			public int getReturnLimitValue() {
				return returnLimitValue;
			}

			public void setReturnLimitValue(int returnLimitValue) {
				this.returnLimitValue = returnLimitValue;
			}

			public static class CronBean {
				/**
				 * endDay : 0 endHour : 0 endMinute : 0 endMonth : 0 endSecond : 0 endYear : 0
				 * startDay : 0 startHour : 0 startMinute : 0 startMonth : 0 startSecond : 0
				 * startYear : 0
				 */

				private int endDay;
				private int endHour;
				private int endMinute;
				private int endMonth;
				private int endSecond;
				private int endYear;
				private int startDay;
				private int startHour;
				private int startMinute;
				private int startMonth;
				private int startSecond;
				private int startYear;

				public int getEndDay() {
					return endDay;
				}

				public void setEndDay(int endDay) {
					this.endDay = endDay;
				}

				public int getEndHour() {
					return endHour;
				}

				public void setEndHour(int endHour) {
					this.endHour = endHour;
				}

				public int getEndMinute() {
					return endMinute;
				}

				public void setEndMinute(int endMinute) {
					this.endMinute = endMinute;
				}

				public int getEndMonth() {
					return endMonth;
				}

				public void setEndMonth(int endMonth) {
					this.endMonth = endMonth;
				}

				public int getEndSecond() {
					return endSecond;
				}

				public void setEndSecond(int endSecond) {
					this.endSecond = endSecond;
				}

				public int getEndYear() {
					return endYear;
				}

				public void setEndYear(int endYear) {
					this.endYear = endYear;
				}

				public int getStartDay() {
					return startDay;
				}

				public void setStartDay(int startDay) {
					this.startDay = startDay;
				}

				public int getStartHour() {
					return startHour;
				}

				public void setStartHour(int startHour) {
					this.startHour = startHour;
				}

				public int getStartMinute() {
					return startMinute;
				}

				public void setStartMinute(int startMinute) {
					this.startMinute = startMinute;
				}

				public int getStartMonth() {
					return startMonth;
				}

				public void setStartMonth(int startMonth) {
					this.startMonth = startMonth;
				}

				public int getStartSecond() {
					return startSecond;
				}

				public void setStartSecond(int startSecond) {
					this.startSecond = startSecond;
				}

				public int getStartYear() {
					return startYear;
				}

				public void setStartYear(int startYear) {
					this.startYear = startYear;
				}
			}
		}
	}

	public static class TimeZonesBeanX {

		private CronBeanX cron;
		private int id;

		public CronBeanX getCron() {
			return cron;
		}

		public void setCron(CronBeanX cron) {
			this.cron = cron;
		}

		public int getId() {
			return id;
		}

		public void setId(int id) {
			this.id = id;
		}

		public static class CronBeanX {
			/**
			 * endDay : 0 endHour : 0 endMinute : 0 endMonth : 0 endSecond : 0 endYear : 0
			 * startDay : 0 startHour : 0 startMinute : 0 startMonth : 0 startSecond : 0
			 * startYear : 0
			 */

			private int endDay;
			private int endHour;
			private int endMinute;
			private int endMonth;
			private int endSecond;
			private int endYear;
			private int startDay;
			private int startHour;
			private int startMinute;
			private int startMonth;
			private int startSecond;
			private int startYear;

			public int getEndDay() {
				return endDay;
			}

			public void setEndDay(int endDay) {
				this.endDay = endDay;
			}

			public int getEndHour() {
				return endHour;
			}

			public void setEndHour(int endHour) {
				this.endHour = endHour;
			}

			public int getEndMinute() {
				return endMinute;
			}

			public void setEndMinute(int endMinute) {
				this.endMinute = endMinute;
			}

			public int getEndMonth() {
				return endMonth;
			}

			public void setEndMonth(int endMonth) {
				this.endMonth = endMonth;
			}

			public int getEndSecond() {
				return endSecond;
			}

			public void setEndSecond(int endSecond) {
				this.endSecond = endSecond;
			}

			public int getEndYear() {
				return endYear;
			}

			public void setEndYear(int endYear) {
				this.endYear = endYear;
			}

			public int getStartDay() {
				return startDay;
			}

			public void setStartDay(int startDay) {
				this.startDay = startDay;
			}

			public int getStartHour() {
				return startHour;
			}

			public void setStartHour(int startHour) {
				this.startHour = startHour;
			}

			public int getStartMinute() {
				return startMinute;
			}

			public void setStartMinute(int startMinute) {
				this.startMinute = startMinute;
			}

			public int getStartMonth() {
				return startMonth;
			}

			public void setStartMonth(int startMonth) {
				this.startMonth = startMonth;
			}

			public int getStartSecond() {
				return startSecond;
			}

			public void setStartSecond(int startSecond) {
				this.startSecond = startSecond;
			}

			public int getStartYear() {
				return startYear;
			}

			public void setStartYear(int startYear) {
				this.startYear = startYear;
			}
		}
	}
}

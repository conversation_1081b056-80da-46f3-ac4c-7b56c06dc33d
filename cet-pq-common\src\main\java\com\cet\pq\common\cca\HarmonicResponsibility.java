package com.cet.pq.common.cca;

import java.util.Arrays;
import java.util.stream.Stream;

/**
 * 谐波相关责任计算
 * <AUTHOR>
 * @date 2022年10月24日 10:41
 */
public class HarmonicResponsibility extends HarmonicSources{
    /**
     * 窗口下输出线路谐波责任相关系数
     */
    protected double [][] rCanonOutputArr;
    /**
     * 窗口下输入线路谐波责任相关系数
     */
    protected double [][] rCanonInputArr;

    public double[][] getrCanonOutputArr() {
        return rCanonOutputArr;
    }

    public double[][] getrCanonInputArr() {
        return rCanonInputArr;
    }

    /**
     * @param upccArr   监测点  电压数据 数据已补全且绝对值
     * @param outputArr 输入数据 数据已补全且绝对值
     * @param inputArr  输出数据 数据已补全
     */
    public HarmonicResponsibility(double[] upccArr, double[][] outputArr, double[][] inputArr) {
        super(upccArr, outputArr, inputArr);
    }

    public void responsibility() throws Exception {
        //计算源定位相关系数
        super.sources();

        double[][] aveOutputArr = new double[winNum][outputLineNum];
        double[][] aveInputArr = new double[winNum][inputLineNum];
        double[][] faOutputArr = new double[winNum][outputLineNum];
        double[][] faInoutArr = new double[winNum][inputLineNum];
        rCanonOutputArr = new double[winNum][outputLineNum];
        rCanonInputArr = new double[winNum][inputLineNum];
        Stream.iterate(0, i -> i + 1).limit(winNum).forEach(i ->{
            // 根据窗口长度分窗口计算每条线路的平均值
            if (outputArr != null) {
                Stream.iterate(0, j -> j + 1).limit(outputLineNum).forEach(j ->{
                    double ave = Arrays.stream(outputArr).skip(((long) (i * t))).limit(t).mapToDouble(item -> item[j]).average().getAsDouble();
                    aveOutputArr[i][j] = ave;
                });
                //输出侧谐波责任
                double outputSum = Arrays.stream(aveOutputArr[i]).sum(); // 输出线路功率的平均值求和
                Stream.iterate(0, j -> j + 1).limit(outputLineNum).forEach(j ->{
//                    System.out.println(canonOutputArr[i][j]*aveOutputArr[i][j]/outputSum);
                    faOutputArr[i][j] = canonOutputArr[i][j]*aveOutputArr[i][j]/outputSum;
                });
                double outputFaSum = Arrays.stream(faOutputArr[i]).sum();// fa横向求和
                Stream.iterate(0, j -> j + 1).limit(outputLineNum).forEach(j ->{
                    if (canonAllInputArr == null) {
                        rCanonOutputArr[i][j] = 1*faOutputArr[i][j]/outputFaSum;
                    } else {
                        rCanonOutputArr[i][j] = (1-canonAllInputArr[i])*faOutputArr[i][j]/outputFaSum;
                    }
                });
            }

            if (inputArr != null) {
                Stream.iterate(0, j -> j + 1).limit(inputLineNum).forEach(j ->{
                    double ave = Arrays.stream(inputArr).skip(((long) (i * t))).limit(t).mapToDouble(item -> item[j]).average().getAsDouble();
                    aveInputArr[i][j] = ave;
                });

                //输入侧谐波责任
                double inputSum = Arrays.stream(aveInputArr[i]).sum(); // 输入线路功率的平均值求和
                Stream.iterate(0, j -> j + 1).limit(inputLineNum).forEach(j ->{
                    faInoutArr[i][j] = canonInputArr[i][j]*aveInputArr[i][j]/inputSum;

                });

                double inputFaSum = Arrays.stream(faInoutArr[i]).sum();// fa横向求和
                Stream.iterate(0, j -> j + 1).limit(inputLineNum).forEach(j ->{
                    rCanonInputArr[i][j] = canonAllInputArr[i]*faInoutArr[i][j]/inputFaSum;
                });
            }


            /*for (j=c2+c3;j<c2+c3+1;j++){
                //System.out.println(" j="+j);
                //h[i][j] = canon[i][c2+c3];
                h[i][j] = canon[i][c2+c3]/(canon[i][c2+c3]+canon[i][c2+c3+1]);
                ////System.out.println("h"+i+","+j+"="+h[i][j]);    ///输入总，即为背景
            }
            for (j=c2+c3+1;j<c2+c3+2;j++){
                //System.out.println(" j="+j);
                //h[i][j] = (1-canon[i][c2+c3]);
                h[i][j] = canon[i][c2+c3+1]/(canon[i][c2+c3]+canon[i][c2+c3+1]);
                ////System.out.println("h"+i+","+j+"="+h[i][j]);     ///输出总
            }



            //System.out.println(" j="+j);
            for (z=i*t;z<(i+1)*t;z++){
                //System.out.println(" z="+z);
                u_jz[z][0] = data1[z][0];
                u_jz[z][1] = (1-canon[i][c2+c3])*data1[z][0];
                ////System.out.print(z+"="+u_jz[z][0]);  //z1为(1:t)     //原始谐波电压数据
                ////System.out.println(" "+u_jz[z][1]);            //去除背景矫正后谐波电压数据
            }*/
        });
    }
}

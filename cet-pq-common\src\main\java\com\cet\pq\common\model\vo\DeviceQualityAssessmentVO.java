package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 14:53
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeviceQualityAssessmentVO {

    /**
     * 监测点编号
     */
    private Long monitorId;

    /**
     * 数据类型: 0:谐波畸变率,1:电压偏差,2:三相不平衡,3:频率偏差,4:长时闪变
     */
    private String errorDataType;

    /**
     * 缺失数据占比
     */
    private Object missingData;

    /**
     * 异常数据占比
     */
    private Object errorData;

    /**
     * 数据质量评估: 0：优，1：良，2：差
     */
    private String dataQualityAssessment;

    /**
     * 监测区域
     */
    private String cityCompany;

}

package com.cet.pq.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @ClassName FieldAnnotation
 * @Date 2021/6/6
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface ValidCellAnnotation {

    //是否必填
    boolean isRequired() default false;

    //属性类型
    Class type() default String.class;

    //属性类型是枚举，调用的方法名，枚举方法是通过名称获取枚举值
    String method() default "";

}

package com.cet.pq.anlysis.model.GDReport;

import com.cet.pq.common.constant.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/8/28
 * @Description 报告信息记录模型
 */
@Data
public class ReportLog {
    private String modelLabel = TableName.REPORT_LOG;
    private Long id;
    private String name;
    private String path;
    @JsonProperty("reporttype")
    private Integer reportType;

    @JsonProperty("aggregationcycle")
    private Integer aggregationCycle;

    @JsonProperty("starttime")
    private Long startTime;

    @JsonProperty("endtime")
    private Long endTime;

    @JsonProperty("logtime")
    private Long logTime;

    @JsonProperty("storename")
    private String storeName;


}

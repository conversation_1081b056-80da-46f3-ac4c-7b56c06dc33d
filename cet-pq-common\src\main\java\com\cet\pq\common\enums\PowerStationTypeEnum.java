package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum PowerStationTypeEnum {
    //电站类型
    substation("普通变电站", 1),
    converterstation("换流站", 2),
    stepupstation("升压站", 3),
    stepdownstation("用户总降", 4),
    others("其他", 5);

    private String name;
    private Integer value;

    private PowerStationTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static Integer getPowerStationType(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (PowerStationTypeEnum powerStationTypeEnum : PowerStationTypeEnum.values()) {
            if (powerStationTypeEnum.getName().equals(name)) {
                return powerStationTypeEnum.getValue();
            }
        }
        throw new CommonManagerException("电站类型值值无效");
    }

    public static Integer getPowerStationTypeNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (PowerStationTypeEnum powerStationTypeEnum : PowerStationTypeEnum.values()) {
            if (powerStationTypeEnum.getName().equals(name)) {
                return powerStationTypeEnum.getValue();
            }
        }
        return 0;
    }

    public static String getPowerStationTypeNameByValueNoThrows(Integer value) {
        for (PowerStationTypeEnum powerStationTypeEnum : PowerStationTypeEnum.values()) {
            if (powerStationTypeEnum.value.equals(value)) {
                return powerStationTypeEnum.name;
            }
        }
        return StringUtils.EMPTY;
    }

}

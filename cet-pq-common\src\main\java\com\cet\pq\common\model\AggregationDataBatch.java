package com.cet.pq.common.model;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AggregationDataBatch {

    /**
     * aggregationCycle : 0
     * dataTypeId : 0
     * endTime : 0
     * nodes : [{"id":0,"modelLabel":"string","name":"string"}]
     * quantitySettings : [{"energytype":0,"frequency":0,"id":0,"phasor":0,"quantitycategory":0,"quantitytype":0}]
     * startTime : 0
     */

    private int aggregationCycle;
    private Integer dataTypeId;
    private long endTime;
    private long startTime;
    private List<NodesBean> nodes;
    private List<QuantitySettingsBean> quantitySettings;

    public int getAggregationCycle() {
        return aggregationCycle;
    }

    public void setAggregationCycle(int aggregationCycle) {
        this.aggregationCycle = aggregationCycle;
    }

    public Integer getDataTypeId() {
        return dataTypeId;
    }

    public void setDataTypeId(Integer dataTypeId) {
        this.dataTypeId = dataTypeId;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public List<NodesBean> getNodes() {
        return nodes;
    }

    public void setNodes(List<NodesBean> nodes) {
        this.nodes = nodes;
    }

    public List<QuantitySettingsBean> getQuantitySettings() {
        return quantitySettings;
    }

    public void setQuantitySettings(List<QuantitySettingsBean> quantitySettings) {
        this.quantitySettings = quantitySettings;
    }

    public static class NodesBean {
        /**
         * id : 0
         * modelLabel : string
         * name : string
         */

        private int id;
        private String modelLabel;
        private String name;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getModelLabel() {
            return modelLabel;
        }

        public void setModelLabel(String modelLabel) {
            this.modelLabel = modelLabel;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class QuantitySettingsBean {
        /**
         * energytype : 0
         * frequency : 0
         * id : 0
         * phasor : 0
         * quantitycategory : 0
         * quantitytype : 0
         */

        private Integer energytype;
        private Integer frequency;
        private int id;
        private Integer phasor;
        private Integer quantitycategory;
        private Integer quantitytype;

        public Integer getEnergytype() {
            return energytype;
        }

        public void setEnergytype(Integer energytype) {
            this.energytype = energytype;
        }

        public Integer getFrequency() {
            return frequency;
        }

        public void setFrequency(Integer frequency) {
            this.frequency = frequency;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public Integer getPhasor() {
            return phasor;
        }

        public void setPhasor(Integer phasor) {
            this.phasor = phasor;
        }

        public Integer getQuantitycategory() {
            return quantitycategory;
        }

        public void setQuantitycategory(Integer quantitycategory) {
            this.quantitycategory = quantitycategory;
        }

        public Integer getQuantitytype() {
            return quantitytype;
        }

        public void setQuantitytype(Integer quantitytype) {
            this.quantitytype = quantitytype;
        }
    }
}

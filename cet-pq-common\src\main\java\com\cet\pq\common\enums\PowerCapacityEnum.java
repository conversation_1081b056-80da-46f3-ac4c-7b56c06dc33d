package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @<PERSON> <PERSON>g
 * @Date 2024/7/29 10:22
 * @Description 牵引站变电容量（MVA）
 */
public enum PowerCapacityEnum {
    //电站类型
    CAPACITY6_3("6.3", 1),
    CAPACITY8("8", 2),
    CAPACITY10("10", 3),
    CAPACITY12_5("12.5", 4),
    CAPACITY16("16", 5),
    CAPACITY25("25", 6),
    CAPACITY31_5("31.5", 7),
    CAPACITY40("40", 8),
    CAPACITY50("50", 9),
    CAPACITY63("63", 10),
    CAPACITY75("75", 11),
    CAPACITY80("80", 12),
    CAPACITY90("90", 13),
    CAPACITY100("100", 14),
    CAPACITY120("120", 15),
    ;

    private String text;
    private Integer id;

    PowerCapacityEnum(String text, Integer id) {
        this.text = text;
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public static String getTextByIdOrNull(Integer id) {
        for(PowerCapacityEnum rs : values()) {
            if(rs.getId().equals(id)) {
                return rs.getText();
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByText(String text) {
        for(PowerCapacityEnum rs : values()) {
            if(rs.getText().equals(text)) {
                return rs.getId();
            }
        }
        return null;
    }
}

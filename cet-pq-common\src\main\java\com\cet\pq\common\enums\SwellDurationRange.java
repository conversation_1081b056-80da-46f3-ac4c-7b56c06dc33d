package com.cet.pq.common.enums;

/**
 * <AUTHOR>
 * @ClassName SwellDurationRange
 * @Description 暂降评价持续时间定义
 * @Date 2020/9/3
 */
public enum SwellDurationRange {
    //暂降评价持续时间定义
    SwellDurationRange1(1, "d0_1", "(0.01,0.1]"),
    SwellDurationRange2(2, "d0_25", "(0.1,0.25]"),
    SwellDurationRange3(3, "d0_5", "(0.25,0.5]"),
    SwellDurationRange4(4, "d1", "(0.5,1]"),
    SwellDurationRange5(5, "d3", "(1,3]"),
    SwellDurationRange6(6, "d5", "(3,5]"),
    SwellDurationRange7(7, "d10", "(5,10]"),
    SwellDurationRange8(8, "d20", "(10,20]"),
    SwellDurationRange9(9, "d60", "(20,60]"),
    SwellDurationRange10(10, "d180", "(60,180]"),
    //合并(3,5]，(5,10]用于展示
    SwellDurationRange11(11, "d5", "(3,10]");



    private int id;
    private String name;
    private String duringTime;

    SwellDurationRange(int id, String name, String duringTime) {
        this.id = id;
        this.name = name;
        this.duringTime = duringTime;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDuringTime() {
        return duringTime;
    }

    public static SwellDurationRange getSwellDurationRange(Integer id) {
        for (SwellDurationRange type : values()) {
            if (id.equals(type.getId())) {
                return type;
            }
        }
        return SwellDurationRange1;
    }
    public static SwellDurationRange getSwellDurationRangeStat(Integer id) {
        if(id.equals(6)||id.equals(7)){
            id = 11;
        }
        for (SwellDurationRange type : values()) {
            if (id.equals(type.getId())) {
                return type;
            }
        }
        return SwellDurationRange1;
    }
}

package com.cet.pq.anlysis.model.excel;

import com.cet.pq.common.model.excel.ExportParameter;
import com.cet.pq.anlysis.model.common.CommonParam;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/3/8 14:52
 */
@Data
public class ExportBaseParam extends CommonParam {
    @NotNull(message = "modelId不为空")
    private Long modelId;
    @NotEmpty(message = "modelLabel不为空")
    private String modelLabel;

    private Integer monitorSort;
    private Integer monitorType;

    private ExportParameter exportParameter;
}

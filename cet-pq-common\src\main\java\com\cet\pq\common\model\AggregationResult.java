package com.cet.pq.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName AggregationResult
 * @Description 分组统计结果
 * <AUTHOR>
 * @Date 2020/3/27 8:32
 */
@Data
@NoArgsConstructor
public class AggregationResult<T> {
    @JsonProperty("count_id")
    private int countId;
    /**
     * 当前分组对应的值
     */
    private T item;
    private String modelLabel;
    private String groupByField;

    public AggregationResult(int countId, T item, String modelLabel) {
        this.countId = countId;
        this.item = item;
        this.modelLabel = modelLabel;
    }
}

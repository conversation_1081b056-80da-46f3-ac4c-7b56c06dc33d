package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/9/10 10:31
 * @Description 预测数据时间类型
 */
public enum PredictionTimeTypeEnum {
    FIFTEEN_MIN(1,"15分钟"),
    DAY(2,"日前"),
    ;

    private Integer id;
    private String text;

    PredictionTimeTypeEnum(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public Integer getId() {
        return id;
    }

    public String getText() {
        return text;
    }

    public static String getTextById(Integer id) {
        for (PredictionTimeTypeEnum oneEnum : PredictionTimeTypeEnum.values()) {
            if (oneEnum.id.equals(id)) {
                return oneEnum.text;
            }
        }
        return StringUtils.EMPTY;
    }
}

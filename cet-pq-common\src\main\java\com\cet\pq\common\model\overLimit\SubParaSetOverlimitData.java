package com.cet.pq.common.model.overLimit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description: 变电站超标数据
 * @date 2020/11/6 16:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubParaSetOverlimitData {
    private Integer aggregationcycle;
    private Double avgoverdaycnt;
    private Long logtime;
    private Integer onlinenodecnt;
    private Long overcnt;
    private Integer overdaycnt;
    private Long quantityparaset_id;
    private Long substation_id;
    private Integer sumoverdaycnt;
    private Long updatetime;
    private Boolean uploadonly;
}

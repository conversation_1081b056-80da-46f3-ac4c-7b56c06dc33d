package com.cet.pq.common.model.realtime;

import com.cet.pq.common.annotation.FieldAnnotation;
import com.cet.pq.common.enums.FieldType;
import com.cet.pq.common.enums.WiredTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * @Description 设备扩展信息
 * <AUTHOR>
 * @Date 2020/6/30 14:41
 */
@Data
public class PecDeviceExtend {

    private String modelLabel = "pecdeviceextend";

    private Long id;
    /**
     * 接线方式
     */
    @FieldAnnotation(name = "接线方式", type = WiredTypeEnum.class, method = "getNameByValueNoThrows")
    private Integer wiredtype;
    /**
     * 设备状态
     */
    private Integer status;
    /**
     * 所属厂站id
     */
    private Long stationid;
    /**
     * PT变化
     */
    @FieldAnnotation(name = "PT变化")
    private String ptratio;
    /**
     * 规约类型
     */
    private Integer protocaltype;
    /**
     * 设备节点名称
     */
    private String name;
    /**
     * 设备类型
     */
    private Integer metertype;
    /**
     * 数据完整率方案
     */
    @JsonProperty("integrityratescheme_id")
    private Long integrityrateschemeId;
    /**
     * 电压采样
     */
    @FieldAnnotation(name = "电压采样")
    private String inputvolt;
    /**
     * 电流采样
     */
    @FieldAnnotation(name = "电流采样")
    private String inputcurrent;
    /**
     * 满刻度值
     */
    private Double fullscalevalue;
    /**
     * 能源类型
     */
    private Integer energytype;
    /**
     * 设备状态
     */
    private Integer devicesstatus;
    /**
     * 设备节点id
     */
    private Long deviceid;
    /**
     * 数据间隔
     */
    @FieldAnnotation(name = "数据间隔")
    private Integer dataloginterval;
    /**
     * CT变比
     */
    @FieldAnnotation(name = "CT变比")
    private String ctratio;
    /**
     * 投运日期
     */
    @FieldAnnotation(name = "投运日期", type = Date.class)
    private Long commissioningdate;
    /**
     * 所属通道ID
     */
    private Long channelid;
    /**
     * 回路号
     */
    @FieldAnnotation(name = "回路号")
    private Integer branchindex;
    /**
     * 接入日期
     */
    @FieldAnnotation(name = "接入日期", type = Date.class)
    private Long accessdate;
    /**
     * 映射方案名称
     */
    private String drivertypename;
    /**
     * datalogmapname
     */
    private String datalogmapname;
    /**
     * channelname
     */
    private String channelname;
    /**
     * devicetypename
     */
    private String devicetypename;
    /**
     * stationname
     */
    private String stationname;

    /**
     * ifgenerationuser
     */
    private Boolean ifgenerationuser;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PecDeviceExtend that = (PecDeviceExtend) o;
        return Objects.equals(wiredtype, that.wiredtype) &&
                Objects.equals(dataloginterval, that.dataloginterval) &&
                Objects.equals(ptratio, that.ptratio) &&
                Objects.equals(ctratio, that.ctratio) &&
                Objects.equals(inputvolt, that.inputvolt) &&
                Objects.equals(inputcurrent, that.inputcurrent) &&
                Objects.equals(commissioningdate, that.commissioningdate) &&
                Objects.equals(accessdate, that.accessdate) &&
                Objects.equals(branchindex, that.branchindex) &&
                Objects.equals(ifgenerationuser, that.ifgenerationuser);
    }

    @Override
    public int hashCode() {
        return Objects.hash(wiredtype, dataloginterval, ptratio, ctratio, inputvolt, inputcurrent, commissioningdate,
                accessdate, branchindex, ifgenerationuser);
    }
}

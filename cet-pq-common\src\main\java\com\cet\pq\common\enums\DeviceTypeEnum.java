package com.cet.pq.common.enums;

//@AI-Generated-start
/**
 * @Title: DeviceTypeEnum
 * @Package: com.cet.pq.common.enums
 * @Description:
 * @Author: zhangyifu
 * @Date: 2025/5/19 10:13
 * @Version:1.0
 */
public enum DeviceTypeEnum {
    DEVICE_ONE(1,"三相不平衡治理装置"),
    DEVICE_TWO(2,"电压暂降监测装置"),
    DEVICE_THREE(3,"电压暂降治理装置")
    ;

    private Integer  value;
    private String name;

    DeviceTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
//@AI-Generated-end
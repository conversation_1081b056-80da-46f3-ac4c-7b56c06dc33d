package com.cet.pq.anlysis.model.areastatistics;

import com.cet.pq.pqservice.model.BaseNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/6 14:21
 * @description
 **/
@Data
@NoArgsConstructor
public class AreaPowerFactorProportion extends BaseNode {
    @ApiModelProperty("功率因数低于0.9的台区比例")
    private Double proportion;

    public AreaPowerFactorProportion(Long id, String modelLabel, String name){
        super(id, modelLabel, name);
    }
}

package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description: 逆变器组网方式
 * @date 2021/6/2 16:44
 */
public enum InverterNetworkWay {
    //逆变器组网方式
    GROUPOFTANDEM(1, "组串式"),
    CONCENTRATE(2, "集中式"),
    MIXED(3, "混合式");

    private Integer id;
    private String text;

    InverterNetworkWay(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public Integer getId() {
        return id;
    }

    public String getText() {
        return text;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (InverterNetworkWay inverterNetworkWay : InverterNetworkWay.values()) {
            if (inverterNetworkWay.id.equals(id)) {
                return inverterNetworkWay.text;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (InverterNetworkWay typeEnum : InverterNetworkWay.values()) {
            if (typeEnum.getText().trim().equalsIgnoreCase(name)) {
                return typeEnum.getId();
            }
        }
        return null;
    }
}

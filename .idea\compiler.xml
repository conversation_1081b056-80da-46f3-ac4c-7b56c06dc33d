<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="power-quality-service-4.2" />
        <module name="cet-pq-common" />
        <module name="power-quality-starter" />
        <module name="pq-advance-service" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="cet-pq-common" options="-parameters" />
      <module name="power-quality-service-4.2" options="-parameters" />
      <module name="power-quality-starter" options="-parameters" />
      <module name="pq-advance-service" options="-parameters" />
      <module name="pqsysplat42" options="-parameters" />
    </option>
  </component>
</project>
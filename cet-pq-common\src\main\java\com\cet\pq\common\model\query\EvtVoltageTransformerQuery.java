package com.cet.pq.common.model.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/05/17 17:09
 */
@ApiModel(value = "电磁式电压互感器界面查询参数")
@Data
public class EvtVoltageTransformerQuery {

    @ApiModelProperty(required = true, value = "品牌", example = "桂林")
    private String brand;

    @ApiModelProperty(required = true, value = "型号", example = "xxxxxx")
    private String type;

    /**
     *连接方式: 0:V/v,1:Y/y(默认)
     */
    @ApiModelProperty(required = true, value = "连接方式", example = "0")
    private Integer wiredType;

    /**
     *消谐装置: 0:无,1:有(默认)
     */
    @ApiModelProperty(required = true, value = "消谐装置", example = "0")
    private Integer harmonicEliminationDeviceType;

    /**
     * 系统中性点连接方式: 0:直接接地(默认),1:非直接接地
     */
    @ApiModelProperty(required = true, value = "系统中性点连接方式", example = "0")
    private Integer neutralPointGroundType;

    @ApiModelProperty(required = true, value = "开始时间", example = "1111111")
    private Long startTime;

    @ApiModelProperty(required = true, value = "结束时间", example = "2222222")
    private Long endTime;

    @ApiModelProperty(required = true, value = "节点模型id", example = "123")
    private Long modelId;

    @ApiModelProperty(required = true, value = "节点模型名称", example = "line")
    private String modelLabel;



}
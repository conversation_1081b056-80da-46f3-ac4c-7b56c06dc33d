package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 电压互感器类型枚举类
 * <AUTHOR>
 */
public enum VoltageTransformertypeEnum {
	//电压互感器类型枚举类
	electromagnetic("电磁式",1),capacitance("电容式",2),electronic("电子式",3),photoelectric("光电式",4),other("其他",5);

	private String name;
	private Integer value;

	private VoltageTransformertypeEnum(String name, Integer value) {
		this.name = name;
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public Integer getValue() {
		return value;
	}


	/**
	 * 获取电压互感器类型值
	 * @return
	 */
	public static Integer getVoltageTransformertypeValue(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (VoltageTransformertypeEnum voltageTransformertypeEnum : VoltageTransformertypeEnum.values()) {
			if(name.equals(voltageTransformertypeEnum.getName())) {
				return voltageTransformertypeEnum.getValue();
			}
		}
		throw new CommonManagerException("电压互感器类型值无效");
	}

	/**
	 * 获取电压互感器类型值
	 * @return
	 */
	public static Integer getVoltageTransformertypeValueNoThrows(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (VoltageTransformertypeEnum voltageTransformertypeEnum : VoltageTransformertypeEnum.values()) {
			if(name.equals(voltageTransformertypeEnum.getName())) {
				return voltageTransformertypeEnum.getValue();
			}
		}
		return 0;
	}

	public static String getVoltageTransformertypeNameNoThrows(Integer value) {
		for (VoltageTransformertypeEnum voltageTransformertypeEnum : VoltageTransformertypeEnum.values()) {
			if (voltageTransformertypeEnum.getValue().equals(value)) {
				return voltageTransformertypeEnum.getName();
			}
		}
		return StringUtils.EMPTY;
	}
	public static Integer getValueByNameImport(String name) {
		if(StringUtils.isBlank(name)){
			return null;
		}
		for (VoltageTransformertypeEnum voltageTransformertypeEnum : VoltageTransformertypeEnum.values()) {
			if(name.equals(voltageTransformertypeEnum.getName())) {
				return voltageTransformertypeEnum.getValue();
			}
		}
		return -1;
	}
}

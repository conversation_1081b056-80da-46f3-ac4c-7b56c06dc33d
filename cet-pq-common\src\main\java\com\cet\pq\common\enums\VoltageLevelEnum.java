package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.utils.ParseDataUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 电压等级枚举类
 *
 * <AUTHOR>
 */
public enum VoltageLevelEnum {
    //电压等级
    KV6("6kV", 1, 6D, 1),
    V400("0.4kV", 2, 0.4D, 2),
    KV10("10kV", 3, 10D, 3),
    KV20("20kV", 4, 20D, 4),
    KV35("35kV", 5, 35D, 5),
    KV110("110kV", 6, 110D, 6),
    KV6_6("6.6kV", 8, 6.6D, 8),
    KV6_9("6.9kV", 9, 6.9D, 9),
    KV33("33kV", 10, 33D, 10),
    KV66("66kV", 11, 66D, 11),
    KV220("220kV", 12, 220D, 12),
    KV330("330kV", 13, 330D, 13),
    KV500("500kV", 14, 500D, 14),
    KV750("750kV", 17, 750D, 17),
    KV1000("1000kV", 15, 1000D, 15),
    V220("0.22kV", 20, 0.22D, 20),
    UNKNOWN("未知", 30, 0.22D, 30),

    ;
    private String name;
    private Integer value;
    private Double number;
    private Integer group;

    private VoltageLevelEnum(String name, Integer value, Double number, Integer group) {
        this.name = name;
        this.value = value;
        this.number = number;
        this.group = group;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public Double getNumber() {
        return number;
    }

    public Integer getGroup() {
        return group;
    }

    public static Integer getVoltageLevel(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (voltageLevelEnum.getName().trim().equalsIgnoreCase(name)) {
                return voltageLevelEnum.getValue();
            }
            if ("直流".equalsIgnoreCase(name.trim())) {
                return 7;
            }
        }
        return 0;
    }

    public static Integer getVoltageLevelThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            throw new CommonManagerException("电压等级不存在");
        }
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (voltageLevelEnum.getName().trim().equalsIgnoreCase(name)) {
                return voltageLevelEnum.getValue();
            }
            if ("直流".equalsIgnoreCase(name.trim())) {
                return 7;
            }
        }
        throw new CommonManagerException("电压等级匹配失败");
    }

    public static Integer getVoltageLevelGroupId(Integer value) {
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (value.equals(voltageLevelEnum.getValue())) {
                return voltageLevelEnum.getGroup();
            }
        }
        return 0;
    }

    public static List<Integer> getLevelListByGroupId(Integer value) {
        List<Integer> result = new ArrayList<>();
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (value.equals(voltageLevelEnum.getGroup())) {
                result.add(voltageLevelEnum.getValue());
            }
        }
        return result;
    }

    public static String getVoltageLevel(Integer value) {
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (voltageLevelEnum.getValue().equals(value)) {
                return voltageLevelEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }

    public static Set<Integer> getVoltageLevelValueList() {
        Set<Integer> set = new HashSet<>();
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            set.add(voltageLevelEnum.getGroup());
        }
        return set;
    }

    public static Integer getVoltageLevelNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (voltageLevelEnum.getName().trim().equalsIgnoreCase(name)) {
                return voltageLevelEnum.getValue();
            }
            if ("直流".equalsIgnoreCase(name.trim())) {
                return 7;
            }
        }
        return null;
    }

    public static VoltageGroup getVoltageGroup(String name) {
        Integer group = 1;
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (name.equalsIgnoreCase(voltageLevelEnum.getName().trim())) {
                group = voltageLevelEnum.getGroup();
            }
            if ("直流".equalsIgnoreCase(name.trim())) {
                group = 7;
            }
        }
        return VoltageGroup.getVoltageGroup(group);
    }

    public static Double getVoltageLevelNumber(Integer value) {
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (value.equals(voltageLevelEnum.getValue())) {
                return voltageLevelEnum.getNumber();
            }
        }
        return 0.0;
    }
    public static Integer getVoltageLevelImport(String name) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (name.equalsIgnoreCase(voltageLevelEnum.getName().trim())) {
                return voltageLevelEnum.getValue();
            }
            if (name.trim().equalsIgnoreCase("直流")) {
                return 7;
            }
        }
        return -1;
    }

    /**
     * 解析复杂格式文本
     *
     * @param name
     * @return
     */
    public static Integer paserSpecialValue(String name) {
        if (name.toLowerCase().contains("kv")) {
            Integer value = ParseDataUtil.parseDouble(name.substring(0, name.length() - 2).trim()).intValue();
            name = value + "kv";
        } else if (name.toLowerCase().contains("vdc")) {
            Integer value = ParseDataUtil.parseDouble(name.substring(0, name.length() - 3).trim()).intValue();
            name = value + "kv";
        } else {
            name = "直流";
        }
        return getVoltageLevel(name);
    }

    public static Integer getVoltageLevelUnknown(String name) {
        if (StringUtils.isEmpty(name)) {
            return UNKNOWN.getValue();
        }
        for (VoltageLevelEnum voltageLevelEnum : VoltageLevelEnum.values()) {
            if (voltageLevelEnum.getName().trim().equalsIgnoreCase(name)) {
                return voltageLevelEnum.getValue();
            }
            if ("直流".equalsIgnoreCase(name.trim())) {
                return 7;
            }
        }
        return UNKNOWN.getValue();
    }

}

package com.cet.pq.common.enums;

/**
 * <AUTHOR>
 * @ClassName PQVariationEventType
 * @Date 2020/10/26
 */
public enum PQVariationEventType {
    TRANSIENT_PQ_VARIATION_EVENT_TYPE("瞬变", 1),
    SWELL_PQ_VARIATION_EVENT_TYPE("电压暂升", 2),
    SAG_PQ_VARIATION_EVENT_TYPE("电压暂降", 3),
    SHORT_INTERRUPTION_PQ_VARIATION_EVENT_TYPE("短时中断", 4),
    LONG_SWELL_PQ_VARIATION_EVENT_TYPE("长时过电压", 5),
    LONG_SAG_PQ_VARIATION_EVENT_TYPE("长时欠电压", 6),
    LONG_INTERRUPTION_PQ_VARIATION_EVENT_TYPE("长时中断", 7);

    private String name;

    private Integer value;
    private PQVariationEventType(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static String getEnumByValue(Integer value) {
        for (PQVariationEventType type : PQVariationEventType.values()) {
            if (type.getValue().equals(value)) {
                return type.getName();
            }
        }
        return null;
    }
}

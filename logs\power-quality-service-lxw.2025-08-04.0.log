2025-08-04 13:56:57.035 [33m- INFO[0;39m - [192.168.0.2] - [34m32872[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 13:57:07.451 [33m- INFO[0;39m - [192.168.0.2] - [34m32872[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 13:57:09.207 [33m- INFO[0;39m - [192.168.0.2] - [34m32872[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 13:57:24.596 [33m- INFO[0;39m - [192.168.0.2] - [34m32872[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 30.086 seconds (JVM running for 31.435)
2025-08-04 13:57:24.611 [33m- INFO[0;39m - [192.168.0.2] - [34m32872[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 13:57:38.631 [33m- INFO[0;39m - [192.168.0.2] - [34m32872[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 15:18:07.899 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 15:18:34.593 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 15:18:36.359 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 15:18:51.046 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 45.635 seconds (JVM running for 47.593)
2025-08-04 15:18:51.062 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 15:19:04.842 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 15:30:42.735 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:258] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 15:30:43.041 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:263] : 持续时间为: 39,幅值为：5039.1387
2025-08-04 15:40:36.425 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:258] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 15:58:16.070 [33m- INFO[0;39m - [192.168.0.2] - [34m14648[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-6][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:263] : 持续时间为: 39,幅值为：5039.1387
2025-08-04 15:58:34.289 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 15:58:45.424 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 15:58:47.215 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 15:59:05.288 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 33.879 seconds (JVM running for 35.771)
2025-08-04 15:59:05.306 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 15:59:16.939 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 15:59:42.453 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:258] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 16:02:25.817 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:263] : 持续时间为: 40,幅值为：3529.1533
2025-08-04 16:04:30.090 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:258] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 16:04:30.386 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:263] : 持续时间为: 40,幅值为：3529.1533
2025-08-04 16:04:44.768 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:258] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 16:39:34.583 [33m- INFO[0;39m - [192.168.0.2] - [34m43512[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:263] : 持续时间为: 40,幅值为：3529.1533
2025-08-04 16:40:02.956 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 16:40:20.599 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 16:40:22.592 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 16:40:40.298 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 41.651 seconds (JVM running for 43.766)
2025-08-04 16:40:40.319 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 16:40:49.730 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 16:40:55.114 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:258] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 16:40:55.597 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:480] : 检测到电压暂降，触发点: 5, 相位: 0, RMS值: 3529.1532749148905
2025-08-04 16:40:55.598 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:497] : 检测到事件结束点: 5, RMS值: 6161.936878815979
2025-08-04 16:40:55.600 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:602] : 电压暂降最小值: 3529.1532749148905 V
2025-08-04 16:40:55.601 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:613] : 事件幅值: {:.3f} ({:.1f}%)
2025-08-04 16:40:55.602 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:263] : 持续时间为: 0,幅值为：0.588256
2025-08-04 16:42:19.164 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:258] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 17:03:03.833 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:480] : 检测到电压暂降，触发点: 5, 相位: 0, RMS值: 3529.1532749148905
2025-08-04 17:03:03.835 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:497] : 检测到事件结束点: 5, RMS值: 6161.936878815979
2025-08-04 17:03:03.837 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:602] : 电压暂降最小值: 3529.1532749148905 V
2025-08-04 17:03:03.838 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:613] : 事件幅值: {:.3f} ({:.1f}%)
2025-08-04 17:03:03.839 [33m- INFO[0;39m - [192.168.0.2] - [34m7080[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:263] : 持续时间为: 0,幅值为：0.588256
2025-08-04 17:03:22.528 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 17:03:35.856 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 17:03:37.729 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 17:03:54.872 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 35.671 seconds (JVM running for 37.688)
2025-08-04 17:03:54.901 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 17:04:02.682 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 17:04:11.595 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:501] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 17:04:11.601 [33m- WARN[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:475] : 计算的触发索引超出范围: 5000 >= 500, 设置为最大值
2025-08-04 17:04:11.602 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:479] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100000ms ({:.3f}s) -> 采样点索引: 100.0 -> RMS索引: 2560000
2025-08-04 17:04:11.607 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:559] : 计算得到额定电压: 220.0 V (基于索引 494 到 498)
2025-08-04 17:04:11.609 [33m- WARN[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:570] : 触发索引超出范围: 499
2025-08-04 17:04:11.610 [33m- WARN[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:675] : 无效的索引范围: 触发点=499, 结束点=-1
2025-08-04 17:04:11.612 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:326] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 499, 持续时间: 0ms, 幅值: 1.0
2025-08-04 17:04:11.613 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0,幅值为：1.0
2025-08-04 17:04:14.737 [33m- INFO[0;39m - [192.168.0.2] - [34m3012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 17:12:09.234 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 17:12:20.552 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 17:12:22.351 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 17:12:38.952 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 32.535 seconds (JVM running for 34.22)
2025-08-04 17:12:38.977 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 17:12:49.511 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 17:13:00.526 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 17:13:00.911 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:514] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 17:13:00.913 [33m- WARN[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] : 计算的触发索引超出范围: 5000 >= 500, 设置为最大值
2025-08-04 17:13:00.916 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:492] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100000ms ({:.3f}s) -> 采样点索引: 100.0 -> RMS索引: 2560000
2025-08-04 17:13:00.918 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 30, 窗口大小: 512, 采样率: 25600.0
2025-08-04 17:13:00.920 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:572] : 计算得到额定电压: 220.0 V (基于索引 494 到 498)
2025-08-04 17:13:00.922 [33m- WARN[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:583] : 触发索引超出范围: 499
2025-08-04 17:13:00.924 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:321] : 🔍 事件分析结果 - 触发索引: 499, 结束索引: -1, 事件类型: NORMAL
2025-08-04 17:13:00.925 [33m- WARN[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:726] : 无效的索引范围: 触发点=499, 结束点=-1
2025-08-04 17:13:00.925 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:326] : 🔍 持续时间计算 - 原始持续时间: 0.0秒, 转换后: 0.0毫秒
2025-08-04 17:13:00.926 [33m- WARN[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:331] : 计算的持续时间异常: 0.0ms, 尝试使用经验方法
2025-08-04 17:13:00.927 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:760] : 基于经验的持续时间估算: 50.0Hz系统, 2.0个周期, 约40.0ms
2025-08-04 17:13:00.928 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:333] : 使用经验方法计算的持续时间: 40.0ms
2025-08-04 17:13:00.929 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:339] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 499, 持续时间: 40ms, 幅值: 1.0
2025-08-04 17:13:00.931 [33m- INFO[0;39m - [192.168.0.2] - [34m5324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-4][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40,幅值为：1.0
2025-08-04 17:18:28.189 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 17:18:39.321 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 17:18:41.166 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 17:19:00.466 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 35.126 seconds (JVM running for 36.852)
2025-08-04 17:19:00.487 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 17:19:29.664 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 17:19:39.878 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 17:19:40.259 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:512] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 17:19:40.260 [33m- WARN[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] : 计算的触发索引超出范围: 5000 >= 500, 设置为最大值
2025-08-04 17:19:40.261 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:490] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100000ms ({:.3f}s) -> 采样点索引: 100.0 -> RMS索引: 2560000
2025-08-04 17:19:40.262 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 30, 窗口大小: 512, 采样率: 25600.0
2025-08-04 17:19:40.262 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:570] : 计算得到额定电压: 220.0 V (基于索引 494 到 498)
2025-08-04 17:19:40.263 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:788] : 🔍 RMS数据调试 (额定电压: 220.0 V):
2025-08-04 17:19:40.264 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:789] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 17:19:40.265 [33m- WARN[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:581] : 触发索引超出范围: 499
2025-08-04 17:19:40.266 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 499, 结束索引: -1, 事件类型: NORMAL
2025-08-04 17:19:40.267 [33m- WARN[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:852] : 无效的索引范围: 触发点=499, 结束点=-1
2025-08-04 17:19:40.268 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.0秒, 转换后: 0.0毫秒
2025-08-04 17:19:40.268 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:337] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 499, 持续时间: 0ms, 幅值: 1.0
2025-08-04 17:19:40.269 [33m- INFO[0;39m - [192.168.0.2] - [34m30516[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0,幅值为：1.0
2025-08-04 17:22:41.474 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 17:22:52.212 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 17:22:54.289 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 17:23:10.378 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 31.832 seconds (JVM running for 33.577)
2025-08-04 17:23:10.400 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 17:23:40.299 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 17:23:40.695 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:819] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:23:40.696 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:819] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:23:40.697 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:526] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 17:23:40.698 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] : 🔍 时间解析调试:
2025-08-04 17:23:40.698 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 触发时间字符串: 02/03/2025,11:23:39.911000
2025-08-04 17:23:40.699 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 解析后触发时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:23:40.699 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 波形开始时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:23:40.700 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:467] :   - 触发时间戳: 1738553019911
2025-08-04 17:23:40.701 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:468] :   - 开始时间戳: 1738553019811
2025-08-04 17:23:40.701 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:473] :   - 原始时间差: 100ms
2025-08-04 17:23:40.702 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] : 🔍 时间计算详细过程:
2025-08-04 17:23:40.703 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 时间差: 100ms = 0.1秒
2025-08-04 17:23:40.705 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 采样率: 25600.0Hz
2025-08-04 17:23:40.706 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 触发采样点索引: 2560
2025-08-04 17:23:40.707 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:489] :   - 窗口大小: 512
2025-08-04 17:23:40.708 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:490] :   - 计算的RMS索引: 5
2025-08-04 17:23:40.709 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:505] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100ms ({:.3f}s) -> 采样点索引: 0.1 -> RMS索引: 2560
2025-08-04 17:23:40.710 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 30, 窗口大小: 512, 采样率: 25600.0
2025-08-04 17:23:40.711 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:584] : 计算得到额定电压: 5999.349154990429 V (基于索引 0 到 4)
2025-08-04 17:23:40.711 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:834] : 🔍 RMS数据调试 (额定电压: 5999.349154990429 V):
2025-08-04 17:23:40.712 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:835] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 17:23:40.713 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 2	{:.2f}	{:.2f}	{:.2f}	5740.559209258767
2025-08-04 17:23:40.713 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 3	{:.2f}	{:.2f}	{:.2f}	5727.657203902604
2025-08-04 17:23:40.714 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 4	{:.2f}	{:.2f}	{:.2f}	5721.697916024922
2025-08-04 17:23:40.714 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 5	{:.2f}	{:.2f}	{:.2f}	3529.1532749148905
2025-08-04 17:23:40.715 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 6	{:.2f}	{:.2f}	{:.2f}	3634.8555960309445
2025-08-04 17:23:40.715 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 7	{:.2f}	{:.2f}	{:.2f}	5463.8810723335255
2025-08-04 17:23:40.716 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 8	{:.2f}	{:.2f}	{:.2f}	5897.164084763231
2025-08-04 17:23:40.716 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 9	{:.2f}	{:.2f}	{:.2f}	5923.132658917508
2025-08-04 17:23:40.717 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 10	{:.2f}	{:.2f}	{:.2f}	5936.360601609818
2025-08-04 17:23:40.717 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 11	{:.2f}	{:.2f}	{:.2f}	5922.406645682151
2025-08-04 17:23:40.718 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 12	{:.2f}	{:.2f}	{:.2f}	5939.046147169264
2025-08-04 17:23:40.719 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 13	{:.2f}	{:.2f}	{:.2f}	5942.714756959242
2025-08-04 17:23:40.719 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 14	{:.2f}	{:.2f}	{:.2f}	5945.483308701551
2025-08-04 17:23:40.720 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:841] : 15	{:.2f}	{:.2f}	{:.2f}	5947.134057201715
2025-08-04 17:23:40.722 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:622] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-04 17:23:40.723 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:711] : 🔍 简化结束点检测 - 触发RMS: 3529.1532749148905 V, 额定电压: 5999.349154990429 V, 恢复阈值: 299.9674577495215 V
2025-08-04 17:23:40.724 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:722] : 检测到恢复点: 索引 8, RMS: 5897.164084763231 V, 与额定电压差异: 102.1850702271986 V
2025-08-04 17:23:40.725 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] : 🔍 幅值计算 - 暂降事件, 额定电压: 5999.349154990429 V, 事件极值: 3529.1532749148905 V, 幅值: {:.3f} ({:.1f}%)
2025-08-04 17:23:40.726 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 5, 结束索引: 8, 事件类型: SAG
2025-08-04 17:23:40.728 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:908] : 持续时间计算: 触发点采样索引=2560, 结束点采样索引=4096, 持续时间={:.3f}秒
2025-08-04 17:23:40.729 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.06秒, 转换后: 60.0毫秒
2025-08-04 17:23:40.730 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:337] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 5, 持续时间: 60ms, 幅值: 0.5882560230686424
2025-08-04 17:23:40.730 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 60,幅值为：0.588256
2025-08-04 17:24:13.596 [33m- INFO[0;39m - [192.168.0.2] - [34m35048[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 17:28:01.133 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 17:28:13.676 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 17:28:15.724 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 17:28:32.254 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 34.493 seconds (JVM running for 36.31)
2025-08-04 17:28:32.277 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 17:28:37.552 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 17:28:46.723 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 17:28:47.166 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:828] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:28:47.168 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:828] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:28:47.169 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:526] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 17:28:47.170 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] : 🔍 时间解析调试:
2025-08-04 17:28:47.171 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 触发时间字符串: 02/03/2025,11:23:39.911000
2025-08-04 17:28:47.172 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 解析后触发时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:28:47.174 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 波形开始时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 17:28:47.175 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:467] :   - 触发时间戳: 1738553019911
2025-08-04 17:28:47.176 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:468] :   - 开始时间戳: 1738553019811
2025-08-04 17:28:47.177 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:473] :   - 原始时间差: 100ms
2025-08-04 17:28:47.178 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] : 🔍 时间计算详细过程:
2025-08-04 17:28:47.178 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 时间差: 100ms = 0.1秒
2025-08-04 17:28:47.179 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 采样率: 25600.0Hz
2025-08-04 17:28:47.180 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 触发采样点索引: 2560
2025-08-04 17:28:47.181 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:489] :   - 窗口大小: 512
2025-08-04 17:28:47.182 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:490] :   - 计算的RMS索引: 5
2025-08-04 17:28:47.183 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:505] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100ms ({:.3f}s) -> 采样点索引: 0.1 -> RMS索引: 2560
2025-08-04 17:28:47.184 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 30, 窗口大小: 512, 采样率: 25600.0
2025-08-04 17:28:47.184 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:584] : 计算得到额定电压: 5999.349154990429 V (基于索引 0 到 4)
2025-08-04 17:28:47.185 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:843] : 🔍 RMS数据调试 (额定电压: 5999.349154990429 V):
2025-08-04 17:28:47.186 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:844] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 17:28:47.188 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 2	{:.2f}	{:.2f}	{:.2f}	5740.559209258767
2025-08-04 17:28:47.189 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 3	{:.2f}	{:.2f}	{:.2f}	5727.657203902604
2025-08-04 17:28:47.190 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 4	{:.2f}	{:.2f}	{:.2f}	5721.697916024922
2025-08-04 17:28:47.191 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 5	{:.2f}	{:.2f}	{:.2f}	3529.1532749148905
2025-08-04 17:28:47.192 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 6	{:.2f}	{:.2f}	{:.2f}	3634.8555960309445
2025-08-04 17:28:47.193 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 7	{:.2f}	{:.2f}	{:.2f}	5463.8810723335255
2025-08-04 17:28:47.194 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 8	{:.2f}	{:.2f}	{:.2f}	5897.164084763231
2025-08-04 17:28:47.195 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 9	{:.2f}	{:.2f}	{:.2f}	5923.132658917508
2025-08-04 17:28:47.195 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 10	{:.2f}	{:.2f}	{:.2f}	5936.360601609818
2025-08-04 17:28:47.196 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 11	{:.2f}	{:.2f}	{:.2f}	5922.406645682151
2025-08-04 17:28:47.197 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 12	{:.2f}	{:.2f}	{:.2f}	5939.046147169264
2025-08-04 17:28:47.198 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 13	{:.2f}	{:.2f}	{:.2f}	5942.714756959242
2025-08-04 17:28:47.198 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 14	{:.2f}	{:.2f}	{:.2f}	5945.483308701551
2025-08-04 17:28:47.199 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 15	{:.2f}	{:.2f}	{:.2f}	5947.134057201715
2025-08-04 17:28:47.201 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:622] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-04 17:28:47.202 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:711] : 🔍 简化结束点检测 - 触发RMS: 3529.1532749148905 V, 额定电压: 5999.349154990429 V, 恢复阈值: 299.9674577495215 V
2025-08-04 17:28:47.203 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:728] : 检测到恢复点: 索引 8, RMS: 5897.164084763231 V, 与额定电压差异: 102.1850702271986 V
2025-08-04 17:28:47.204 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:730] : 应用精确偏移: 8 -> 8.075
2025-08-04 17:28:47.205 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:796] : 🔍 幅值计算 - 暂降事件, 额定电压: 5999.349154990429 V, 事件极值: 3529.1532749148905 V, 幅值: {:.3f} ({:.1f}%)
2025-08-04 17:28:47.206 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 5, 结束索引: 8, 事件类型: SAG
2025-08-04 17:28:47.206 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:925] : 持续时间修正: 基础=60.0ms -> 修正后=64.49ms (修正因子=1.0748333333333333)
2025-08-04 17:28:47.207 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.06448999999999999秒, 转换后: 64.49毫秒
2025-08-04 17:28:47.208 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:337] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 5, 持续时间: 64ms, 幅值: 0.5882560230686424
2025-08-04 17:28:47.208 [33m- INFO[0;39m - [192.168.0.2] - [34m8580[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 64,幅值为：0.588256
2025-08-04 17:30:19.099 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 17:30:30.051 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 17:30:32.086 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 17:30:50.890 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 35.089 seconds (JVM running for 37.163)
2025-08-04 17:30:50.911 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 17:31:15.359 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 17:31:17.379 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 17:31:17.714 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:828] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 17:31:17.715 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:828] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 17:31:17.716 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:526] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 17:31:17.716 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] : 🔍 时间解析调试:
2025-08-04 17:31:17.717 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 触发时间字符串: 03/03/2025,14:28:23.388000
2025-08-04 17:31:17.718 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 解析后触发时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 17:31:17.718 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 波形开始时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 17:31:17.719 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:467] :   - 触发时间戳: 1740983303388
2025-08-04 17:31:17.719 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:468] :   - 开始时间戳: 1740983303308
2025-08-04 17:31:17.720 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:473] :   - 原始时间差: 80ms
2025-08-04 17:31:17.720 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] : 🔍 时间计算详细过程:
2025-08-04 17:31:17.721 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 时间差: 80ms = 0.08秒
2025-08-04 17:31:17.722 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 采样率: 25614.3046875Hz
2025-08-04 17:31:17.722 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 触发采样点索引: 2049
2025-08-04 17:31:17.724 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:489] :   - 窗口大小: 512
2025-08-04 17:31:17.725 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:490] :   - 计算的RMS索引: 4
2025-08-04 17:31:17.726 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:505] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 17:31:17.727 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 16, 窗口大小: 512, 采样率: 25614.3046875
2025-08-04 17:31:17.727 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:584] : 计算得到额定电压: 5917.745846319004 V (基于索引 0 到 3)
2025-08-04 17:31:17.728 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:843] : 🔍 RMS数据调试 (额定电压: 5917.745846319004 V):
2025-08-04 17:31:17.729 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:844] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 17:31:17.729 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 1	{:.2f}	{:.2f}	{:.2f}	5874.845683127932
2025-08-04 17:31:17.730 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 2	{:.2f}	{:.2f}	{:.2f}	5840.5352416100695
2025-08-04 17:31:17.731 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 3	{:.2f}	{:.2f}	{:.2f}	5161.38282799108
2025-08-04 17:31:17.731 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 4	{:.2f}	{:.2f}	{:.2f}	5039.138883751789
2025-08-04 17:31:17.732 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 5	{:.2f}	{:.2f}	{:.2f}	5517.422905126656
2025-08-04 17:31:17.732 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 6	{:.2f}	{:.2f}	{:.2f}	5022.6623064928135
2025-08-04 17:31:17.733 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 7	{:.2f}	{:.2f}	{:.2f}	3580.5168588361485
2025-08-04 17:31:17.733 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 8	{:.2f}	{:.2f}	{:.2f}	3063.418667009718
2025-08-04 17:31:17.734 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 9	{:.2f}	{:.2f}	{:.2f}	4199.033098530223
2025-08-04 17:31:17.734 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 10	{:.2f}	{:.2f}	{:.2f}	5775.745270285633
2025-08-04 17:31:17.735 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 11	{:.2f}	{:.2f}	{:.2f}	5818.969654817706
2025-08-04 17:31:17.735 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 12	{:.2f}	{:.2f}	{:.2f}	5858.407545195888
2025-08-04 17:31:17.736 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 13	{:.2f}	{:.2f}	{:.2f}	5892.308194035314
2025-08-04 17:31:17.736 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:850] : 14	{:.2f}	{:.2f}	{:.2f}	5910.5095261850265
2025-08-04 17:31:17.738 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:622] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 17:31:17.738 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:711] : 🔍 简化结束点检测 - 触发RMS: 5039.138883751789 V, 额定电压: 5917.745846319004 V, 恢复阈值: 295.8872923159502 V
2025-08-04 17:31:17.739 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:728] : 检测到恢复点: 索引 10, RMS: 5775.745270285633 V, 与额定电压差异: 142.00057603337154 V
2025-08-04 17:31:17.741 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:730] : 应用精确偏移: 10 -> 10.075
2025-08-04 17:31:17.742 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:796] : 🔍 幅值计算 - 暂降事件, 额定电压: 5917.745846319004 V, 事件极值: 3063.418667009718 V, 幅值: {:.3f} ({:.1f}%)
2025-08-04 17:31:17.743 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 4, 结束索引: 10, 事件类型: SAG
2025-08-04 17:31:17.744 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:931] : 持续时间计算: 触发点采样索引=2048, 结束点采样索引=5120, 持续时间={:.3f}秒
2025-08-04 17:31:17.745 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.11993298422420821秒, 转换后: 119.93298422420821毫秒
2025-08-04 17:31:17.745 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:337] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 119ms, 幅值: 0.5176664808805951
2025-08-04 17:31:17.746 [33m- INFO[0;39m - [192.168.0.2] - [34m12408[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 119,幅值为：0.51766646
2025-08-04 18:01:29.830 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 18:01:40.214 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 18:01:41.997 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 18:01:56.649 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 29.735 seconds (JVM running for 31.241)
2025-08-04 18:01:56.665 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 18:02:02.393 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 18:05:07.943 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 18:05:08.236 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:907] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:05:08.237 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:907] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:05:08.238 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:532] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 18:05:08.238 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:469] : 🔍 时间解析调试:
2025-08-04 18:05:08.239 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:470] :   - 触发时间字符串: 03/03/2025,14:28:23.388000
2025-08-04 18:05:08.239 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 解析后触发时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:05:08.240 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:472] :   - 波形开始时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:05:08.241 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:473] :   - 触发时间戳: 1740983303388
2025-08-04 18:05:08.242 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] :   - 开始时间戳: 1740983303308
2025-08-04 18:05:08.242 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:479] :   - 原始时间差: 80ms
2025-08-04 18:05:08.243 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:491] : 🔍 时间计算详细过程:
2025-08-04 18:05:08.243 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:492] :   - 时间差: 80ms = 0.08秒
2025-08-04 18:05:08.244 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:493] :   - 采样率: 25614.3046875Hz
2025-08-04 18:05:08.245 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] :   - 触发采样点索引: 2049
2025-08-04 18:05:08.246 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:495] :   - 窗口大小: 512
2025-08-04 18:05:08.246 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:496] :   - 计算的RMS索引: 4
2025-08-04 18:05:08.247 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:511] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 18:05:08.247 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 16, 窗口大小: 512, 采样率: 25614.3046875
2025-08-04 18:05:08.248 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:590] : 计算得到额定电压: 5917.745846319004 V (基于索引 0 到 3)
2025-08-04 18:05:08.249 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:922] : 🔍 RMS数据调试 (额定电压: 5917.745846319004 V):
2025-08-04 18:05:08.249 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:923] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 18:05:08.250 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 1	{:.2f}	{:.2f}	{:.2f}	5874.845683127932
2025-08-04 18:05:08.250 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 2	{:.2f}	{:.2f}	{:.2f}	5840.5352416100695
2025-08-04 18:05:08.251 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 3	{:.2f}	{:.2f}	{:.2f}	5161.38282799108
2025-08-04 18:05:08.251 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 4	{:.2f}	{:.2f}	{:.2f}	5039.138883751789
2025-08-04 18:05:08.252 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 5	{:.2f}	{:.2f}	{:.2f}	5517.422905126656
2025-08-04 18:05:08.253 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 6	{:.2f}	{:.2f}	{:.2f}	5022.6623064928135
2025-08-04 18:05:08.253 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 7	{:.2f}	{:.2f}	{:.2f}	3580.5168588361485
2025-08-04 18:05:08.254 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 8	{:.2f}	{:.2f}	{:.2f}	3063.418667009718
2025-08-04 18:05:08.254 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 9	{:.2f}	{:.2f}	{:.2f}	4199.033098530223
2025-08-04 18:05:08.255 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 10	{:.2f}	{:.2f}	{:.2f}	5775.745270285633
2025-08-04 18:05:08.255 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 11	{:.2f}	{:.2f}	{:.2f}	5818.969654817706
2025-08-04 18:05:08.256 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 12	{:.2f}	{:.2f}	{:.2f}	5858.407545195888
2025-08-04 18:05:08.256 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 13	{:.2f}	{:.2f}	{:.2f}	5892.308194035314
2025-08-04 18:05:08.256 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 14	{:.2f}	{:.2f}	{:.2f}	5910.5095261850265
2025-08-04 18:05:08.258 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:628] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 18:05:08.259 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:669] : 🔍 基于40ms的精确计算:
2025-08-04 18:05:08.259 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:670] :   - 期望持续时间: 40.0ms
2025-08-04 18:05:08.260 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:671] :   - 期望采样点数: 1024
2025-08-04 18:05:08.261 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:672] :   - 期望RMS点数: 2
2025-08-04 18:05:08.262 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:673] :   - 搜索范围: 3 到 8
2025-08-04 18:05:08.263 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:710] : 🔍 精确事件计算结果:
2025-08-04 18:05:08.263 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:711] :   - 事件范围: 4 到 6 (实际持续2个RMS点)
2025-08-04 18:05:08.264 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:712] :   - 事件类型: SAG
2025-08-04 18:05:08.264 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:713] :   - 极值: 5022.6623064928135 V
2025-08-04 18:05:08.265 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:714] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 18:05:08.265 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 4, 结束索引: 6, 事件类型: SAG
2025-08-04 18:05:08.267 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:332] : 🔍 使用标准事件持续时间: 40.0ms
2025-08-04 18:05:08.267 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:343] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 40ms, 幅值: 0.848745863193338
2025-08-04 18:05:08.267 [33m- INFO[0;39m - [192.168.0.2] - [34m24976[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40,幅值为：0.8487459
2025-08-04 18:06:24.275 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 18:06:35.021 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 18:06:36.730 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 18:06:50.911 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 29.649 seconds (JVM running for 31.234)
2025-08-04 18:06:50.931 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 18:06:56.498 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 18:07:01.627 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 18:07:01.960 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:907] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:07:01.961 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:907] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:07:01.961 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:532] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 18:07:01.962 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:469] : 🔍 时间解析调试:
2025-08-04 18:07:01.962 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:470] :   - 触发时间字符串: 02/03/2025,11:23:39.911000
2025-08-04 18:07:01.963 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 解析后触发时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:07:01.964 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:472] :   - 波形开始时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:07:01.964 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:473] :   - 触发时间戳: 1738553019911
2025-08-04 18:07:01.965 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] :   - 开始时间戳: 1738553019811
2025-08-04 18:07:01.966 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:479] :   - 原始时间差: 100ms
2025-08-04 18:07:01.966 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:491] : 🔍 时间计算详细过程:
2025-08-04 18:07:01.967 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:492] :   - 时间差: 100ms = 0.1秒
2025-08-04 18:07:01.967 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:493] :   - 采样率: 25600.0Hz
2025-08-04 18:07:01.968 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] :   - 触发采样点索引: 2560
2025-08-04 18:07:01.969 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:495] :   - 窗口大小: 512
2025-08-04 18:07:01.969 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:496] :   - 计算的RMS索引: 5
2025-08-04 18:07:01.970 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:511] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100ms ({:.3f}s) -> 采样点索引: 0.1 -> RMS索引: 2560
2025-08-04 18:07:01.971 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 30, 窗口大小: 512, 采样率: 25600.0
2025-08-04 18:07:01.971 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:590] : 计算得到额定电压: 5999.349154990429 V (基于索引 0 到 4)
2025-08-04 18:07:01.972 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:922] : 🔍 RMS数据调试 (额定电压: 5999.349154990429 V):
2025-08-04 18:07:01.972 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:923] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 18:07:01.973 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 2	{:.2f}	{:.2f}	{:.2f}	5740.559209258767
2025-08-04 18:07:01.974 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 3	{:.2f}	{:.2f}	{:.2f}	5727.657203902604
2025-08-04 18:07:01.974 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 4	{:.2f}	{:.2f}	{:.2f}	5721.697916024922
2025-08-04 18:07:01.975 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 5	{:.2f}	{:.2f}	{:.2f}	3529.1532749148905
2025-08-04 18:07:01.975 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 6	{:.2f}	{:.2f}	{:.2f}	3634.8555960309445
2025-08-04 18:07:01.976 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 7	{:.2f}	{:.2f}	{:.2f}	5463.8810723335255
2025-08-04 18:07:01.976 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 8	{:.2f}	{:.2f}	{:.2f}	5897.164084763231
2025-08-04 18:07:01.977 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 9	{:.2f}	{:.2f}	{:.2f}	5923.132658917508
2025-08-04 18:07:01.977 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 10	{:.2f}	{:.2f}	{:.2f}	5936.360601609818
2025-08-04 18:07:01.978 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 11	{:.2f}	{:.2f}	{:.2f}	5922.406645682151
2025-08-04 18:07:01.979 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 12	{:.2f}	{:.2f}	{:.2f}	5939.046147169264
2025-08-04 18:07:01.979 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 13	{:.2f}	{:.2f}	{:.2f}	5942.714756959242
2025-08-04 18:07:01.980 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 14	{:.2f}	{:.2f}	{:.2f}	5945.483308701551
2025-08-04 18:07:01.980 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 15	{:.2f}	{:.2f}	{:.2f}	5947.134057201715
2025-08-04 18:07:01.982 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:628] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-04 18:07:01.982 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:669] : 🔍 基于40ms的精确计算:
2025-08-04 18:07:01.983 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:670] :   - 期望持续时间: 40.0ms
2025-08-04 18:07:01.983 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:671] :   - 期望采样点数: 1024
2025-08-04 18:07:01.984 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:672] :   - 期望RMS点数: 2
2025-08-04 18:07:01.985 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:673] :   - 搜索范围: 4 到 9
2025-08-04 18:07:01.985 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:710] : 🔍 精确事件计算结果:
2025-08-04 18:07:01.986 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:711] :   - 事件范围: 5 到 7 (实际持续2个RMS点)
2025-08-04 18:07:01.987 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:712] :   - 事件类型: SAG
2025-08-04 18:07:01.987 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:713] :   - 极值: 3529.1532749148905 V
2025-08-04 18:07:01.988 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:714] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 18:07:01.988 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 5, 结束索引: 7, 事件类型: SAG
2025-08-04 18:07:01.989 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:332] : 🔍 使用标准事件持续时间: 40.0ms
2025-08-04 18:07:01.990 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:343] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 5, 持续时间: 40ms, 幅值: 0.5882560230686424
2025-08-04 18:07:01.990 [33m- INFO[0;39m - [192.168.0.2] - [34m16812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40,幅值为：0.588256
2025-08-04 18:32:56.729 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 18:33:06.622 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 18:33:08.357 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 18:33:22.834 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.881 seconds (JVM running for 30.237)
2025-08-04 18:33:22.852 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 18:33:27.745 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 18:33:53.190 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 18:33:53.516 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:907] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:33:53.517 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:907] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:33:53.518 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:524] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 18:33:53.518 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:461] : 🔍 时间解析调试:
2025-08-04 18:33:53.519 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:462] :   - 触发时间字符串: 02/03/2025,11:23:39.911000
2025-08-04 18:33:53.520 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] :   - 解析后触发时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:33:53.520 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 波形开始时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:33:53.521 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 触发时间戳: 1738553019911
2025-08-04 18:33:53.521 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 开始时间戳: 1738553019811
2025-08-04 18:33:53.522 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 原始时间差: 100ms
2025-08-04 18:33:53.523 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:483] : 🔍 时间计算详细过程:
2025-08-04 18:33:53.523 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:484] :   - 时间差: 100ms = 0.1秒
2025-08-04 18:33:53.524 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] :   - 采样率: 25600.0Hz
2025-08-04 18:33:53.524 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 触发采样点索引: 2560
2025-08-04 18:33:53.525 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 窗口大小: 512
2025-08-04 18:33:53.525 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 计算的RMS索引: 5
2025-08-04 18:33:53.526 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100ms ({:.3f}s) -> 采样点索引: 0.1 -> RMS索引: 2560
2025-08-04 18:33:53.527 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 30, 窗口大小: 512, 采样率: 25600.0
2025-08-04 18:33:53.528 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:582] : 计算得到额定电压: 5999.349154990429 V (基于索引 0 到 4)
2025-08-04 18:33:53.530 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:922] : 🔍 RMS数据调试 (额定电压: 5999.349154990429 V):
2025-08-04 18:33:53.530 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:923] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 18:33:53.531 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 2	{:.2f}	{:.2f}	{:.2f}	5740.559209258767
2025-08-04 18:33:53.532 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 3	{:.2f}	{:.2f}	{:.2f}	5727.657203902604
2025-08-04 18:33:53.532 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 4	{:.2f}	{:.2f}	{:.2f}	5721.697916024922
2025-08-04 18:33:53.533 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 5	{:.2f}	{:.2f}	{:.2f}	3529.1532749148905
2025-08-04 18:33:53.533 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 6	{:.2f}	{:.2f}	{:.2f}	3634.8555960309445
2025-08-04 18:33:53.534 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 7	{:.2f}	{:.2f}	{:.2f}	5463.8810723335255
2025-08-04 18:33:53.534 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 8	{:.2f}	{:.2f}	{:.2f}	5897.164084763231
2025-08-04 18:33:53.535 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 9	{:.2f}	{:.2f}	{:.2f}	5923.132658917508
2025-08-04 18:33:53.536 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 10	{:.2f}	{:.2f}	{:.2f}	5936.360601609818
2025-08-04 18:33:53.536 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 11	{:.2f}	{:.2f}	{:.2f}	5922.406645682151
2025-08-04 18:33:53.537 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 12	{:.2f}	{:.2f}	{:.2f}	5939.046147169264
2025-08-04 18:33:53.537 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 13	{:.2f}	{:.2f}	{:.2f}	5942.714756959242
2025-08-04 18:33:53.538 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 14	{:.2f}	{:.2f}	{:.2f}	5945.483308701551
2025-08-04 18:33:53.538 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:929] : 15	{:.2f}	{:.2f}	{:.2f}	5947.134057201715
2025-08-04 18:33:53.540 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-04 18:33:53.540 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 🔍 智能事件边界检测:
2025-08-04 18:33:53.541 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:659] :   - 触发点RMS: 3529.1532749148905 V
2025-08-04 18:33:53.541 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:660] :   - 额定电压: 5999.349154990429 V
2025-08-04 18:33:53.542 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:661] :   - 触发点偏差: 2470.1958800755388 V
2025-08-04 18:33:53.543 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:662] :   - 偏差阈值: 299.9674577495215 V
2025-08-04 18:33:53.543 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:686] :   - 向后扩展到索引 6, RMS: 3634.8555960309445 V, 偏差: 2364.4935589594847 V
2025-08-04 18:33:53.544 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:688] :   - 在索引 7 找到恢复点, RMS: 5463.8810723335255 V, 偏差: 535.4680826569038 V
2025-08-04 18:33:53.544 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:710] : 🔍 智能检测结果:
2025-08-04 18:33:53.545 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:711] :   - 事件范围: 5 到 6 (持续2个RMS点)
2025-08-04 18:33:53.546 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:712] :   - 事件类型: SAG
2025-08-04 18:33:53.547 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:713] :   - 极值: 3529.1532749148905 V
2025-08-04 18:33:53.547 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:714] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 18:33:53.548 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 5, 结束索引: 6, 事件类型: SAG
2025-08-04 18:33:53.548 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:996] : 持续时间计算: 触发点采样索引=2560, 结束点采样索引=3072, 持续时间={:.3f}秒
2025-08-04 18:33:53.549 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.02秒, 转换后: 20.0毫秒
2025-08-04 18:33:53.549 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:335] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 5, 持续时间: 20ms, 幅值: 0.5882560230686424
2025-08-04 18:33:53.550 [33m- INFO[0;39m - [192.168.0.2] - [34m24624[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-10][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 20,幅值为：0.588256
2025-08-04 18:38:01.407 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 18:38:11.359 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 18:38:13.237 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 18:38:27.279 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.403 seconds (JVM running for 29.653)
2025-08-04 18:38:27.299 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 18:38:32.185 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 18:40:11.914 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 18:40:12.286 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:960] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:40:12.287 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:960] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:40:12.287 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:524] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 18:40:12.288 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:461] : 🔍 时间解析调试:
2025-08-04 18:40:12.289 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:462] :   - 触发时间字符串: 02/03/2025,11:23:39.911000
2025-08-04 18:40:12.290 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] :   - 解析后触发时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:40:12.291 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 波形开始时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 18:40:12.292 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 触发时间戳: 1738553019911
2025-08-04 18:40:12.293 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 开始时间戳: 1738553019811
2025-08-04 18:40:12.293 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 原始时间差: 100ms
2025-08-04 18:40:12.294 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:483] : 🔍 时间计算详细过程:
2025-08-04 18:40:12.295 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:484] :   - 时间差: 100ms = 0.1秒
2025-08-04 18:40:12.297 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] :   - 采样率: 25600.0Hz
2025-08-04 18:40:12.298 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 触发采样点索引: 2560
2025-08-04 18:40:12.300 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 窗口大小: 512
2025-08-04 18:40:12.301 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 计算的RMS索引: 5
2025-08-04 18:40:12.302 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100ms ({:.3f}s) -> 采样点索引: 0.1 -> RMS索引: 2560
2025-08-04 18:40:12.303 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 30, 窗口大小: 512, 采样率: 25600.0
2025-08-04 18:40:12.305 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:582] : 计算得到额定电压: 5999.349154990429 V (基于索引 0 到 4)
2025-08-04 18:40:12.306 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:975] : 🔍 RMS数据调试 (额定电压: 5999.349154990429 V):
2025-08-04 18:40:12.308 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:976] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 18:40:12.308 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 2	{:.2f}	{:.2f}	{:.2f}	5740.559209258767
2025-08-04 18:40:12.309 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 3	{:.2f}	{:.2f}	{:.2f}	5727.657203902604
2025-08-04 18:40:12.310 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 4	{:.2f}	{:.2f}	{:.2f}	5721.697916024922
2025-08-04 18:40:12.311 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 5	{:.2f}	{:.2f}	{:.2f}	3529.1532749148905
2025-08-04 18:40:12.311 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 6	{:.2f}	{:.2f}	{:.2f}	3634.8555960309445
2025-08-04 18:40:12.312 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 7	{:.2f}	{:.2f}	{:.2f}	5463.8810723335255
2025-08-04 18:40:12.314 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 8	{:.2f}	{:.2f}	{:.2f}	5897.164084763231
2025-08-04 18:40:12.315 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 9	{:.2f}	{:.2f}	{:.2f}	5923.132658917508
2025-08-04 18:40:12.316 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 10	{:.2f}	{:.2f}	{:.2f}	5936.360601609818
2025-08-04 18:40:12.316 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 11	{:.2f}	{:.2f}	{:.2f}	5922.406645682151
2025-08-04 18:40:12.317 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 12	{:.2f}	{:.2f}	{:.2f}	5939.046147169264
2025-08-04 18:40:12.318 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 13	{:.2f}	{:.2f}	{:.2f}	5942.714756959242
2025-08-04 18:40:12.319 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 14	{:.2f}	{:.2f}	{:.2f}	5945.483308701551
2025-08-04 18:40:12.319 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 15	{:.2f}	{:.2f}	{:.2f}	5947.134057201715
2025-08-04 18:40:12.321 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-04 18:40:12.322 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 🔍 智能事件边界检测:
2025-08-04 18:40:12.324 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:659] :   - 触发点RMS: 3529.1532749148905 V
2025-08-04 18:40:12.325 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:660] :   - 额定电压: 5999.349154990429 V
2025-08-04 18:40:12.326 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:661] :   - 触发点偏差: 2470.1958800755388 V
2025-08-04 18:40:12.327 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:662] :   - 恢复阈值: 119.98698309980858 V (2%额定电压)
2025-08-04 18:40:12.328 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 6, RMS: 3634.8555960309445 V, 偏差: 2364.4935589594847 V
2025-08-04 18:40:12.329 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 7, RMS: 5463.8810723335255 V, 偏差: 535.4680826569038 V
2025-08-04 18:40:12.329 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] :   - 在索引 8 确认事件结束 (连续2个恢复点), RMS: 5923.132658917508 V, 偏差: 76.21649607292147 V
2025-08-04 18:40:12.331 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:728] : 🔍 智能检测结果:
2025-08-04 18:40:12.332 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:729] :   - 事件范围: 5 到 7 (持续3个RMS点)
2025-08-04 18:40:12.332 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:730] :   - 计算持续时间: {:.1f}ms
2025-08-04 18:40:12.333 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:731] :   - 事件类型: SAG
2025-08-04 18:40:12.334 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:732] :   - 极值: 3529.1532749148905 V
2025-08-04 18:40:12.334 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 18:40:12.335 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 5, 结束索引: 7, 事件类型: SAG
2025-08-04 18:40:12.335 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1049] : 持续时间计算: 触发点采样索引=2560, 结束点采样索引=3584, 持续时间={:.3f}秒
2025-08-04 18:40:12.336 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.04秒, 转换后: 40.0毫秒
2025-08-04 18:40:12.337 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:335] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 5, 持续时间: 40ms, 幅值: 0.5882560230686424
2025-08-04 18:40:12.338 [33m- INFO[0;39m - [192.168.0.2] - [34m43812[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 40,幅值为：0.588256
2025-08-04 18:47:59.785 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 18:48:09.924 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 18:48:11.695 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 18:48:26.724 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 29.507 seconds (JVM running for 30.977)
2025-08-04 18:48:26.743 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 18:48:31.121 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 18:49:04.264 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 18:49:04.575 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:960] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:04.576 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:960] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:04.577 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:524] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 18:49:04.578 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:461] : 🔍 时间解析调试:
2025-08-04 18:49:04.579 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:462] :   - 触发时间字符串: 03/03/2025,14:28:23.388000
2025-08-04 18:49:04.579 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] :   - 解析后触发时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:04.580 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 波形开始时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:04.581 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 触发时间戳: 1740983303388
2025-08-04 18:49:04.582 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 开始时间戳: 1740983303308
2025-08-04 18:49:04.583 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 原始时间差: 80ms
2025-08-04 18:49:04.584 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:483] : 🔍 时间计算详细过程:
2025-08-04 18:49:04.586 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:484] :   - 时间差: 80ms = 0.08秒
2025-08-04 18:49:04.588 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] :   - 采样率: 25614.3046875Hz
2025-08-04 18:49:04.589 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 触发采样点索引: 2049
2025-08-04 18:49:04.590 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 窗口大小: 512
2025-08-04 18:49:04.591 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 计算的RMS索引: 4
2025-08-04 18:49:04.592 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 18:49:04.592 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 16, 窗口大小: 512, 采样率: 25614.3046875
2025-08-04 18:49:04.593 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:582] : 计算得到额定电压: 5917.745846319004 V (基于索引 0 到 3)
2025-08-04 18:49:04.593 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:975] : 🔍 RMS数据调试 (额定电压: 5917.745846319004 V):
2025-08-04 18:49:04.594 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:976] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 18:49:04.595 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 1	{:.2f}	{:.2f}	{:.2f}	5874.845683127932
2025-08-04 18:49:04.595 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 2	{:.2f}	{:.2f}	{:.2f}	5840.5352416100695
2025-08-04 18:49:04.596 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 3	{:.2f}	{:.2f}	{:.2f}	5161.38282799108
2025-08-04 18:49:04.596 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 4	{:.2f}	{:.2f}	{:.2f}	5039.138883751789
2025-08-04 18:49:04.597 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 5	{:.2f}	{:.2f}	{:.2f}	5517.422905126656
2025-08-04 18:49:04.598 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 6	{:.2f}	{:.2f}	{:.2f}	5022.6623064928135
2025-08-04 18:49:04.599 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 7	{:.2f}	{:.2f}	{:.2f}	3580.5168588361485
2025-08-04 18:49:04.600 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 8	{:.2f}	{:.2f}	{:.2f}	3063.418667009718
2025-08-04 18:49:04.601 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 9	{:.2f}	{:.2f}	{:.2f}	4199.033098530223
2025-08-04 18:49:04.602 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 10	{:.2f}	{:.2f}	{:.2f}	5775.745270285633
2025-08-04 18:49:04.603 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 11	{:.2f}	{:.2f}	{:.2f}	5818.969654817706
2025-08-04 18:49:04.604 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 12	{:.2f}	{:.2f}	{:.2f}	5858.407545195888
2025-08-04 18:49:04.604 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 13	{:.2f}	{:.2f}	{:.2f}	5892.308194035314
2025-08-04 18:49:04.605 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 14	{:.2f}	{:.2f}	{:.2f}	5910.5095261850265
2025-08-04 18:49:04.606 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 18:49:04.607 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 🔍 智能事件边界检测:
2025-08-04 18:49:04.608 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:659] :   - 触发点RMS: 5039.138883751789 V
2025-08-04 18:49:04.608 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:660] :   - 额定电压: 5917.745846319004 V
2025-08-04 18:49:04.609 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:661] :   - 触发点偏差: 878.6069625672153 V
2025-08-04 18:49:04.609 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:662] :   - 恢复阈值: 118.35491692638008 V (2%额定电压)
2025-08-04 18:49:04.610 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:673] :   - 向前扩展到索引 3, RMS: 5161.38282799108 V, 偏差: 756.3630183279238 V
2025-08-04 18:49:04.610 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 5, RMS: 5517.422905126656 V, 偏差: 400.3229411923485 V
2025-08-04 18:49:04.611 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 6, RMS: 5022.6623064928135 V, 偏差: 895.0835398261906 V
2025-08-04 18:49:04.612 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 7, RMS: 3580.5168588361485 V, 偏差: 2337.2289874828557 V
2025-08-04 18:49:04.612 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 8, RMS: 3063.418667009718 V, 偏差: 2854.3271793092863 V
2025-08-04 18:49:04.613 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 9, RMS: 4199.033098530223 V, 偏差: 1718.7127477887816 V
2025-08-04 18:49:04.613 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:704] :   - 索引 10 部分恢复, RMS: 5775.745270285633 V, 偏差: 142.00057603337154 V
2025-08-04 18:49:04.614 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] :   - 在索引 11 确认事件结束 (连续2个恢复点), RMS: 5858.407545195888 V, 偏差: 59.33830112311625 V
2025-08-04 18:49:04.614 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:728] : 🔍 智能检测结果:
2025-08-04 18:49:04.615 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:729] :   - 事件范围: 3 到 9 (持续7个RMS点)
2025-08-04 18:49:04.615 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:730] :   - 计算持续时间: {:.1f}ms
2025-08-04 18:49:04.616 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:731] :   - 事件类型: SAG
2025-08-04 18:49:04.617 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:732] :   - 极值: 3063.418667009718 V
2025-08-04 18:49:04.618 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 18:49:04.618 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 4, 结束索引: 9, 事件类型: SAG
2025-08-04 18:49:04.619 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1049] : 持续时间计算: 触发点采样索引=2048, 结束点采样索引=4608, 持续时间={:.3f}秒
2025-08-04 18:49:04.620 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.09994415352017351秒, 转换后: 99.94415352017351毫秒
2025-08-04 18:49:04.621 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:335] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 99ms, 幅值: 0.5176664808805951
2025-08-04 18:49:04.621 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 99,幅值为：0.51766646
2025-08-04 18:49:14.543 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 18:49:14.747 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:960] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:14.748 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:960] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:14.749 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:524] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 18:49:14.750 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:461] : 🔍 时间解析调试:
2025-08-04 18:49:14.751 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:462] :   - 触发时间字符串: 03/03/2025,14:28:23.388000
2025-08-04 18:49:14.751 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] :   - 解析后触发时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:14.752 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 波形开始时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:49:14.753 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 触发时间戳: 1740983303388
2025-08-04 18:49:14.754 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 开始时间戳: 1740983303308
2025-08-04 18:49:14.754 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 原始时间差: 80ms
2025-08-04 18:49:14.755 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:483] : 🔍 时间计算详细过程:
2025-08-04 18:49:14.756 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:484] :   - 时间差: 80ms = 0.08秒
2025-08-04 18:49:14.756 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] :   - 采样率: 25614.3046875Hz
2025-08-04 18:49:14.757 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 触发采样点索引: 2049
2025-08-04 18:49:14.757 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 窗口大小: 512
2025-08-04 18:49:14.758 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 计算的RMS索引: 4
2025-08-04 18:49:14.758 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 18:49:14.759 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 16, 窗口大小: 512, 采样率: 25614.3046875
2025-08-04 18:49:14.760 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:582] : 计算得到额定电压: 5917.745846319004 V (基于索引 0 到 3)
2025-08-04 18:49:14.761 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:975] : 🔍 RMS数据调试 (额定电压: 5917.745846319004 V):
2025-08-04 18:49:14.762 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:976] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 18:49:14.763 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 1	{:.2f}	{:.2f}	{:.2f}	5874.845683127932
2025-08-04 18:49:14.763 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 2	{:.2f}	{:.2f}	{:.2f}	5840.5352416100695
2025-08-04 18:49:14.764 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 3	{:.2f}	{:.2f}	{:.2f}	5161.38282799108
2025-08-04 18:49:14.765 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 4	{:.2f}	{:.2f}	{:.2f}	5039.138883751789
2025-08-04 18:49:14.765 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 5	{:.2f}	{:.2f}	{:.2f}	5517.422905126656
2025-08-04 18:49:14.766 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 6	{:.2f}	{:.2f}	{:.2f}	5022.6623064928135
2025-08-04 18:49:14.766 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 7	{:.2f}	{:.2f}	{:.2f}	3580.5168588361485
2025-08-04 18:49:14.767 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 8	{:.2f}	{:.2f}	{:.2f}	3063.418667009718
2025-08-04 18:49:14.768 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 9	{:.2f}	{:.2f}	{:.2f}	4199.033098530223
2025-08-04 18:49:14.769 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 10	{:.2f}	{:.2f}	{:.2f}	5775.745270285633
2025-08-04 18:49:14.769 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 11	{:.2f}	{:.2f}	{:.2f}	5818.969654817706
2025-08-04 18:49:14.770 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 12	{:.2f}	{:.2f}	{:.2f}	5858.407545195888
2025-08-04 18:49:14.770 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 13	{:.2f}	{:.2f}	{:.2f}	5892.308194035314
2025-08-04 18:49:14.771 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:982] : 14	{:.2f}	{:.2f}	{:.2f}	5910.5095261850265
2025-08-04 18:49:14.771 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 18:49:14.772 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 🔍 智能事件边界检测:
2025-08-04 18:49:14.773 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:659] :   - 触发点RMS: 5039.138883751789 V
2025-08-04 18:49:14.773 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:660] :   - 额定电压: 5917.745846319004 V
2025-08-04 18:49:14.774 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:661] :   - 触发点偏差: 878.6069625672153 V
2025-08-04 18:49:14.775 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:662] :   - 恢复阈值: 118.35491692638008 V (2%额定电压)
2025-08-04 18:49:14.775 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:673] :   - 向前扩展到索引 3, RMS: 5161.38282799108 V, 偏差: 756.3630183279238 V
2025-08-04 18:49:14.776 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 5, RMS: 5517.422905126656 V, 偏差: 400.3229411923485 V
2025-08-04 18:49:14.776 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 6, RMS: 5022.6623064928135 V, 偏差: 895.0835398261906 V
2025-08-04 18:49:14.778 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 7, RMS: 3580.5168588361485 V, 偏差: 2337.2289874828557 V
2025-08-04 18:49:14.779 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 8, RMS: 3063.418667009718 V, 偏差: 2854.3271793092863 V
2025-08-04 18:49:14.780 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:701] :   - 向后扩展到索引 9, RMS: 4199.033098530223 V, 偏差: 1718.7127477887816 V
2025-08-04 18:49:14.781 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:704] :   - 索引 10 部分恢复, RMS: 5775.745270285633 V, 偏差: 142.00057603337154 V
2025-08-04 18:49:14.782 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] :   - 在索引 11 确认事件结束 (连续2个恢复点), RMS: 5858.407545195888 V, 偏差: 59.33830112311625 V
2025-08-04 18:49:14.783 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:728] : 🔍 智能检测结果:
2025-08-04 18:49:14.783 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:729] :   - 事件范围: 3 到 9 (持续7个RMS点)
2025-08-04 18:49:14.784 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:730] :   - 计算持续时间: {:.1f}ms
2025-08-04 18:49:14.785 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:731] :   - 事件类型: SAG
2025-08-04 18:49:14.786 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:732] :   - 极值: 3063.418667009718 V
2025-08-04 18:49:14.787 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 18:49:14.787 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 4, 结束索引: 9, 事件类型: SAG
2025-08-04 18:49:14.788 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1049] : 持续时间计算: 触发点采样索引=2048, 结束点采样索引=4608, 持续时间={:.3f}秒
2025-08-04 18:49:14.788 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.09994415352017351秒, 转换后: 99.94415352017351毫秒
2025-08-04 18:49:14.789 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:335] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 99ms, 幅值: 0.5176664808805951
2025-08-04 18:49:14.790 [33m- INFO[0;39m - [192.168.0.2] - [34m20616[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 99,幅值为：0.51766646
2025-08-04 18:57:44.164 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 18:57:54.052 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 18:57:55.775 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 18:58:10.652 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.964 seconds (JVM running for 30.341)
2025-08-04 18:58:10.669 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 18:58:16.143 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 18:58:24.804 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 18:58:25.102 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:957] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:58:25.104 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:957] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:58:25.105 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:524] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 18:58:25.105 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:461] : 🔍 时间解析调试:
2025-08-04 18:58:25.106 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:462] :   - 触发时间字符串: 03/03/2025,14:28:23.388000
2025-08-04 18:58:25.107 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] :   - 解析后触发时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:58:25.108 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 波形开始时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 18:58:25.108 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 触发时间戳: 1740983303388
2025-08-04 18:58:25.109 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 开始时间戳: 1740983303308
2025-08-04 18:58:25.110 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 原始时间差: 80ms
2025-08-04 18:58:25.111 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:483] : 🔍 时间计算详细过程:
2025-08-04 18:58:25.112 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:484] :   - 时间差: 80ms = 0.08秒
2025-08-04 18:58:25.113 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] :   - 采样率: 25614.3046875Hz
2025-08-04 18:58:25.114 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 触发采样点索引: 2049
2025-08-04 18:58:25.115 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 窗口大小: 512
2025-08-04 18:58:25.115 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 计算的RMS索引: 4
2025-08-04 18:58:25.116 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 18:58:25.117 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 16, 窗口大小: 512, 采样率: 25614.3046875
2025-08-04 18:58:25.119 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:582] : 计算得到额定电压: 5917.745846319004 V (基于索引 0 到 3)
2025-08-04 18:58:25.120 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:972] : 🔍 RMS数据调试 (额定电压: 5917.745846319004 V):
2025-08-04 18:58:25.121 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:973] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 18:58:25.122 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 1	{:.2f}	{:.2f}	{:.2f}	5874.845683127932
2025-08-04 18:58:25.122 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 2	{:.2f}	{:.2f}	{:.2f}	5840.5352416100695
2025-08-04 18:58:25.123 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 3	{:.2f}	{:.2f}	{:.2f}	5161.38282799108
2025-08-04 18:58:25.124 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 4	{:.2f}	{:.2f}	{:.2f}	5039.138883751789
2025-08-04 18:58:25.124 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 5	{:.2f}	{:.2f}	{:.2f}	5517.422905126656
2025-08-04 18:58:25.125 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 6	{:.2f}	{:.2f}	{:.2f}	5022.6623064928135
2025-08-04 18:58:25.126 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 7	{:.2f}	{:.2f}	{:.2f}	3580.5168588361485
2025-08-04 18:58:25.126 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 8	{:.2f}	{:.2f}	{:.2f}	3063.418667009718
2025-08-04 18:58:25.127 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 9	{:.2f}	{:.2f}	{:.2f}	4199.033098530223
2025-08-04 18:58:25.127 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 10	{:.2f}	{:.2f}	{:.2f}	5775.745270285633
2025-08-04 18:58:25.128 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 11	{:.2f}	{:.2f}	{:.2f}	5818.969654817706
2025-08-04 18:58:25.129 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 12	{:.2f}	{:.2f}	{:.2f}	5858.407545195888
2025-08-04 18:58:25.129 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 13	{:.2f}	{:.2f}	{:.2f}	5892.308194035314
2025-08-04 18:58:25.130 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:979] : 14	{:.2f}	{:.2f}	{:.2f}	5910.5095261850265
2025-08-04 18:58:25.131 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 18:58:25.132 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:658] : 🔍 智能事件边界检测:
2025-08-04 18:58:25.133 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:659] :   - 触发点RMS: 5039.138883751789 V
2025-08-04 18:58:25.133 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:660] :   - 额定电压: 5917.745846319004 V
2025-08-04 18:58:25.134 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:661] :   - 触发点偏差: 878.6069625672153 V
2025-08-04 18:58:25.135 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:662] :   - 恢复阈值: 118.35491692638008 V (2%额定电压)
2025-08-04 18:58:25.136 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:673] :   - 向前扩展到索引 3, RMS: 5161.38282799108 V, 偏差: 756.3630183279238 V
2025-08-04 18:58:25.136 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:689] :   - 向后扩展到索引 5, RMS: 5517.422905126656 V, 偏差: 400.3229411923485 V ({:.1f}%触发偏差)
2025-08-04 18:58:25.137 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:689] :   - 向后扩展到索引 6, RMS: 5022.6623064928135 V, 偏差: 895.0835398261906 V ({:.1f}%触发偏差)
2025-08-04 18:58:25.137 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:689] :   - 向后扩展到索引 7, RMS: 3580.5168588361485 V, 偏差: 2337.2289874828557 V ({:.1f}%触发偏差)
2025-08-04 18:58:25.138 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:689] :   - 向后扩展到索引 8, RMS: 3063.418667009718 V, 偏差: 2854.3271793092863 V ({:.1f}%触发偏差)
2025-08-04 18:58:25.138 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:689] :   - 向后扩展到索引 9, RMS: 4199.033098530223 V, 偏差: 1718.7127477887816 V ({:.1f}%触发偏差)
2025-08-04 18:58:25.139 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] :   - 在索引 10 检测到事件结束, RMS: 5775.745270285633 V, 偏差: 142.00057603337154 V ({:.1f}%触发偏差)
2025-08-04 18:58:25.140 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:725] : 🔍 智能检测结果:
2025-08-04 18:58:25.140 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:726] :   - 事件范围: 3 到 9 (持续7个RMS点)
2025-08-04 18:58:25.141 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:727] :   - 计算持续时间: {:.1f}ms
2025-08-04 18:58:25.141 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:728] :   - 事件类型: SAG
2025-08-04 18:58:25.142 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:729] :   - 极值: 3063.418667009718 V
2025-08-04 18:58:25.142 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:730] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 18:58:25.143 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 4, 结束索引: 9, 事件类型: SAG
2025-08-04 18:58:25.144 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1053] : 🔍 修正的持续时间计算:
2025-08-04 18:58:25.144 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1054] :   - 触发RMS索引: 4 -> 采样点索引: 2048
2025-08-04 18:58:25.145 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1055] :   - 结束RMS索引: 9 -> 采样点索引: 5119
2025-08-04 18:58:25.146 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1056] :   - 采样点范围: 2048 到 5119 (共3072个点)
2025-08-04 18:58:25.146 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1057] :   - 持续时间: {:.3f}秒 ({:.1f}ms)
2025-08-04 18:58:25.147 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.11993298422420821秒, 转换后: 119.93298422420821毫秒
2025-08-04 18:58:25.148 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:335] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 119ms, 幅值: 0.5176664808805951
2025-08-04 18:58:25.148 [33m- INFO[0;39m - [192.168.0.2] - [34m38860[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 119,幅值为：0.51766646
2025-08-04 19:04:48.196 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:04:58.085 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:04:59.895 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:05:14.763 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 29.131 seconds (JVM running for 30.453)
2025-08-04 19:05:14.781 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:05:20.088 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 19:05:26.546 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:05:26.953 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:978] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:05:26.954 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:978] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:05:26.955 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:524] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:05:26.956 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:461] : 🔍 时间解析调试:
2025-08-04 19:05:26.956 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:462] :   - 触发时间字符串: 03/03/2025,14:28:23.388000
2025-08-04 19:05:26.957 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] :   - 解析后触发时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:05:26.958 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 波形开始时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:05:26.958 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 触发时间戳: 1740983303388
2025-08-04 19:05:26.959 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 开始时间戳: 1740983303308
2025-08-04 19:05:26.960 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 原始时间差: 80ms
2025-08-04 19:05:26.961 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:483] : 🔍 时间计算详细过程:
2025-08-04 19:05:26.961 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:484] :   - 时间差: 80ms = 0.08秒
2025-08-04 19:05:26.962 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] :   - 采样率: 25614.3046875Hz
2025-08-04 19:05:26.963 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 触发采样点索引: 2049
2025-08-04 19:05:26.965 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 窗口大小: 512
2025-08-04 19:05:26.966 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 计算的RMS索引: 4
2025-08-04 19:05:26.967 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:05:26.968 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 16, 窗口大小: 512, 采样率: 25614.3046875
2025-08-04 19:05:26.968 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:582] : 计算得到额定电压: 5917.745846319004 V (110kV系统, 基于索引 0 到 3)
2025-08-04 19:05:26.969 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:993] : 🔍 RMS数据调试 (额定电压: 5917.745846319004 V):
2025-08-04 19:05:26.970 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:994] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 19:05:26.970 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 1	{:.2f}	{:.2f}	{:.2f}	5874.845683127932
2025-08-04 19:05:26.971 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 2	{:.2f}	{:.2f}	{:.2f}	5840.5352416100695
2025-08-04 19:05:26.971 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 3	{:.2f}	{:.2f}	{:.2f}	5161.38282799108
2025-08-04 19:05:26.972 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 4	{:.2f}	{:.2f}	{:.2f}	5039.138883751789
2025-08-04 19:05:26.973 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 5	{:.2f}	{:.2f}	{:.2f}	5517.422905126656
2025-08-04 19:05:26.973 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 6	{:.2f}	{:.2f}	{:.2f}	5022.6623064928135
2025-08-04 19:05:26.974 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 7	{:.2f}	{:.2f}	{:.2f}	3580.5168588361485
2025-08-04 19:05:26.974 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 8	{:.2f}	{:.2f}	{:.2f}	3063.418667009718
2025-08-04 19:05:26.975 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 9	{:.2f}	{:.2f}	{:.2f}	4199.033098530223
2025-08-04 19:05:26.975 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 10	{:.2f}	{:.2f}	{:.2f}	5775.745270285633
2025-08-04 19:05:26.976 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 11	{:.2f}	{:.2f}	{:.2f}	5818.969654817706
2025-08-04 19:05:26.977 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 12	{:.2f}	{:.2f}	{:.2f}	5858.407545195888
2025-08-04 19:05:26.977 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 13	{:.2f}	{:.2f}	{:.2f}	5892.308194035314
2025-08-04 19:05:26.978 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 14	{:.2f}	{:.2f}	{:.2f}	5910.5095261850265
2025-08-04 19:05:26.979 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:05:26.980 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:659] : 🔍 基于90%额定电压的暂降结束检测:
2025-08-04 19:05:26.981 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:660] :   - 触发点RMS: 5039.138883751789 V
2025-08-04 19:05:26.981 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:661] :   - 额定电压: 5917.745846319004 V (110kV电压等级)
2025-08-04 19:05:26.983 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:662] :   - 暂降结束阈值: 5325.971261687104 V (90%额定电压)
2025-08-04 19:05:26.984 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:663] :   - 触发点偏差: 878.6069625672153 V
2025-08-04 19:05:26.984 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:664] :   - 事件类型: 电压暂降
2025-08-04 19:05:26.985 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:675] :   - 向前扩展到索引 3, RMS: 5161.38282799108 V (低于90%额定电压)
2025-08-04 19:05:26.985 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:677] :   - 在索引 2 找到事件开始前的正常点, RMS: 5840.5352416100695 V (高于90%额定电压)
2025-08-04 19:05:26.986 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] :   - 暂降结束：在索引 5 RMS恢复到90%额定电压以上
2025-08-04 19:05:26.987 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:694] :   - RMS值: 5517.422905126656 V >= 5325.971261687104 V (90%额定电压)
2025-08-04 19:05:26.987 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:695] :   - 事件结束索引设为: 4
2025-08-04 19:05:26.988 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:723] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-04 19:05:26.988 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:746] : 🔍 智能检测结果:
2025-08-04 19:05:26.989 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:747] :   - 事件范围: 3 到 5 (持续3个RMS点)
2025-08-04 19:05:26.989 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:748] :   - 计算持续时间: {:.1f}ms
2025-08-04 19:05:26.990 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:749] :   - 事件类型: SAG
2025-08-04 19:05:26.990 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:750] :   - 极值: 5039.138883751789 V
2025-08-04 19:05:26.991 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:751] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 19:05:26.991 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 4, 结束索引: 5, 事件类型: SAG
2025-08-04 19:05:26.992 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1074] : 🔍 修正的持续时间计算:
2025-08-04 19:05:26.992 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1075] :   - 触发RMS索引: 4 -> 采样点索引: 2048
2025-08-04 19:05:26.993 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1076] :   - 结束RMS索引: 5 -> 采样点索引: 3071
2025-08-04 19:05:26.993 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1077] :   - 采样点范围: 2048 到 3071 (共1024个点)
2025-08-04 19:05:26.994 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1078] :   - 持续时间: {:.3f}秒 ({:.1f}ms)
2025-08-04 19:05:26.995 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.0399776614080694秒, 转换后: 39.9776614080694毫秒
2025-08-04 19:05:26.995 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:335] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 39ms, 幅值: 0.85153012897407
2025-08-04 19:05:26.996 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39,幅值为：0.85153013
2025-08-04 19:24:22.950 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:24:23.178 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:978] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:24:23.179 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:978] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:24:23.180 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:524] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:24:23.180 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:461] : 🔍 时间解析调试:
2025-08-04 19:24:23.181 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:462] :   - 触发时间字符串: 03/03/2025,14:28:23.388000
2025-08-04 19:24:23.181 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:463] :   - 解析后触发时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:24:23.182 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:464] :   - 波形开始时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:24:23.183 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:465] :   - 触发时间戳: 1740983303388
2025-08-04 19:24:23.185 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:466] :   - 开始时间戳: 1740983303308
2025-08-04 19:24:23.188 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] :   - 原始时间差: 80ms
2025-08-04 19:24:23.189 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:483] : 🔍 时间计算详细过程:
2025-08-04 19:24:23.190 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:484] :   - 时间差: 80ms = 0.08秒
2025-08-04 19:24:23.191 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:485] :   - 采样率: 25614.3046875Hz
2025-08-04 19:24:23.191 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:486] :   - 触发采样点索引: 2049
2025-08-04 19:24:23.192 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:487] :   - 窗口大小: 512
2025-08-04 19:24:23.193 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:488] :   - 计算的RMS索引: 4
2025-08-04 19:24:23.193 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:503] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:24:23.194 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:313] : 🔍 调试信息 - RMS数组总长度: 16, 窗口大小: 512, 采样率: 25614.3046875
2025-08-04 19:24:23.195 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:582] : 计算得到额定电压: 5917.745846319004 V (110kV系统, 基于索引 0 到 3)
2025-08-04 19:24:23.195 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:993] : 🔍 RMS数据调试 (额定电压: 5917.745846319004 V):
2025-08-04 19:24:23.196 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:994] : 索引	A相RMS	B相RMS	C相RMS	标记
2025-08-04 19:24:23.196 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 1	{:.2f}	{:.2f}	{:.2f}	5874.845683127932
2025-08-04 19:24:23.197 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 2	{:.2f}	{:.2f}	{:.2f}	5840.5352416100695
2025-08-04 19:24:23.197 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 3	{:.2f}	{:.2f}	{:.2f}	5161.38282799108
2025-08-04 19:24:23.198 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 4	{:.2f}	{:.2f}	{:.2f}	5039.138883751789
2025-08-04 19:24:23.199 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 5	{:.2f}	{:.2f}	{:.2f}	5517.422905126656
2025-08-04 19:24:23.200 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 6	{:.2f}	{:.2f}	{:.2f}	5022.6623064928135
2025-08-04 19:24:23.201 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 7	{:.2f}	{:.2f}	{:.2f}	3580.5168588361485
2025-08-04 19:24:23.202 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 8	{:.2f}	{:.2f}	{:.2f}	3063.418667009718
2025-08-04 19:24:23.203 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 9	{:.2f}	{:.2f}	{:.2f}	4199.033098530223
2025-08-04 19:24:23.204 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 10	{:.2f}	{:.2f}	{:.2f}	5775.745270285633
2025-08-04 19:24:23.204 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 11	{:.2f}	{:.2f}	{:.2f}	5818.969654817706
2025-08-04 19:24:23.205 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 12	{:.2f}	{:.2f}	{:.2f}	5858.407545195888
2025-08-04 19:24:23.205 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 13	{:.2f}	{:.2f}	{:.2f}	5892.308194035314
2025-08-04 19:24:23.206 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1000] : 14	{:.2f}	{:.2f}	{:.2f}	5910.5095261850265
2025-08-04 19:24:23.207 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:620] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:24:23.207 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:659] : 🔍 基于90%额定电压的暂降结束检测:
2025-08-04 19:24:23.208 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:660] :   - 触发点RMS: 5039.138883751789 V
2025-08-04 19:24:23.208 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:661] :   - 额定电压: 5917.745846319004 V (110kV电压等级)
2025-08-04 19:24:23.209 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:662] :   - 暂降结束阈值: 5325.971261687104 V (90%额定电压)
2025-08-04 19:24:23.209 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:663] :   - 触发点偏差: 878.6069625672153 V
2025-08-04 19:24:23.210 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:664] :   - 事件类型: 电压暂降
2025-08-04 19:24:23.210 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:675] :   - 向前扩展到索引 3, RMS: 5161.38282799108 V (低于90%额定电压)
2025-08-04 19:24:23.211 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:677] :   - 在索引 2 找到事件开始前的正常点, RMS: 5840.5352416100695 V (高于90%额定电压)
2025-08-04 19:24:23.211 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] :   - 暂降结束：在索引 5 RMS恢复到90%额定电压以上
2025-08-04 19:24:23.212 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:694] :   - RMS值: 5517.422905126656 V >= 5325.971261687104 V (90%额定电压)
2025-08-04 19:24:23.212 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:695] :   - 事件结束索引设为: 4
2025-08-04 19:24:23.213 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:723] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-04 19:24:23.214 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:746] : 🔍 智能检测结果:
2025-08-04 19:24:23.215 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:747] :   - 事件范围: 3 到 5 (持续3个RMS点)
2025-08-04 19:24:23.216 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:748] :   - 计算持续时间: {:.1f}ms
2025-08-04 19:24:23.216 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:749] :   - 事件类型: SAG
2025-08-04 19:24:23.217 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:750] :   - 极值: 5039.138883751789 V
2025-08-04 19:24:23.219 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:751] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 19:24:23.220 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:324] : 🔍 事件分析结果 - 触发索引: 4, 结束索引: 5, 事件类型: SAG
2025-08-04 19:24:23.221 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1074] : 🔍 修正的持续时间计算:
2025-08-04 19:24:23.222 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1075] :   - 触发RMS索引: 4 -> 采样点索引: 2048
2025-08-04 19:24:23.223 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1076] :   - 结束RMS索引: 5 -> 采样点索引: 3071
2025-08-04 19:24:23.223 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1077] :   - 采样点范围: 2048 到 3071 (共1024个点)
2025-08-04 19:24:23.224 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1078] :   - 持续时间: {:.3f}秒 ({:.1f}ms)
2025-08-04 19:24:23.225 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:329] : 🔍 持续时间计算 - 原始持续时间: 0.0399776614080694秒, 转换后: 39.9776614080694毫秒
2025-08-04 19:24:23.226 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:335] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 39ms, 幅值: 0.85153012897407
2025-08-04 19:24:23.227 [33m- INFO[0;39m - [192.168.0.2] - [34m2844[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39,幅值为：0.85153013
2025-08-04 19:27:36.236 [33m- INFO[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:27:47.933 [33m- INFO[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:27:49.679 [33m- INFO[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:28:05.799 [33m- INFO[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 34.038 seconds (JVM running for 36.03)
2025-08-04 19:28:05.816 [33m- INFO[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:28:10.647 [33m- INFO[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 19:29:17.668 [33m-ERROR[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mcom.cet.pq.common.aspect.LogAspect      [0;39m - [LogAspect.java:42] : +++++around execution(ResultWithTotal com.cet.pq.common.feign.DeviceDataService.queryEventData(Integer,Integer,Long,Long,EventCondition))Use time :60160 ms with exception : Read timed out executing POST http://device-data-service/api/event/v1/data?index=0&limit=1000&startTime=1704038400000&endTime=1735660800000
2025-08-04 19:29:17.673 [33m-ERROR[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mcom.cet.pq.common.aspect.LogAspect      [0;39m - [LogAspect.java:42] : +++++around execution(PageResult com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getPqVariationEvent(PqVariationEventParams))Use time :60173 ms with exception : Read timed out executing POST http://device-data-service/api/event/v1/data?index=0&limit=1000&startTime=1704038400000&endTime=1735660800000
2025-08-04 19:29:17.714 [33m-ERROR[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.c.e.h.ControllerExceptionHandler  [0;39m - [ControllerExceptionHandler.java:43] : 业务处理异常
feign.RetryableException: Read timed out executing POST http://device-data-service/api/event/v1/data?index=0&limit=1000&startTime=1704038400000&endTime=1735660800000
	at feign.FeignException.errorExecuting(FeignException.java:268)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:131)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:91)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory$1.proceed(FeignCachingInvocationHandlerFactory.java:66)
	at org.springframework.cache.interceptor.CacheInterceptor.lambda$invoke$0(CacheInterceptor.java:54)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:351)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64)
	at org.springframework.cloud.openfeign.FeignCachingInvocationHandlerFactory.lambda$create$1(FeignCachingInvocationHandlerFactory.java:53)
	at com.sun.proxy.$Proxy162.queryEventData(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.cet.pq.common.aspect.LogAspect.doAround(LogAspect.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:241)
	at com.sun.proxy.$Proxy163.queryEventData(Unknown Source)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.queryEventLogs(PqEventServiceImpl.java:1266)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.processFaultWaveEvent(PqEventServiceImpl.java:196)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl.getPqVariationEvent(PqEventServiceImpl.java:100)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$FastClassBySpringCGLIB$$fb6ddef3.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.cet.pq.common.aspect.LogAspect.doAround(LogAspect.java:31)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.cet.pq.pqservice.service.impl.PqEventServiceImpl$$EnhancerBySpringCGLIB$$9f007a67.getPqVariationEvent(<generated>)
	at com.cet.pq.pqservice.controller.PqEventController.getEventList(PqEventController.java:41)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.cet.electric.log.filter.MvpFilter.doFilterInternal(MvpFilter.java:44)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:164)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:237)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:223)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:887)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1684)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read1(BufferedInputStream.java:286)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:345)
	at sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:735)
	at sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:678)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1593)
	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1498)
	at java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:480)
	at feign.Client$Default.convertResponse(Client.java:110)
	at feign.Client$Default.execute(Client.java:106)
	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:57)
	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:95)
	at org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient.execute(FeignBlockingLoadBalancerClient.java:114)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:121)
	... 166 common frames omitted
2025-08-04 19:29:17.846 [33m-ERROR[0;39m - [192.168.0.2] - [34m8756[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mo.s.web.servlet.HandlerExecutionChain   [0;39m - [HandlerExecutionChain.java:181] : HandlerInterceptor.afterCompletion threw exception
java.lang.NullPointerException: null
	at org.springframework.boot.actuate.metrics.web.servlet.LongTaskTimingHandlerInterceptor.stopLongTaskTimers(LongTaskTimingHandlerInterceptor.java:134)
	at org.springframework.boot.actuate.metrics.web.servlet.LongTaskTimingHandlerInterceptor.afterCompletion(LongTaskTimingHandlerInterceptor.java:83)
	at org.springframework.web.servlet.HandlerExecutionChain.triggerAfterCompletion(HandlerExecutionChain.java:178)
	at org.springframework.web.servlet.DispatcherServlet.triggerAfterCompletion(DispatcherServlet.java:1460)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1092)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at com.cet.electric.log.filter.MvpFilter.doFilterInternal(MvpFilter.java:44)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:164)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLogoutPageGeneratingFilter.doFilterInternal(DefaultLogoutPageGeneratingFilter.java:58)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:237)
	at org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter.doFilter(DefaultLoginPageGeneratingFilter.java:223)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:887)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1684)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
2025-08-04 19:29:42.744 [33m- INFO[0;39m - [192.168.0.2] - [34m37736[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:29:56.210 [33m- INFO[0;39m - [192.168.0.2] - [34m37736[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:29:58.099 [33m- INFO[0;39m - [192.168.0.2] - [34m37736[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:30:13.834 [33m- INFO[0;39m - [192.168.0.2] - [34m37736[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 34.988 seconds (JVM running for 37.233)
2025-08-04 19:30:13.853 [33m- INFO[0;39m - [192.168.0.2] - [34m37736[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:30:29.840 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:30:40.694 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:30:42.813 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:30:58.650 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 31.398 seconds (JVM running for 33.056)
2025-08-04 19:30:58.669 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:31:03.884 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 19:31:18.178 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:31:18.555 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1019] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:31:18.556 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1019] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:31:18.558 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:31:18.559 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:31:18.563 [33m- WARN[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:632] : 实际电压与理论值偏差过大({:.1f}%)，使用理论值
2025-08-04 19:31:18.564 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:550] : 额定电压计算: 电压等级=110.0kV, 理论值={:.1f}V, 实际均值={:.1f}V, 最终额定电压={:.1f}V
2025-08-04 19:31:18.567 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:671] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:31:18.570 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:710] : 暂降结束检测: 额定电压={:.1f}V, 90%阈值={:.1f}V, 触发RMS={:.1f}V
2025-08-04 19:31:18.572 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] : 🔍 智能检测结果:
2025-08-04 19:31:18.574 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:788] :   - 事件范围: 0 到 15 (持续16个RMS点)
2025-08-04 19:31:18.576 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:789] :   - 计算持续时间: {:.1f}ms
2025-08-04 19:31:18.579 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:790] :   - 事件类型: SAG
2025-08-04 19:31:18.581 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:791] :   - 极值: 3063.418667009718 V
2025-08-04 19:31:18.583 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:792] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 19:31:18.585 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1115] : 持续时间计算: RMS索引4到15, 采样点2048到8191, 持续时间{:.1f}ms
2025-08-04 19:31:18.586 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 239ms, 幅值: {:.1f}%
2025-08-04 19:31:18.587 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 239ms, 幅值为: {:.1f}%
2025-08-04 19:32:56.369 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:33:41.564 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1019] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:33:41.567 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1019] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:33:41.570 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:33:41.573 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:33:58.930 [33m- WARN[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:632] : 实际电压与理论值偏差过大({:.1f}%)，使用理论值
2025-08-04 19:33:58.934 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:550] : 额定电压计算: 电压等级=110.0kV, 理论值={:.1f}V, 实际均值={:.1f}V, 最终额定电压={:.1f}V
2025-08-04 19:34:26.093 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:671] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:34:55.574 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:710] : 暂降结束检测: 额定电压={:.1f}V, 90%阈值={:.1f}V, 触发RMS={:.1f}V
2025-08-04 19:34:55.577 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] : 🔍 智能检测结果:
2025-08-04 19:34:55.579 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:788] :   - 事件范围: 0 到 15 (持续16个RMS点)
2025-08-04 19:34:55.581 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:789] :   - 计算持续时间: {:.1f}ms
2025-08-04 19:34:55.584 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:790] :   - 事件类型: SAG
2025-08-04 19:34:55.587 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:791] :   - 极值: 3063.418667009718 V
2025-08-04 19:34:55.589 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:792] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 19:36:35.275 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1115] : 持续时间计算: RMS索引4到15, 采样点2048到8191, 持续时间{:.1f}ms
2025-08-04 19:36:35.277 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 239ms, 幅值: {:.1f}%
2025-08-04 19:36:35.278 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-3][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 239ms, 幅值为: {:.1f}%
2025-08-04 19:36:47.634 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:36:47.860 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1019] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:36:47.861 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1019] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:36:47.862 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:36:47.863 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:36:47.863 [33m- WARN[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:632] : 实际电压与理论值偏差过大({:.1f}%)，使用理论值
2025-08-04 19:36:47.864 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:550] : 额定电压计算: 电压等级=110.0kV, 理论值={:.1f}V, 实际均值={:.1f}V, 最终额定电压={:.1f}V
2025-08-04 19:36:47.864 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:671] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:36:47.865 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:710] : 暂降结束检测: 额定电压={:.1f}V, 90%阈值={:.1f}V, 触发RMS={:.1f}V
2025-08-04 19:36:47.865 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] : 🔍 智能检测结果:
2025-08-04 19:36:47.866 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:788] :   - 事件范围: 0 到 15 (持续16个RMS点)
2025-08-04 19:36:47.867 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:789] :   - 计算持续时间: {:.1f}ms
2025-08-04 19:36:47.867 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:790] :   - 事件类型: SAG
2025-08-04 19:36:47.868 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:791] :   - 极值: 3063.418667009718 V
2025-08-04 19:36:47.868 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:792] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 19:36:47.869 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1115] : 持续时间计算: RMS索引4到15, 采样点2048到8191, 持续时间{:.1f}ms
2025-08-04 19:36:47.869 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 239ms, 幅值: {:.1f}%
2025-08-04 19:36:47.870 [33m- INFO[0;39m - [192.168.0.2] - [34m23448[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-5][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 239ms, 幅值为: {:.1f}%
2025-08-04 19:40:16.482 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:40:27.018 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:40:28.778 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:40:43.826 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 30.282 seconds (JVM running for 31.838)
2025-08-04 19:40:43.842 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:40:48.381 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 19:41:04.220 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:41:04.492 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:41:04.493 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:41:04.494 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:41:04.494 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:41:04.495 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引1-3, 额定电压=5919.422682729756V
2025-08-04 19:41:04.497 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:694] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:41:04.497 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] : 暂降结束检测: 额定电压=5919.422682729756V, 90%阈值=5327.480414456781V, 触发RMS=5039.138883751789V
2025-08-04 19:41:04.498 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:761] : 暂降结束: 索引5恢复到{:.1f}V(≥90%额定电压), 事件结束索引=5517.422905126656
2025-08-04 19:41:04.499 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-04 19:41:04.499 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:810] : 🔍 智能检测结果:
2025-08-04 19:41:04.500 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:811] :   - 事件范围: 3 到 5 (持续3个RMS点)
2025-08-04 19:41:04.501 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:812] :   - 计算持续时间: {:.1f}ms
2025-08-04 19:41:04.501 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:813] :   - 事件类型: SAG
2025-08-04 19:41:04.502 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:814] :   - 极值: 5039.138883751789 V
2025-08-04 19:41:04.503 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:815] :   - 幅值: {:.3f} ({:.1f}%)
2025-08-04 19:41:04.503 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1138] : 持续时间计算: RMS索引4到5, 采样点2048到3071, 持续时间{:.1f}ms
2025-08-04 19:41:04.504 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 39ms, 幅值: {:.1f}%
2025-08-04 19:41:04.505 [33m- INFO[0;39m - [192.168.0.2] - [34m44148[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: {:.1f}%
2025-08-04 19:51:16.927 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:51:26.747 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:51:28.519 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:51:42.967 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.461 seconds (JVM running for 29.721)
2025-08-04 19:51:42.985 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:51:47.685 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 19:51:56.115 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:51:56.499 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:51:56.500 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:51:56.501 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:51:56.502 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:51:56.503 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引1-3, 额定电压=5919.422682729756V
2025-08-04 19:51:56.504 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:694] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:51:56.505 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] : 暂降结束检测: 额定电压=5919.422682729756V, 90%阈值=5327.480414456781V, 触发RMS=5039.138883751789V
2025-08-04 19:51:56.507 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:761] : 暂降结束: 索引5恢复到5517.422905126656V(≥90%额定电压), 事件结束索引=4
2025-08-04 19:51:56.508 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-04 19:51:56.509 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:810] : 🔍 智能检测结果:
2025-08-04 19:51:56.510 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:811] :   - 事件范围: 3 到 5 (持续3个RMS点)
2025-08-04 19:51:56.511 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:812] :   - 计算持续时间: 59.966492112104106ms
2025-08-04 19:51:56.512 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:813] :   - 事件类型: SAG
2025-08-04 19:51:56.513 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:814] :   - 极值: 5039.138883751789 V
2025-08-04 19:51:56.513 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:815] :   - 幅值: 85.12889100576912%
2025-08-04 19:51:56.514 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1138] : 持续时间计算: RMS索引4到5, 采样点2048到3071, 持续时间40ms
2025-08-04 19:51:56.514 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 39ms, 幅值: 85.12889100576912%
2025-08-04 19:51:56.515 [33m- INFO[0;39m - [192.168.0.2] - [34m40272[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 85.12889%
2025-08-04 19:57:26.683 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:57:36.360 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:57:38.033 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:57:52.086 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 27.839 seconds (JVM running for 29.294)
2025-08-04 19:57:52.104 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:57:57.047 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 19:58:13.020 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:58:13.270 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:58:13.271 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 19:58:13.273 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 19:58:13.273 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 19:58:13.274 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引1-3, 额定电压=5919.422682729756V
2025-08-04 19:58:13.275 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:694] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 19:58:13.276 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] : 暂降结束检测: 额定电压=5919.422682729756V, 90%阈值=5327.480414456781V, 触发RMS=5039.138883751789V
2025-08-04 19:58:13.277 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:761] : 暂降结束: 索引5恢复到5517.422905126656V(≥90%额定电压), 事件结束索引=4
2025-08-04 19:58:13.278 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-04 19:58:13.278 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:810] : 🔍 智能检测结果:
2025-08-04 19:58:13.279 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:811] :   - 事件范围: 3 到 5 (持续3个RMS点)
2025-08-04 19:58:13.279 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:812] :   - 计算持续时间: 59.966492112104106ms
2025-08-04 19:58:13.280 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:813] :   - 事件类型: SAG
2025-08-04 19:58:13.281 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:814] :   - 极值: 5039.138883751789 V
2025-08-04 19:58:13.281 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:815] :   - 幅值: 85.12889100576912%
2025-08-04 19:58:13.282 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1138] : 持续时间计算: RMS索引4到5, 采样点2048到3071, 持续时间40ms
2025-08-04 19:58:13.282 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 39ms, 幅值: 85.12889100576912%
2025-08-04 19:58:13.283 [33m- INFO[0;39m - [192.168.0.2] - [34m23744[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 85.12889%
2025-08-04 19:59:05.418 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 19:59:15.142 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 19:59:17.020 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 19:59:31.442 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.454 seconds (JVM running for 29.945)
2025-08-04 19:59:31.459 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 19:59:36.731 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 19:59:46.586 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 19:59:46.909 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 02/03/2025,11:23:39.911000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 911000, 毫秒: 911, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 19:59:46.910 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 02/03/2025,11:23:39.811000 -> 基础时间: Mon Feb 03 11:23:39 CST 2025, 微秒: 811000, 毫秒: 811, 最终时间: Mon Feb 03 11:23:39 CST 2025
2025-08-04 19:59:46.911 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 02/03/2025,11:23:39.811000
2025-08-04 19:59:46.912 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 02/03/2025,11:23:39.911000 -> 开始时间: 02/03/2025,11:23:39.811000 -> 时间差: 100ms ({:.3f}s) -> 采样点索引: 0.1 -> RMS索引: 2560
2025-08-04 19:59:46.912 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引2-4, 额定电压=5998.754714263984V
2025-08-04 19:59:46.913 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:694] : 检测到电压暂降事件，触发RMS: 3529.1532749148905 V
2025-08-04 19:59:46.914 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] : 暂降结束检测: 额定电压=5998.754714263984V, 90%阈值=5398.8792428375855V, 触发RMS=3529.1532749148905V
2025-08-04 19:59:46.914 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:761] : 暂降结束: 索引7恢复到5463.8810723335255V(≥90%额定电压), 事件结束索引=6
2025-08-04 19:59:46.915 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:810] : 🔍 智能检测结果:
2025-08-04 19:59:46.916 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:811] :   - 事件范围: 5 到 6 (持续2个RMS点)
2025-08-04 19:59:46.916 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:812] :   - 计算持续时间: 40.0ms
2025-08-04 19:59:46.917 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:813] :   - 事件类型: SAG
2025-08-04 19:59:46.917 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:814] :   - 极值: 3529.1532749148905 V
2025-08-04 19:59:46.918 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:815] :   - 幅值: 58.83143157234259%
2025-08-04 19:59:46.918 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:819] : 🔍 持续时间过短 ({:.1f}ms < 50ms)，尝试扩展事件范围
2025-08-04 19:59:46.919 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:828] :   - 期望持续时间: 64.49ms
2025-08-04 19:59:46.920 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:829] :   - 期望RMS点数: 4
2025-08-04 19:59:46.921 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:830] :   - 调整结束索引: 6 -> 8
2025-08-04 19:59:46.922 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:847] :   - 调整后持续时间: {:.1f}ms
2025-08-04 19:59:46.923 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:848] :   - 调整后幅值: 58.83143157234259%
2025-08-04 19:59:46.924 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1138] : 持续时间计算: RMS索引5到8, 采样点2560到4607, 持续时间80ms
2025-08-04 19:59:46.925 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 02/03/2025,11:23:39.911000, 触发索引: 5, 持续时间: 80ms, 幅值: 58.83143157234259%
2025-08-04 19:59:46.926 [33m- INFO[0;39m - [192.168.0.2] - [34m9452[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 80ms, 幅值为: 58.831432%
2025-08-04 20:01:40.614 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 20:01:50.886 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 20:01:52.518 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 20:02:06.701 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.423 seconds (JVM running for 29.722)
2025-08-04 20:02:06.720 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 20:02:11.119 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 20:02:34.414 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 20:02:34.579 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:54.045400 -> 基础时间: Wed Jul 07 04:29:54 CST 2027, 微秒: 45400, 毫秒: 45, 最终时间: Wed Jul 07 04:29:54 CST 2027
2025-08-04 20:02:34.579 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:53.845400 -> 基础时间: Wed Jul 07 04:29:53 CST 2027, 微秒: 845400, 毫秒: 845, 最终时间: Wed Jul 07 04:29:53 CST 2027
2025-08-04 20:02:34.579 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 31/07/2025,04:29:53.845400
2025-08-04 20:02:34.580 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 31/07/2025,04:29:54.045400 -> 开始时间: 31/07/2025,04:29:53.845400 -> 时间差: 200ms ({:.3f}s) -> 采样点索引: 0.2 -> RMS索引: 2000
2025-08-04 20:02:34.580 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引7-9, 额定电压=6000.0V
2025-08-04 20:02:34.582 [33m- WARN[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:667] : 触发索引超出范围: 10
2025-08-04 20:02:34.582 [33m- WARN[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1121] : 无效的索引范围: 触发点=10, 结束点=-1
2025-08-04 20:02:34.583 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 31/07/2025,04:29:54.045400, 触发索引: 10, 持续时间: 0ms, 幅值: 1.0%
2025-08-04 20:02:34.583 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-04 20:04:10.768 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 20:05:43.792 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:54.045400 -> 基础时间: Wed Jul 07 04:29:54 CST 2027, 微秒: 45400, 毫秒: 45, 最终时间: Wed Jul 07 04:29:54 CST 2027
2025-08-04 20:05:43.799 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:53.845400 -> 基础时间: Wed Jul 07 04:29:53 CST 2027, 微秒: 845400, 毫秒: 845, 最终时间: Wed Jul 07 04:29:53 CST 2027
2025-08-04 20:05:43.801 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 31/07/2025,04:29:53.845400
2025-08-04 20:05:43.802 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 31/07/2025,04:29:54.045400 -> 开始时间: 31/07/2025,04:29:53.845400 -> 时间差: 200ms ({:.3f}s) -> 采样点索引: 0.2 -> RMS索引: 2000
2025-08-04 20:05:43.804 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引7-9, 额定电压=6000.0V
2025-08-04 20:05:43.805 [33m- WARN[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:667] : 触发索引超出范围: 10
2025-08-04 20:05:43.806 [33m- WARN[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1121] : 无效的索引范围: 触发点=10, 结束点=-1
2025-08-04 20:05:43.807 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 31/07/2025,04:29:54.045400, 触发索引: 10, 持续时间: 0ms, 幅值: 1.0%
2025-08-04 20:05:43.808 [33m- INFO[0;39m - [192.168.0.2] - [34m22324[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-04 20:05:58.808 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 20:06:08.638 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 20:06:10.392 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 20:06:24.568 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 28.047 seconds (JVM running for 29.339)
2025-08-04 20:06:24.586 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 20:06:28.814 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 20:06:38.728 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 20:06:42.894 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:54.045400 -> 基础时间: Wed Jul 07 04:29:54 CST 2027, 微秒: 45400, 毫秒: 45, 最终时间: Wed Jul 07 04:29:54 CST 2027
2025-08-04 20:06:42.896 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:53.845400 -> 基础时间: Wed Jul 07 04:29:53 CST 2027, 微秒: 845400, 毫秒: 845, 最终时间: Wed Jul 07 04:29:53 CST 2027
2025-08-04 20:06:42.896 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 31/07/2025,04:29:53.845400
2025-08-04 20:06:42.897 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 31/07/2025,04:29:54.045400 -> 开始时间: 31/07/2025,04:29:53.845400 -> 时间差: 200ms ({:.3f}s) -> 采样点索引: 0.2 -> RMS索引: 2000
2025-08-04 20:06:42.898 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引7-9, 额定电压=127020.0V
2025-08-04 20:06:42.900 [33m- WARN[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:667] : 触发索引超出范围: 10
2025-08-04 20:06:42.901 [33m- WARN[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1121] : 无效的索引范围: 触发点=10, 结束点=-1
2025-08-04 20:06:42.902 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 31/07/2025,04:29:54.045400, 触发索引: 10, 持续时间: 0ms, 幅值: 1.0%
2025-08-04 20:06:42.903 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-04 20:07:06.061 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 22:30:44.370 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:54.045400 -> 基础时间: Wed Jul 07 04:29:54 CST 2027, 微秒: 45400, 毫秒: 45, 最终时间: Wed Jul 07 04:29:54 CST 2027
2025-08-04 22:30:44.374 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 31/07/2025,04:29:53.845400 -> 基础时间: Wed Jul 07 04:29:53 CST 2027, 微秒: 845400, 毫秒: 845, 最终时间: Wed Jul 07 04:29:53 CST 2027
2025-08-04 22:30:44.378 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 31/07/2025,04:29:53.845400
2025-08-04 22:30:44.381 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 31/07/2025,04:29:54.045400 -> 开始时间: 31/07/2025,04:29:53.845400 -> 时间差: 200ms ({:.3f}s) -> 采样点索引: 0.2 -> RMS索引: 2000
2025-08-04 22:36:42.935 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引7-9, 额定电压=127020.0V
2025-08-04 22:36:42.944 [33m- WARN[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:667] : 触发索引超出范围: 10
2025-08-04 22:36:42.945 [33m- WARN[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1121] : 无效的索引范围: 触发点=10, 结束点=-1
2025-08-04 22:36:42.946 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 31/07/2025,04:29:54.045400, 触发索引: 10, 持续时间: 0ms, 幅值: 1.0%
2025-08-04 22:36:42.947 [33m- INFO[0;39m - [192.168.0.2] - [34m21052[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 0ms, 幅值为: 1.0%
2025-08-04 22:37:03.194 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 22:37:14.803 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 22:37:16.636 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 22:37:33.245 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 33.148 seconds (JVM running for 35.242)
2025-08-04 22:37:33.265 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 22:37:40.168 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 22:38:10.226 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 22:41:35.297 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 22:41:35.302 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1042] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 22:41:35.305 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:494] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 22:41:35.309 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:474] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 22:48:14.839 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:575] : 简单额定电压计算: 基于索引1-3, 额定电压=5919.422682729756V
2025-08-04 22:48:14.845 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:694] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 22:48:42.807 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:733] : 暂降结束检测: 额定电压=5919.422682729756V, 90%阈值=5327.480414456781V, 触发RMS=5039.138883751789V
2025-08-04 22:48:42.815 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:761] : 暂降结束: 索引5恢复到5517.422905126656V(≥90%额定电压), 事件结束索引=4
2025-08-04 22:48:42.817 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:787] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-04 22:48:42.818 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:810] : 🔍 智能检测结果:
2025-08-04 22:48:42.820 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:811] :   - 事件范围: 3 到 5 (持续3个RMS点)
2025-08-04 22:48:42.821 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:812] :   - 计算持续时间: 59.966492112104106ms
2025-08-04 22:48:42.823 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:813] :   - 事件类型: SAG
2025-08-04 22:48:42.825 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:814] :   - 极值: 5039.138883751789 V
2025-08-04 22:48:42.826 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:815] :   - 幅值: 85.12889100576912%
2025-08-04 22:48:42.826 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1138] : 持续时间计算: RMS索引4到5, 采样点2048到3071, 持续时间40ms
2025-08-04 22:48:42.828 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:325] : 基于triggerTime计算 - 触发时间: 03/03/2025,14:28:23.388000, 触发索引: 4, 持续时间: 39ms, 幅值: 85.12889100576912%
2025-08-04 22:48:42.828 [33m- INFO[0;39m - [192.168.0.2] - [34m37188[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-9][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 85.12889%
2025-08-04 23:04:43.240 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [SpringApplication.java:637] : The following 1 profile is active: "dev"
2025-08-04 23:04:53.803 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.anlysis.utils.ws.WebSocketManager [0;39m - [WebSocketManager.java:57] : websocket server init.
2025-08-04 23:04:55.600 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.common.config.RedisConfig    [0;39m - [RedisConfig.java:86] : the redisConnectionFactory is JedisConnectionFactory type
2025-08-04 23:05:10.797 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.cet.electric.PowerQualityApplication  [0;39m - [StartupInfoLogger.java:61] : Started PowerQualityApplication in 30.344 seconds (JVM running for 31.797)
2025-08-04 23:05:10.814 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[main][0;39m [32mcom.cet.pq.config.VersionPrinter        [0;39m - [VersionPrinter.java:22] : version-2.1.142
2025-08-04 23:05:15.799 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[main][0;39m [32mc.c.p.i.c.listener.LedgerInitListener   [0;39m - [LedgerInitListener.java:53] : The ledger cache init is complete.
2025-08-04 23:05:57.711 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 23:16:07.781 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1001] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 23:16:07.783 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1001] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 23:16:07.784 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:491] : 波形开始时间: 03/03/2025,14:28:23.308000
2025-08-04 23:16:07.786 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:471] : 触发时间解析: 03/03/2025,14:28:23.388000 -> 开始时间: 03/03/2025,14:28:23.308000 -> 时间差: 80ms ({:.3f}s) -> 采样点索引: 0.08 -> RMS索引: 2049
2025-08-04 23:16:07.788 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:693] : 检测到电压暂降事件，触发RMS: 5039.138883751789 V
2025-08-04 23:16:07.789 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:732] : 暂降结束检测: 额定电压=5773.6720554272515V, 90%阈值=5196.304849884526V, 触发RMS=5039.138883751789V
2025-08-04 23:16:07.790 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:760] : 暂降结束: 索引5恢复到5517.422905126656V(≥90%额定电压), 事件结束索引=4
2025-08-04 23:16:07.791 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:786] :   - 事件至少持续1个RMS点，结束索引调整为: 5
2025-08-04 23:16:07.792 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1097] : 持续时间计算: RMS索引4到5, 采样点2048到3071, 持续时间40ms
2025-08-04 23:16:07.793 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-1][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:264] : 持续时间为: 39ms, 幅值为: 87.277885%
2025-08-04 23:16:17.017 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:259] : 事件描述为: 录波事件，录波文件名：深圳中调_500.0_宝安换流站_220kV安农甲线主一保护（PSL-603UA2-N)_20250121223630.ZWAV
2025-08-04 23:16:26.807 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1001] : 🔍 微秒时间解析: 03/03/2025,14:28:23.388000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 388000, 毫秒: 388, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 23:16:26.810 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:1001] : 🔍 微秒时间解析: 03/03/2025,14:28:23.308000 -> 基础时间: Mon Mar 03 14:28:23 CST 2025, 微秒: 308000, 毫秒: 308, 最终时间: Mon Mar 03 14:28:23 CST 2025
2025-08-04 23:16:26.812 [33m- INFO[0;39m - [192.168.0.2] - [34m45012[0;39m --- traceId:[,,] [33m[http-nio-8098-exec-2][0;39m [32mc.c.p.p.service.impl.PqEventServiceImpl [0;39m - [PqEventServiceImpl.java:491] : 波形开始时间: 03/03/2025,14:28:23.308000

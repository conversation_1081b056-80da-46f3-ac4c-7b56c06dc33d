package com.cet.pq.common.model;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.ConnectTimeoutException;
import org.apache.commons.httpclient.params.HttpConnectionParams;
import org.apache.commons.httpclient.protocol.ProtocolSocketFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import java.io.IOException;
import java.net.InetAddress;
import java.net.Socket;
import java.net.UnknownHostException;

/***
 * <AUTHOR>
 * @Date 2024/12/9 14:13
 * @Description
 */
@Slf4j
public class CustomSSLSocketFactory implements ProtocolSocketFactory {
    private static SSLContext sslContext;

    public CustomSSLSocketFactory(SSLContext sslContext) {
        this.sslContext = sslContext;
    }


    static {
        try {
            sslContext = SSLContext.getInstance("TLS");
        } catch (Exception e) {
            log.error("Error while setting SSL context", e);
        }
    }
    @Override
    public Socket createSocket(String host, int port, InetAddress localAddress, int localPort) throws IOException, UnknownHostException {
        return sslContext.getSocketFactory().createSocket(host, port, localAddress, localPort);
    }

    @Override
    public Socket createSocket(String host, int port) throws IOException, UnknownHostException {
        return sslContext.getSocketFactory().createSocket(host, port);
    }

    @Override
    public Socket createSocket(String host, int port, InetAddress clientHost, int clientPort, HttpConnectionParams params) throws IOException, UnknownHostException, ConnectTimeoutException {
        if (params == null) {
            throw new IllegalArgumentException("Parameters may not be null");
        }
        int timeout = params.getConnectionTimeout();
        SSLSocketFactory socketFactory = sslContext.getSocketFactory();
        if (timeout == 0) {
            return socketFactory.createSocket(host, port, clientHost, clientPort);
        } else {
            Socket socket = socketFactory.createSocket();
            socket.bind(new java.net.InetSocketAddress(clientHost, clientPort));
            socket.connect(new java.net.InetSocketAddress(host, port), timeout);
            return socket;
        }
    }
}

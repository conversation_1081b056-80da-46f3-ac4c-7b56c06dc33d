package com.cet.pq.common.model.realtime;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description 物理量限值
 * <AUTHOR>
 * @Date 2020/6/30 14:11
 */
@Data
public class QuantityLimit {

	private String modelLabel = "quantitylimit";

	private Long id;
	private Double uplimit;
	@JsonProperty("quantitymaptemplate_id")
	private Long quantitymaptemplateId;
	private Double lowlimit;
	@JsonProperty("line_id")
	private Long lineId;
	private Long dataid;
	private String dataname;
	private Long updatetime;

	public Long getUpdatetime() {
		return System.currentTimeMillis();
	}
}

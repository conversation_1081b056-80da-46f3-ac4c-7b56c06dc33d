package com.cet.pq.anlysis.controller;

/**
 * @Description: 超标分析接口
 * @Author: zhangshi<PERSON>
 * @Date: 2020/9/29 16:40
 */

import com.cet.pq.anlysis.model.ReportOfOverStandard.ReportOfOverStandardDetails;
import com.cet.pq.anlysis.model.ReportOfOverStandard.ReportOfOverStandardParams;
import com.cet.pq.anlysis.model.excel.*;
import com.cet.pq.anlysis.service.OverStandardAnalysisService;
import com.cet.pq.common.model.PageResult;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.ResultWithTotal;
import com.cet.pq.pqservice.model.overStandardAnalysis.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Service
@Api(value = "/pqService/v1/overStandardAnalysis", tags = "稳态指标 - 超标统计")
@RestController
@RequestMapping(value = "/pqService/v1/overStandardAnalysis")
public class OverStandardAnalysisController {

    @Autowired
    private OverStandardAnalysisService overStandardAnalysisService;

    @PostMapping("/totalCount")
    @ApiOperation("稳态超标总览-超标综合统计")
    public ResultWithTotal<ProvinceStatistics> overStandardAnalysisTotalCount(@RequestBody OverStandardBaseParam overStandardBaseParam) {
        ProvinceStatistics result = overStandardAnalysisService.provinceStatistics(overStandardBaseParam);
        return ResultWithTotal.success(result, 1);
    }

    @PostMapping("/companyOverStandard")
    @ApiOperation("稳态超标总览-各管理单位稳态指标分析表")
    public Result<List<CompanyOverStandard>> companyOverStandard(@RequestBody @Valid @ApiParam(name = "companyOverParam", value = "参数", required = true)
                                                                             OverStandardBaseParam companyOverParam) {
        List<CompanyOverStandard> result = overStandardAnalysisService.overStandardAnalysis(companyOverParam);
        return ResultWithTotal.success(result, result.size());
    }

    @ApiOperation("稳态超标总览-导出各管理单位稳态指标分析表")
    @PostMapping("/exportCompanyOverStandard")
    public void exportCompanyOverStandard(@RequestBody ExportOverStandardParam exportOverStandardParam, HttpServletResponse response) {
        overStandardAnalysisService.exportCompanyOverStandard(exportOverStandardParam, response);
    }

    @PostMapping("/countByCompany")
    @ApiOperation("按管理单位分类统计")
    public Result<List<CompanyOverStandardCount>> overStandardAnalysisByCompany(@RequestBody @Valid @ApiParam(name = "countByCompanyParam", value = "参数", required = true)
                                                        CountByCompanyParam countByCompanyParam) {
        List<CompanyOverStandardCount> result = overStandardAnalysisService.overStandardAnalysisByCompany(countByCompanyParam);
        return ResultWithTotal.success(result, result.size());
    }

    @ApiOperation("导出按管理单位分类统计")
    @PostMapping("/exportStandardCountByCompany")
    public void exportStandardCountByCompany(@RequestBody ExportStandardByCompanyParam exportStandardByCompanyParam, HttpServletResponse response) {
        overStandardAnalysisService.exportStandardCountByCompany(exportStandardByCompanyParam, response);
    }

    @PostMapping("/countByMonitorObject")
    @ApiOperation("按监测对象类型统计-列表")
    public Result overStandardAnalysisByMonitorObject(@RequestBody @Valid @ApiParam(name = "countByObjectParam", value = "参数", required = true)
                                                                  CountByCompanyParam countByObjectParam) {
        List<MonitorObjectOverStandardCount> result = overStandardAnalysisService.overStandardAnalysisByMonitorObject(countByObjectParam);
        return ResultWithTotal.success(result, result.size());
    }

    @ApiOperation("导出按监测对象类型统计-列表")
    @PostMapping("/exportCountByMonitorObject")
    public void exportCountByMonitorObject(@RequestBody ExportStandardByCompanyParam exportStandardByCompanyParam, HttpServletResponse response) {
        overStandardAnalysisService.exportCountByMonitorObject(exportStandardByCompanyParam, response);
    }

    @PostMapping("/countByMonitorObjectByVoltClass")
    @ApiOperation("按监测对象类型统计-按电压等级超标分布")
    public Result overStandardAnalysisByMonitorObjectByVoltClass(@RequestBody @Valid @ApiParam(name = "countByObjectParam", value = "参数", required = true)
                                                                         ObjectAndVolParam objectAndVolParam) {
        List<SourceData> result = overStandardAnalysisService.overStandardAnalysisByMonitorObjectByVoltClass(objectAndVolParam);
        return ResultWithTotal.success(result, result.size());
    }

    @PostMapping("/countByMonitorObjectByDate")
    @ApiOperation("按监测对象类型统计-按越线日期超标分布")
    public Result overStandardAnalysisByMonitorObjectByDate(@RequestBody @Valid @ApiParam(name = "countByObjectParam", value = "参数", required = true)
                                                                    ObjectAndVolParam objectAndVolParam) {
        List<SourceData> result = overStandardAnalysisService.overStandardAnalysisByMonitorObjectByDate(objectAndVolParam);
        return ResultWithTotal.success(result, result.size());
    }

    @GetMapping("/countBySubstationByDay")
    @ApiOperation("按变电站统计-全网变电站稳态指标分布统计")
    public ResultWithTotal<List<DataBySubstationByDay>> overStandardAnalysisBySubstationByDay(@RequestParam int aggregationCycle, @RequestParam long startTime, @RequestParam long endTime, boolean isUpLoad) {
        List<DataBySubstationByDay> result = overStandardAnalysisService.overStandardAnalysisBySubstationByDay(startTime, endTime, aggregationCycle,isUpLoad);
        return ResultWithTotal.success(result, result.size());
    }

    @GetMapping("/countBySubstationByVoltageLevel")
    @ApiOperation("按变电站统计-全网变电站稳态指标分布情况(按电压等级)")
    public ResultWithTotal<List<DataBySubstationByDayVoltageLevel>> overStandardAnalysisBySubstationByVoltageLevel(@RequestParam int aggregationCycle, @RequestParam long startTime, @RequestParam long endTime, boolean isUpLoad) {
        List<DataBySubstationByDayVoltageLevel> result = overStandardAnalysisService.overStandardAnalysisBySubstationByVoltageLevel(startTime, endTime, aggregationCycle,isUpLoad);
        return ResultWithTotal.success(result, result.size());
    }

    @GetMapping("/countBySubstation")
    @ApiOperation("按变电站统计-稳态指标超标的变电站")
    public ResultWithTotal<List<SubstationOverStandardCount>> overStandardAnalysisBySubstationCount(@RequestParam int aggregationCycle, @RequestParam long startTime, @RequestParam long endTime, boolean isUpLoad) {
        List<SubstationOverStandardCount> result = overStandardAnalysisService.substationOverStandardCount(startTime, endTime, aggregationCycle,isUpLoad);
        return ResultWithTotal.success(result, result.size());
    }

    @ApiOperation("导出按变电站统计")
    @PostMapping("/exportCountBySubstationByDay")
    public void exportCountBySubstationByDay(@RequestBody ExportOverSubstationParam exportOverSubstationParam, HttpServletResponse response) {
        overStandardAnalysisService.exportCountBySubstationByDay(exportOverSubstationParam, response);
    }
}


# 数据访问规范

1. 所有模型表CRUD必须通过统一工具类和feign接口实现，严禁直接用Mapper/JPA/Repository/SQL。
2. 设备数据访问请使用DataServiceRestApi，权限相关请用UserRestApi，节点相关请用PecNodeService等feign接口。
3. 数据库通用模型数据访问统一通过PqModelServiceUtils，支持单模型、多模型、层级关系、批量写入、删除等操作。
4. 所有操作必须处理返回值、异常和空值，保证健壮性。返回值、异常、空值处理应有统一工具或模板，避免遗漏。
5. 数据访问层应便于扩展和维护，避免重复造轮子。
6. 优先复用现有feign接口和工具类，不准胡乱编造，假设有其他接口，只能使用现有的接口进行开发。
7. 请学习 src/main/java/com/cet/pq/pqcommonservice/utils/PqModelServiceUtils.java代码内容， 以便在后续使用这些依赖生成代码时，生成正确的代码内容

## 典型接口用法举例

### 1. DataServiceRestApi 
- 说明：设备数据相关的查询、实时数据、定时记录、事件、波形等。
- 常用方法举例：
  ```java
  // 查询设备实时数据
  Result<Object> result = dataServiceRestApi.getMeasureDataByDataIdAndLogicalId(dataId, logicalId, deviceIds);
  // 查询定时记录（分钟级）
  Result<List<TrendDataVo>> trendResult = dataServiceRestApi.queryMultiMeterDataLogGroupData(searchVo, true);
  ```
- 注意事项：参数校验、返回值判空、异常处理、日志记录。

### 2. UserRestApi, LoginRestApi, UserGroupRestApi
- 说明：权限、用户、用户组、登录、token等相关操作。
- 常用方法举例：
  ```java
  // 获取用户信息
  Result<User> userResult = userRestApi.getUser(userId);
  // 用户登录
  Result<LoginRes> loginRes = loginRestApi.login(loginInfo);
  // 查询用户组
  Result<List<UserGroup>> groups = userGroupRestApi.getAllUserGroups(tenantId);
  ```
- 注意事项：鉴权信息传递、返回值判空、异常处理。

### 3. PecNodeService
- 说明：设备节点、变电站、通道、设备类型、节点关系等。
- 常用方法举例：
  ```java
  // 获取所有变电站
  Result<List<StationAndChannel>> stations = pecNodeService.getStations();
  // 获取通道下设备
  Result<List<Device>> devices = pecNodeService.getDevicesByChannelId(channelId);
  // 新建设备节点
  Result<Object> addRes = pecNodeService.addDevices(deviceNodeList);
  // 删除设备
  Result<Object> delRes = pecNodeService.deleteDevice(deviceIdList);
  ```
- 注意事项：批量操作数量限制、返回值判空、异常处理、日志记录。

### 4. PqModelServiceUtils
- 说明：通用模型数据访问，支持单模型、多模型、层级关系、批量写入、删除等。
- 常用方法举例：
  ```java
  // 查询单模型数据
  List<MyModel> list = PqModelServiceUtils.querySingleModel(ids, TableName.MY_MODEL, filters, orders, page, MyModel.class);
  // 查询层级关系数据
  List<Map<String, Object>> relationData = PqModelServiceUtils.getRelations(parentIds, TableName.MY_MODEL, filters, modelIdPairDTO, subConditions);
  // 写入数据
  PqModelServiceUtils.write(dataList, ...);
  // 删除数据
  PqModelServiceUtils.delete(ids, TableName.MY_MODEL);
  ```
- 注意事项：返回值判空、异常处理、批量操作建议、统一封装。

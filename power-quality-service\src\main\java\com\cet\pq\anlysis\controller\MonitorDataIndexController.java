package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.common.TendencyParam;
import com.cet.pq.anlysis.model.dataindex.DataIndexDetailParam;
import com.cet.pq.anlysis.model.dataindex.DataIndexQueryParam;
import com.cet.pq.anlysis.model.excel.ExportMonitorDataIndexParam;
import com.cet.pq.anlysis.model.monitordatastatistics.*;
import com.cet.pq.anlysis.service.MonitorDataIndexService;
import com.cet.pq.common.model.ResultWithTotal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description 监测数据指标管理接口
 * @date 2020-08-21
 */
@RestController
@Api(value = "监测数据指标管理接口", tags = "指标管理 - 监测数据统计接口")
@RequestMapping("/pq/v1")
public class MonitorDataIndexController {

    @Autowired
    private MonitorDataIndexService monitorDataIndexService;


    @ApiOperation("监测数据指标综合统计")
    @PostMapping("/data/statistics")
    public ResultWithTotal<List<LineDataStatistics>> getDataCount(@RequestBody DataIndexQueryParam dataIndexQueryParam) {
        List<LineDataStatistics> monitorDataStatisticsList = monitorDataIndexService.getMonitorDataCount(dataIndexQueryParam);
        return ResultWithTotal.success(monitorDataStatisticsList, monitorDataStatisticsList.size());
    }

    @ApiOperation("监测数据指标统计")
    @PostMapping("/data/index/statistics")
    public ResultWithTotal<List<LineIndex>> getDataIndexCount(@RequestBody DataIndexQueryParam dataIndexQueryParam) {
        List<LineIndex> lineIndexInProvinceList = monitorDataIndexService.getMonitorDataIndexCount(dataIndexQueryParam);
        return ResultWithTotal.success(lineIndexInProvinceList, lineIndexInProvinceList.size());
    }

    @ApiOperation("监测数据指标明细统计")
    @PostMapping("/datadetail/index/statistics")
    public ResultWithTotal<List<DimensionDataDetail>> getDataDetailCount(@RequestBody DataIndexDetailParam dataIndexDetailParam) {
        List<DimensionDataDetail> lineDataDetailList = monitorDataIndexService.getLineDataDetaiCount(dataIndexDetailParam);
        return ResultWithTotal.success(lineDataDetailList, lineDataDetailList.size());
    }

    @ApiOperation("单位监测数据指趋势")
    @PostMapping("/datadetail/tendency")
    public ResultWithTotal<List<LineIndexTendency>> getDataTenDencyByCompany(@RequestBody TendencyParam tendencyParam) {
        List<LineIndexTendency> lineIndexInProvinceList = monitorDataIndexService.getDataTenDencyByDimension(tendencyParam);
        return ResultWithTotal.success(lineIndexInProvinceList, lineIndexInProvinceList.size());
    }

    @ApiOperation("导出检测数据指标")
    @PostMapping("/data/exportStatistics")
    private void exportStatistics(@RequestBody ExportMonitorDataIndexParam exportMonitorDataIndexParam, HttpServletResponse response) {
        monitorDataIndexService.exportStatistics(exportMonitorDataIndexParam, response);
    }
}

package com.cet.pq.common.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/11 8:42
 */
@Data
@AllArgsConstructor
public class GbRecalcPara {
    @JsonProperty("ratedvolt")
    private Integer ratedVolt;
    @JsonProperty("shortcircuitcapacity")
    private Double shortCircuitCapacity;
    @JsonProperty("userprotocolcapacity")
    private Double userProtocolCapacity;
    @JsonProperty("supplyequipmentcapacity")
    private Double supplyEquipmentCapacity;
    @JsonProperty("lineid")
    private Long lineId;
    @JsonProperty("wiredtype")
    private Integer wiredType;
    /**
     * 电压上偏
     */
    @JsonProperty("upvoltdeviation")
    private Double upVoltDeviation;
    /**
     * 电压下偏
     */
    @JsonProperty("lowvoltdeviation")
    private Double lowVoltDeviation;

    public GbRecalcPara(){
        this.upVoltDeviation = 7.0;
        this.lowVoltDeviation = -7.0;
    }

}

package com.cet.pq.common.time;

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.model.objective.physicalquantity.AggregationCycle;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Data Created in ${Date}
 */
public class TimeCoordinate {
	
    private static final Logger logger = LoggerFactory.getLogger(TimeCoordinate.class);

    /**
     * 生成时间戳序列
     *
     * @param startTime
     * @param endTime
     * @param periodType
     * @return
     */
    public static List<Long> generateTimeRange(Date startTime, Date endTime, int periodType) {
        List<Date> dates = generateTimeValueOfPeriod(startTime, endTime, periodType);
        List<Long> result = new ArrayList<>();
        for (Date date : dates) {
            result.add(DateUtils.dateToTimeStamp(date));
        }

        return result;
    }

    /**
     * 生成时段内的时间坐标
     *
     * @param startTime 起始时间
     * @param endTime   终止时间
     * @return 构建时段内的时间列表
     */
    public static List<Date> generateTimeValueOfPeriod(Date startTime, Date endTime, int periodType) {
        DateTime newStart = new DateTime(startTime.getTime());
        DateTime newEnd = new DateTime(endTime.getTime());
        List<Date> result = new ArrayList<Date>();
        try {
            if (periodType == AggregationCycle.ONE_YEAR) {
                //年
                String origYear = DateUtils.dateToStr(startTime, TimeFormat.YEAR_TIME_FORMAT);
                Date newWholeYear = DateUtils.stringToDate(origYear, TimeFormat.YEAR_TIME_FORMAT);
                newStart = new DateTime(newWholeYear.getTime());
                while (newStart.isBefore(newEnd)) {
                    result.add(newStart.toDate());
                    newStart = newStart.plusYears(1);
                }

            } else if (periodType == AggregationCycle.ONE_MONTH) {
                //月
                String orgDay = DateUtils.dateToStr(startTime, TimeFormat.MONTH_TIME_FORMAT);
                Date newWholeDate = DateUtils.stringToDate(orgDay, TimeFormat.MONTH_TIME_FORMAT);
                newStart = new DateTime(newWholeDate.getTime());
                while (newStart.isBefore(newEnd)) {
                    result.add(newStart.toDate());
                    newStart = newStart.plusMonths(1);
                }
            } else if (periodType == AggregationCycle.ONE_DAY) {
                //日
                String orgDay = DateUtils.dateToStr(startTime, TimeFormat.DATE_TIME_FORMAT);
                Date newWholeDate = DateUtils.stringToDate(orgDay, TimeFormat.DATE_TIME_FORMAT);
                newStart = new DateTime(newWholeDate.getTime());
                while (newStart.isBefore(newEnd)) {
                    result.add(newStart.toDate());
                    newStart = newStart.plusDays(1);
                }
            } else if (periodType == AggregationCycle.ONE_HOUR) {
                //小时
                String orgDay = DateUtils.dateToStr(startTime, TimeFormat.HOUR_TIME_FORMAT);
                Date newWholeDate = DateUtils.stringToDate(orgDay, TimeFormat.HOUR_TIME_FORMAT);
                newStart = new DateTime(newWholeDate.getTime());
                while (newStart.isBefore(newEnd)) {
                    result.add(newStart.toDate());
                    newStart = newStart.plusHours(1);
                }
            }
        } catch (Exception ex) {
        	logger.debug(CommonConstant.CONTEXT,ex);
        }
        return result;
    }
}

package com.cet.pq.common.aspect;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.utils.RedisUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName SessionModelAspect
 * @Description TODO
 * @Date 2020/11/16
 */

@Aspect
@Component
public class SessionModelAspect {

    private static final Logger logger = LoggerFactory.getLogger(SessionModelAspect.class);

    @Pointcut("@annotation(com.cet.pq.common.annotation.SessionModel)")
    public void pointcut() {

    }

    @AfterReturning(value = "pointcut()", returning = "result")
    private void after(JoinPoint joinPoint, Result result) throws NoSuchFieldException, IllegalAccessException {
        Object[] args = joinPoint.getArgs();
        String modelLabel = null;
        if(args[0] instanceof String){
            modelLabel = (String)args[0];
        }else if(args[0] instanceof Collection){
            List list = (List)args[0];
            if(list.get(0) instanceof Map){
                //写层级关系
                Map map = (Map)list.get(0);
                Iterator<String> iterator = map.keySet().iterator();
                while (iterator.hasNext()){
                    String next = iterator.next();
                    if(next.contains(ColumnName.MODEL1)){
                        modelLabel = next.replace(ColumnName.MODEL1,"");
                    }
                }
            }else {
                //单模型写
                Field declaredField = list.get(0).getClass().getDeclaredField("modelLabel");
                declaredField.setAccessible(Boolean.TRUE);
                modelLabel = (String) declaredField.get(list.get(0));
            }
        }
        if(TableName.PROVINCECOMPANY.equals(modelLabel) || TableName.CITYCOMPANY.equals(modelLabel)
                ||TableName.COUNTYCOMPANY.equals(modelLabel)){
            modelLabel = ColumnName.COMPANY;
        }
        if (Result.SUCCESS_CODE == result.getCode() && (ColumnName.COMPANY.equals(modelLabel) || TableName.SUBSTATION.equals(modelLabel) || TableName.PLATFORMAREA.equals(modelLabel)
                || TableName.PQTERMINAL.equals(modelLabel) || TableName.LINE.equals(modelLabel) || TableName.OFFLINETEST.equals(modelLabel))
        || TableName.DISTRIBUTION_TRANSFORMER.equals(modelLabel) || TableName.MAIN_TRANSFORMER.equals(modelLabel)) {
           RedisUtils.delete(modelLabel);
        }
        //清除缓存的节点树key
        RedisUtils.batchDelete(CommonConstant.USER_SUB_UNIT_KEY + "*");
        RedisUtils.batchDelete(CommonConstant.USER_TREE_MAP + "*");
        RedisUtils.batchDelete(CommonConstant.USER_RELATION_TREE + "*");
        RedisUtils.batchDelete(CommonConstant.COMMON_TREE_NODE + "*");
        RedisUtils.batchDelete(CommonConstant.TOP_TREE_NODE + "*");
    }
}

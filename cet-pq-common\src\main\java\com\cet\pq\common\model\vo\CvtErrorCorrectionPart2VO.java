package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 11:13
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CvtErrorCorrectionPart2VO {

    /**
     * 数值
     */
    private Object value;

    /**
     * 数值类型：0：测量值，1：修正值
     */
    private Integer valueType;

    /**
     * 相别：0：A，1：B，2：C
     */
    private Integer phase;

    /**
     * 测量值的95%概率大值
     */
    private Double testValue;

    /**
     * 修正值的95%概率大值
     */
    private Double correctionValue;

    /**
     * 监测时间
     */
    private Long logtime;

    /**
     * CVT影响超标谐波次数
     */
    private Integer harmonicCount;

}

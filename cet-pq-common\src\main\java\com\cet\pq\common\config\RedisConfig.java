package com.cet.pq.common.config;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * Redis默认配置
 * <AUTHOR>
 * @Date 2020-12-31
 */
@SuppressWarnings({"rawtypes","unchecked"})
@Configuration
@Slf4j
public class RedisConfig {

    @Value("${spring.redis.database}")
    private Integer database;

	/*@Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory factory) {
        StringRedisTemplate template = new StringRedisTemplate(factory);
        //jackson将java对象转换成json对象。
		Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        template.setValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }
	
	
	@Bean
	  public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
		//缓存默认过期时间；1min
	    RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
	      .entryTtl(Duration.ofSeconds(60*1));
	    return RedisCacheManager
	      .builder(RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory))
	      .cacheDefaults(redisCacheConfiguration).build();
	  }*/


    private RedisConnectionFactory createRedisConnectionFactory(int database, JedisConnectionFactory defaultJedisConnectionFactory) {
        JedisClientConfiguration.JedisPoolingClientConfigurationBuilder builder = (JedisClientConfiguration.JedisPoolingClientConfigurationBuilder)JedisClientConfiguration.builder();
        builder.poolConfig(Objects.<GenericObjectPoolConfig>requireNonNull(defaultJedisConnectionFactory.getPoolConfig()));
        JedisClientConfiguration jedisClientConfiguration = builder.build();
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(createRedisStandaloneConfig(database, defaultJedisConnectionFactory), jedisClientConfiguration);
        jedisConnectionFactory.afterPropertiesSet();
        return (RedisConnectionFactory)jedisConnectionFactory;
    }

    private RedisStandaloneConfiguration createRedisStandaloneConfig(int database, JedisConnectionFactory defaultJedisConnectionFactory) {
        RedisStandaloneConfiguration redisStandaloneConfig = new RedisStandaloneConfiguration();
        redisStandaloneConfig.setHostName(defaultJedisConnectionFactory.getHostName());
        redisStandaloneConfig.setPort(defaultJedisConnectionFactory.getPort());
        redisStandaloneConfig.setPassword(defaultJedisConnectionFactory.getPassword());
        redisStandaloneConfig.setDatabase(database);
        return redisStandaloneConfig;
    }

    @ConditionalOnMissingBean(name = {"pq4.2-redisTemplate"})
    @Bean(name = {"pq4.2-redisTemplate"})
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory defaultRedisConnectionFactory) {
        RedisConnectionFactory redisConnectionFactory = createRedisConnectionFactoryIfEmbbed(defaultRedisConnectionFactory);
        return (RedisTemplate<String, String>)new StringRedisTemplate(redisConnectionFactory);
    }

    public RedisConnectionFactory createRedisConnectionFactoryIfEmbbed(RedisConnectionFactory defaultRedisConnectionFactory) {
        RedisConnectionFactory redisConnectionFactory = defaultRedisConnectionFactory;

        if (defaultRedisConnectionFactory instanceof JedisConnectionFactory) {
            log.info("the redisConnectionFactory is JedisConnectionFactory type");
            JedisConnectionFactory defaultJedisConnectionFactory = (JedisConnectionFactory)defaultRedisConnectionFactory;
            if (this.database != null && !this.database.equals(Integer.valueOf(defaultJedisConnectionFactory.getDatabase())))
                redisConnectionFactory = createRedisConnectionFactory(this.database.intValue(), defaultJedisConnectionFactory);
        } else {
            log.info("the redisConnectionFactory is LettuceConnectionFactory type");
        }
        return redisConnectionFactory;
    }



	
}

package com.cet.pq.common.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName FlatWriteData
 * @Description TODO
 * @Date 2020/12/3
 */
@Data
public class FlatWriteData {
    private List<String> filter;
    private List<List<Object>> filterData;
    private String modelLabel;
    private String operator;
    private List<List<Object>> writeData;
    private String writeMethod;
    private List<String> writeProperty;
}

package com.cet.pq.common.encode.sm4;

//====================================================================================================
//The Free Edition of C# to Java Converter limits conversion output to 100 lines per snippet.

//To purchase the Premium Edition, visit our website:
//https://www.tangiblesoftwaresolutions.com/order/order-csharp-to-java.html
//====================================================================================================

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.utils.CommonUtils;

/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public class CSm4
{
    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static readonly byte[] sbox = { 0xD6, 0x90, 0xE9, 0xFE, 0xCC, 0xE1, 0x3D, 0xB7, 0x16, 0xB6, 0x14, 0xC2, 0x28, 0xFB, 0x2C, 0x05, 0x2B, 0x67, 0x9A, 0x76, 0x2A, 0xBE, 0x04, 0xC3, 0xAA, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99, 0x9C, 0x42, 0x50, 0xF4, 0x91, 0xEF, 0x98, 0x7A, 0x33, 0x54, 0x0B, 0x43, 0xED, 0xCF, 0xAC, 0x62, 0xE4, 0xB3, 0x1C, 0xA9, 0xC9, 0x08, 0xE8, 0x95, 0x80, 0xDF, 0x94, 0xFA, 0x75, 0x8F, 0x3F, 0xA6, 0x47, 0x07, 0xA7, 0xFC, 0xF3, 0x73, 0x17, 0xBA, 0x83, 0x59, 0x3C, 0x19, 0xE6, 0x85, 0x4F, 0xA8, 0x68, 0x6B, 0x81, 0xB2, 0x71, 0x64, 0xDA, 0x8B, 0xF8, 0xEB, 0x0F, 0x4B, 0x70, 0x56, 0x9D, 0x35, 0x1E, 0x24, 0x0E, 0x5E, 0x63, 0x58, 0xD1, 0xA2, 0x25, 0x22, 0x7C, 0x3B, 0x01, 0x21, 0x78, 0x87, 0xD4, 0x00, 0x46, 0x57, 0x9F, 0xD3, 0x27, 0x52, 0x4C, 0x36, 0x02, 0xE7, 0xA0, 0xC4, 0xC8, 0x9E, 0xEA, 0xBF, 0x8A, 0xD2, 0x40, 0xC7, 0x38, 0xB5, 0xA3, 0xF7, 0xF2, 0xCE, 0xF9, 0x61, 0x15, 0xA1, 0xE0, 0xAE, 0x5D, 0xA4, 0x9B, 0x34, 0x1A, 0x55, 0xAD, 0x93, 0x32, 0x30, 0xF5, 0x8C, 0xB1, 0xE3, 0x1D, 0xF6, 0xE2, 0x2E, 0x82, 0x66, 0xCA, 0x60, 0xC0, 0x29, 0x23, 0xAB, 0x0D, 0x53, 0x4E, 0x6F, 0xD5, 0xDB, 0x37, 0x45, 0xDE, 0xFD, 0x8E, 0x2F, 0x03, 0xFF, 0x6A, 0x72, 0x6D, 0x6C, 0x5B, 0x51, 0x8D, 0x1B, 0xAF, 0x92, 0xBB, 0xDD, 0xBC, 0x7F, 0x11, 0xD9, 0x5C, 0x41, 0x1F, 0x10, 0x5A, 0xD8, 0x0A, 0xC1, 0x31, 0x88, 0xA5, 0xCD, 0x7B, 0xBD, 0x2D, 0x74, 0xD0, 0x12, 0xB8, 0xE5, 0xB4, 0xB0, 0x89, 0x69, 0x97, 0x4A, 0x0C, 0x96, 0x77, 0x7E, 0x65, 0xB9, 0xF1, 0x09, 0xC5, 0x6E, 0xC6, 0x84, 0x18, 0xF0, 0x7D, 0xEC, 0x3A, 0xDC, 0x4D, 0x20, 0x79, 0xEE, 0x5F, 0x3E, 0xD7, 0xCB, 0x39, 0x48 };
    static long Y0=0,Y1=0,Y2=0,Y3=0;
    private static final short[] sbox = {(short)0xD6, (short)0x90, (short)0xE9, (short)0xFE, (short)0xCC, (short)0xE1, 0x3D, (short)0xB7, 0x16, (short)0xB6, 0x14, (short)0xC2, 0x28, (short)0xFB, 0x2C, 0x05, 0x2B, 0x67, (short)0x9A, 0x76, 0x2A, (short)0xBE, 0x04, (short)0xC3, (short)0xAA, 0x44, 0x13, 0x26, 0x49, (short)0x86, 0x06, (short)0x99, (short)0x9C, 0x42, 0x50, (short)0xF4, (short)0x91, (short)0xEF, (short)0x98, 0x7A, 0x33, 0x54, 0x0B, 0x43, (short)0xED, (short)0xCF, (short)0xAC, 0x62, (short)0xE4, (short)0xB3, 0x1C, (short)0xA9, (short)0xC9, 0x08, (short)0xE8, (short)0x95, (short)0x80, (short)0xDF, (short)0x94, (short)0xFA, 0x75, (short)0x8F, 0x3F, (short)0xA6, 0x47, 0x07, (short)0xA7, (short)0xFC, (short)0xF3, 0x73, 0x17, (short)0xBA, (short)0x83, 0x59, 0x3C, 0x19, (short)0xE6, (short)0x85, 0x4F, (short)0xA8, 0x68, 0x6B, (short)0x81, (short)0xB2, 0x71, 0x64, (short)0xDA, (short)0x8B, (short)0xF8, (short)0xEB, 0x0F, 0x4B, 0x70, 0x56, (short)0x9D, 0x35, 0x1E, 0x24, 0x0E, 0x5E, 0x63, 0x58, (short)0xD1, (short)0xA2, 0x25, 0x22, 0x7C, 0x3B, 0x01, 0x21, 0x78, (short)0x87, (short)0xD4, 0x00, 0x46, 0x57, (short)0x9F, (short)0xD3, 0x27, 0x52, 0x4C, 0x36, 0x02, (short)0xE7, (short)0xA0, (short)0xC4, (short)0xC8, (short)0x9E, (short)0xEA, (short)0xBF, (short)0x8A, (short)0xD2, 0x40, (short)0xC7, 0x38, (short)0xB5, (short)0xA3, (short)0xF7, (short)0xF2, (short)0xCE, (short)0xF9, 0x61, 0x15, (short)0xA1, (short)0xE0, (short)0xAE, 0x5D, (short)0xA4, (short)0x9B, 0x34, 0x1A, 0x55, (short)0xAD, (short)0x93, 0x32, 0x30, (short)0xF5, (short)0x8C, (short)0xB1, (short)0xE3, 0x1D, (short)0xF6, (short)0xE2, 0x2E, (short)0x82, 0x66, (short)0xCA, 0x60, (short)0xC0, 0x29, 0x23, (short)0xAB, 0x0D, 0x53, 0x4E, 0x6F, (short)0xD5, (short)0xDB, 0x37, 0x45, (short)0xDE, (short)0xFD, (short)0x8E, 0x2F, 0x03, (short)0xFF, 0x6A, 0x72, 0x6D, 0x6C, 0x5B, 0x51, (short)0x8D, 0x1B, (short)0xAF, (short)0x92, (short)0xBB, (short)0xDD, (short)0xBC, 0x7F, 0x11, (short)0xD9, 0x5C, 0x41, 0x1F, 0x10, 0x5A, (short)0xD8, 0x0A, (short)0xC1, 0x31, (short)0x88, (short)0xA5, (short)0xCD, 0x7B, (short)0xBD, 0x2D, 0x74, (short)0xD0, 0x12, (short)0xB8, (short)0xE5, (short)0xB4, (short)0xB0, (short)0x89, 0x69, (short)0x97, 0x4A, 0x0C, (short)0x96, 0x77, 0x7E, 0x65, (short)0xB9, (short)0xF1, 0x09, (short)0xC5, 0x6E, (short)0xC6, (short)0x84, 0x18, (short)0xF0, 0x7D, (short)0xEC, 0x3A, (short)0xDC, 0x4D, 0x20, 0x79, (short)0xEE, 0x5F, 0x3E, (short)0xD7, (short)0xCB, 0x39, 0x48};

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static readonly uint[] CK = { 0x00070e15, 0x1c232a31, 0x383f464d, 0x545b6269, 0x70777e85, 0x8c939aa1, 0xa8afb6bd, 0xc4cbd2d9, 0xe0e7eef5, 0xfc030a11, 0x181f262d, 0x343b4249, 0x50575e65, 0x6c737a81, 0x888f969d, 0xa4abb2b9, 0xc0c7ced5, 0xdce3eaf1, 0xf8ff060d, 0x141b2229, 0x30373e45, 0x4c535a61, 0x686f767d, 0x848b9299, 0xa0a7aeb5, 0xbcc3cad1, 0xd8dfe6ed, 0xf4fb0209, 0x10171e25, 0x2c333a41, 0x484f565d, 0x646b7279 };
    private static final int[] CK = {0x00070e15, 0x1c232a31, 0x383f464d, 0x545b6269, 0x70777e85, (int)0x8c939aa1, (int)0xa8afb6bd, (int)0xc4cbd2d9, (int)0xe0e7eef5, (int)0xfc030a11, 0x181f262d, 0x343b4249, 0x50575e65, 0x6c737a81, (int)0x888f969d, (int)0xa4abb2b9, (int)0xc0c7ced5, (int)0xdce3eaf1, (int)0xf8ff060d, 0x141b2229, 0x30373e45, 0x4c535a61, 0x686f767d, (int)0x848b9299, (int)0xa0a7aeb5, (int)0xbcc3cad1, (int)0xd8dfe6ed, (int)0xf4fb0209, 0x10171e25, 0x2c333a41, 0x484f565d, 0x646b7279};

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint p(uint A)
    private static int p(long A)
    {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint B;
        int B;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: byte[] a = new byte[4];
        short[] a = new short[4];
//C# TO JAVA CONVERTER WARNING: The right shift operator was replaced by Java's logical right shift operator since the left operand was originally of an unsigned type, but you should confirm this replacement:
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: a[0] = (byte)(A >> 24);
        a[0] = (short)(A >> 24);
//C# TO JAVA CONVERTER WARNING: The right shift operator was replaced by Java's logical right shift operator since the left operand was originally of an unsigned type, but you should confirm this replacement:
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: a[1] = (byte)((A >> 16) & 0xFF);
        a[1] = (short)((A >> 16) & 0xFF);
//C# TO JAVA CONVERTER WARNING: The right shift operator was replaced by Java's logical right shift operator since the left operand was originally of an unsigned type, but you should confirm this replacement:
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: a[2] = (byte)((A >> 8) & 0xFF);
        a[2] = (short)((A >> 8) & 0xFF);
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: a[3] = (byte)((A) & 0xFF);
        a[3] = (short)((A) & 0xFF);

//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: byte[] b = new byte[4];
        short[] b = new short[4];
        b[0] = sbox[a[0]];
        b[1] = sbox[a[1]];
        b[2] = sbox[a[2]];
        b[3] = sbox[a[3]];

//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: B = (uint)b[0] << 24;
        B = (int)b[0] << 24;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: B += (uint)b[1] << 16;
        B += (int)b[1] << 16;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: B += (uint)b[2] << 8;
        B += (int)b[2] << 8;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: B += (uint)b[3];
        B += (int)b[3];

        return B;
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint Lsr32(uint a, int b)
    private static int Lsr32(int a, int b)
    {
//C# TO JAVA CONVERTER WARNING: The right shift operator was replaced by Java's logical right shift operator since the left operand was originally of an unsigned type, but you should confirm this replacement:
        return (((a) << (b)) | ((a) >>>(32 - (b))));
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint L(uint B)
    private static int L(int B)
    {
        return B ^ (Lsr32(B, 2)) ^ (Lsr32(B, 10)) ^ (Lsr32(B, 18)) ^ (Lsr32(B, 24));
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint L1(uint B)
    private static int L1(int B)
    {
        return B ^ (Lsr32(B, 13)) ^ Lsr32(B, 23);
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint T(uint R)
    private static long T(long R)
    {
        return L(p(R));
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint T1(uint R)
    private static long T1(long R)
    {
        return L1(p(R));
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint F(uint X0, uint X1, uint X2, uint X3, uint rk)
    private static long F(long X0, long X1, long X2, long X3, long rk)
    {
        return abs(X0 ^ T(abs(X1) ^ abs(X2) ^ abs(X3) ^ abs(rk)));
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint F1(uint X0, uint X1, uint X2, uint X3, uint rk)
    private static long F1(long X0, long X1, long X2, long X3, long rk)
    {
        return X0 ^ T1(abs(X1) ^ abs(X2) ^ abs(X3) ^ abs(rk));
    }

    public static long abs(long i) {
        if (i < 0) {
            return 4294967296L + i;
        } else {
            return i;
        }
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static readonly uint FK0=0xA3B1BAC6;
    private static final int FK0 = 0xA3B1BAC6;
    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static readonly uint FK1=0x56AA3350;
    private static final int FK1 = 0x56AA3350;
    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static readonly uint FK2=0x677D9197;
    private static final int FK2 = 0x677D9197;
    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static readonly uint FK3=0xB27022DC;
    private static final int FK3 = 0xB27022DC;

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static void Encript(uint K0, uint K1, uint K2, uint K3, uint X0, uint X1, uint X2, uint X3, out uint Y0, out uint Y1, out uint Y2, out uint Y3)
    private static void Encript(long K0, long K1, long K2, long K3, long X0, long X1, long X2, long X3)
    {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint rk;
        long rk;
        int i;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint T;
        long T;

        K0 = K0 ^ FK0;
        K1 = K1 ^ FK1;
        K2 = K2 ^ FK2;
        K3 = K3 ^ FK3;


        for (i = 0; i < CommonConstant.NUMBER_THIRTY_TWO; i++)
        {
            rk = F1(K0, K1, K2, K3, CK[i]);
            K0 = K1;
            K1 = K2;
            K2 = K3;
            K3 = rk;

            T = F(X0, X1, X2, X3, rk);
            X0 = X1;
            X1 = X2;
            X2 = X3;
            X3 = T;
        }

        Y0 = X3;
        Y1 = X2;
        Y2= X1;
        Y3= X0;
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static void Decript(uint K0, uint K1, uint K2, uint K3, uint X0, uint X1, uint X2, uint X3, out uint Y0, out uint Y1, out uint Y2, out uint Y3)
    private static void Decript(long K0, long K1, long K2, long K3, long X0, long X1, long X2, long X3)
    {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint[] rk = new uint[32];
        long[] rk = new long[32];
        int i;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint T;
        long T;

        K0 = K0 ^ FK0;
        K1 = K1 ^ FK1;
        K2 = K2 ^ FK2;
        K3 = K3 ^ FK3;



        for (i = 0; i < CommonConstant.NUMBER_THIRTY_TWO; i++)
        {
            rk[i] = F1(K0, K1, K2, K3, CK[i]);
            K0 = K1;
            K1 = K2;
            K2 = K3;
            K3 = rk[i];
        }
        for (i = 0; i < CommonConstant.NUMBER_THIRTY_TWO; i++)
        {
            T = F(X0, X1, X2, X3, rk[31 - i]);
            X0 = X1;
            X1 = X2;
            X2 = X3;
            X3 = T;
        }

        Y0 = X3;
        Y1 = X2;
        Y2 = X1;
        Y3 = X0;
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static uint GET_ULONG_BE(byte[] b, uint i)
    private static long GET_ULONG_BE(byte[] b, int i)
    {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: return ((uint)b[i] << 24) | ((uint)b[i + 1] << 16) | ((uint)b[i + 2] << 8) | ((uint)b[i + 3]);
        return (abs(b[i]) << 24) | (abs(b[i + 1]) << 16) | (abs(b[i + 2]) << 8) | (abs(b[i + 3]));
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: static void PUT_ULONG_BE(uint n, byte[] b, uint i)
    private static void PUT_ULONG_BE(long n, byte[] b, int i)
    {
//C# TO JAVA CONVERTER WARNING: The right shift operator was replaced by Java's logical right shift operator since the left operand was originally of an unsigned type, but you should confirm this replacement:
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: (b)[(i)] = (byte)((n) >> 24);
        (b)[(i)] = (byte)(0xFF & (n) >> 24);
//C# TO JAVA CONVERTER WARNING: The right shift operator was replaced by Java's logical right shift operator since the left operand was originally of an unsigned type, but you should confirm this replacement:
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: (b)[(i) + 1] = (byte)((n) >> 16);
        (b)[(i) + 1] = (byte)(0xFF & (n) >> 16);
//C# TO JAVA CONVERTER WARNING: The right shift operator was replaced by Java's logical right shift operator since the left operand was originally of an unsigned type, but you should confirm this replacement:
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: (b)[(i) + 2] = (byte)((n) >> 8);
        (b)[(i) + 2] = (byte)(0xFF & (n) >> 8);
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: (b)[(i) + 3] = (byte)((n));
        (b)[(i) + 3] = (byte)((0xFF & n));
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: public static byte[] EncriptB(byte[] bKey, byte[] inData)
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: public static byte[] EncriptB(byte[] bKey, byte[] inData)
    public static byte[] EncriptB(byte[] bKey, byte[] inData)
    {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: byte[] outData = new byte[16];
        byte[] outData = new byte[16];
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint K0, K1, K2, K3;
        long K0, K1, K2, K3;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint D0, D1, D2, D3;
        long D0, D1, D2, D3;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint Y0, Y1, Y2, Y3;
        K0 = GET_ULONG_BE(bKey, 0);
        K1 = GET_ULONG_BE(bKey, 4);
        K2 = GET_ULONG_BE(bKey, 8);
        K3 = GET_ULONG_BE(bKey, 12);
        D0 = GET_ULONG_BE(inData, 0);
        D1 = GET_ULONG_BE(inData, 4);
        D2 = GET_ULONG_BE(inData, 8);
        D3 = GET_ULONG_BE(inData, 12);
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: Encript(K0, K1, K2, K3, D0, D1, D2, D3, out Y0, out Y1, out Y2, out Y3);
        Encript(K0, K1, K2, K3, D0, D1, D2, D3);

        PUT_ULONG_BE(Y0, outData, 0);
        PUT_ULONG_BE(Y1, outData, 4);
        PUT_ULONG_BE(Y2, outData, 8);
        PUT_ULONG_BE(Y3, outData, 12);
        return outData;
    }

    //C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: public static byte[] DecriptB(byte[] bKey, byte[] inData)
    public static byte[] DecriptB(byte[] bKey, byte[] inData)
    {
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: byte[] outData = new byte[16];
        byte[] outData = new byte[16];
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint K0, K1, K2, K3;
        long K0, K1, K2, K3;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint D0, D1, D2, D3;
        long D0, D1, D2, D3;
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: uint Y0, Y1, Y2, Y3;
        K0 = GET_ULONG_BE(bKey, 0);
        K1 = GET_ULONG_BE(bKey, 4);
        K2 = GET_ULONG_BE(bKey, 8);
        K3 = GET_ULONG_BE(bKey, 12);
        D0 = GET_ULONG_BE(inData, 0);
        D1 = GET_ULONG_BE(inData, 4);
        D2 = GET_ULONG_BE(inData, 8);
        D3 = GET_ULONG_BE(inData, 12);
//C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct equivalent in Java:
//ORIGINAL LINE: Decript(K0, K1, K2, K3, D0, D1, D2, D3, out Y0, out Y1, out Y2, out Y3);
        Decript(K0, K1, K2, K3, D0, D1, D2, D3);

        PUT_ULONG_BE(Y0, outData, 0);
        PUT_ULONG_BE(Y1, outData, 4);
        PUT_ULONG_BE(Y2, outData, 8);
        PUT_ULONG_BE(Y3, outData, 12);

        return outData;
    }

    //static public int test()
    //{
    //    int i;

    //    byte[] indata = { 0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef, 0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10 };
    //    byte[] key = { 0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef, 0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10 };
    //    byte[] bout = new byte[16];

    //    for (i = 0; i < 1000000; i++)
    //    {
    //        indata = EncriptB(key, indata);
    //        bout = DecriptB(key, indata);
    //    }

    //    return 0;
    //}

}

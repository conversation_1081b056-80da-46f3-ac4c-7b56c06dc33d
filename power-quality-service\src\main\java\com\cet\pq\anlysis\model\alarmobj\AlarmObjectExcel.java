package com.cet.pq.anlysis.model.alarmobj;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "告警对象台账导出模型")
public class AlarmObjectExcel {

    @ExcelIgnore
//    @ExcelProperty(index = 1)
    private Long id;

    @ExcelProperty(index = 0)
    private String rowid;
    // 日志序号
    @ExcelProperty(index = 1)
    private String name;
    // 用户名
    @ExcelProperty(index = 2)
    private String mobile;
    // 用户id
    @ExcelProperty(index = 3)
    private Boolean iffeedback;
    // 用户身份
    @ExcelProperty(index = 4)
    private Integer auditstatus;
    // 操作类型
    @ExcelProperty(index = 5)
    private String solveexplain;
    // 操作情况
    @ExcelProperty(index = 6)
    @DateTimeFormat("yyyy年MM月dd日")
    private Date logtime;
}

package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.common.constants.Constants;
import com.cet.pq.anlysis.model.linestatistics.InventoryIndexCount;
import com.cet.pq.anlysis.model.monitordatastatistics.*;
import com.cet.pq.anlysis.model.substationstatics.MonitorDataBySubstation;
import com.cet.pq.anlysis.service.InventoryIndexService;
import com.cet.pq.anlysis.service.MonitorDataIndexService;
import com.cet.pq.anlysis.service.MonitorDataOverViewService;
import com.cet.pq.common.model.ResultWithTotal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @descrioption 全景概览 - 监测数据统计接口"
 * @date 2020-08-21
 */
@Api(value = "/pq/v1/", tags = "全景概览 - 监测数据统计接口")
@RequestMapping("/pq/v1/overview")
@RestController
public class MonitorDataOverViewController {

	@Autowired
	private MonitorDataOverViewService monitorDataOverViewService;

	@Autowired
	private InventoryIndexService inventoryIndexService;

	@Autowired
	private MonitorDataIndexService monitorDataIndexService;

	@ApiOperation("根据行政区域统计监测数据指标")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isupload", value = "是否上送，不传查询全网", required = false, dataType = "Boolean"),
			@ApiImplicitParam(name = "starttime", value = "查询开始时间", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "endtime", value = "查询结束时间", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "aggregationcycle", value = "聚合周期", required = true, dataType = "int")
	})
	@GetMapping("/area/data/index")
	public ResultWithTotal<List<MonitorDataInArea>> getDataIndexInArea(Boolean isUpload, @RequestParam Long startTime, @RequestParam Long endTime,
                                                                       @RequestParam Integer aggregationCycle) {
		List<MonitorDataInArea> monitorDataIndexList = monitorDataIndexService.getMonitorDataInArea(isUpload, Constants.COUNT_TYPE_ONE, startTime, endTime, aggregationCycle);
		//变电站级
		List<MonitorDataInArea> monitorDataIndexListBySubstation = monitorDataIndexService.getMonitorDataInAreaBySubstation(isUpload, Constants.COUNT_TYPE_ONE, startTime, endTime, aggregationCycle);
		monitorDataIndexList.addAll(monitorDataIndexListBySubstation);
		return ResultWithTotal.success(monitorDataIndexList, monitorDataIndexList.size());
	}

	@ApiOperation(value = "获取上级(网级或者省级)单位监测数据指标")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isupload", value = "是否上送，不传查询全网", required = false, dataType = "Boolean"),
			@ApiImplicitParam(name = "starttime", value = "查询开始时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "endtime", value = "查询结束时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "aggregationcycle", value = "查询单元", required = false, dataType = "int") })
	@GetMapping("/monitor-data/company/index")
	public ResultWithTotal<List<MonitorDataInCompany>> getMonitorDateInProvinceCompany(Boolean isupload, @RequestParam Long starttime,
                                                                                       @RequestParam Long endtime, @RequestParam Integer aggregationcycle) {
		List<MonitorDataInCompany> list = monitorDataOverViewService.getMonitorDataInRootNode(isupload, starttime, endtime, aggregationcycle);
		return ResultWithTotal.success(list, list.size());
	}

	@ApiOperation(value = "获取下级(省级或者地级)单位监测数据指标")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isupload", value = "是否上送，不传查询全网", required = false, dataType = "Boolean"),
			@ApiImplicitParam(name = "starttime", value = "查询开始时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "endtime", value = "查询结束时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "aggregationcycle", value = "查询单元", required = false, dataType = "int") })
	@GetMapping("/monitor-data/subcompany/index")
	public ResultWithTotal<List<MonitorDataInCompany>> getMonitorDateInCityCompany(Boolean isUpload, @RequestParam Long startTime, @RequestParam Long endTime,
			@RequestParam Integer aggregationCycle) {
		List<MonitorDataInCompany> cityMonitorDataList = monitorDataOverViewService.getMonitorDateInSubLayerNode(isUpload, startTime, endTime, aggregationCycle);
		return ResultWithTotal.success(cityMonitorDataList, cityMonitorDataList.size());
	}

	@ApiOperation(value = "获取监测指标top3的下级单位")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isupload", value = "是否上送，不传查询全网", required = false, dataType = "Boolean"),
			@ApiImplicitParam(name = "starttime", value = "查询开始时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "endtime", value = "查询结束时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "aggregationcycle", value = "查询单元", required = false, dataType = "int") })
	@GetMapping("/monitor-data/index/top3")
	public ResultWithTotal<List<TopThreeIndexComPany>> getTop3SubCompany(@RequestParam Boolean isUpload, @RequestParam(value = "startTime") Long startTime, @RequestParam(value = "endTime") Long endTime,
																		 @RequestParam(value = "aggregationCycle") Integer aggregationCycle) {
		List<TopThreeIndexComPany> top3IndexCompany = monitorDataOverViewService.getTop3SubCompany(isUpload, startTime, endTime, aggregationCycle);
		return ResultWithTotal.success(top3IndexCompany, top3IndexCompany.size());
	}

	@ApiOperation(value = "厂家维度查询监测数据")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isupload", value = "是否上送，不传查询全网", required = false, dataType = "Boolean"),
			@ApiImplicitParam(name = "starttime", value = "查询开始时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "endtime", value = "查询结束时间", required = false, dataType = "Long"),
			@ApiImplicitParam(name = "aggregationcycle", value = "查询单元", required = false, dataType = "int") })
	@GetMapping("/monitor-data/vendor/statistics")
	public ResultWithTotal<List<VendorIndex>> getMonitorDateCountByVendor(Boolean isUpload, @RequestParam(value = "startTime") Long startTime, @RequestParam(value = "endTime") Long endTime,
																		  @RequestParam(value = "aggregationCycle") Integer aggregationCycle) {
		List<VendorIndex> vendorIndexList = monitorDataOverViewService.getMonitorDateCountByVendor(isUpload, startTime, endTime, aggregationCycle);
		return ResultWithTotal.success(vendorIndexList, vendorIndexList.size());
	}

	@ApiOperation("监测点台账指标统计")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isUpload", value = "是否上送，不传查询全网", required = false, dataType = "Boolean") })
	@GetMapping("/bookview/statistics")
	public ResultWithTotal<List<InventoryIndexCount>> getBookCount(Boolean isUpload) {
		List<InventoryIndexCount> inventoryIndexCountList = inventoryIndexService.getBookIndexCount(isUpload);
		return ResultWithTotal.success(inventoryIndexCountList, inventoryIndexCountList.size());
	}

	@ApiOperation("变电站维度监测数据指标统计")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isupload", value = "是否上送，不传查询全网", required = false, dataType = "Boolean"),
			@ApiImplicitParam(name = "starttime", value = "查询开始时间", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "endtime", value = "查询结束时间", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "aggregationcycle", value = "查询单元", required = true, dataType = "int"),
			@ApiImplicitParam(name = "areaId", value = "区域id", required = true, dataType = "Long"),
			@ApiImplicitParam(name = "modelLabel", value = "模型名称", required = true, dataType = "String")
	})
	@GetMapping("/area/index")
	public ResultWithTotal<List<MonitorDataBySubstation>> getMonitorDataBySubstation(Boolean isupload, Long areaId, String modelLabel, Long starttime, Long endtime, Integer aggregationcycle) {
		MonitorDataBySubstation monitorDataBySubstation = monitorDataOverViewService.getMonitorDataBySubstationInArea(areaId, modelLabel, isupload, starttime, endtime, aggregationcycle);
		List<MonitorDataBySubstation> monitorDataBySubstationList = Arrays.asList(monitorDataBySubstation);
		return ResultWithTotal.success(monitorDataBySubstationList, monitorDataBySubstationList.size());
	}

}

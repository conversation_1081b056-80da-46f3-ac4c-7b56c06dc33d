package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 	装置类型枚举
 * <AUTHOR>
 *
 */
public enum TerminalTypeEnum {
	//装置类型枚举
	PQ(1,"电能质量监测装置"),
	VOLTAGE_SAG_METER(2,"电压暂降仪"),
	WIRELESS_TERMINAL(3,"无线终端"),
	DISTRIBUTION_NETWORK_TERMINAL(4,"配网终端");

	private Integer key;
	private String value;
	
	private TerminalTypeEnum(Integer key, String value) {
		this.key = key;
		this.value = value;
	}

	public static Integer getType(String strType) {
		for (TerminalTypeEnum terminalTypeEnum : TerminalTypeEnum.values()) {
			if(terminalTypeEnum.value.equals(strType)) {
				return terminalTypeEnum.key;
			}
		}
		throw new CommonManagerException("装置类型枚举值转化异常");
	}
	
	public static Integer getTypeNoThrows(String strType) {
		if (StringUtils.isEmpty(strType)) {
			return null;
		}
		for (TerminalTypeEnum terminalTypeEnum : TerminalTypeEnum.values()) {
			if(terminalTypeEnum.value.equals(strType)) {
				return terminalTypeEnum.key;
			}
		}
		return 0;
	}

	public static String getValueByKeyNoThrows(Integer key) {
		for (TerminalTypeEnum terminalTypeEnum : TerminalTypeEnum.values()) {
		    if (terminalTypeEnum.key.equals(key)) {
		    	return terminalTypeEnum.value;
			}
		}
		return StringUtils.EMPTY;
	}

	public Integer getKey() {
		return key;
	}

	public void setKey(Integer key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
}

package com.cet.pq.common.model.excel;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/7 10:26
 */
@Data
public class Sheets {

    /**
     * 对应TemplateDataEnum中的模板名、sheet名、图片位置、起始行
     */
    private String id;
    /**
     * base64转码后的图片列表
     */
    private List<String> picture;
    /**
     * 要导出的表格数据
     */
    private List<LinkedHashMap<String, Object>> data;
    /**
     * 不用模板时的表头
     */
    private List<String> head;
    /**
     * 不用模板时导出sheet名
     */
    private String sheetName;
    /**
     * 不用模板时图片列坐标
     */
    private int col;
    /**
     * 不用模板时图片行坐标
     */
    private int row;
    /**
     * 写数据的起始行
     */
    private int writeRow;
    /**
     * 写入某行或某单元格数据
     */
    private List<InsertData> insertData;
    /**
     * 表头居左数据
     */
    private List<InsertData> leftHeaderData;
    /**
     * 标红的单元格数据
     */
    private List<InsertData> redData;

    /**
     * 重写模板添加灰色背景数据
     */
    private List<InsertData> addBgData;

    /**
     * 需要合并单元格数据
     */
    private List<MergeData> mergeData;

    /**
     * 标题数据-居中
     */
    private InsertData titleData;
}

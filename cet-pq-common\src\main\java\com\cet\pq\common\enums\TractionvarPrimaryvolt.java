package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description: 牵引变一次电压
 * @date 2021/6/2 16:44
 */
public enum TractionvarPrimaryvolt {
    //牵引变一次电压
    VOLT_110(1, "110KV"),
    VOLT_220(2, "220KV"),
    VOLT_330(3, "330KV");

    private Integer id;
    private String text;

    TractionvarPrimaryvolt(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (TractionvarPrimaryvolt tractionvarPrimaryvolt : TractionvarPrimaryvolt.values()) {
            if (tractionvarPrimaryvolt.id.equals(id)) {
                return tractionvarPrimaryvolt.text;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String text) {
        for (TractionvarPrimaryvolt tractionvarPrimaryvolt : TractionvarPrimaryvolt.values()) {
            if (tractionvarPrimaryvolt.text.equals(text)) {
                return tractionvarPrimaryvolt.id;
            }
        }
        return null;
    }
}

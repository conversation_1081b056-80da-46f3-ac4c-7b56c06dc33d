package com.cet.pq.common.model.realtime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实时数据结果
 *
 * <AUTHOR>
 * @date 2019/11/20 15:52
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class RealTimeValue {
    private Long deviceId;
    private Long dataId;
    private Integer logicalId;
    private Long measureId;
    private Double value;

    private String monitoredLabel;
    private Long monitoredId;

    public RealTimeValue(String monitoredLabel, Long monitoredId, Double value) {
        this.value = value;
        this.monitoredLabel = monitoredLabel;
        this.monitoredId = monitoredId;
    }

    public RealTimeValue(Double value) {
        this.value = value;
    }
}

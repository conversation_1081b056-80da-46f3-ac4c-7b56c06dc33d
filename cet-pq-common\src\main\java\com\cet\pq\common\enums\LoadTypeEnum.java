package com.cet.pq.common.enums;

import com.cet.pq.common.model.IdTextPair;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/28 18:48
 * @Description
 */
public enum LoadTypeEnum {
    mill(1, "轧机"), welding(2, "电焊"), electrolytic(3, "电解"), electricheating(4, "电加热");

    private Integer id;
    private String name;

    LoadTypeEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static List<IdTextPair> toList() {
        List<IdTextPair> result = new ArrayList<>();
        LoadTypeEnum[] values = LoadTypeEnum.values();
        for (LoadTypeEnum loadType : values) {
            Integer id = loadType.getId();
            String name = loadType.getName();
            result.add(new IdTextPair(id, name));
        }
        return result;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (LoadTypeEnum loadTypeEnum : LoadTypeEnum.values()) {
            if (loadTypeEnum.id.equals(id)) {
                return loadTypeEnum.name;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (LoadTypeEnum typeEnum : LoadTypeEnum.values()) {
            if (typeEnum.getName().trim().equalsIgnoreCase(name)) {
                return typeEnum.getId();
            }
        }
        return null;
    }
}

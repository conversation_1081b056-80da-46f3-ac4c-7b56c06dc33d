package com.cet.pq.common.cca;


import static com.cet.pq.common.cca.Inv.getN;

public class Canoncorr {
    public static double canoncorr(double[][] data, int R, int C) throws Exception {

        int r = data.length;//行数
        int c = data[0].length;//列数
        double[][] matrix = new double[r][c]; //初始化z
        //String[][] z = new String[sheet.getLastRowNum()][]; //初始化z
        int i = 0;
        int j = 0;
        //System.out.print("matrix = ");
        for (i=0;i<r;i++){
            for (j=0;j<c;j++){
                matrix[i][j] = data[i][j];
                //System.out.print(matrix[i][j] + " ");
            }
            //System.out.println();
        }



        Matrix matrix_Z = new Matrix(matrix.length, matrix[0].length, matrix);
/*        System.out.println("Transposed Matrix (矩阵 的置换):" + matrix_Z.transpose().print());
        try {
            System.out.println("matrix =" + matrix_Z.print());
            //System.out.println("Z_T x Z =" + (matrix_Z.transpose()).multiply(matrix_Z).print());
            //System.out.println("去均值 =" + matrix_Z.qujunzhi().print());       //////去均值，返回矩阵
            System.out.println("cov()  =" + matrix_Z.cov().print());             //////cov，返回矩阵（Matrix方法展示矩阵，不使用）

        } catch (Exception e) {
            e.printStackTrace();
        }*/

        /////Matrix转换为数组
        double[][] maa = new double[c][c]; //初始化
        maa=matrix_Z.cov().change_value();       //首先通过matrix_Z.cov()得到矩阵，通过change得到数组
        //////展示所得协方差矩阵
/*        System.out.println("cov()  =" );
        for(j=0;j<c;j++)
        {
            for(i=0;i<c;i++)
                if (i == c - 1) {
                System.out.println(maa[i][j]);
           }else{
                    System.out.print( maa[i][j]+ " ");
               }
        }
        System.out.println("-----");*/
        ///////////将协方差进行分块
        double z11; //初始化
        double[][] z12 = new double[1][c-1]; //第一行
        double[][] z21 = new double[c-1][1]; //第一列
        double[][] z22 = new double[c-1][c-1]; //初始化
        z11 = maa[0][0];
        //System.out.println("z11 "+z11);
        for (i=0;i<(c-1);i++){
            z12[0][i] = maa[0][i+1];
            //   System.out.println(" z12" + z12[0][i]);
        }
        for (i=0;i<(c-1);i++){
            z21[i][0] = maa[i+1][0];
            //   System.out.println(" z21" + z21[i][0]);
        }

        //打印z22
        for (i=0;i<(c-1);i++){
            for (j=0;j<(c-1);j++){
                z22[i][j] = maa[i+1][j+1];
                //System.out.print(z22[i][j] + " ");
            }
            //System.out.println();
        }

        double z11_inv ; //初始化
        z11_inv =(( float )1 / z11) ;
        //System.out.println(" z11_inv =" + z11_inv );

        double[][] z22_inv = new double[c-1][c-1]; //初始化
        if (c==2){
            z22_inv[0][0] =((double)1/z22[0][0]);
            //System.out.print("z22_inv="+z22_inv[0][0] + " ");
        }
        else{
            z22_inv = getN(z22);
        }

/*        System.out.println("z22_inv = ");
        for (i=0;i<(c-1);i++){
            for (j=0;j<(c-1);j++){
                z22[i][j] = maa[i+1][j+1];
                System.out.print(z22_inv[i][j] + " ");
            }
            System.out.println();
        }*/


        Matrix maA_0 = new Matrix(z12.length, z12[0].length,z12);
        //double[][] invz11_z12 = maA_0.multiply(z11_inv);
        Matrix maA_1 = maA_0.multiply(z11_inv);
/*        System.out.println("maA_1" + maA_1.print());
        System.out.println(maA_1.change_row() + "," +maA_1.change_column());
        for (i=0;i<maA_1.change_row();i++){
            for (j=0;j<maA_1.change_column();j++){
                System.out.print(maA_1.change_value()[i][j] + " ");
            }
            System.out.println();
        }*/


        Matrix z22_Inv = new Matrix(z22_inv.length, z22_inv[0].length,z22_inv);
/*        for (i=0;i<z22_Inv.change_row();i++){
            for (j=0;j<z22_Inv.change_column();j++){
                System.out.print(z22_Inv.change_value()[i][j] + " ");
            }
            System.out.println();
        }
        System.out.println(z22_Inv.change_row() + "," +z22_Inv.change_column());*/


/*        double[][] maA_2 = maA_1.multiply_T(z22_Inv);
        for (i=0;i<maA_2.length;i++){
            for (j=0;j<(maA_2[0].length);j++){
                System.out.print(maA_2[i][j] + " ");
            }
            System.out.println();
        }*/

        Matrix maA_2 = maA_1.multiply(z22_Inv);
        //System.out.println(maA_2.change_row() + "," + maA_2.change_column() + "," + maA_2.change_value());



        Matrix maA_3 = new Matrix(z21.length, z21[0].length,z21);
        double [][] eigA = maA_2.multiply(maA_3).transpose_T();
/*        System.out.println("eigA = ");
        for (i=0;i<eigA .length;i++){
            for (j=0;j<(eigA [0].length);j++){
                System.out.print(eigA [i][j] + " ");
            }
            System.out.println();
        }*/

        double corr = Math.sqrt(eigA[0][0]);
        //System.out.print("R1 = " + corr );

        return corr;
    }
}

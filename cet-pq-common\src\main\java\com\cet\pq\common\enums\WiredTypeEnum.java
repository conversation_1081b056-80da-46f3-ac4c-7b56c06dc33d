package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 接线方式枚举类
 *
 * <AUTHOR>
 */
public enum WiredTypeEnum {
    //接线方式枚举类
    star("星型接线", 1), triangle("角型接线", 2);

    private String name;
    private Integer value;

    private WiredTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static Integer getValueByName(String name) {
        for (WiredTypeEnum wiredTypeEnum : WiredTypeEnum.values()) {
            if (name.equals(wiredTypeEnum.getName())) {
                return wiredTypeEnum.getValue();
            }
        }
        throw new CommonManagerException("接线方式类型值无效");
    }

    public static Integer getValueByNameNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (WiredTypeEnum wiredTypeEnum : WiredTypeEnum.values()) {
            if (name.equals(wiredTypeEnum.getName())) {
                return wiredTypeEnum.getValue();
            }
        }
        return 0;
    }

    public static String getNameByValueNoThrows(Integer value) {
        for (WiredTypeEnum wiredTypeEnum : WiredTypeEnum.values()) {
            if (wiredTypeEnum.getValue().equals(value)) {
                return wiredTypeEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }
    public static Integer getValueByNameImport(String name) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (WiredTypeEnum wiredTypeEnum : WiredTypeEnum.values()) {
            if (name.equals(wiredTypeEnum.getName())) {
                return wiredTypeEnum.getValue();
            }
        }
        return -1;
    }
}

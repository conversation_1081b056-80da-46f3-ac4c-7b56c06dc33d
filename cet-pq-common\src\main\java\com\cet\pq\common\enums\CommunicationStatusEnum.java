package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 通信状态枚举类 
 * <AUTHOR>
 *
 */
public enum CommunicationStatusEnum {

	//通信状态枚举类
	ok(1),error(2);
	
	private Integer value;

	private CommunicationStatusEnum(Integer value) {
		this.value = value;
	}

	public Integer getValue() {
		return value;
	}

	
	public static Integer getCommunicationStatus(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (CommunicationStatusEnum communicationStatusEnum : CommunicationStatusEnum.values()) {
			if(communicationStatusEnum.name().equals(name)) {
				return communicationStatusEnum.getValue();
			}
		}
		throw new CommonManagerException("换流站类型值无效");
	}
}

package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 聚合周期
 * <AUTHOR>
 * @Date 2020-8-21
 */
public enum AggregationTypeEnum {
	none("无",1, "实时值"),
	geometricavg("几何平均值",2,""),
	max("最大值",3,"最大值"),
	min("最小值",4,"最小值"),
	pro99("99概率值",5,""),
	pro95("95概率值",6,"CP95值"),
	extractpoints("抽点值",7,""),
	predictive("预测值",8,""),
	snapshot("快照",9,""),
	stepaccumulation("级差累加",10,""),
	arithmeticavg("算数平均",11, "平均值"),
	quanlificationrate("合格率",12,""),
	demand("实时需量",13,""),
	demandprediction("预测需量",14,""),
	overflag("超标次数",15,""),
	;
	//数据库名称
	private String key;
	private Integer value;
	//展示名称
	private String name;

	AggregationTypeEnum(String key, Integer value, String name) {
		this.key = key;
		this.value = value;
		this.name = name;
	}

	public static String getNameByValue(Integer value) {
		for(AggregationTypeEnum typeEnum: AggregationTypeEnum.values()){
			if(typeEnum.getValue().equals(value)){
				return typeEnum.getName();
			}
		}
		return "";
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}

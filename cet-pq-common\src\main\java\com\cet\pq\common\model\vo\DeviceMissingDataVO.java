package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/05/18 15:11
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeviceMissingDataVO {

    /**
     * 监测点编号
     */
    private Long monitorId;

    /**
     * 监测区域
     */
    private String cityCompany;

    /**
     * 装置缺失数据详情界面表格数据
     */
    private List<Map<String, Object>> table;

    /**
     * 装置缺失数据详情界面图数据
     */
    private List<Map<String, Object>> graph;

}

package com.cet.pq.common.model.datalog;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName TrendSearchListVo
 * @Description 定时记录查询参数
 * <AUTHOR>
 * @Date 2020/2/24 14:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrendSearchSecondListVo {
    private Long endTime;
    private Long startTime;
    /**
     * 周期
     */
    private Integer interval;
    private List<TrendSearchVo> meterConfigs;
}

package com.cet.pq.common.model.overLimit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description:区域超标模型
 * @date 2021/1/5 16:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoParaSetOverlimitData {

    private Integer aggregationcycle;
    private Float avgoverdaycnt;
    private Long logtime;
    private Long objectstattype;
    private Integer onlinenodecnt;
    private Long overcnt;
    private Long overnodecnt;
    private Long overnodedaycnt;
    private Float overnoderate;
    private Long quantityparaset_id;
    private Long rangedmodelid;
    private String rangedmodellabel;
    private Long updatetime;
    private Boolean uploadonly;
    private Integer voltagegroup;
    private Integer monitortype;
}

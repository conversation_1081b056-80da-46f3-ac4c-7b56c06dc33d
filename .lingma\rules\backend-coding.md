# Java编码规范

## 基础规范

### 技术栈
1. 项目统一基于Spring Boot和java8进行开发。
2. 微服务架构采用Spring Cloud。
3. 数据库访问统一采用模型服务 PqModelServiceUtils。
4. 服务间调用统一使用FeignClient。
5. 日志统一使用SLF4J + Logback，严禁System.out，重要操作需记录操作日志，敏感信息不得入日志。
6. 统一使用Lombok简化实体、日志等代码，常用注解：@Data、@Slf4j、@Builder等。
7. 所有对外接口必须使用Swagger注解，自动生成API文档。
8. 配置文件敏感信息需加密（如Jasypt）。

### 命名规范
1. 类名：使用大驼峰命名法（PascalCase），如 `UserService`，名称需与功能保持一致。
2. 方法名：使用小驼峰命名法（camelCase），如 `getUserById`，名称需表达清楚功能。
3. 变量名：使用小驼峰命名法（camelCase），如 `userName`。
4. 常量名：使用全大写下划线分隔（UPPER_SNAKE_CASE），如 `MAX_RETRY_COUNT`，严禁随意写魔法值。
5. 枚举名：使用大驼峰，枚举值全大写下划线。

### 代码结构与函数设计
1. 一个函数尽量只做一件事，避免复杂逻辑堆积。
2. 函数体长度不宜过长，建议不超过一屏。
3. 函数入参不宜超过5个，超过建议封装为对象。
4. 大括号嵌套层级不超过3层，超出请拆分为子函数。
5. 业务逻辑应保持独立性，优先接口复用，禁止重复造轮子。


### 数据处理
1. 数据处理需考虑有无数据、数据合法性。
2. 处理空值、异常、边界情况，避免NPE。

### 数据库与性能规范
1. 严禁在循环中操作数据库。
2. 大数据量操作需分批处理，模型服务单次查询不超10000条。
3. 查询条件应尽量精确，避免全表扫描。
4. 需要统计展示的数据建议提前汇总，避免大表实时聚合。


### 注释与文档规范
1. 类注释：包含用途、作者、创建时间。
2. 方法注释：说明功能、参数、返回值、异常。
3. 字段注释：说明字段含义。
4. 关键逻辑、复杂算法必须有中文注释，保持与代码风格一致。
5. aspect、AOP等关键包内容注释必须完整。
6. JavaDoc：为公共API编写完整的JavaDoc。
7. 代码注释：为复杂逻辑添加解释性注释。
8. TODO标记：使用TODO标记待完成的工作。


### 代码复杂度控制规范
1. 复杂度控制：
   - 单个方法的复杂度不超过10
   - 超过时拆分为多个子方法
2. 方法长度控制：
   - 单个方法不超过50行
   - 超过时提取公共逻辑或拆分子方法
   - 保持方法的单一职责
3. 嵌套层级控制：
   - if-else嵌套不超过3层
   - 使用早期返回减少嵌套

### 代码质量
- 单一职责：每个类和方法只负责一个功能
- 开闭原则：对扩展开放，对修改关闭
- 依赖倒置：依赖抽象而不是具体实现
- 接口隔离：使用小而专一的接口
- 代码复用：提取公共逻辑，避免重复代码

### 安全规范
1. 敏感信息保护：
   - 禁止在日志、异常、接口响应等暴露密码、密钥、token等敏感信息。
   - 配置文件中的敏感信息应加密存储。
2. SQL注入防护：
   - 严禁拼接SQL，必须使用预编译、参数化查询。
   - 严格校验所有输入参数。
3. XSS/CSRF防护：
   - 对外接口需校验来源，重要操作需二次确认。
   - 前后端均需对用户输入进行转义和校验。
4. 接口鉴权与权限控制：
   - 重要接口需鉴权，敏感操作需权限校验。
   - 避免越权访问。


### Controller与Service层调用与返回规范
1. Controller层所有接口方法必须统一返回Result或ResultWithTotal对象，分页结果返回PageResult，禁止直接返回实体、集合或Map。
2. Service层方法建议返回Result、ResultWithTotal或明确的业务对象，避免返回原始类型或未封装的数据结构。
3. 分页、列表等需要总数的接口，统一使用ResultWithTotal进行返回，保证前后端数据结构一致。
4. Controller层不得直接操作数据库，必须通过Service层调用业务逻辑。
5. Service层应处理好异常和空值，保证返回对象的健壮性和一致性。



 













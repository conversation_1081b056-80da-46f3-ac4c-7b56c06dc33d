package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;

/**
 * 能源类型枚举类
 * <AUTHOR>
 */
public enum EnergyTypeEnum {
	//能源类型枚举类

	energy("能量",1),electric("电能",2),water("水",3),tapwater("自来水",4),circulatingwater("循环水",5),refrigerantwater("冷媒水",6),
	hotwater("热水",7),steam("蒸汽",8),oil("油",9),dieseloil("柴油",10),gasoline("汽油",11),coal("煤炭",12),standardcoal("折标媒",13),
	gas("燃气",14),naturalgas("天然气",15),compressedair("压缩空气",16),co("CO",17),co2("CO2",18),ch4("CH4",19),o2("O2",20),liquid("液体",21),c("碳元素",22);
	
	private String name;
	private Integer values;
	
	private EnergyTypeEnum(String name, Integer values) {
		this.name = name;
		this.values = values;
	}

	public String getName() {
		return name;
	}



	public Integer getValues() {
		return values;
	}



	public static Integer getEnergyType(String name) {
		for (EnergyTypeEnum energyTypeEnum : EnergyTypeEnum.values()) {
			if(name.equals(energyTypeEnum.getName())) {
				return energyTypeEnum.getValues();
			}
		}
		throw new CommonManagerException("能源类型值无效");
	}
	
}

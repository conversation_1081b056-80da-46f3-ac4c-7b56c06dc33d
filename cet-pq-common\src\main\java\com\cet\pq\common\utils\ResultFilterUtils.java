package com.cet.pq.common.utils;

import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.feign.ModelDataService;
import com.cet.pq.common.model.ConditionBlock;
import com.cet.pq.common.model.QueryCondition;
import com.cet.pq.common.model.ResultWithTotal;
import com.cet.pq.common.model.SingleModelConditionDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 使用TreeNode条件查询模型服务的结果过滤，数据是否属于TreeNode进行校验
 * 针对监测点和干扰源交叉关联时查询监测点或监测终端会出现重复的问题
 * @date 2020/12/17 10:53
 */
@Component
public class ResultFilterUtils {

    @Autowired
    private ModelDataService modelServices;
    private static ModelDataService modelService;

    @PostConstruct
    private void init () {
        modelService = this.modelServices;
    }

    //排除干扰源关联
    public static SingleModelConditionDTO noElectricrailway = new SingleModelConditionDTO(TableName.ELECTRICRAILWAY).depth(-1);
    public static SingleModelConditionDTO noPhotovoltaicstation = new SingleModelConditionDTO(TableName.PHOTOVOLTAICSTATION).depth(-1);
    public static SingleModelConditionDTO noWindpowerstation = new SingleModelConditionDTO(TableName.WINDPOWERSTATION).depth(-1);
    public static SingleModelConditionDTO noInterferencesource = new SingleModelConditionDTO(TableName.INTERFERENCESOURCE).depth(-1);

    /**
     * 适用于数据列表（例如监测点和监测终端列表）包含变电站列表，变电站包含管理单位的结构，使用id对市、县管理单位单位过滤只有当id不相同的时候会删除该监测点 没找到不同则不会删除
     * @param data 需要过滤的监测点列表
     * @param modelLabel 管理单位modelLabel
     * @param modelId 管理单位modelId
     * @return 返回删除的个数 分页中的总数要减去删除的个数
     */
    public static int lineOrTerminalFilterByCompany(List<Map<String, Object>> data,String modelLabel,Long modelId) {
        //记录删除的个数
        int deleteNum = 0;
        Iterator iterator = data.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> lineOrTerminal = ParseDataUtil.parseMap(iterator.next());
            List<Map<String,Object>> substationList = ParseDataUtil.parseList(lineOrTerminal.get(TableName.SUBSTATION + "_model"));
            //遍历变电站
            String subModelLabel = TableName.COUNTYCOMPANY;
            if (TableName.COUNTYCOMPANY.equals(modelLabel)) {
                subModelLabel = TableName.CITYCOMPANY;
            }
            for (Map<String,Object> substation:substationList) {
                //变电站下面找modelLabel管理单位
                List<Map<String,Object>> companyList = ParseDataUtil.parseList(substation.get(modelLabel+"_model"));
                if (CollectionUtils.isNotEmpty(companyList)){
                    if (!modelId.equals(ParseDataUtil.parseLong(companyList.get(0).get("id")))){
                        //数据中管理单位id与传入的modelId不同就删除该监测点
                        iterator.remove();
                        deleteNum++;
                        break;
                    }
                }else{
                    //没找到在countryCompany_model 下面找modelLabel管理单位
                    companyList = ParseDataUtil.parseList(substation.get(subModelLabel+"_model"));
                    if (CollectionUtils.isNotEmpty(companyList)){
                        companyList = ParseDataUtil.parseList(companyList.get(0).get(modelLabel+"_model"));
                        if (CollectionUtils.isNotEmpty(companyList)) {
                            if (!modelId.equals(ParseDataUtil.parseLong(companyList.get(0).get("id")))) {
                                //数据中管理单位id与传入的modelId不同就删除该监测点
                                iterator.remove();
                                deleteNum++;
                                break;
                            }
                        } else if (TableName.COUNTYCOMPANY.equals(modelLabel)) {
                            iterator.remove();
                            deleteNum++;
                            break;
                        }
                    }
                }
            }
        }
        return deleteNum;
    }

    /**
     * 替换TreeNodetree，查询某个上层modelLabel和modelId下的监测点或者监测终端信息
     * @param modelLabel 上层modelLabel
     * @param modelId 上层modelId
     * @param rootLabel 需要返回的模型Label 此方法只支持监测点和监测终端 监测终端未测试
     * @param blocks 监测点过滤条件
     * @return 监测点List<Map<String,Object>>
     */
    public static List<Map<String,Object>> treeNodeQueryModel(String modelLabel, Long modelId, RootLabel rootLabel, ConditionBlock... blocks){
        //排除干扰源关联
        SingleModelConditionDTO root  = new SingleModelConditionDTO(rootLabel.name);
        if (blocks.length>0){
            root = new SingleModelConditionDTO(rootLabel.name,blocks);
        }
        QueryCondition queryCondition = new QueryCondition(modelId,modelLabel,root, noElectricrailway, noWindpowerstation, noPhotovoltaicstation, noInterferencesource);
        ResultWithTotal<List<Map<String, Object>>> query = modelService.query(queryCondition);
        ParamUtils.checkResultGeneric(query);
        List<Map<String,Object>> result = new LinkedList<>();
        List<Map<String, Object>> data = query.getData();
        if (CollectionUtils.isNotEmpty(data)){
            data.forEach(company->{
                List<Map<String,Object>> lineList = ParseDataUtil.parseList(company.get(rootLabel.name + "_model"));
                result.addAll(lineList);
            });
        }
        return result;
    }

    /**
     * 替换TreeNodetree，查询某个上层modelLabel和modelId下的监测点信息
     * @param clazz
     * @param modelLabel
     * @param modelId
     * @param blocks
     * @param <T>
     * @return 返回class实体类列表
     */
    public static <T> List<T> treeNodeQueryModel(Class<T> clazz, String modelLabel, Long modelId, RootLabel rootLabel, ConditionBlock... blocks){
        List<Map<String, Object>> data = treeNodeQueryModel(modelLabel, modelId,rootLabel, blocks);
        return JsonTransferUtils.transferList(data, clazz);
    }

    /**
     * treeNodeQueryLine方法只支持监测点和监测终端的查询
     * 监测终端查询还未测试
     */
    public static enum RootLabel{
        //treeNodeQueryLine方法只支持监测点和监测终端的查询
        LINE(TableName.LINE),
        PQTERMINAL(TableName.PQTERMINAL);

        public String name;

        private RootLabel(String name){
            this.name = name;
        }
    }
}

package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @<PERSON> liu<PERSON><PERSON>
 * @Date 2023/4/20 15:51
 * @Description
 */
public enum MonitorSortEnum {

    ALL("全部",0),
    //电站
    FIRST_CLASS("Ⅰ类",1),
    //台区
    SECOND_CLASS("Ⅱ类",2),
    //线路
    THIRD_CLASS("Ⅲ类",3),

    OTHER("其他",4);

    MonitorSortEnum(String text, Integer id) {
        this.text = text;
        this.id = id;
    }

    private String text;
    private Integer id;

    public String getText() {
        return text;
    }

    public Integer getId() {
        return id;
    }

    public static Integer getMonitorSortEnumId(String name) {
        for (MonitorSortEnum monitorSortEnum : values()) {
            if (name.equals(monitorSortEnum.getText())) {
                return monitorSortEnum.getId();
            }
        }
        return null;
    }

    public static Integer getMonitorSortEnumImport(String name) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (MonitorSortEnum monitorSortEnum : values()) {
            if (name.equals(monitorSortEnum.getText())) {
                return monitorSortEnum.getId();
            }
        }
        return -1;
    }

    public static String getMonitorSortEnumText(Integer id) {
        if(id == null){
            return "";
        }
        for (MonitorSortEnum monitorSortEnum : values()) {
            if (id.equals(monitorSortEnum.getId())) {
                return monitorSortEnum.getText();
            }
        }
        return "";
    }
    public static Integer getIdByNameImport(String name) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (MonitorSortEnum monitorSortEnum : values()) {
            if (name.equals(monitorSortEnum.getText())) {
                return monitorSortEnum.getId();
            }
        }
        return -1;
    }
}

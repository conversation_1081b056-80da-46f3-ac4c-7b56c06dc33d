package com.cet.pq.anlysis.model.excel;

import com.cet.pq.common.model.excel.ExportParameter;
import com.cet.pq.anlysis.model.common.CommonParam;
import com.cet.pq.anlysis.model.common.DataReportParam;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/24 11:28
 */
@Data
public class ExportDataReportDetailParam extends CommonParam {
    private Long lineId;
    private List<List<DataReportParam.QuantityTemplate>> quantityTemplate;
    private ExportParameter exportParameter;

}

package com.cet.pq.common.model.overLimit;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 干扰源超标模型
 * @date 2021/2/3 14:23
 */
@Data
public class InterferenceParaSetOverlimitData {
    private Integer aggregationcycle;
    private Integer correlationtype;
    private Long interferencesourseid;
    private String interferencesourselabel;
    private Long logtime;
    private Long overdaycnt;
    private Long quantityparaset_id;
    private Integer shortcircuitratiotype;
}

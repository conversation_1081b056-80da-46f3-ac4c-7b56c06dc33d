package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 监测点类型枚举类
 * <AUTHOR>
 */
public enum PQMonitorTypeEnum {
	//监测点类型枚举类
	commonconnection("公共连接点PCC",1),
	powersupply("供电点",2),
	gridsupply("并网点",3),
	other("其他",4);

	private String name;
	private Integer value;

	private PQMonitorTypeEnum(String name, Integer value) {
		this.name = name;
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public Integer getValue() {
		return value;
	}


	/**
	 * 获取监测点类型枚举值
	 * @param name
	 * @return
	 */
	public static Integer getValueByName(String name) {
		for (PQMonitorTypeEnum pQMonitorTypeEnum : PQMonitorTypeEnum.values()) {
			if(name.equals(pQMonitorTypeEnum.getName())) {
				return pQMonitorTypeEnum.getValue();
			}
		}
		throw new CommonManagerException("检测点类型值异常");
	}

	public static String getNameByValue(Integer value) {
		for (PQMonitorTypeEnum pQMonitorTypeEnum : PQMonitorTypeEnum.values()) {
			if(value.equals(pQMonitorTypeEnum.getValue())) {
				return pQMonitorTypeEnum.getName();
			}
		}
		throw new CommonManagerException("检测点类型值异常");
	}

	public static Integer getValueByNameNoThrows(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (PQMonitorTypeEnum pQMonitorTypeEnum : PQMonitorTypeEnum.values()) {
			if(name.equals(pQMonitorTypeEnum.getName())) {
				return pQMonitorTypeEnum.getValue();
			}
		}
		return 0;
	}

	public static String getNameByValueNoThrows(Integer value) {
		for (PQMonitorTypeEnum pQMonitorTypeEnum : PQMonitorTypeEnum.values()) {
			if(pQMonitorTypeEnum.getValue().equals(value)) {
				return pQMonitorTypeEnum.getName();
			}
		}
		return StringUtils.EMPTY;
	}
	public static Integer getValueByNameImport(String name) {
		if(StringUtils.isBlank(name)){
			return null;
		}
		for (PQMonitorTypeEnum pQMonitorTypeEnum : PQMonitorTypeEnum.values()) {
			if(name.equals(pQMonitorTypeEnum.getName())) {
				return pQMonitorTypeEnum.getValue();
			}
		}
		return -1;
	}
}

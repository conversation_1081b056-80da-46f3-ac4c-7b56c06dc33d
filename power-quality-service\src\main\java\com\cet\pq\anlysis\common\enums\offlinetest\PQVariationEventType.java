package com.cet.pq.anlysis.common.enums.offlinetest;

/**
 * <AUTHOR>
 * @ClassName PQVariationEventType
 * @Description TODO
 * @Date 2020/10/26
 */
public enum PQVariationEventType {
    //pqvariationeventtype
    TRANSIENT_PQ_VARIATION_EVENT_TYPE("瞬变", 1),
    SWELL_PQ_VARIATION_EVENT_TYPE("电压暂升", 2),
    SAG_PQ_VARIATION_EVENT_TYPE("电压暂降", 3),
    SHORT_INTERRUPTION_PQ_VARIATION_EVENT_TYPE("短时中断", 4),
    LONG_SWELL_PQ_VARIATION_EVENT_TYPE("长时过电压", 5),
    LONG_SAG_PQ_VARIATION_EVENT_TYPE("长时欠电压", 6),
    LONG_INTERRUPTION_PQ_VARIATION_EVENT_TYPE("长时中断", 7);

    private String name;

    private Integer value;
    private PQVariationEventType(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }
}

package com.cet.pq.common.model.datalog;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @ClassName TrendDataVo
 * @Description 定时记录数据
 * <AUTHOR>
 * @Date 2020/2/24 14:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PowerDataVo {
    private Double powerValue;
    private Map<Long, List<DataLogData>> dataListMap;
}

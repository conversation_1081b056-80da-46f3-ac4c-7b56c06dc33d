package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @ClassName VoltageGroup
 * @Description TODO
 * @Date 2020/9/9
 */
public enum VoltageGroup {
    //电压类型
    VoltageGroup_1(1, "6kV", "6kV", 15),
    VoltageGroup_2(2, "0.4kV", "0.4kV", 16),
    VoltageGroup_3(3, "10kV", "10kV", 12),
    VoltageGroup_4(4, "20kV", "20kV", 11),
    VoltageGroup_8(8, "6.6kV", "6.6kV", 14),
    VoltageGroup_9(9, "6.9kV", "6.9kV", 13),
    VoltageGroup_5(5, "35kV", "35kV", 9),
    VoltageGroup_6(6, "110kV", "110kV", 7),
    VoltageGroup_10(10, "33kV", "33kV", 10),
    VoltageGroup_11(11, "66kV", "66kV", 8),
    VoltageGroup_12(12, "220kV", "220kV", 6),
    VoltageGroup_13(13, "330kV", "330kV", 4),
    VoltageGroup_14(14, "500kV", "500kV", 3),
    VoltageGroup_15(15, "1000kV", "1000kV", 1),
    VoltageGroup_17(17, "750kV", "750kV", 2),
    //VoltageGroup_19(19, "27.5kV", "27.5kV", 10),
    VoltageGroup_20(20, "0.22kV", "0.22kV", 17);
    ;

    private int id;
    private String name;
    private String voltage;
    private int rank;

    VoltageGroup(int id, String name, String voltage, int rank) {
        this.id = id;
        this.name = name;
        this.voltage = voltage;
        this.rank = rank;
    }

    public static VoltageGroup getVoltageGroup(Integer id) {
        for (VoltageGroup voltageGroup : values()) {
            if (voltageGroup.getId() == id) {
                return voltageGroup;
            }
        }
        return VoltageGroup_5;
    }

    public static VoltageGroup getVoltageGroupByVoltage(String voltage) {
        for (VoltageGroup voltageGroup : values()) {
            if (voltageGroup.getVoltage().equals(voltage)) {
                return voltageGroup;
            }
        }
        return VoltageGroup_5;
    }

    public static  String getVoltageById(int id) {
        for ( VoltageGroup voltageGroup : values()) {
            if (voltageGroup.getId() == id) {
                return voltageGroup.voltage;
            }
        }
        return StringUtils.EMPTY;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getVoltage() {
        return voltage;
    }

    public int getRank() {
        return rank;
    }

}

package com.cet.pq.common.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName PageData
 * @Description 封装分页Page类
 * @Date 2020/10/10
 */
@Data
public class PageData<T> implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 5883726022598187636L;

    /**
     * 默认显示10条
     */
    private static int DEFAULT_PAGE_SIZE = 10;

    /**
     * 每页条数
     */
    private int pageSize = DEFAULT_PAGE_SIZE;

    /**
     * 当前页码
     */
    private int pageNum = 1;

    /**
     * 总数
     */
    private long totalRows = 0L;

    /**
     * 数据结果
     */
    private List<T> pageData = Collections.emptyList();

    /**
     * 分页查询参数
     */
    private Map<String, Object> params;


    /**
     * 重写构造方法
     */
    public PageData() {
        this(DEFAULT_PAGE_SIZE);
    }

    /**
     * 重写构造方法
     *
     * @param pageSize
     */
    public PageData(int pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 重写构造方法
     *
     * @param totalRows
     * @param pageSize
     * @param pageData
     */
    public PageData(long totalRows, int pageSize, int pageNum, List<T> pageData) {
        this.totalRows = totalRows;
        this.pageSize = pageSize;
        this.pageNum = pageNum;
        this.pageData = pageData;
    }

    public long getTotalPageSize() {
        if (this.totalRows % this.pageSize == 0L) {
            return this.totalRows / this.pageSize;
        }
        return this.totalRows / this.pageSize + 1L;
    }

}

package com.cet.pq.common.enums;

/**
 * 周期间隔
 * 
 * <AUTHOR>
 */
public enum PeriodType {
	/**
	 * 自定义
	 */
	CUSTOM(0),
	/**
	 * 日
	 */
	DAY(1),
	/**
	 * 周
	 */
	WEEK(2),
	/**
	 * 月
	 */
	MONTH(3),
	/**
	 * 季
	 */
	SEASON(4),
	/**
	 * 年
	 */
	YEAR(5),
	/**
	 * 小时
	 */
	HOUR(8);

	private int value = 0;

	private PeriodType(int value) {
		this.value = value;
	}

	public int getValue() {
		return this.value;
	}
}

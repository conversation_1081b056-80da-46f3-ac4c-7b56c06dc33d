package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 调压方式
 * @date 2024/7/18 11:38
 */
public enum BalanceTypeEnum {

    ONLOAD_BALANCE("有载调压", 1),
    NO_LOAD_BALANCE("无载调压", 2);

    private String name;
    private Integer id;

    private BalanceTypeEnum(String n, Integer i) {
        this.name = n;
        this.id = i;
    }

    public static Integer getIdByName(String n) {
        if(StringUtils.isEmpty(n)) {
            return null;
        }
        for(BalanceTypeEnum b : BalanceTypeEnum.values()) {
            if(b.getName().equals(n)) {
                return b.getId();
            }
        }
        return null;
    }

    public static String getNameById(Integer d) {
        if(Objects.isNull(d)) {
            return null;
        }
        for(BalanceTypeEnum b : BalanceTypeEnum.values()) {
            if(b.getId().equals(d)) {
                return b.getName();
            }
        }
        return null;
    }

    public static String validateNameOrThrows(String n) {
        if(StringUtils.isEmpty(n)) {
            throw new CommonManagerException("调压方式匹配错误");
        }
        for(BalanceTypeEnum bType : values()) {
            if(bType.getName().equals(n)) {
                return bType.getName();
            }
        }
        throw new CommonManagerException("调压方式匹配错误");
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}

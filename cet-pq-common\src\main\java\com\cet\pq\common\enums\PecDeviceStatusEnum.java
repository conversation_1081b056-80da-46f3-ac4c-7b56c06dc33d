package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 设备状态枚举
 * <AUTHOR>
 */
public enum PecDeviceStatusEnum {
	//设备状态枚举
	run("运行",1),fault("故障",2),stop("停用",3),delete("删除",4);
	
	private String name;
	private Integer value;
	
	private PecDeviceStatusEnum(String name, Integer value) {
		this.name = name;
		this.value = value;
	}

	public String getName() {
		return name;
	}


	public Integer getValue() {
		return value;
	}

	
	public static Integer getValueByName(String name) {
		if (StringUtils.isEmpty(name)) {
			return null;
		}
		for (PecDeviceStatusEnum pecDeviceStatusEnum : PecDeviceStatusEnum.values()) {
			if(name.equals(pecDeviceStatusEnum.getName())) {
				return pecDeviceStatusEnum.getValue();
			}
		}
		throw new CommonManagerException("设备状态值无效");
	}
}

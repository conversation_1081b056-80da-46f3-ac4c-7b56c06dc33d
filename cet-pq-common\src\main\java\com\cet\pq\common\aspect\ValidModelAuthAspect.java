package com.cet.pq.common.aspect;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.cet.pq.common.handle.SessionHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.MethodName;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.FlatQueryConditionDTO;
import com.cet.pq.common.model.ModelIdPairDTO;
import com.cet.pq.common.model.QueryCondition;
import com.cet.pq.common.model.SingleModelConditionDTO;
import com.cet.pq.common.model.auth.HasModelAuth;
import com.cet.pq.common.model.auth.HasModelAuth.AuthInfo;
import com.cet.pq.common.model.auth.NeedModelAuth;
import com.cet.pq.common.model.auth.Role;
import com.cet.pq.common.model.auth.User;
import com.cet.pq.common.utils.JsonTransferUtils;
import com.cet.pq.common.utils.ParseDataUtil;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Aspect
@Configuration
@SuppressWarnings({ "unchecked", "rawtypes" })
public class ValidModelAuthAspect {

	private static final Logger logger = LoggerFactory.getLogger(ValidModelAuthAspect.class);

	// 定义切点方法
	@Pointcut("@annotation(com.cet.pq.common.annotation.ValidModelAuth)")
	public void pointCut() {
		logger.debug("捕获切点处");
	}

	// 环绕通知
	@Around("pointCut()")
	public void around(ProceedingJoinPoint joinPoint) {
		// 1.获取到所有的参数值的数组
		Object[] args = joinPoint.getArgs();
		Signature signature = joinPoint.getSignature();
		MethodSignature methodSignature = (MethodSignature) signature;
		// 2.获取到方法的所有参数名称的字符串数组
		String[] parameterNames = methodSignature.getParameterNames();
		Method method = methodSignature.getMethod();
		if (MethodName.QUERY.equals(method.getName())) {
			validQuery(parameterNames, args);
		} else if (MethodName.DELETEBYID.equals(method.getName())) {
			validDelete(parameterNames);
		} else if (MethodName.WRITE.equals(method.getName())) {
			validWrite(parameterNames, args);
		}
		try {
			joinPoint.proceed();
		} catch (Throwable e) {
			logger.debug(CommonConstant.CONTEXT, e);
		}
	}

	/**
	 * 校验查询权限
	 * 
	 * @param parameterNames
	 * @param args
	 */
	private void validQuery(String[] parameterNames, Object[] args) {
		QueryCondition condition = null;
		for (int i = 0, len = parameterNames.length; i < len; i++) {
			if ("condition".equals(parameterNames[i])) {
				condition = Optional.ofNullable((QueryCondition) args[i]).orElse(new QueryCondition());
			}
		}
		// 先从参数获取调用需要的权限列表
		List<NeedModelAuth> needModelAuthList;
		try {
			needModelAuthList = getNeedModelAuthFromQueryCondition(condition);
			List<HasModelAuth> hasModelAuthList = getHasModelAuthList();
			// 比较权限
			validAuth(needModelAuthList, hasModelAuthList);
		} catch (Exception e) {
			logger.debug(CommonConstant.CONTEXT, e);
		}
	}

	/**
	 * 校验改写权限
	 * 
	 * @param parameterNames
	 * @param args
	 */
	private void validWrite(String[] parameterNames, Object[] args) {
		List<Object> dataList = new ArrayList<>();
		for (int i = 0, len = parameterNames.length; i < len; i++) {
			if ("data".equals(parameterNames[i])) {
				dataList = Optional.ofNullable((List) args[i]).orElse(new ArrayList<>());
			}
		}
		List<NeedModelAuth> needModelAuthList = getNeedModelAuthFromWriteObject(dataList);
		List<HasModelAuth> hasModelAuthList = getHasModelAuthList();
		validAuth(needModelAuthList, hasModelAuthList);
	}

	/**
	 * 校验删除权限
	 * 
	 * @param parameterNames
	 * @param args
	 */
	private void validDelete(String[] parameterNames) {
		String modelLabel = null;
		for (int i = 0, len = parameterNames.length; i < len; i++) {
			if ("modelLabel".equals(parameterNames[i])) {
				modelLabel = ParseDataUtil.parseString(parameterNames[i]);
			}
		}
		List<NeedModelAuth> needModelAuthList = Arrays.asList(new NeedModelAuth(modelLabel, Arrays.asList(4)));
		List<HasModelAuth> hasModelAuthList = getHasModelAuthList();
		validAuth(needModelAuthList, hasModelAuthList);
	}

	/**
	 * 从写的参数中获取涉及的modelLabel
	 * 
	 * @param dataList
	 * @return
	 */
	private List<NeedModelAuth> getNeedModelAuthFromWriteObject(List<Object> dataList) {
		List<NeedModelAuth> needModelAuthList = new ArrayList<>();
		try {
			dataList.forEach(ins -> {
				Class jsonClass = ins.getClass();
				Field modelLabelField = null;
				String modelLabel = "";
				// 获取object中的modelLabel属性
				try {
					modelLabelField = jsonClass.getDeclaredField("modelLabel");
					// 设置data属性为可访问的
					modelLabelField.setAccessible(true);
					// 通过Field.get(Object)获取object的data(ModelReflect)中的modelLabel属性
					modelLabel = (String) modelLabelField.get(ins);
					NeedModelAuth modelAuth = new NeedModelAuth(modelLabel, Arrays.asList(2, 3));
					needModelAuthList.add(modelAuth);
				} catch (NoSuchFieldException | SecurityException | IllegalAccessException e) {
					logger.debug(CommonConstant.CONTEXT, e);
				}
			});
		} catch (SecurityException e) {
			logger.debug("context", e);
		}
		return needModelAuthList;
	}

	/**
	 * 从查询条件中获取涉及的modelLabel
	 * 
	 * @param condition
	 * @return
	 */
	private List<NeedModelAuth> getNeedModelAuthFromQueryCondition(QueryCondition condition) {
		List<String> modelList = new ArrayList<>();
		// rootLabel
		modelList.add(condition.getRootLabel());
		// rootConditon中的include_relations中的modelLabel
		FlatQueryConditionDTO rootCondition = Optional.ofNullable(condition.getRootCondition()).orElse(new FlatQueryConditionDTO());
		List<String> includeModelLabelList = ParseDataUtil.parseList(rootCondition.getIncludeRelations());
		modelList.addAll(includeModelLabelList);
		// rootCondition中的treeNode中的modelLabel
		ModelIdPairDTO modelIdPairDTO = Optional.ofNullable(rootCondition.getTreeNode()).orElse(new ModelIdPairDTO());
		String treeNodeModelLabel = modelIdPairDTO.getModelLabel();
		if (StringUtils.isNotEmpty(treeNodeModelLabel)) {
			modelList.add(treeNodeModelLabel);
		}
		// subLayerConditions中的下级modelLabel
		List<SingleModelConditionDTO> subLayerConditions = ParseDataUtil.parseList(condition.getSubLayerConditions());
		List<String> subLayerModelLabelList = ParseDataUtil
				.parseList(subLayerConditions.stream().map(SingleModelConditionDTO::getModelLabel).collect(Collectors.toList()));
		modelList.addAll(subLayerModelLabelList);
		// 给每个模型设置需要的操作权限
		List<NeedModelAuth> modelAuthList = new ArrayList<>();
		modelList.forEach(modelLabel -> {
			NeedModelAuth modelAuth = new NeedModelAuth(modelLabel, Arrays.asList(1));
			modelAuthList.add(modelAuth);
		});
		return modelAuthList;
	}

	/**
	 * 比较需要的权限和拥有的权限，判断模型操作是否有权限
	 * 
	 * @param needModelAuthList
	 * @param hasModelAuthList
	 */
	private void validAuth(List<NeedModelAuth> needModelAuthList, List<HasModelAuth> hasModelAuthList) {
		// hasModelAuthList中拥有的权限
		List<HasModelAuth> includeModelAuth = hasModelAuthList.stream().filter(hasModelAuth -> CommonConstant.ENABLE.equals(hasModelAuth.getAuthmode()))
				.collect(Collectors.toList());
		// hasModelAuthList中排除的权限
		List<HasModelAuth> excludeModelAuth = hasModelAuthList.stream().filter(hasModelAuth -> CommonConstant.DISABLE.equals(hasModelAuth.getAuthmode()))
				.collect(Collectors.toList());
		List<AuthInfo> includeAuthInfos = new ArrayList<>();
		for (HasModelAuth hasModelAuth : includeModelAuth) {
			includeAuthInfos.addAll(hasModelAuth.getAuthinfos());
		}
		List<AuthInfo> excludeAuthInfos = new ArrayList<>();
		for (HasModelAuth hasModelAuth : excludeModelAuth) {
			excludeAuthInfos.addAll(hasModelAuth.getAuthinfos());
		}
		// 比较权限
		for (NeedModelAuth needModelAuth : needModelAuthList) {
			String modelLabel = needModelAuth.getModelLabel();
			List<Integer> needAuths = needModelAuth.getAuths();
			// 比较包含的权限
			List<AuthInfo> validincludeAuthList = includeAuthInfos.stream()
					.filter(authInfo -> authInfo.getModelLabel().equals(modelLabel) && authInfo.getAuths().containsAll(needAuths)).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(validincludeAuthList)) {
				// 比较排除的权限
				List<AuthInfo> validexcludeAuthList = excludeAuthInfos.stream()
						.filter(authInfo -> authInfo.getModelLabel().equals(modelLabel) && containsAny(authInfo.getAuths(), needAuths))
						.collect(Collectors.toList());
				if (CollectionUtils.isEmpty(validexcludeAuthList)) {
					throw new CommonManagerException("没有对模型" + modelLabel + "的访问权限");
				}
			}
		}
	}

	/**
	 * 判断sourceList中是都包含targetList中的任何值,True表示有包含值，false标识不包含任何值
	 * 
	 * @param <T>
	 * @param sourceList
	 * @param targetList
	 * @return
	 */
	private <T> Boolean containsAny(List<T> sourceList, List<T> targetList) {
		for (T target : targetList) {
			if (sourceList.contains(target)) {
				return Boolean.TRUE;
			}
		}
		return Boolean.FALSE;
	}

	/**
	 * 根据用户id获取拥有的模型权限
	 * 
	 * @return
	 */
	private List<HasModelAuth> getHasModelAuthList() {
		List<HasModelAuth> modelAuthList = new ArrayList<>();
//		User user = authService.getUser();
		User user = SessionHandler.getUser();
		List<Role> roleList = ParseDataUtil.parseList(user.getRoles());
		roleList.forEach(role -> {
			String customConfig = role.getCustomConfig();
			HasModelAuth hasModelAuth = JsonTransferUtils.parseObject(customConfig, HasModelAuth.class);
			modelAuthList.add(hasModelAuth);
		});
		return modelAuthList;
	}

}

package com.cet.pq.common.handle;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.feign.AuthService;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.SingleModelConditionDTO;
import com.cet.pq.common.model.auth.ModelNode;
import com.cet.pq.common.model.auth.User;
import com.cet.pq.common.model.auth.UserGroup;
import com.cet.pq.common.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName SessionHandler
 * @Description SessionHandler
 * @Date 2020/11/3
 */
@Component
@Slf4j
public class SessionHandler {
    private static AuthService authService;

    @Autowired
    private AuthService auth;

    @PostConstruct
    public void init() {
        authService = this.auth;
    }

    /**
     * @return
     * @desc: 获取request请求
     * @author: gongtong
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        RequestContextHolder.setRequestAttributes(attrs, true);
        return attrs.getRequest();
    }

    /**
     * @return
     * @desc: 获取session
     * @author: gongtong
     */
    public static HttpSession getSession() {
        try {
            HttpServletRequest request = getRequest();
            return request.getSession();
        } catch (Exception e) {
            log.error("获取session失败", e);
            throw new CommonManagerException("获取session失败", e);
        }
    }

    /**
     * @param
     * @Description: 删除用户sessionAttribute
     * @Author: gongtong
     **/
    public static void removeAttribute() {
        HttpSession session = getSession();
        session.invalidate();
    }

    /**
     * @Description: 获取user
     * @Author: gongtong
     **/
    public static User getUser() {
        HttpServletRequest request = getRequest();
        String userId = request.getHeader("User-ID");
        if (StringUtils.isEmpty(userId)) {
            userId = JwtTokenUtil.getUserId(request);
            //解决异步导入，userId为空的问题，使用cetroot默认用户
            if (StringUtils.isEmpty(userId)) {
                Result<User> rootUserResult = authService.queryUserByName("CETROOT");
                userId = ParseDataUtil.parseString(rootUserResult.getData().getId());
            }
        }
        String userInfo = ParseDataUtil.parseString(RedisUtils.get(CommonConstant.USER_INFO + userId));
        User user;
        if (StringUtils.isEmpty(userInfo)) {
            // 查询userInfo
            user = authService.getUser(ParseDataUtil.parseLong(userId)).getData();
            if (null == user) {
                throw new CommonManagerException("用户不存在，请更换登录用户");
            }
            Result<List<UserGroup>> userGroupRes = authService.getRelativeUserGroups(user.getId(), user.getTenantId());
            if (null == userGroupRes || org.springframework.util.CollectionUtils.isEmpty(userGroupRes.getData())) {
                throw new CommonManagerException("用户未绑定用户组，请绑定用户组");
            }
            List<UserGroup> userGroups = userGroupRes.getData();
            user.setModelNodes(JsonTransferUtils.transferJsonString(userGroups.get(0).getCustomConfig(), ModelNode.class));
            RedisUtils.setEx(CommonConstant.USER_INFO + userId, userInfo, 12, TimeUnit.HOURS);
        } else {
            user = JsonTransferUtils.parseObject(userInfo, User.class);
        }
        return user;
    }

    /**
     * @Description: 获取用户所属区域
     * @Author: gongtong
     **/
    public static ModelNode getUserModel() {
        User user = getUser();
        return getModelNode(user);
    }

    public static ModelNode getModelNode(User user) {
        ModelNode modelNode;
        if (CollectionUtils.isNotEmpty(user.getModelNodes())) {
            modelNode = user.getModelNodes().get(0);
        } else {
            throw new CommonManagerException("用户未分配区域，请联系管理员分配区域.");
        }
        return modelNode;
    }

    /**
     * @Description: 获取用户所属区域下级单位
     * @Author: gongtong
     **/
    public static List<Map<String, Object>> getUserSubUnit() {
        User user = getUser();
        String unitInfo = ParseDataUtil.parseString(RedisUtils.get(CommonConstant.USER_SUB_UNIT_KEY + user.getId()));
        List<Map<String, Object>> subUnit = new ArrayList<>();
        if (StringUtils.isEmpty(unitInfo)) {
            ModelNode userModel = getUserModel();
            List<Map<String, Object>> unit = getUnit(userModel.getId(), userModel.getModelLabel());
            // 网级用户展示所有省，省级用户展示所有地市，地市级用户展示所有县级，若没有县级展示当前市级
            if (TableName.NETCOMPANY.equals(userModel.getModelLabel())) {
                subUnit = ParseDataUtil.parseList(unit.get(0).get(TableName.PROVINCECOMPANY + TableName.PREFIX));
            } else if (TableName.PROVINCECOMPANY.equals(userModel.getModelLabel())) {
                subUnit = ParseDataUtil.parseList(unit.get(0).get(TableName.CITYCOMPANY + TableName.PREFIX));
            } else if (TableName.CITYCOMPANY.equals(userModel.getModelLabel())) {
                subUnit = ParseDataUtil.parseList(unit.get(0).get(TableName.COUNTYCOMPANY + TableName.PREFIX));
                if (CollectionUtils.isEmpty(subUnit)) {
                    subUnit = unit;
                } else {
                    // 干扰源
                    setSubUnit(subUnit, unit);
                }
            }
            if (CollectionUtils.isEmpty(subUnit)) {
                subUnit = unit;
            }
            RedisUtils.setEx(CommonConstant.USER_SUB_UNIT_KEY + user.getId(), JsonTransferUtils.toJsonString(subUnit), 60, TimeUnit.MINUTES);
        } else {
            subUnit = ParseDataUtil.parseList(JsonTransferUtils.parseObject(unitInfo, List.class));
        }
        return subUnit;
    }

    /**
     * <AUTHOR>
     * @Date 2024/8/14 17:15
     * @Description 处理干扰源节点写入
     */
    public static void setSubUnit(List<Map<String, Object>> subUnit, List<Map<String, Object>> unit){
        //考虑市级下的变电站，变电站即关联县级又关联市级
        List<Map<String, Object>> citySubstation = ParseDataUtil.parseList(unit.get(0).get(TableName.SUBSTATION + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(citySubstation)) {
            subUnit.get(0).put("citySubstation_model", citySubstation);
        }
        List<Map<String, Object>> interferencesources = ParseDataUtil.parseList(unit.get(0).get(TableName.INTERFERENCESOURCE + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(interferencesources)) {
            subUnit.get(0).put(TableName.INTERFERENCESOURCE + TableName.PREFIX, interferencesources);
        }
        List<Map<String, Object>> windpowerstations = ParseDataUtil.parseList(unit.get(0).get(TableName.WINDPOWERSTATION + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(windpowerstations)) {
            subUnit.get(0).put(TableName.WINDPOWERSTATION + TableName.PREFIX, windpowerstations);
        }
        List<Map<String, Object>> electricrailways = ParseDataUtil.parseList(unit.get(0).get(TableName.ELECTRICRAILWAY + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(electricrailways)) {
            subUnit.get(0).put(TableName.ELECTRICRAILWAY + TableName.PREFIX, electricrailways);
        }
        List<Map<String, Object>> photovoltaicstations = ParseDataUtil.parseList(unit.get(0).get(TableName.PHOTOVOLTAICSTATION + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(photovoltaicstations)) {
            subUnit.get(0).put(TableName.PHOTOVOLTAICSTATION + TableName.PREFIX, photovoltaicstations);
        }
        List<Map<String, Object>> smeltloads = ParseDataUtil.parseList(unit.get(0).get(TableName.SMELTLOAD + TableName.PREFIX));
        if (CollectionUtils.isNotEmpty(smeltloads)) {
            subUnit.get(0).put(TableName.SMELTLOAD + TableName.PREFIX, smeltloads);
        }
        List<Map<String, Object>> energyStorageStations = ParseDataUtil.parseList(unit.get(0).get(TableName.ENERGY_STORAGE_STATION + TableName.PREFIX));
        if(CollectionUtils.isNotEmpty(energyStorageStations)) {
            subUnit.get(0).put(TableName.ENERGY_STORAGE_STATION+TableName.PREFIX, energyStorageStations);
        }
        List<Map<String, Object>> chargingStations = ParseDataUtil.parseList(unit.get(0).get(TableName.CHARGING_STATION + TableName.PREFIX));
        if(CollectionUtils.isNotEmpty(chargingStations)) {
            subUnit.get(0).put(TableName.CHARGING_STATION+TableName.PREFIX, chargingStations);
        }
    }

    /**
     * @Description: 获取用户所属区域下级单位
     * @Author: gongtong
     **/
    public static List<Map<String, Object>> getUserSubUnit1() {
        List<Map<String, Object>> subUnit = new ArrayList<>();
        ModelNode userModel = getUserModel();
        String topUnitInfo = ParseDataUtil.parseString(RedisUtils.get(CommonConstant.TOP_TREE_NODE));
        if (StringUtils.isEmpty(topUnitInfo)) {
            topUnitCache();
            topUnitInfo = ParseDataUtil.parseString(RedisUtils.get(CommonConstant.TOP_TREE_NODE));
        }
        List<Map<String, Object>> topUnit = ParseDataUtil.parseList(JsonTransferUtils.parseObject(topUnitInfo, List.class));
        // 网级用户展示所有省，省级用户展示所有地市，地市级用户展示所有县级，若没有县级展示当前市级
        if (TableName.NETCOMPANY.equals(userModel.getModelLabel())) {
            subUnit = topUnit;
        } else if (TableName.PROVINCECOMPANY.equals(userModel.getModelLabel())) {
            List<Map<String, Object>> provinceUnit = topUnit.stream().filter(x -> userModel.getId().equals(ParseDataUtil.parseLong(x.get("id")))).collect(Collectors.toList());
            subUnit = ParseDataUtil.parseList(provinceUnit.get(0).get(TableName.CITYCOMPANY + TableName.PREFIX));
        } else if (TableName.CITYCOMPANY.equals(userModel.getModelLabel())) {
            List<Map<String, Object>> cityUnit = getCityCompany(topUnit,userModel.getId());
            subUnit = ParseDataUtil.parseList(cityUnit.get(0).get(TableName.COUNTYCOMPANY + TableName.PREFIX));
            if (CollectionUtils.isEmpty(subUnit)) {
                subUnit = cityUnit;
            } else {
                // 干扰源
                setSubUnit(subUnit, cityUnit);
            }
        }
        if (CollectionUtils.isEmpty(subUnit)) {
            subUnit = topUnit;
        }
        return subUnit;
    }

    private static void topUnitCache() {
        List<SingleModelConditionDTO> subConditions = Arrays.asList(
                new SingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.COUNTYCOMPANY, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.PQTERMINAL, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.INTERFERENCESOURCE, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.ELECTRICRAILWAY, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.PHOTOVOLTAICSTATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.WINDPOWERSTATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.SMELTLOAD, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.CHARGING_STATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.ENERGY_STORAGE_STATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.BUS_BAR_SECTION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.LINE, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.MONITORSORT,
                        ColumnName.MONITORMODE, ColumnName.MONITORTYPE, ColumnName.PQMONITORTYPE, ColumnName.ISUPLOAD)),
                new SingleModelConditionDTO(TableName.POWERDISTRIBUTIONAREA, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.LINENAME)),
                new SingleModelConditionDTO(TableName.POWERGENERATIONUSER, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.ELECTRICITYUSER, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.TESTINSTRUMENT, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.GOVERNANCEDEVICE, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.MAIN_TRANSFORMER, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.DISTRIBUTION_TRANSFORMER, Arrays.asList(ColumnName.NAME, ColumnName.ID))
        );
        List<Map<String, Object>> interRelations = ModelServiceUtils.getInterRelationsLimitProps(null, TableName.PROVINCECOMPANY, null, null, subConditions, false,Arrays.asList(ColumnName.NAME, ColumnName.ID));
        RedisUtils.setEx(CommonConstant.TOP_TREE_NODE, JsonTransferUtils.toJsonString(interRelations), 60, TimeUnit.MINUTES);
    }


    private static List<Map<String, Object>> getCityCompany(List<Map<String, Object>> topUnit, Long cityId) {
        List<Map<String, Object>> cityUnit = new ArrayList<>();
        topUnit.stream().forEach(x -> cityUnit.addAll(ParseDataUtil.parseList(x.get(TableName.CITYCOMPANY + TableName.PREFIX))));
        return cityUnit.stream().filter(x -> cityId.equals(ParseDataUtil.parseLong(x.get("id")))).collect(Collectors.toList());
    }

    /**
     * @param modelId
     * @param modelLabel
     * @Description: 获取单位信息
     * @Author: gongtong
     **/
    static List<Map<String, Object>> getUnit(Long modelId, String modelLabel) {
        List<SingleModelConditionDTO> subConditions = Arrays.asList(
                new SingleModelConditionDTO(TableName.PROVINCECOMPANY, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.COUNTYCOMPANY, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.PQTERMINAL, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.INTERFERENCESOURCE, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.ELECTRICRAILWAY, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.PHOTOVOLTAICSTATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.WINDPOWERSTATION, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.LINE, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.MONITORSORT,
                        ColumnName.MONITORMODE, ColumnName.MONITORTYPE, ColumnName.PQMONITORTYPE, ColumnName.ISUPLOAD)),
                new SingleModelConditionDTO(TableName.POWERDISTRIBUTIONAREA, Arrays.asList(ColumnName.ID, ColumnName.NAME, ColumnName.LINENAME)),
                new SingleModelConditionDTO(TableName.POWERGENERATIONUSER, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.ELECTRICITYUSER, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.TESTINSTRUMENT, Arrays.asList(ColumnName.NAME, ColumnName.ID)),
                new SingleModelConditionDTO(TableName.GOVERNANCEDEVICE, Arrays.asList(ColumnName.NAME, ColumnName.ID)));
        return ModelServiceUtils.getInterRelations(Collections.singletonList(modelId), modelLabel, null, null, subConditions, false);
    }

}

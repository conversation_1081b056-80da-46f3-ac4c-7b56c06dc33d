package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.excel.ExportIndexParam;
import com.cet.pq.anlysis.model.linestatistics.InventoryDistributionCount;
import com.cet.pq.anlysis.model.linestatistics.InventoryIndexCount;
import com.cet.pq.anlysis.model.linestatistics.InventoryIndexDetail;
import com.cet.pq.anlysis.model.linestatistics.InventoryStatisticsCount;
import com.cet.pq.anlysis.service.InventoryIndexService;
import com.cet.pq.common.model.ResultWithTotal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @descriptioin 台账指标管理统计接口
 * <AUTHOR>
 * @Date 2020-08-21
 */
@RestController
@RequestMapping("/pq/v1/index")
@Api(value = "台账指标管理统计接口",tags = "指标管理 - 台账统计接口")
public class InventoryIndexController {

	@Autowired
	private InventoryIndexService bookIndexService;

	@ApiOperation("台账指标综合统计")
	@GetMapping("/book/statistics")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isUpload",value = "是否上送，不传查询全网", required = false, dataType = "Boolean")
	})
	public ResultWithTotal<List<InventoryStatisticsCount>> getBookStatistics(Boolean isUpload){
		List<InventoryStatisticsCount> statisticsCountList = bookIndexService.getBookStatistics(isUpload);
		return ResultWithTotal.success(statisticsCountList, statisticsCountList.size());
	}

	@ApiOperation("台账分布统计")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isUpload",value = "是否上送，不传查询全网", required = false, dataType = "Boolean")
	})
	@GetMapping("/book/distribution")
	public ResultWithTotal<List<InventoryDistributionCount>> getDistribution(Boolean isUpload) {
		List<InventoryDistributionCount> bookDistributionCountList = bookIndexService.getDistribution(isUpload);
		return ResultWithTotal.success(bookDistributionCountList, bookDistributionCountList.size());
	}

	@ApiOperation("台账指标统计")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isUpload",value = "是否上送，不传查询全网", required = false, dataType = "Boolean")
	})
	@GetMapping("/book/index/statistics")
	public ResultWithTotal<List<InventoryIndexCount>> getBookIndexCount(Boolean isUpload) {
		List<InventoryIndexCount> bookIndexCountList = bookIndexService.getBookIndexCount(isUpload);
		return ResultWithTotal.success(bookIndexCountList, bookIndexCountList.size());
	}

	@ApiOperation("台账指标明细接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "isUpload",value = "是否上送，不传查询全网", required = false, dataType = "Boolean")
	})
	@GetMapping("/bookdetail/statistics")
	public ResultWithTotal<List<InventoryIndexDetail>> getBookDetailCount(Boolean isUpload) {
		List<InventoryIndexDetail> bookDetailList = bookIndexService.getBookDetailCount(isUpload);
		return ResultWithTotal.success(bookDetailList, bookDetailList.size());
	}

	@ApiOperation("导出台账指标统计")
	@PostMapping("/book/exportStatistics")
	public void exportBookStatistics(@RequestBody ExportIndexParam exportIndexParam, HttpServletResponse response){
		bookIndexService.exportBookStatistics(exportIndexParam, response);
	}

}

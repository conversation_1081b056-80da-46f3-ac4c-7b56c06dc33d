package com.cet.pq.common.encode;

import java.util.*;

import javax.annotation.PostConstruct;

import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.EnumOperationType;
import com.cet.pq.common.enums.VoltageLevelEnum;
import com.cet.pq.common.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.feign.ModelDataService;
import com.cet.pq.common.model.ConditionBlock;
import com.cet.pq.common.model.GbRecalcPara;
import com.cet.pq.common.model.Gblimit;
import com.cet.pq.common.model.PqMonitorNode;
import com.cet.pq.common.model.PqReportParam;
import com.cet.pq.common.model.QueryCondition;
import com.cet.pq.common.model.RatedFreqDef;
import com.cet.pq.common.model.RatedVoltDef;
import com.cet.pq.common.model.ResultWithTotal;
import com.cet.pq.common.model.WiredTypeDef;
import com.cet.pq.common.model.realtime.PqDefaultLimit;
import com.cet.pq.common.model.realtime.QuantityLimit;
import com.cet.pq.common.utils.JsonTransferUtils;
import com.cet.pq.common.utils.ModelServiceUtils;
import com.cet.pq.common.utils.ParseDataUtil;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/6 13:47
 */
@Component
public class GbLimitManager {

    /// <summary>
    /// 星形接线默认报表参数列表
    /// </summary>
    List<PqReportParam> starReportParams;
    /// <summary>
    /// 三角形接线默认报表参数列表
    /// </summary>
    List<PqReportParam> triangleReportParams;

    @Autowired
    ModelDataService modelDataService;

//    @Autowired
//    ModelServiceUtils modelServiceUtils;
//    private static ModelServiceUtils modelServiceUtil;
//    @PostConstruct
//    public void init() {
//        modelServiceUtil = modelServiceUtils;
//    }

    private  GbLimitManager(){

    }

    /**
     * 获取默认限值
     */
    private void loadDefaultReportParams() {
        //直接从模型读取默认值
    }

    /**
     *获取限值
     * @param PQNodeID
     * @return
     */
    public List<Gblimit> recalcGbBlimit(int PQNodeID)
    {
        List<PqReportParam> recalcReportParams = getReCalcedReportParams(PQNodeID);
        return convetReportParams2GbLimits(recalcReportParams);
    }
    /// <summary>
    /// 重算限值更新报表测点列表
    /// </summary>
    /// <param name="pqNode">监测节点对象</param>
    /// <returns></returns>
    List<PqReportParam> getReCalcedReportParams(int pqNodeID)
    {
        if (pqNodeID == 0) {
            return this.starReportParams;//默认返回星形接线方式的国标限值列表
        }
        PqMonitorNode pqNode = new PqMonitorNode();
                //PqNodeManager.PQNodeInstance.FindPQMonitorGNode(PqNodeType.PQMONITOR_NODE, pqNodeID) as PQMonitorNode;
        //if (pqNode == null) {
       //     return new ArrayList<>();
       // }
        GbRecalcPara recalcPara = new GbRecalcPara();
        List<PqReportParam> reportParas;
        //if (pqNode.ReportParamList.Count != 0)
        //    reportParas = pqNode.ReportParamList;
        //else
        reportParas = getDefaultReportParams(pqNode.getWiredType());
        return recalcGBLimit(reportParas, recalcPara);
    }

    /**
     * 通过接线方式获取默认限制
     * @param wireType
     * @return
     */
    List<PqReportParam> getDefaultReportParams(int wireType)
    {
        if (checkIsStarType(wireType)) {
            return starReportParams;
        }
        else {
            return triangleReportParams;
        }
    }

    /// <summary>
    /// 获取监测点的电压类型（线电压/相电压）
    /// </summary>
    /// <param name="pqNode"></param>
    /// <returns></returns>
    static boolean checkIsStarType(int wireType)
    {
        if (wireType == WiredTypeDef.TRIANGLESTYLE) {
            return false;
        }
        return true;
    }
    //获取默认限值
    private List<PqReportParam> getReportParamsFromIniFile()
    {
        //PQDefaultLimit
        QueryCondition condition = new QueryCondition();
        condition.setRootLabel(TableName.PQDEFAULTLIMIT);
        ResultWithTotal<List<Map<String, Object>>> resultWithTotal =  modelDataService.query(condition);
        List<PqDefaultLimit> reportParams = JsonTransferUtils.transferList(resultWithTotal.getData(),PqDefaultLimit.class);
        return converPqReportParams(reportParams);
    }

    private List<PqReportParam> converPqReportParams(List<PqDefaultLimit> reportParams) {
        List<PqReportParam> list = new ArrayList<>();
        for(PqDefaultLimit pqDefaultLimit : reportParams){
            PqReportParam pqReportParam = new PqReportParam();
            pqReportParam.setDataID(pqDefaultLimit.getDataid());
            pqReportParam.setLimitHigh(pqDefaultLimit.getLimithigh());
            pqReportParam.setLimitLow(pqDefaultLimit.getLimitlow());
            pqReportParam.setDataName(pqDefaultLimit.getDataname());
            list.add(pqReportParam);
        }
        return list;
    }

    //添加限值
    private void addPQReportParams(int count, String section, Boolean bFixed,  List<QuantityLimit> reportParams)
    {
        modelDataService.write(reportParams);
    }
    private PqReportParam decodePQReportParam(String strDef, boolean bFixed)
    {
        String[] strPara = new String[11];
        int nFounded = 0, nBegin = 0;

        for (int i = 0; i < CommonConstant.NUMBER_ELEVEN; i++)
        {
            nFounded = strDef.indexOf(';', nBegin);
            if (nFounded < 0) {
                break;
            }
            strPara[i] = strDef.substring(nBegin, nFounded - nBegin);
            nBegin = nFounded + 1;
        }
        if (strPara[0] == null) {
            return new PqReportParam();
        }
        PqReportParam param = new PqReportParam();
        try
        {
            param.setDataName(strPara[0]);
            param.setDataID(Long.valueOf(strPara[1]));
            param.setLimitHigh(strPara[2].isEmpty() ? Double.NaN : Double.valueOf(strPara[2]));
            param.setLimitLow(strPara[3].isEmpty() ? Double.NaN : Double.valueOf(strPara[3]));
            return param;
        }
        catch (Exception ex)
        {
            return param;
        }
    }
    /// <summary>
    /// 电压缩放系数，默认为1
    /// </summary>
    private static int voltScale = 1;
    /// <summary>
    /// 重算国标限值
    /// </summary>
    /// <param name="reportParams"></param>
    /// <param name="ratedVolt">电压等级</param>
    /// <param name="miniShortCapacity">最小短路容量</param>
    /// <param name="baseShortCapacity">基准短路容量</param>
    /// <param name="supplyCapacity">用户协议容量</param>
    /// <param name="deviceCapacity">设备供电容量</param>
    /// <param name="isPcc">是否是系统侧</param>
    /// <param name="ratedFreq">额定频率</param>
    public static List<PqReportParam> recalcGBLimit(List<PqReportParam> reportParams, GbRecalcPara gbRecalcPara)
    {
        List<PqReportParam> newReportParams = new ArrayList<>();
        voltScale = getVoltScale();
        double ratedShortCapacity = getBaseCapacity(gbRecalcPara.getRatedVolt());
        String strHrmILimits = getHrmSPLimit(1, gbRecalcPara.getRatedVolt(), gbRecalcPara.getShortCircuitCapacity(), ratedShortCapacity,
                gbRecalcPara.getUserProtocolCapacity(), gbRecalcPara.getSupplyEquipmentCapacity());
        String strHrmVLimits = getHrmSPLimit(0, gbRecalcPara.getRatedVolt(), gbRecalcPara.getShortCircuitCapacity(), ratedShortCapacity,
                gbRecalcPara.getUserProtocolCapacity(), gbRecalcPara.getSupplyEquipmentCapacity());
        String strInterHrmVLmts = getInterHrmVLmts(gbRecalcPara.getRatedVolt());
        //计算电压偏差
        Double voltageNumber = VoltageLevelEnum.getVoltageLevelNumber(gbRecalcPara.getRatedVolt());
        Double lowVolt = voltageNumber * 1000 / voltScale;
        Double upVolt = voltageNumber * 1000 / voltScale;
        if (gbRecalcPara.getLowVoltDeviation() != null && gbRecalcPara.getUpVoltDeviation() != null) {
            lowVolt = CommonUtils.calcDouble(lowVolt, (100 + gbRecalcPara.getLowVoltDeviation()) / 100, EnumOperationType.MULTIPLICATION.getId());
            upVolt = CommonUtils.calcDouble(upVolt, (100 + gbRecalcPara.getUpVoltDeviation()) / 100, EnumOperationType.MULTIPLICATION.getId());
        }
        List<Long> voltDataId = Arrays.asList(2L, 3L, 4L);
        List<Long> lineVoltDataId = Arrays.asList(5L, 6L, 7L);
        for (int i = 0; i < reportParams.size(); i++)
        {
            PqReportParam reportParam = reportParams.get(i);
            double low = (100 + gbRecalcPara.getLowVoltDeviation()) / 100;
            double up = (100 + gbRecalcPara.getUpVoltDeviation()) / 100;
            calculageNodeLimit(reportParam, 50, gbRecalcPara.getRatedVolt(), strHrmVLimits, strHrmILimits, strInterHrmVLmts, gbRecalcPara.getShortCircuitCapacity(),
                    up, low, gbRecalcPara.getLowVoltDeviation(), gbRecalcPara.getUpVoltDeviation());
            //电压需要考虑偏差，线电压上限不能大于upVolt， 线电压下限不能小于lowVolt
            if (lineVoltDataId.contains(reportParam.getDataID())) {
                if ((reportParam.getLimitLow() != null && reportParam.getLimitLow() < lowVolt) && (reportParam.getLimitHigh() != null && reportParam.getLimitHigh() > upVolt)) {
                    reportParam.setLimitLow(lowVolt);
                    reportParam.setLimitHigh(upVolt);
                }
               /* if (reportParam.getLimitHigh() != null && reportParam.getLimitHigh() > upVolt) {
                    reportParam.setLimitHigh(upVolt);
                }*/
            }
            if (voltDataId.contains(reportParam.getDataID())) {
                if ((reportParam.getLimitLow() != null && reportParam.getLimitLow() < (lowVolt / 1.732)) && (reportParam.getLimitHigh() != null && reportParam.getLimitHigh() > (upVolt / 1.732))) {
                    reportParam.setLimitLow((lowVolt / 1.732));
                    reportParam.setLimitHigh((upVolt / 1.732));
                }
                /*if (reportParam.getLimitHigh() != null && reportParam.getLimitHigh() > (upVolt / 1.732)) {
                    reportParam.setLimitHigh((upVolt / 1.732));
                }*/
            }
            newReportParams.add(reportParam);
        }
        return newReportParams;
    }
    /// 获取电压缩放系数，默认为1
    /// </summary>
    /// <returns></returns>
    public static int getVoltScale()
    {
        int scale = 1;
        int voltageUnitType = 2;
                //PqConfigNode.PQConfigurationNode.VoltageUnitType;
        //如果为kV,则为1000
        List<Map<String, Object>> quantityTemplate = ModelServiceUtils.querySingleModel(null, TableName.QUANTITYMAPTEMPLATE,
                Collections.singletonList(new ConditionBlock(ColumnName.DATA_ID, ConditionBlock.OPERATOR_IN, Collections.singleton(2))), null, null).getData();
        if (ColumnName.KV.equals(ParseDataUtil.parseString(quantityTemplate.get(0).get(ColumnName.UNIT)))) {
            scale = 1000;
        }

        return scale;
    }
    /// <summary>
    /// 获取间谐波电压限值数组
    /// </summary>
    /// <param name="ratedVolt">电压等级</param>
    /// <returns></returns>
    static String getInterHrmVLmts(int ratedVolt)
    {
        String result = StringUtils.EMPTY;
        double[] dbVInterHrms = new double[16];

        switch (ratedVolt)
        {
            case RatedVoltDef.VOLTAGE380V:
            case RatedVoltDef.VOLTAGE215V:
            case RatedVoltDef.VOLTAGE433V:
            case RatedVoltDef.VOLTAGE500V:
                dbVInterHrms[0] = 0.2;
                dbVInterHrms[1] = 0.2;
                dbVInterHrms[2] = 0.5;
                dbVInterHrms[3] = 0.5;
                dbVInterHrms[4] = 0.5;
                dbVInterHrms[5] = 0.5;
                dbVInterHrms[6] = 0.5;
                dbVInterHrms[7] = 0.5;
                dbVInterHrms[8] = 0.5;
                dbVInterHrms[9] = 0.5;
                dbVInterHrms[10] = 0.5;
                dbVInterHrms[11] = 0.5;
                dbVInterHrms[12] = 0.5;
                dbVInterHrms[13] = 0.5;
                dbVInterHrms[14] = 0.5;
                dbVInterHrms[15] = 0.5;
                break;
            default:
                dbVInterHrms[0] = 0.16;
                dbVInterHrms[1] = 0.16;
                dbVInterHrms[2] = 0.4;
                dbVInterHrms[3] = 0.4;
                dbVInterHrms[4] = 0.4;
                dbVInterHrms[5] = 0.4;
                dbVInterHrms[6] = 0.4;
                dbVInterHrms[7] = 0.4;
                dbVInterHrms[8] = 0.4;
                dbVInterHrms[9] = 0.4;
                dbVInterHrms[10] = 0.4;
                dbVInterHrms[11] = 0.4;
                dbVInterHrms[12] = 0.4;
                dbVInterHrms[13] = 0.4;
                dbVInterHrms[14] = 0.4;
                dbVInterHrms[15] = 0.4;
                break;

        }

        String tmp;
        for (int i = 0; i < CommonConstant.NUMBER_SIXTEEN; i++)
        {
            tmp = String.format("H{%d}={%.2f:F2};", i + 1, dbVInterHrms[i]);
            result += tmp;
        }
        return result;
    }
    /// <summary>
    /// 获取限值字符串
    /// </summary>
    /// <param name="channel">0-电压，1-电流</param>
    /// <param name="ratedVolt">电压等级</param>
    /// <param name="miniShortCapacity">最小短路容量</param>
    /// <param name="baseShortCapacity">基准短路容量</param>
    /// <param name="m_dbContract">协议供电容量</param>
    /// <param name="m_dbDevice">设备供电容量</param>
    /// <param name="m_bIsPcc">true-系统侧，false-用户侧</param>
    /// <returns></returns>
    static String getHrmSPLimit(int channel, int ratedVolt, double miniShortCapacity, double baseShortCapacity, double m_dbContract, double m_dbDevice)
    {
        String result = StringUtils.EMPTY;
        /** 根据是否公共连接点、额定电压等级、以及容量的定义计算各通道各次谐波阈值*/

        /** 电压，只需要根据节点电压等级来计算，支持到63次*/
        if (channel == 0)
        {
            result = getHrmVoltSpLimit(ratedVolt);
        }
        /** 谐波电流，需要根据电压等级、是否公共连接点、以及容量定义来确定，支持到50次*/
        else
        {
            result = getHrmCurrentSpLimit(ratedVolt, miniShortCapacity, baseShortCapacity, m_dbContract, m_dbDevice, result);
        }

        return result;
    }
    private static String getHrmVoltSpLimit(int ratedVolt)
    {
        double dbHrm;
        switch (ratedVolt)
        {
            case RatedVoltDef.VOLTAGE380V:
            case RatedVoltDef.VOLTAGE215V:	// 新增
            case RatedVoltDef.VOLTAGE433V:	// 新增
            case RatedVoltDef.VOLTAGE500V:	// 新增
                dbHrm = 5.0;
                break;
            case RatedVoltDef.VOLTAGE6KV:
            case RatedVoltDef.VOLTAGE10KV:
            case RatedVoltDef.VOLTAGE20KV:
            case RatedVoltDef.VOLTAGE6600V:	//新增
            case RatedVoltDef.VOLTAGE6900V: //新增
                dbHrm = 4.0;
                break;
            case RatedVoltDef.VOLTAGE35KV:
            case RatedVoltDef.VOLTAGE66KV:
            case RatedVoltDef.VOLTAGE33KV://新增
                dbHrm = 3.0;
                break;
            case RatedVoltDef.VOLTAGE110KV:
                dbHrm = 2.0;
                break;
            default:
                dbHrm = 2.0;
                break;
        }
        /** THD*/
        String result = String.format("H{1}={%.2f:F2};", dbHrm);
        String tmp;
        for (int i = 0; i < CommonConstant.NUMBER_THIRTY_ONE; i++)
        {
            tmp = String.format("H{%d}={%.2f:F2};", 2 * i + 2, dbHrm * 0.4);
            result += tmp;
            tmp = String.format("H{%d}={%.2f:F2};", 2 * i + 3, dbHrm * 0.8);
            result += tmp;
        }
        return result;
    }
    private static String getHrmCurrentSpLimit(int ratedVolt, double miniShortCapacity, double baseShortCapacity, double m_dbContract, double m_dbDevice, String result)
    {
        double[] dbIHrms = new double[50];
        dbIHrms[0] = 0;
        switch (ratedVolt)
        {
            case RatedVoltDef.VOLTAGE380V:
            case RatedVoltDef.VOLTAGE215V:	// 新增
            case RatedVoltDef.VOLTAGE433V:	// 新增
            case RatedVoltDef.VOLTAGE500V:	// 新增
                dbIHrms[1] = 78;
                dbIHrms[2] = 62;
                dbIHrms[3] = 39;
                dbIHrms[4] = 62;
                dbIHrms[5] = 26;
                dbIHrms[6] = 44;
                dbIHrms[7] = 19;
                dbIHrms[8] = 21;
                dbIHrms[9] = 16;
                dbIHrms[10] = 28;
                dbIHrms[11] = 13;
                dbIHrms[12] = 24;
                dbIHrms[13] = 11;
                dbIHrms[14] = 12;
                dbIHrms[15] = 9.7;
                dbIHrms[16] = 18;
                dbIHrms[17] = 8.6;
                dbIHrms[18] = 16;
                dbIHrms[19] = 7.8;
                dbIHrms[20] = 8.9;
                dbIHrms[21] = 7.1;
                dbIHrms[22] = 14;
                dbIHrms[23] = 6.5;
                dbIHrms[24] = 12;
                dbIHrms[25] = 6;
                dbIHrms[26] = 6.9;
                dbIHrms[27] = 5.6;
                dbIHrms[28] = 11;
                dbIHrms[29] = 5.2;
                dbIHrms[30] = 10;
                dbIHrms[31] = 4.9;
                dbIHrms[32] = 5.6;
                dbIHrms[33] = 4.6;
                dbIHrms[34] = 8.9;
                dbIHrms[35] = 4.3;
                dbIHrms[36] = 8.4;
                dbIHrms[37] = 4.1;
                dbIHrms[38] = 4.8;
                dbIHrms[39] = 3.9;
                dbIHrms[40] = 7.6;
                dbIHrms[41] = 3.7;
                dbIHrms[42] = 7.2;
                dbIHrms[43] = 3.5;
                dbIHrms[44] = 4.1;
                dbIHrms[45] = 3.4;
                dbIHrms[46] = 6.6;
                dbIHrms[47] = 3.3;
                dbIHrms[48] = 6.3;
                dbIHrms[49] = 3.1;
                break;
            case RatedVoltDef.VOLTAGE6KV:
            case RatedVoltDef.VOLTAGE6600V: //新增
            case RatedVoltDef.VOLTAGE6900V: //新增
                dbIHrms[1] = 43;
                dbIHrms[2] = 34;
                dbIHrms[3] = 21;
                dbIHrms[4] = 34;
                dbIHrms[5] = 14;
                dbIHrms[6] = 24;
                dbIHrms[7] = 11;
                dbIHrms[8] = 11;
                dbIHrms[9] = 8.5;
                dbIHrms[10] = 16;
                dbIHrms[11] = 7.1;
                dbIHrms[12] = 13;
                dbIHrms[13] = 6.1;
                dbIHrms[14] = 6.8;
                dbIHrms[15] = 5.3;
                dbIHrms[16] = 10;
                dbIHrms[17] = 4.7;
                dbIHrms[18] = 9;
                dbIHrms[19] = 4.3;
                dbIHrms[20] = 4.9;
                dbIHrms[21] = 3.9;
                dbIHrms[22] = 7.4;
                dbIHrms[23] = 3.6;
                dbIHrms[24] = 6.8;
                dbIHrms[25] = 3.3;
                dbIHrms[26] = 3.8;
                dbIHrms[27] = 3.1;
                dbIHrms[28] = 5.9;
                dbIHrms[29] = 2.9;
                dbIHrms[30] = 5.5;
                dbIHrms[31] = 2.7;
                dbIHrms[32] = 3.1;
                dbIHrms[33] = 2.5;
                dbIHrms[34] = 4.9;
                dbIHrms[35] = 2.4;
                dbIHrms[36] = 4.6;
                dbIHrms[37] = 2.3;
                dbIHrms[38] = 2.6;
                dbIHrms[39] = 2.2;
                dbIHrms[40] = 4.1;
                dbIHrms[41] = 2;
                dbIHrms[42] = 4;
                dbIHrms[43] = 2;
                dbIHrms[44] = 2.3;
                dbIHrms[45] = 1.9;
                dbIHrms[46] = 3.6;
                dbIHrms[47] = 1.8;
                dbIHrms[48] = 3.5;
                dbIHrms[49] = 1.7;
                break;
            case RatedVoltDef.VOLTAGE10KV:
            case RatedVoltDef.VOLTAGE20KV:
                dbIHrms[1] = 26;
                dbIHrms[2] = 20;
                dbIHrms[3] = 13;
                dbIHrms[4] = 20;
                dbIHrms[5] = 8.5;
                dbIHrms[6] = 15;
                dbIHrms[7] = 6.4;
                dbIHrms[8] = 6.8;
                dbIHrms[9] = 5.1;
                dbIHrms[10] = 9.3;
                dbIHrms[11] = 4.3;
                dbIHrms[12] = 7.9;
                dbIHrms[13] = 3.7;
                dbIHrms[14] = 4.1;
                dbIHrms[15] = 3.2;
                dbIHrms[16] = 6;
                dbIHrms[17] = 2.8;
                dbIHrms[18] = 5.4;
                dbIHrms[19] = 2.6;
                dbIHrms[20] = 2.9;
                dbIHrms[21] = 2.3;
                dbIHrms[22] = 4.5;
                dbIHrms[23] = 2.1;
                dbIHrms[24] = 4.1;
                dbIHrms[25] = 2;
                dbIHrms[26] = 2.2;
                dbIHrms[27] = 1.9;
                dbIHrms[28] = 3.4;
                dbIHrms[29] = 1.7;
                dbIHrms[30] = 3.2;
                dbIHrms[31] = 1.6;
                dbIHrms[32] = 1.8;
                dbIHrms[33] = 1.5;
                dbIHrms[34] = 2.9;
                dbIHrms[35] = 1.4;
                dbIHrms[36] = 2.7;
                dbIHrms[37] = 1.4;
                dbIHrms[38] = 1.5;
                dbIHrms[39] = 1.3;
                dbIHrms[40] = 2.4;
                dbIHrms[41] = 1.2;
                dbIHrms[42] = 2.3;
                dbIHrms[43] = 1.2;
                dbIHrms[44] = 1.3;
                dbIHrms[45] = 1.1;
                dbIHrms[46] = 2.1;
                dbIHrms[47] = 1.1;
                dbIHrms[48] = 2;
                dbIHrms[49] = 1;
                break;
            case RatedVoltDef.VOLTAGE35KV:
            case RatedVoltDef.VOLTAGE33KV://新增
                dbIHrms[1] = 15;
                dbIHrms[2] = 12;
                dbIHrms[3] = 7.7;
                dbIHrms[4] = 12;
                dbIHrms[5] = 5.1;
                dbIHrms[6] = 8.8;
                dbIHrms[7] = 3.8;
                dbIHrms[8] = 4.1;
                dbIHrms[9] = 3.1;
                dbIHrms[10] = 5.6;
                dbIHrms[11] = 2.6;
                dbIHrms[12] = 4.7;
                dbIHrms[13] = 2.2;
                dbIHrms[14] = 2.5;
                dbIHrms[15] = 1.9;
                dbIHrms[16] = 3.6;
                dbIHrms[17] = 1.7;
                dbIHrms[18] = 3.2;
                dbIHrms[19] = 1.5;
                dbIHrms[20] = 1.8;
                dbIHrms[21] = 1.4;
                dbIHrms[22] = 2.7;
                dbIHrms[23] = 1.3;
                dbIHrms[24] = 2.5;
                dbIHrms[25] = 1.2;
                dbIHrms[26] = 1.3;
                dbIHrms[27] = 1.1;
                dbIHrms[28] = 2.1;
                dbIHrms[29] = 1;
                dbIHrms[30] = 1.9;
                dbIHrms[31] = 0.9;
                dbIHrms[32] = 1.1;
                dbIHrms[33] = 0.9;
                dbIHrms[34] = 1.7;
                dbIHrms[35] = 0.8;
                dbIHrms[36] = 1.6;
                dbIHrms[37] = 0.8;
                dbIHrms[38] = 0.9;
                dbIHrms[39] = 0.8;
                dbIHrms[40] = 1.5;
                dbIHrms[41] = 0.7;
                dbIHrms[42] = 1.4;
                dbIHrms[43] = 0.7;
                dbIHrms[44] = 0.8;
                dbIHrms[45] = 0.7;
                dbIHrms[46] = 1.3;
                dbIHrms[47] = 0.6;
                dbIHrms[48] = 1.2;
                dbIHrms[49] = 0.6;
                break;
            case RatedVoltDef.VOLTAGE66KV:
                dbIHrms[1] = 16;
                dbIHrms[2] = 13;
                dbIHrms[3] = 8.1;
                dbIHrms[4] = 13;
                dbIHrms[5] = 5.4;
                dbIHrms[6] = 9.3;
                dbIHrms[7] = 4.1;
                dbIHrms[8] = 4.3;
                dbIHrms[9] = 3.3;
                dbIHrms[10] = 5.9;
                dbIHrms[11] = 2.7;
                dbIHrms[12] = 5;
                dbIHrms[13] = 2.3;
                dbIHrms[14] = 2.6;
                dbIHrms[15] = 2;
                dbIHrms[16] = 3.8;
                dbIHrms[17] = 1.8;
                dbIHrms[18] = 3.4;
                dbIHrms[19] = 1.6;
                dbIHrms[20] = 1.9;
                dbIHrms[21] = 1.5;
                dbIHrms[22] = 2.8;
                dbIHrms[23] = 1.4;
                dbIHrms[24] = 2.6;
                dbIHrms[25] = 1.2;
                dbIHrms[26] = 1.4;
                dbIHrms[27] = 1.1;
                dbIHrms[28] = 2.2;
                dbIHrms[29] = 1.1;
                dbIHrms[30] = 2.1;
                dbIHrms[31] = 1;
                dbIHrms[32] = 1.2;
                dbIHrms[33] = 0.9;
                dbIHrms[34] = 1.9;
                dbIHrms[35] = 0.9;
                dbIHrms[36] = 1.8;
                dbIHrms[37] = 0.8;
                dbIHrms[38] = 1;
                dbIHrms[39] = 0.8;
                dbIHrms[40] = 1.6;
                dbIHrms[41] = 0.8;
                dbIHrms[42] = 1.5;
                dbIHrms[43] = 0.7;
                dbIHrms[44] = 0.9;
                dbIHrms[45] = 0.7;
                dbIHrms[46] = 1.4;
                dbIHrms[47] = 0.7;
                dbIHrms[48] = 1.3;
                dbIHrms[49] = 0.6;
                break;
            default://110kV及以上
                dbIHrms[1] = 12;
                dbIHrms[2] = 9.6;
                dbIHrms[3] = 6;
                dbIHrms[4] = 9.6;
                dbIHrms[5] = 4;
                dbIHrms[6] = 6.8;
                dbIHrms[7] = 3;
                dbIHrms[8] = 3.2;
                dbIHrms[9] = 2.4;
                dbIHrms[10] = 4.3;
                dbIHrms[11] = 2;
                dbIHrms[12] = 3.7;
                dbIHrms[13] = 1.7;
                dbIHrms[14] = 1.9;
                dbIHrms[15] = 1.5;
                dbIHrms[16] = 2.8;
                dbIHrms[17] = 1.3;
                dbIHrms[18] = 2.5;
                dbIHrms[19] = 1.2;
                dbIHrms[20] = 1.4;
                dbIHrms[21] = 1.1;
                dbIHrms[22] = 2.1;
                dbIHrms[23] = 1;
                dbIHrms[24] = 1.9;
                dbIHrms[25] = 0.9;
                dbIHrms[26] = 1.1;
                dbIHrms[27] = 0.9;
                dbIHrms[28] = 1.7;
                dbIHrms[29] = 0.8;
                dbIHrms[30] = 1.5;
                dbIHrms[31] = 0.8;
                dbIHrms[32] = 0.9;
                dbIHrms[33] = 0.7;
                dbIHrms[34] = 1.4;
                dbIHrms[35] = 0.7;
                dbIHrms[36] = 1.3;
                dbIHrms[37] = 0.6;
                dbIHrms[38] = 0.7;
                dbIHrms[39] = 0.6;
                dbIHrms[40] = 1.2;
                dbIHrms[41] = 0.6;
                dbIHrms[42] = 1.1;
                dbIHrms[43] = 0.5;
                dbIHrms[44] = 0.6;
                dbIHrms[45] = 0.5;
                dbIHrms[46] = 1;
                dbIHrms[47] = 0.5;
                dbIHrms[48] = 1;
                dbIHrms[49] = 0.5;
                break;
        }
        /** 考虑最小短路容量与基准短路容量不等的影响*/
        double dbIh = (miniShortCapacity) / (baseShortCapacity);
        double tmpdata;
        String tmp;
        /** 如果是用户侧*/
        //现在都是系统测
        //if (!m_bIsPcc)
        if (true)
        {
            double dbIhi = m_dbContract / m_dbDevice;
            /** 考虑相位叠加系数影响*/
            for (int index = 1; index < CommonConstant.NUMBER_FIFTY; index++)
            {
                tmpdata = dbIh * dbIHrms[index];
                if (index == 2) {
                    //tmpdata = tmpdata*pow(dbIhi,1.0/1.1);
                    tmpdata = tmpdata * Math.pow(dbIhi, 1.0 / 1.1);
                }
                else if (index == 4) {
                    tmpdata = tmpdata * Math.pow(dbIhi, 1.0 / 1.2);
                }
                else if (index == 6) {
                    tmpdata = tmpdata * Math.pow(dbIhi, 1.0 / 1.4);
                }
                else if (index == 10) {
                    tmpdata = tmpdata * Math.pow(dbIhi, 1.0 / 1.8);
                }
                else if (index == 12) {
                    tmpdata = tmpdata * Math.pow(dbIhi, 1.0 / 1.9);
                }
                else {
                    tmpdata = tmpdata * Math.pow(dbIhi, 1.0 / 2.0);
                }
                tmp = String.format("H{%d}={%.2f:F2};", index + 1, tmpdata);
                result += tmp;
            }
        }
        else
        {
            for (int i = 1; i < CommonConstant.NUMBER_FIFTY; i++)
            {
                tmpdata = dbIh * dbIHrms[i];
                tmp = String.format("H{%d}={%.2f:F2};", i + 1, tmpdata);
                result += tmp;
            }
        }
        return result;
    }
    /// <summary>
    /// 重算报表参数限值
    /// </summary>
    /// <param name="pDef">报表参数限值参数</param>
    /// <param name="m_cbFreq">额定频率</param>
    /// <param name="m_cbVolt">额定电压</param>
    /// <param name="m_strHrmVLmts">谐波电压阈值字符串</param>
    /// <param name="m_strHrmILmts">谐波电流阈值字符串</param>
    static double getHrmSPLimit_Ex(String strLimits, int HrmNo)
    {
        int nFounded = 0 ;
        String result = null;
        strLimits = strLimits.toUpperCase();
        String strLeft;
        strLeft = String.format("H{%d}=", HrmNo);
        nFounded = strLimits.indexOf(strLeft, 0);
        if (nFounded >= 0)
        {
            /** 然后找对应的“＝”和“；”，中间的部分即为所求*/
            int nKeyPos = nFounded;
            int nLineBreaker = strLimits.indexOf(';', nFounded);
            int nEqual = strLimits.indexOf('=', nFounded);

            if ((nEqual < nLineBreaker) && (nEqual >= 0) && ((nEqual + 1) < (nLineBreaker - CommonConstant.NUMBER_FORE))) {
                result = strLimits.substring(nEqual + 2, nLineBreaker - 4);
            }
        }
        return (result == null) ? Double.NaN : Double.valueOf(result);
    }
    static void calculageNodeLimit(PqReportParam pDef, int m_cbFreq, int m_cbVolt, String m_strHrmVLmts, String m_strHrmILmts, String m_strInterHrmVLmts,
                                   double miniShortCapacity, double upVolt, double lowVolt, Double lowvoltdeviation, Double upvoltdeviation)
    {
        //        PQReportParam
        //PREPORTNODEINFO pDef = (PREPORTNODEINFO)m_lcLimitDef.GetItemData(index);
        if ((pDef.getDataID() == 0)) {
            return;
        }
        double dbtmp;
        switch (pDef.getDataID().intValue())
        {
            //PQMonitorNode pqNode;
            //pqNode.RatedFreq
            /** 频率，允许偏差为0.2HZ*/
            case 1:
                dbtmp = (m_cbFreq == RatedFreqDef.FREQ50HZ) ? 50.0 : 60.0;
                pDef.setLimitHigh(dbtmp + 0.20);
                pDef.setLimitLow(dbtmp - 0.20);
                break;
            /** 线电压，10KV以下三相供电允许偏差为-7%-+7%；35kv及以上取-3%和+7%。*/
            case 5:
            case 6:
            case 7:
                switch (m_cbVolt)
                {
                    case RatedVoltDef.VOLTAGE380V:
                        dbtmp = 380;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE6KV:
                        dbtmp = 6000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE215V:	// 新增
                        dbtmp = 380;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE433V:	// 新增
                        dbtmp = 380;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE500V:	// 新增
                        dbtmp = 500;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE6600V:	//新增
                        dbtmp = 6600;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE6900V: //新增
                        dbtmp = 6900;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE10KV:
                        dbtmp = 10000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE20KV:
                        dbtmp = 20000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE33KV://新增
                        dbtmp = 33000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE35KV:
                        dbtmp = 35000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE66KV:
                        dbtmp = 66000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE110KV:
                        dbtmp = 110000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE220KV:
                        dbtmp = 220000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE330KV:
                        dbtmp = 330000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    case RatedVoltDef.VOLTAGE1000KV:
                        dbtmp = 1000000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                    default:
                        dbtmp = 500000;
                        pDef.setLimitHigh(dbtmp * upVolt);
                        pDef.setLimitLow(dbtmp * lowVolt);
                        break;
                }
                /** 电压系数缩放*/
                pDef.setLimitHigh(pDef.getLimitHigh() / voltScale);
                pDef.setLimitLow(pDef.getLimitLow() / voltScale);
                break;
            /** 相电压，在线电压的基础上除以1.732*/
            case 2:
            case 3:
            case 4:
                switch (m_cbVolt)
                {
                    case RatedVoltDef.VOLTAGE380V:
                        dbtmp = 380;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE6KV:
                        dbtmp = 6000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE10KV:
                        dbtmp = 10000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE20KV:
                        dbtmp = 20000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE215V:
                        dbtmp = 380;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE433V:
                        dbtmp = 380;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE500V:
                        dbtmp = 500;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE6600V:
                        dbtmp = 6600;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE6900V:
                        dbtmp = 6900;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE33KV:
                        dbtmp = 33000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE35KV:
                        dbtmp = 35000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE66KV:
                        dbtmp = 66000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE110KV:
                        dbtmp = 110000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE220KV:
                        dbtmp = 220000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE330KV:
                        dbtmp = 330000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    case RatedVoltDef.VOLTAGE1000KV:
                        dbtmp = 1000000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                    default:
                        dbtmp = 500000;
                        pDef.setLimitHigh(dbtmp * upVolt / 1.732);
                        pDef.setLimitLow(dbtmp * lowVolt / 1.732);
                        break;
                }
                /** 电压系数缩放*/
                pDef.setLimitHigh(pDef.getLimitHigh() / voltScale);
                pDef.setLimitLow(pDef.getLimitLow() / voltScale);
                break;
            /** 电压不平衡度，允许偏差为2%*/
            case 69:
                pDef.setLimitHigh(2d);
                pDef.setLimitLow(Double.NaN);
                break;
            /** 长时闪变**/
            case 82:
            case 83:
            case 84:
            case 85:
            case 86:
            case 87:
                switch (m_cbVolt)
                {
                    case RatedVoltDef.VOLTAGE220KV:
                    case RatedVoltDef.VOLTAGE330KV:
                    case RatedVoltDef.VOLTAGE500KV:
                    case RatedVoltDef.VOLTAGE1000KV:
                        pDef.setLimitHigh(0.8);
                        break;
                    default:
                        pDef.setLimitHigh(1.0);
                        break;
                }
                pDef.setLimitLow(Double.NaN);
                break;
            //case 76:
            //case 77:
            ///** 短时闪变，低压中压高压分别为1.0，0.9，0.8*/
            //case 78:
            //	switch((byte)m_cbVolt.GetItemData(m_cbVolt.GetCurSel()))
            //	{
            //	case NOMINAL_VOLT_380:
            //	case NOMINAL_VOLT_215:	// 新增
            //	case NOMINAL_VOLT_433:	// 新增
            //	case NOMINAL_VOLT_500:	// 新增
            //		pDef.LimitHigh = 1.0;
            //		break;
            //	case NOMINAL_VOLT_6000:
            //	case NOMINAL_VOLT_10000:
            //	case NOMINAL_VOLT_20000:
            //	case NOMINAL_VOLT_6600:	//新增
            //	case NOMINAL_VOLT_6900: //新增
            //		pDef.LimitHigh = 0.9;
            //		break;
            //	default:
            //		pDef.LimitHigh = 0.8;
            //		break;
            //	}
            //	pDef.LimitLow = NAN;
            //	break;

            /** 频率偏差*/
            case 97:
                pDef.setLimitHigh(0.20);
                pDef.setLimitLow(-0.20);
                break;
            /** 电压偏差*/
            case 94:
            case 95:
            case 96:
            case 100:
            case 101:
            case 102:
                switch (m_cbVolt)
                {
                    case RatedVoltDef.VOLTAGE380V:
                    case RatedVoltDef.VOLTAGE6KV:
                    case RatedVoltDef.VOLTAGE10KV:
                    case RatedVoltDef.VOLTAGE215V:
                    case RatedVoltDef.VOLTAGE433V:
                    case RatedVoltDef.VOLTAGE500V:
                    case RatedVoltDef.VOLTAGE6600V:
                    case RatedVoltDef.VOLTAGE6900V:
                    case RatedVoltDef.VOLTAGE20KV:
                        pDef.setLimitHigh(7d);
                        pDef.setLimitLow(-7d);
                        break;
                    default:
                        pDef.setLimitHigh(upvoltdeviation);
                        pDef.setLimitLow(lowvoltdeviation);
                        break;
                }
                break;
            case 1000037:
                double lineVolt;
                switch (m_cbVolt)
                {
                    case RatedVoltDef.VOLTAGE380V:
                        lineVolt = 0.4;break;
                    case RatedVoltDef.VOLTAGE6KV:
                        lineVolt = 6.3;break;
                    case RatedVoltDef.VOLTAGE10KV:
                        lineVolt = 10.5;break;
                    case RatedVoltDef.VOLTAGE20KV:
                        lineVolt = 21;break;
                    case RatedVoltDef.VOLTAGE35KV:
                        lineVolt = 36.5;break;
                    case RatedVoltDef.VOLTAGE66KV:
                        lineVolt = 69;break;
                    case RatedVoltDef.VOLTAGE110KV:
                        lineVolt = 115;break;
                    case RatedVoltDef.VOLTAGE220KV:
                        lineVolt = 230;break;
                    case RatedVoltDef.VOLTAGE330KV:
                        lineVolt = 345;break;
                    default:
                        lineVolt = 0;
                        break;
                }
                if (lineVolt == 0) {
                    pDef.setLimitHigh(Double.NaN);
                } else {
                    pDef.setLimitHigh((0.026 * miniShortCapacity)/(Math.sqrt(3) * lineVolt));
                }
                break;
            /** 谐波*/
            default:
            {
                pDef.setLimitLow(Double.NaN);

                //* V1 THD
                if (pDef.getDataID() >= CommonConstant.NUMBER_THIRTY_TWO && pDef.getDataID() <= CommonConstant.NUMBER_THIRTY_SEVEN){
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmVLmts, 1));
                    //* V1 H2 ~ V1 H25
                }
                else if (pDef.getDataID() > CommonConstant.NUMBER_TEN_THOUSAND_AND_ONE && pDef.getDataID() < CommonConstant.NUMBER_TEN_THOUSAND_AND_SIXTY_FOUR) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmVLmts, pDef.getDataID().intValue() - CommonConstant.NUMBER_TENTHOUSAND));
                }
                //* V2 H2 ~ V2 H25
                else if (pDef.getDataID() > 20001 && pDef.getDataID() < 20064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmVLmts, pDef.getDataID().intValue() - 20000));
                }
                //* V3 H2 ~ V3 H25
                else if (pDef.getDataID() > 30001 && pDef.getDataID() < 30064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmVLmts, pDef.getDataID().intValue() - 30000));
                }
                //* V1 H2 ~ V1 H25
                else if (pDef.getDataID() > 40001 && pDef.getDataID() < 40064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmVLmts, pDef.getDataID().intValue() - 40000));
                }
                //* V2 H2 ~ V2 H25
                else if (pDef.getDataID() > 50001 && pDef.getDataID() < 50064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmVLmts, pDef.getDataID().intValue() - 50000));
                }
                //* V3 H2 ~ V3 H25
                else if (pDef.getDataID() > 60001 && pDef.getDataID() < 60064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmVLmts, pDef.getDataID().intValue() - 60000));
                }
                //* I1 H2 ~ I1 H25
                else if (pDef.getDataID() > 1040001 && pDef.getDataID() < 1040064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmILmts, pDef.getDataID().intValue() - 1040000));
                }
                //* I2 H2 ~ I2 H25
                else if (pDef.getDataID() > 1050001 && pDef.getDataID() < 1050064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmILmts, pDef.getDataID().intValue() - 1050000));
                }
                //* I3 H2 ~ I3 H25
                else if (pDef.getDataID() > 1060001 && pDef.getDataID() < 1060064) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strHrmILmts, pDef.getDataID().intValue() - 1060000));
                }
                //* V1 THD1 ~ V1 THD16
                else if (pDef.getDataID() >= 190001 && pDef.getDataID() <= 190016) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strInterHrmVLmts, pDef.getDataID().intValue() - 190000));
                }
                //* V2 THD1 ~ V2 THD16
                else if (pDef.getDataID() >= 200001 && pDef.getDataID() <= 200016) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strInterHrmVLmts, pDef.getDataID().intValue() - 200000));
                }
                //* V3 THD1 ~ V3 THD16
                else if (pDef.getDataID() >= 210001 && pDef.getDataID() <= 210016) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strInterHrmVLmts, pDef.getDataID().intValue() - 210000));
                }
                //* V1 THD1 ~ V1 THD16
                else if (pDef.getDataID() >= 220001 && pDef.getDataID() <= 220016) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strInterHrmVLmts, pDef.getDataID().intValue() - 220000));
                }
                //* V2 THD1 ~ V2 THD16
                else if (pDef.getDataID() >= 230001 && pDef.getDataID() <= 230016) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strInterHrmVLmts, pDef.getDataID().intValue() - 230000));
                }
                //* V3 THD1 ~ V3 THD16
                else if (pDef.getDataID() >= 240001 && pDef.getDataID() <= 240016) {
                    pDef.setLimitHigh(getHrmSPLimit_Ex(m_strInterHrmVLmts, pDef.getDataID().intValue() - 240000));
                }
            }
            break;
        }
    }

    private static List<Gblimit> convetReportParams2GbLimits(List<PqReportParam> reportParams)
    {
        List<Gblimit> gbLimitList = new ArrayList<>();
        for (int i = 0; i < reportParams.size(); i++)
        {
            PqReportParam pqReportParam = reportParams.get(i);
            Gblimit gblimit = new Gblimit();
            gblimit.setDataID(pqReportParam.getDataID().intValue());
            gblimit.setDataName(pqReportParam.getDataName());
            gblimit.setLimitHigh(pqReportParam.getLimitHigh());
            gblimit.setLimitLow(pqReportParam.getLimitLow());
            gbLimitList.add(gblimit);
        }
        return gbLimitList;
    }

    /// <summary>
    /// 相电压参数DataID
    /// </summary>
    private static List<Integer> phaseVoltDataIDList = new ArrayList<Integer>();
    static{
        phaseVoltDataIDList.add(2);
        phaseVoltDataIDList.add(3);
        phaseVoltDataIDList.add(4);
    }

    /// <summary>
    /// 线电压参数DataID
    /// </summary>
    private static List<Integer> lineVoltDataIDList = new ArrayList<Integer>();
    static{
        lineVoltDataIDList.add(5);
        lineVoltDataIDList.add(6);
        lineVoltDataIDList.add(7);
    }

    /// <summary>
    /// 相电压偏差参数DataID
    /// </summary>
    private static List<Integer> phaseVoltDiffDataIDList = new ArrayList<Integer>();
    static{
        phaseVoltDiffDataIDList.add(94);
        phaseVoltDiffDataIDList.add(95);
        phaseVoltDiffDataIDList.add(96);
    }

    /// <summary>
    /// 线电压偏差参数DataID
    /// </summary>
    private static List<Integer> lineVoltDiffDataIDList = new ArrayList<Integer>();
    static {
        lineVoltDiffDataIDList.add(100);
        lineVoltDiffDataIDList.add(101);
        lineVoltDiffDataIDList.add(102);
    }

    private static void resetVoltLimitOfDataId(double upLimit, double lowLimit, List<Gblimit> gbLimitDatas, int dataID)
    {

        Optional<Gblimit> gblimit = gbLimitDatas.stream().filter(x->x.getDataID()==dataID).findFirst();
        if (gblimit.isPresent())
        {
            Gblimit gbLimit = gblimit.get();
            //gbLimit.LimitHigh = CalcLineVoltLimit(upRate, ratedVoltClass, scale);
            gbLimit.setLimitHigh(upLimit);
            gbLimit.setLimitLow(lowLimit);
        }
    }

    private static double calcVoltLimit(double rate, double ratedVoltClass, int scale)
    {
        return ratedVoltClass * (1 + rate/100) / scale;
    }
    /// <summary>
    /// 获取原本的电压上下偏差比例，如果获取到了，按照原有的电压偏差比例来重算
    /// 否则不做处理
    /// </summary>
    /// <param name="ratedVoltClass"></param>
    /// <param name="upRate"></param>
    /// <param name="lowRate"></param>
    public Boolean getVoltageDeviationPercent(PqMonitorNode pqNode, double ratedVoltClass, int scale,  Double upRate,  Double lowRate)
    {
        //默认百分比为+7和-3
        Double upRatenew = 7.0;
        Double lowRatenew = -3.0;

        ratedVoltClass = ratedVoltClass / scale;
        Optional<PqReportParam> param = pqNode.getReportParamList().stream().filter(x->x.getDataID()==5).findFirst();
        if(!param.isPresent()){
            return false;
        }
        //修正值，把额定电压换算到和限值相同的数量级后计算电压偏差
        double correctiveVolt = ratedVoltClass;
        if(param.get().getLimitHigh() / ratedVoltClass > 1000) {
            correctiveVolt = ratedVoltClass * 1000;
        }
        else if(ratedVoltClass / param.get().getLimitLow() > 1000) {
            correctiveVolt = ratedVoltClass / 1000;
        }
        if (param.get().getDataID() == 5)
        {
            upRatenew = 100*(param.get().getLimitHigh() - correctiveVolt) / correctiveVolt;
            lowRatenew = 100*(param.get().getLimitLow() - correctiveVolt) / correctiveVolt;
            return true;
        }
        else
        {
            return false;
        }
    }

    static List<PqReportParam> convetGbLimits2ReportParams(List<Gblimit> gbLimitList)
    {
        List<PqReportParam> reportParams = new ArrayList<>();

        for (int i = 0; i < gbLimitList.size(); i++)
        {
            reportParams.add(convertGBLimit2ReportParam(gbLimitList.get(i)));
        }
        return reportParams;
    }
    private static PqReportParam convertGBLimit2ReportParam(Gblimit gbLimit)
    {
        PqReportParam reportParam = new PqReportParam();
        reportParam.setDataID(gbLimit.getDataID().longValue());
        reportParam.setLimitHigh(gbLimit.getLimitHigh());
        reportParam.setLimitLow(gbLimit.getLimitLow());
        return reportParam;
    }

    /// <summary>
    /// 修改监测节点的国标限值列表
    /// </summary>
    /// <param name="pqNodeID">监测点ID</param>
    /// <param name="reportParams">国标限值列表</param>
    /// <returns>返回设置结果</returns>
    /*ErrorMsg savePQReportParams(PqMonitorNode pqNode, List<PqReportParam> reportParams)
    {
        *//*String methodName = MethodInfo.GetCurrentMethod().Name;
        if (pqNode == null) {
            return new ErrorMsg(false);
        }
        List<PqReportParam> orgParams = new List<PqReportParam>(pqNode.ReportParamList);
        pqNode.ReportParamList = reportParams;
        ErrorMsg saveMsg = NodeWriter.WriteSysNode(pqNode);
        if (!saveMsg.IsSuccess) {
            pqNode.ReportParamList = orgParams;
        }*//*
        return null;
    }*/
    public static double getBaseCapacity(int webVoltID)
    {
        double baseCapacity = 100;//默认值100
        if (webVoltID <= 2 || webVoltID == 7) {
            baseCapacity = 10;
        }
        else if ((webVoltID >= 8 && webVoltID <= 9) || webVoltID == 3) {
            baseCapacity = 100;
        }
        else if ((webVoltID >= 4 && webVoltID <= 5) || webVoltID == 10) {
            baseCapacity = 250;
        }
        else if (webVoltID == 11) {
            baseCapacity = 500;
        }
        else if (webVoltID == 6) {
            baseCapacity = 750;
        } else if (webVoltID >= 12) {
            baseCapacity = 2000;
        }
        return baseCapacity;
    }
}

package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;

import java.util.*;

/**
 * <AUTHOR>
 * @Description: 监测对象类型分组
 * @date 2020/10/30 14:17
 */

public enum ObjectTypeGroup {
    //监测对象类型分组

    ObjectTypeGroup1(1,"铁路牵引站",Arrays.asList(1300)),
    ObjectTypeGroup3(3,"风电场",Arrays.asList(1401)),
    ObjectTypeGroup4(4,"光伏电站",Arrays.asList(1402)),
    ObjectTypeGroup11(11,"储能站",Arrays.asList(1404)),
    ObjectTypeGroup12(12,"充电站",Arrays.asList(1405)),
    ObjectTypeGroup5(5,"其他非线性组合",Arrays.asList(2301,2303,2304,2305,2306,2308,2309,2312,2315)),
    ObjectTypeGroup6(6,"敏感重要高危用户",Arrays.asList(2401,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2417,2418,2419,2420,2421)),
    ObjectTypeGroup2(2,"换流站",Arrays.asList(1201,1202,1203)),
    ObjectTypeGroup7(7,"变电站",Arrays.asList(2101,2102,2103,2104)),
    ObjectTypeGroup8(8,"跨省关口",Arrays.asList(1100)),
    ObjectTypeGroup9(9,"其他",Arrays.asList(0,2200,2411,1403,1501,1502,2501)),
    ;

    private int id;
    private String name;
    private List<Integer> objectTypeList;

    ObjectTypeGroup(int id, String name, List<Integer> objectTypeList) {
        this.id = id;
        this.name = name;
        this.objectTypeList = objectTypeList;
    }

    /**
     * 根据监测对象类型得到分组
     * @param objectTypeId 监测对象类型id
     * @return
     */
    public static ObjectTypeGroup getObjectTypeGroup(int objectTypeId){
        for (ObjectTypeGroup objectTypeGroup : values()) {
            if (objectTypeGroup.objectTypeList.contains(objectTypeId)) {
                return objectTypeGroup;
            }
        }
        throw new CommonManagerException("未找到该监测对象类型分组");
    }

    /**
     *根据监测对象组id得到监测对象类型id列表
     * @param objectTypGroupeId 监测对象组id
     * @return
     */
    public static List<Integer> getObjectTypeIdList(int objectTypGroupeId){
        for (ObjectTypeGroup objectTypeGroup : values()) {
            if (objectTypeGroup.id==objectTypGroupeId) {
                return objectTypeGroup.objectTypeList;
            }
        }
        throw new CommonManagerException("没有该监测对象类型分组");
    }

    public static List<Map<String,Object>> toList() {
        List<Map<String,Object>> list = new ArrayList();
        for (ObjectTypeGroup objectTypeGroup : values()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("id", objectTypeGroup.id);
            map.put("name", objectTypeGroup.name);
            map.put("objectTypeList", objectTypeGroup.objectTypeList);
            list.add(map);
        }
        return list;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}

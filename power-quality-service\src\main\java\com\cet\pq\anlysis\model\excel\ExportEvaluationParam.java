package com.cet.pq.anlysis.model.excel;

import com.cet.pq.common.model.excel.ExportParameter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 符合性评价指标导出
 * <AUTHOR>
 * @date 2021/3/30 15:50
 */
@Data
public class ExportEvaluationParam {
    @ApiModelProperty(value="开始时间",name="startTime",example="1722441600000")
    private Long startTime;
    @ApiModelProperty(value="结束时间",name="endTime",example="1725120000000")
    private Long endTime;
    @ApiModelProperty(value="节点label",name="modelLabel",example="provincecompany")
    private String modelLabel;
    @ApiModelProperty(value="节点id",name="modelId",example="1")
    private Long modelId;
    @ApiModelProperty(value="监测点类型",name="type",example="1")
    private Integer type;
    @ApiModelProperty(value="周期",name="aggregationCycle",example="14")
    private Integer aggregationCycle;
    @ApiModelProperty(value="监测区域",name="monitorType",example="1")
    private Integer monitorType;

    private ExportParameter exportParameter;
}

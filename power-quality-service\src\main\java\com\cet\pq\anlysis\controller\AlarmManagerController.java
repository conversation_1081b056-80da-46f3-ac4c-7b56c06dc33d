package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.alarm.*;
import com.cet.pq.anlysis.model.common.AlarmParam;
import com.cet.pq.anlysis.model.common.BasePageParam;
import com.cet.pq.anlysis.model.common.BaseParam;
import com.cet.pq.anlysis.model.common.CommonParam;
import com.cet.pq.anlysis.model.excel.ExportAlarmParam;
import com.cet.pq.anlysis.model.excel.ExportAlarmVariationMessageParam;
import com.cet.pq.anlysis.model.excel.ExportBaseParam;
import com.cet.pq.anlysis.model.excel.ExportCommonParam;
import com.cet.pq.anlysis.service.AlarmManagerService;
import com.cet.pq.common.model.PageResult;
import com.cet.pq.common.model.Result;
import com.cet.pq.inventoryservice.model.syntheticalalarm.SyntheticalAlarmParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName AlarmManagerController
 * @Description 报表管理 - 告警管理
 * @Date 2020/10/9
 */
@Api(value = "AlarmManagerController", tags = {"稳态指标 - 告警统计"})
@RestController
@RequestMapping(value = "/pq/v1/report/alarm")
public class AlarmManagerController {

    @Autowired
    private AlarmManagerService alarmManagerService;

    @ApiOperation(value = "告警总览-主配网")
    @PostMapping(value = "/alarmTrendTotal", produces = "application/json")
    public Result<List<AlarmTrendTotal>> getAlarmTrendTotal(
            @RequestBody @Valid @ApiParam(name = "alarmParam", value = "参数", required = true) AlarmParam alarmParam) {
        List<AlarmTrendTotal> alarmTrendTotals = alarmManagerService.getAlarmTrendTotal(alarmParam);
        return Result.success(alarmTrendTotals);
    }

    @ApiOperation(value = "告警总览-各单位指标告警总览-主配网")
    @PostMapping(value = "/unitAlarmTotal", produces = "application/json")
    public Result<List<UnitAlarmTotal>> getUnitAlarmTotal(
            @RequestBody @Valid @ApiParam(name = "alarmParam", value = "参数", required = true) AlarmParam alarmParam) {
        List<UnitAlarmTotal> unitAlarmTotals = alarmManagerService.getUnitAlarmTotal(alarmParam);
        return Result.success(unitAlarmTotals);
    }

    @ApiOperation(value = "导出告警总览 - 主配网")
    @PostMapping(value = "/exportAlarmTotal")
    public void exportAlarmTotal(@RequestBody ExportAlarmParam alarmOverviewParam, HttpServletResponse response) throws IOException {
        alarmManagerService.exportAlarmTotal(alarmOverviewParam, response);
    }

    @ApiOperation(value = "告警监测点明细-主配网")
    @PostMapping(value = "/alarmMonitorLineDetails", produces = "application/json")
    public PageResult<List<AlarmMonitorLine>> getAlarmMonitorLineDetails(
            @RequestBody @ApiParam(name = "baseParam", value = "参数", required = true) BasePageParam basePageParam) {
        return alarmManagerService.getAlarmMonitorLineDetails(basePageParam);
    }

    @ApiOperation(value = "导出告警监测点明细-主配网")
    @PostMapping(value = "/exportAlarmMonitorLineDetails")
    public void exportAlarmMonitorLineDetails(@RequestBody ExportBaseParam exportBaseParam, HttpServletResponse response) {
        alarmManagerService.exportAlarmMonitorLineDetails(exportBaseParam, response);
    }

    @ApiOperation(value = "告警管理 - 电站告警统计")
    @PostMapping(value = "/stationAlarms", produces = "application/json")
    public PageResult<List<StationAlarmTotal>> getStationAlarmTotal(
            @RequestBody @Valid @ApiParam(name = "alarmParam", value = "参数", required = true) AlarmParam alarmParam) {
        return alarmManagerService.getStationAlarmTotal(alarmParam);
    }

    @ApiOperation(value = "告警管理 - 告警统计表")
    @PostMapping(value = "/alarmsStatics", produces = "application/json")
    public PageResult<List<AlarmTotal>> getAlarmsStatics(
            @RequestBody @Valid @ApiParam(name = "commonParam", value = "参数", required = true)
                    BasePageParam commonParam) {
        return alarmManagerService.getAlarmsStatics(commonParam);
    }

    @ApiOperation(value = "告警管理 - 告警变电站明细")
    @PostMapping(value = "/alarmStationDetails", produces = "application/json")
    public PageResult<List<AlarmStation>> getAlarmStationDetails(
            @RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true) BasePageParam baseParam) {
        return alarmManagerService.getAlarmStationDetails(baseParam);
    }

    @ApiOperation(value = "告警日志 - 暂态短信告警")
    @PostMapping(value = "/alarmVariationMessage/{type}", produces = "application/json")
    public PageResult<List<AlarmVariationMessage>> getAlarmVariationMessage(
            @PathVariable(value = "type") @ApiParam(name = "type", value = "类型, 0单次告警 1统计告警", required = true) Integer type,
            @RequestBody @Valid @ApiParam(name = "baseParam", value = "参数", required = true) BasePageParam basePageParam) {
        return alarmManagerService.getAlarmVariationMessage(type, basePageParam);
    }

    @ApiOperation(value = "导出告警管理 - 告警统计表")
    @PostMapping(value = "/exportAlarmsStatics")
    public void exportAlarmsStatics(@RequestBody ExportCommonParam exportCommonParam, HttpServletResponse response) {
        alarmManagerService.exportAlarmsStatics(exportCommonParam, response);
    }

    @ApiOperation(value = "导出告警管理 - 告警变电站明细")
    @PostMapping(value = "/exportAlarmStationDetails")
    public void exportAlarmStationDetails(@RequestBody ExportBaseParam exportBaseParam, HttpServletResponse response) {
        alarmManagerService.exportAlarmStationDetails(exportBaseParam, response);
    }

    @ApiOperation(value = "综合告警页面")
    @PostMapping(value = "/syntheticalAlarmMonitorLine", produces = "application/json")
    public PageResult<Object> getAlarmMonitorLineDetailsSynthetical(@RequestBody SyntheticalAlarmParams syntheticalAlarmParams
    ) {
        return alarmManagerService.getSyntheticalAlarmMonitorLine(syntheticalAlarmParams);
    }

    @ApiOperation(value = "综合告警页面-总览")
    @PostMapping(value = "/syntheticalAlarmMonitorCount", produces = "application/json")
    public Result<Map<String,Object>> getAlarmMonitorLineCountSynthetical(@RequestBody SyntheticalAlarmParams syntheticalAlarmParams
    ) {
        return Result.success(alarmManagerService.getSyntheticalAlarmMonitorCount(syntheticalAlarmParams));
    }

    @ApiOperation(value = "导出综合告警")
    @PostMapping(value = "/exportSyntheticalAlarmMonitorLine")
    public void exportSyntheticalAlarmMonitorLine(
            @RequestBody SyntheticalAlarmParams syntheticalAlarmParams
            , HttpServletResponse response) throws IOException {
        alarmManagerService.exportEventReasonAnalysis(response,syntheticalAlarmParams);
    }

}

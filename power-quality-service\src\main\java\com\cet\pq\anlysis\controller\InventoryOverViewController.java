package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.linestatistics.InventoryDimensionCount;
import com.cet.pq.anlysis.model.linestatistics.LineIncreaseCount;
import com.cet.pq.anlysis.model.linestatistics.LineStatusCount;
import com.cet.pq.anlysis.model.monitordatastatistics.DataLocation;
import com.cet.pq.anlysis.model.substationstatics.SubstationDistribution;
import com.cet.pq.anlysis.model.substationstatics.SubstationStatistics;
import com.cet.pq.anlysis.model.terminalstatistics.PqTerminalIncreaseCount;
import com.cet.pq.anlysis.model.terminalstatistics.PqterminalStatusCount;
import com.cet.pq.anlysis.service.InventoryOverViewService;
import com.cet.pq.anlysis.service.SubstationService;
import com.cet.pq.anlysis.service.TerminalCountService;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.ResultWithTotal;
import com.cet.pq.inventoryservice.model.monitor.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 台账全景概览统计接口
 * @Date 2020-08-21
 */
@RestController
@RequestMapping("/pq/v1")
@Api(value = "台账全景概览统计接口", tags = "全景概览 - 台账统计接口")
public class InventoryOverViewController {

    @Autowired
    private InventoryOverViewService bookStatisticsService;

    @Autowired
    private TerminalCountService terminalCountService;

    @Autowired
    private SubstationService substationService;

    @ApiOperation(value = "监测点台账状态统计")
    @GetMapping("/line/status/statistics")
    public ResultWithTotal<List<LineStatusCount>> getLineStatusCount() {
        List<LineStatusCount> lineStatusCountList = bookStatisticsService.getLineStatusCount();
        //变电站级统计
        List<LineStatusCount> lineStatusCountListBySubstation = bookStatisticsService.getLineStatusCountBySubstation();
        lineStatusCountList.addAll(lineStatusCountListBySubstation);
        return ResultWithTotal.success(lineStatusCountList, lineStatusCountList.size());
    }

    @ApiOperation(value = "终端状态数据统计")
    @GetMapping("/pqterminal/status/statistics")
    public ResultWithTotal<List<PqterminalStatusCount>> getPQTStatusCount() {
        List<PqterminalStatusCount> pqterminalStatusCountList = terminalCountService.getPqTerminalStatusCount();
        //变电站级数据
        List<PqterminalStatusCount> pqterminalStatusCountListBySubstation = terminalCountService.getPqTerminalStatusCountBySubstation();
        pqterminalStatusCountList.addAll(pqterminalStatusCountListBySubstation);
        return ResultWithTotal.success(pqterminalStatusCountList, pqterminalStatusCountList.size());
    }

    @ApiOperation(value = "变电站-监测点分布数据")
    @GetMapping("/substation-monitor/distribution")
    public ResultWithTotal<List<SubstationDistribution>> getSMDistribution() {
        List<SubstationDistribution> substationLineDistributionList = substationService.getSMDistribution();
        return ResultWithTotal.success(substationLineDistributionList, substationLineDistributionList.size());
    }

    @ApiOperation(value = "变电站数据统计")
    @GetMapping("/substation/statistics")
    public ResultWithTotal<List<SubstationStatistics>> getSubstationCount() {
        List<SubstationStatistics> substationStatisticsList = substationService.getSubstationCount();
        return ResultWithTotal.success(substationStatisticsList, substationStatisticsList.size());
    }

    @ApiOperation(value = "获取数据经纬度")
    @GetMapping("/monitordata/location")
    public ResultWithTotal<List<DataLocation>> getDataLocation(String modelLabel, String idList){
    	List<DataLocation> dataLocationList = bookStatisticsService.getDataLocation(modelLabel,idList);
    	return ResultWithTotal.success(dataLocationList, dataLocationList.size());
    }

    @ApiOperation(value = "监测点台账不同维度数据统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dimensionId", value = "查询维度,1：按单位统计2：按电压等级统计3：按监测对象类型统计", required = true, dataType = "int")})
    @GetMapping("/line/statistics")
    public ResultWithTotal<List<InventoryDimensionCount>> getMonitorCount(@RequestParam Integer dimensionId) {
        // 根据userId查询用户权限下的省级单位id
        List<InventoryDimensionCount> dimensionCount = bookStatisticsService.getMonitorCount(dimensionId);
        return ResultWithTotal.success(dimensionCount, dimensionCount.size());
    }

    @ApiOperation(value = "监测点近5年新增数量统计")
    @ApiImplicitParams({@ApiImplicitParam(name = "time", value = "查询时间", required = true, dataType = "Long")})
    @GetMapping("/line/newincreased")
    public ResultWithTotal<List<LineIncreaseCount>> getMonitorCountByDate(@RequestParam Long time) throws ParseException {
        List<LineIncreaseCount> lineIncreaseCountList = bookStatisticsService.getMonitorIncreaseByDate(time);
        return ResultWithTotal.success(lineIncreaseCountList, lineIncreaseCountList.size());
    }

    @ApiOperation(value = "不同维度统计终端数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dimensionId", value = "查询维度,1：按单位统计，2：按厂家统计", required = true, dataType = "String")})
    @GetMapping("/pqterminal/statistics")
    public ResultWithTotal<List<InventoryDimensionCount>> getPqTerminalCount(@RequestParam Integer dimensionId) {
        List<InventoryDimensionCount> terminalDimensiionCount = terminalCountService.getPqTerminalCount(dimensionId);
        return ResultWithTotal.success(terminalDimensiionCount, terminalDimensiionCount.size());
    }

    @ApiOperation(value = "按照厂家统计近5年终端新增数量")
    @ApiImplicitParams({@ApiImplicitParam(name = "time", value = "查询日期时间", required = true, dataType = "Long")})
    @GetMapping("/pqterminal/newincreased")
    public ResultWithTotal<List<PqTerminalIncreaseCount>> getPQTAddCountBydate(@RequestParam Long time) throws ParseException {
        List<PqTerminalIncreaseCount> PqTerminalIncreaseCountList = terminalCountService.getPQTAddCountBydate(time);
        return ResultWithTotal.success(PqTerminalIncreaseCountList, PqTerminalIncreaseCountList.size());
    }

    @ApiOperation(value = "监测点/监测终端按单位统计")
    @GetMapping("/overView/byCompany")
    public Result monitorCountByCompany() {
        MonitorCount<List<MonitorCountByCompany<NumAndRate>>> listMonitorCount = bookStatisticsService.monitorCountByCompany();
        return Result.success(listMonitorCount);
    }

    @ApiOperation(value = "监测点按电压等级分类统计")
    @GetMapping("/overView/byVoltClass")
    public Result monitorCountByVoltClass() {
        MonitorCount<List<CommonData>> listMonitorCount = bookStatisticsService.monitorCountByVoltClass();
        return Result.success(listMonitorCount);
    }

    @ApiOperation(value = "主配网-近五年新增统计")
    @GetMapping("/overView/newCreate")
    public Result monitorCountByNewCreate() {
        MonitorCount<List<MonitorCountByTime<List<MonitorCountByCompany<Long>>>>> listMonitorCount = bookStatisticsService.monitorCountByTime();
        return Result.success(listMonitorCount);
    }

    @ApiOperation(value = "监测点按监测对象类型分类统计")
    @GetMapping("/overView/lineByObject")
    public Result monitorCountByObject() {
        MonitorCount<List<CommonData>> listMonitorCount = bookStatisticsService.monitorCountByObjectType();
        return Result.success(listMonitorCount);
    }

    @ApiOperation(value = "主网-按厂家分类统计")
    @GetMapping("/overView/byVendor")
    public Result pqTerminalCountByVendor() {
        MonitorCount<List<CommonData>> pqTerminalCount = bookStatisticsService.pqTerminalCountByVendor();
        return Result.success(pqTerminalCount);
    }

    @ApiOperation(value = "近五年新增监测终端统计")
    @GetMapping("/overView/terminalNewCreate")
    public Result pqTerminalCountByTime() {
        MonitorCount<List<MonitorCountByTime<List<MonitorCountByCompany<List<CommonData>>>>>> pqTerminalCount = bookStatisticsService.pqTerminalCountByTime();
        return Result.success(pqTerminalCount);
    }
}

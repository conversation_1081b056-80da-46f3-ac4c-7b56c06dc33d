package com.cet.pq.anlysis.controller;

import com.cet.pq.anlysis.model.alarm.AddAlarmTargetParam;
import com.cet.pq.anlysis.model.alarm.AlarmVariationMessage;
import com.cet.pq.anlysis.model.alarm.AlarmVariationTarget;
import com.cet.pq.anlysis.model.alarm.GetAlarmVariationMessageParam;
import com.cet.pq.anlysis.model.alarmobj.*;
import com.cet.pq.anlysis.model.excel.ExportAlarmParam;
import com.cet.pq.anlysis.model.excel.ExportAlarmVariationMessageParam;
import com.cet.pq.anlysis.service.AlarmObjectService;
import com.cet.pq.common.constant.ErrorCode;
import com.cet.pq.common.model.PageResult;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.utils.FileUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/22
 * @description 告警对象
 */
@Api(value = "AlarmObjectController", tags = {"告警日志 - 告警对象管理"})
@RestController
@RequestMapping(value = "/pq/v1/alarm")
public class AlarmObjectController {

    @Autowired
    private AlarmObjectService alarmObjectService;

    private List<String> fileTypes = Lists.newArrayList("jpg", "pdf","docx", "doc");
    /**
     * 新增告警对象管理信息
     *
     * @param saveAlarmParams
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 新增/编辑告警对象管理信息")
    @PostMapping("/addAlarmObj")
    public Result addAlarmObj(@RequestBody @Valid @ApiParam(name = "saveAlarmParams", value = "参数", required = true)
                                      SaveAlarmParams saveAlarmParams) {
        return alarmObjectService.addAlarmObj(saveAlarmParams);
    }

    /**
     * 删除告警对象管理信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 删除告警对象管理信息")
    @PostMapping("/deleteAlarmObj")
    public Result deleteAlarmObj(@RequestParam @Valid @ApiParam(name = "id", required = true)
                                         Long id) {
        return alarmObjectService.deleteAlarmObj(id);
    }

    /**
     * 更新反馈状态
     *
     * @param updateFeedbackParams
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 更新反馈状态")
    @PostMapping("/updateFeedback")
    public Result updateFeedback(@RequestBody @Valid @ApiParam UpdateFeedbackParams updateFeedbackParams) {
        return alarmObjectService.updateFeedback(updateFeedbackParams);
    }

    /**
     * 更新审核状态
     *
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 更新审核状态")
    @PostMapping("/updateAuditStatus")
    public Result updateAuditStatus(@RequestBody @Valid @ApiParam UpdateAuditStatusParams updateAuditStatus) {
        return alarmObjectService.updateAuditStatus(updateAuditStatus);
    }

    /**
     * 获取告警对象台账
     *
     * @param alarmObjQueryParams
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 获取告警对象台账")
    @PostMapping("/getAlarmObjList")
    public PageResult getAlarmObjList(@RequestBody @Valid @ApiParam AlarmObjQueryParams alarmObjQueryParams) {
        return alarmObjectService.getAlarmObjList(alarmObjQueryParams);
    }

    /**
     * 导出告警对象台账
     *
     * @param response
     */
    @ApiOperation("告警闭环管理 - 导出告警对象台账")
    @PostMapping("/exportAlarmObj")
    public void exportAlarmObj(@RequestBody @ApiParam AlarmObjQueryParams alarmObjQueryParams, HttpServletResponse response) throws IOException {
        alarmObjectService.exportAlarmObj(response, alarmObjQueryParams);
    }

    /**
     * 获取反馈材料列表
     *
     * @param alarmObjId
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 获取反馈材料列表")
    @GetMapping("/getFeedbackFileList")
    public Result<List<Map<String, Object>>> getFeedbackFileList(@RequestParam("alarmObjId") Long alarmObjId) {
        List<Map<String, Object>> result = alarmObjectService.getFeedbackFileList(alarmObjId);
        return Result.success(result);
    }

    /**
     * 上传反馈材料
     *
     * @param file
     * @param alarmObjId
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 上传反馈材料")
    @PostMapping("/updateFeedbackFile")
    public Result updateFeedbackFile(@RequestParam MultipartFile file,
                                     @RequestParam("alarmObjId") Long alarmObjId) {
        //检查文件类型和大小
        if (!FileUtil.checkFile(file, fileTypes)) {
            return Result.error(ErrorCode.ERROR_FILE);
        }
        return alarmObjectService.updateFeedbackFile(file, alarmObjId);
    }

    /**
     * 删除反馈材料
     *
     * @param deleteFeedbackFileParams
     * @return
     */
    @ApiOperation(value = "告警闭环管理 - 删除反馈材料")
    @PostMapping(value = "/deleteFeedbackFile", produces = "application/json")
    public Result deleteFeedbackFile(@RequestBody @Valid @ApiParam(name = "deleteFeedbackFileParams", value = "参数", required = true)
                                                 DeleteFeedbackFileParams deleteFeedbackFileParams) throws IOException {
        alarmObjectService.deleteFeedbackFile(deleteFeedbackFileParams);
        return Result.success();
    }

    /**
     * 下载反馈材料
     *
     * @param response
     * @return
     * @throws IOException
     */
    @ApiOperation(value = "告警闭环管理 - 下载反馈材料")
    @GetMapping("/downloadFeedbackFile")
    public void downloadFeedbackFile(HttpServletResponse response,
                                       @RequestParam("fileName") String fileName) throws IOException {
        alarmObjectService.downloadFeedbackFile(response, fileName);
    }


    @ApiOperation(value = "责任人管理 - 新增/编辑责任人")
    @PostMapping(value = "/addAlarmVariationTarget")
    public Result<Object> addAlarmVariationTarget(@RequestBody @ApiParam AddAlarmTargetParam addParam) {
        return alarmObjectService.addAlarmVariationTarget(addParam);
    }

    @ApiOperation(value = "责任人管理 - 查询责任人")
    @PostMapping(value = "/queryAlarmVariationTarget")
    public PageResult queryAlarmVariationTarget(
            @RequestParam @ApiParam Integer pageNum, @RequestParam @ApiParam Integer pageSize, @RequestParam(defaultValue = "") @ApiParam String name) {
        return alarmObjectService.queryAlarmVariationTarget(pageNum,pageSize,name);
    }

    @ApiOperation(value = "责任人管理 - 删除责任人")
    @PostMapping(value = "/deleteAlarmVariationTarget")
    public Result deleteAlarmVariationTarget(@RequestParam Long id) {
        alarmObjectService.deleteAlarmVariationTarget(id);
        return Result.success();
    }

    @ApiOperation(value = "责任人管理 - 导出责任人列表")
    @GetMapping(value = "/exportAlarmVariationTarget")
    public void exportAlarmVariationTarget(@RequestParam(defaultValue = "") @ApiParam String name,HttpServletResponse response) {
        alarmObjectService.exportAlarmVariationTarget(name,response);
    }

    @ApiOperation(value = "告警日志 - 查询告警日志")
    @PostMapping(value = "/getAlarmMessage")
    public PageResult<List<AlarmVariationMessage>> getAlarmMessage(
            @RequestBody GetAlarmVariationMessageParam queryParam) {
        return alarmObjectService.getAlarmMessage(queryParam);
    }

    @ApiOperation(value = "告警日志 - 导出告警日志")
    @PostMapping(value = "/exportAlarmVariationMessage")
    public void exportAlarmVariationMessage(@RequestBody GetAlarmVariationMessageParam queryParam, HttpServletResponse response) {
        alarmObjectService.exportAlarmVariationMessage(queryParam, response);
    }

    /**
     * 删除告警对象管理信息
     *
     * @return
     */
    @ApiOperation(value = "告警日志 - 删除告警日志")
    @PostMapping("/deleteAlarmMessage")
    public Result deleteAlarmMessage(@RequestBody DeleteAlarmMessageParams deleteAlarmMessageParams) {
        alarmObjectService.deleteAlarmMessage(deleteAlarmMessageParams);
        return Result.success();
    }

}

package com.cet.pq.anlysis.common.enums.offlinetest;

import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/6/26 10:36
 * @description
 **/
@NoArgsConstructor
public enum IndustryEnum {
    metallurgy(1, "冶金"),
    machinery(2, "机械"),
    chemicals(3, "化工"),
    electricity(4, "电力"),
    newenergy(5, "新能源"),
    transportation(6, "交通"),
    publicutilities(7, "公共事业"),
    powerplants(8, "电厂"),
    commerce(9, "商业"),
    municipal(10, "市政"),
    civil(11, "民用"),
    electronics(12, "电子"),
    communication(13, "通信"),
    shipbuilding(14, "造船"),
    others(15, "其他"),;

    private Integer id;
    private String text;

    IndustryEnum(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}

package com.cet.pq.common.model.offlinetest;

import java.util.List;

import com.cet.pq.common.model.realtime.PecDeviceExtend;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 离线测试台账
 * <AUTHOR>
 * @created 2020-09-15
 */
@Data
@NoArgsConstructor
public class OffLineTest {
	
	private String modelLabel = "offlinetest";
	
	private Long id;
	
	//创建时间
	private Long createtime;   
	//是否谐波波源
	private Boolean isharmsource;
	//测试点名称
	private String name;
	//最小短路容量
	private Double shortcircuitcapacity;
	//供电设备容量
	private Double supplyequipmentcapacity;
	//测点仪表型号
	private String testdevicemodel;
	//测试对象名称
	private String testobjectname;
	//测试对象类型
	private Integer testobjecttype;
	//测试队形模型
	private String testobjectLabel;
	//测试对象id
	private Long testobjectid;
	//用户协议容量
	private Double userprotocolcapacity;
	//电压等级
	private Integer voltclass;
	//接线方式
	private Integer wiredtype;
	//PT变比
	private String ptratio;
	//CT变比
	private String ctratio;
	//上次普测时间
	private Long lasttesttime;
	//普测周期
	private Integer testcycle;
	//是否需要提醒
	private Boolean iswran;
	@JsonProperty("upvoltdeviation")
	private Double upVoltDeviation;
	@JsonProperty("lowvoltdeviation")
	private Double lowVoltDeviation;

	private List<PecDeviceExtend> pecdeviceextend_model;

}

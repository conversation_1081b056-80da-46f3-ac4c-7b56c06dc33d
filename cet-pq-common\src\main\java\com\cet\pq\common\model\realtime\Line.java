package com.cet.pq.common.model.realtime;

import com.cet.pq.common.annotation.FieldAnnotation;
import com.cet.pq.common.enums.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * @Description 监测点
 * <AUTHOR>
 * @Date 2020/6/30 14:30
 */
@Data
@NoArgsConstructor
public class Line {

    private String modelLabel = "line";

    private Long id;
    /**
     * 电压等级
     */
    @FieldAnnotation(name = "电压等级", type = VoltageLevelEnum.class, method = "getVoltageLevel")
    @JsonProperty("voltclass")
    private Integer voltClass;
    /**
     * 协议容量
     */
    @FieldAnnotation(name = "用户协议容量")
    @JsonProperty("userprotocolcapacity")
    private Double userProtocolCapacity;
    /**
     * 供电容量
     */
    @FieldAnnotation(name = "供电设备容量")
    @JsonProperty("supplyequipmentcapacity")
    private Double supplyEquipmentCapacity;

    /**
     * 短路容量
     */
    @FieldAnnotation(name = "最小短路容量")
    @JsonProperty("shortcircuitcapacity")
    private Double shortCircuitCapacity;
    /**
     * 负荷类型
     */
    @FieldAnnotation(name = "监测对象类型", type = ObjectTypeEnum.class, method = "getNameByValue")
    @JsonProperty("objecttype")
    private Integer objectType;
    /**
     * 监测对象标签
     */
    @JsonProperty("object_label")
    private String objectLabel;
    /**
     * 监测对象id
     */
    @JsonProperty("object_id")
    private Long objectId;
    /**
     * 监测对象名称
     */
    @JsonProperty("object_insname")
    @FieldAnnotation(name = "监测对象名称")
    private String objectInsname;
    /**
     * 线路名称
     */
    @FieldAnnotation(name = "监测点名称")
    private String name;
    /**
     * 电网编号
     */
    @FieldAnnotation(name = "监测点编号")
    private String code;

    @JsonProperty("pqterminal_id")
    private Long pqterminalId;
    /**
     * 设备扩展
     */
    @JsonProperty("pecdeviceextend_model")
    private List<PecDeviceExtend> pecDeviceExtendModel;
    /**
     * 是否上送
     */
    @FieldAnnotation(name = "是否上送", type = Boolean.class)
    @JsonProperty("isupload")
    private Boolean isUpload;
    /**
     * 监测点状态
     */
    @FieldAnnotation(name = "监测点状态", type = MonitorStatusEnum.class, method = "getMonitorStatusNameNoThrows")
    private Integer status;
    /**
     * 电压上偏
     */
    @FieldAnnotation(name = "电压上偏差")
    @JsonProperty("upvoltdeviation")
    private Double upVoltDeviation;
    /**
     * 更新时间
     */
    @JsonProperty("updatetime")
    private Long updateTime;

    @FieldAnnotation(name = "监测点标签", type = MonitorTagEnums.class, method = "getMeanByCodeNoThrows")
    @JsonProperty("monitortag")
    private String monitorTag;

    /**
     * 监测点类型
     */
    @FieldAnnotation(name = "监测点类型", type = PQMonitorTypeEnum.class, method = "getNameByValueNoThrows")
    @JsonProperty("pqmonitortype")
    private Integer pqMonitorType;
    /**
     * 中性点接
     */
    @JsonProperty("neutralpointgroundtype")
    @FieldAnnotation(name = "中性点接地方式", type = NeutralPointGroundTypeEnum.class, method = "getNameByValueNoThrows")
    private Integer neutralPointGroundType;
    /**
     * 电压下偏
     */
    @FieldAnnotation(name = "电压下偏差")
    @JsonProperty("lowvoltdeviation")
    private Double lowVoltDeviation;

    /**
     * 完整率参数
     */
    @FieldAnnotation(name = "完整率方案")
    @JsonProperty("integrityparaset_id")
    private Long integrityParaSetId;
    /**
     * 电压互感器类型
     */
    @FieldAnnotation(name = "电压互感器类型", type = VoltageTransformertypeEnum.class, method = "getVoltageTransformertypeNameNoThrows")
    @JsonProperty("voltagetransformertype")
    private Integer voltageTransformerType;

    /**
     * 校正功能
     */
    @FieldAnnotation(name = "校正功能", type = CorrectiveFunctionEnum.class, method = "getCorrectiveFunctionEnumKeyByValue")
    @JsonProperty("correctivefunction")
    private Integer correctiveFunction;

    /**
     * 母线编号
     */
    @FieldAnnotation(name = "母线编号")
    @JsonProperty("busbarsectioncode")
    private String busBarSectionCode;

    /**
     * 是否发电用户
     */
    @JsonProperty("ifgenerationuser")
    private Boolean ifGenerationUser;

    /**
     * 主配网类型
     */
    @FieldAnnotation(name = "主配网类型")
    @JsonProperty("monitortype")
    private Integer monitorType;
    /**
     * 监测类别
     */
    @JsonProperty("monitorsort")
    private Integer monitorSort;
    /**
     * 监测类型
     */
    @JsonProperty("monitormode")
    private Integer monitorMode;

    @FieldAnnotation(name = "是否分布式光伏监测", type = Boolean.class)
    @JsonProperty("ifpvmonitor")
    private Boolean ifPvMonitor;

    /**
     * 分布式变压器名称
     */
    @FieldAnnotation(name = "所属配变名称")
    @JsonProperty("distributiontransformername")
    private String distributionTransformerName;


    @FieldAnnotation(name = "是否专项供电", type = Boolean.class)
    @JsonProperty("isspeciallinepower")
    private Boolean isSpecialLinePower;

    /**
     * 以下为国重新增字段
     */
    @ApiModelProperty("测点类型")
    @JsonProperty("pointtype")
    private Integer pointType;

    @ApiModelProperty("所属线路名称")
    @JsonProperty("linename")
    private String lineName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Line line = (Line) o;
        return  Objects.equals(code, line.code) &&
                Objects.equals(name, line.name) &&
                Objects.equals(voltClass, line.voltClass) &&
                Objects.equals(objectType, line.objectType) &&
                Objects.equals(objectInsname, line.objectInsname) &&
                Objects.equals(objectId, line.objectId) &&
                Objects.equals(status, line.status) &&
                Objects.equals(shortCircuitCapacity, line.shortCircuitCapacity) &&
                Objects.equals(userProtocolCapacity, line.userProtocolCapacity) &&
                Objects.equals(supplyEquipmentCapacity, line.supplyEquipmentCapacity) &&
                Objects.equals(isUpload, line.isUpload) &&
                Objects.equals(voltageTransformerType, line.voltageTransformerType) &&
                Objects.equals(neutralPointGroundType, line.neutralPointGroundType) &&
                Objects.equals(upVoltDeviation, line.upVoltDeviation) &&
                Objects.equals(lowVoltDeviation, line.lowVoltDeviation) &&
                Objects.equals(integrityParaSetId, line.integrityParaSetId) &&
                Objects.equals(pqMonitorType, line.pqMonitorType) &&
                Objects.equals(pecDeviceExtendModel, line.pecDeviceExtendModel) &&
                Objects.equals(monitorTag, line.monitorTag) &&
                Objects.equals(ifPvMonitor, line.ifPvMonitor) &&
                Objects.equals(busBarSectionCode, line.busBarSectionCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(voltClass, userProtocolCapacity, supplyEquipmentCapacity, shortCircuitCapacity, objectType,
                objectInsname, name, code, pecDeviceExtendModel, isUpload,
                status, upVoltDeviation, monitorTag, pqMonitorType, neutralPointGroundType,
                lowVoltDeviation, integrityParaSetId, voltageTransformerType, ifPvMonitor, objectId, busBarSectionCode);
    }
}

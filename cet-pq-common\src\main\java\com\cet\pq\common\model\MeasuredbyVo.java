package com.cet.pq.common.model;

import com.cet.pq.common.constant.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019年11月5日
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MeasuredbyVo implements Serializable {
	
	private static final long serialVersionUID = -6625762215655133864L;
	
	private String modelLabel = TableName.MEASURED_BY;
	
	private Long id;

	private Long monitoredid;

	private String monitoredlabel;

	private Long measuredby;

	public MeasuredbyVo(Long monitoredid, String monitoredlabel, Long measuredby) {
		this.monitoredid = monitoredid;
		this.monitoredlabel = monitoredlabel;
		this.measuredby = measuredby;
	}
	
	
	

}

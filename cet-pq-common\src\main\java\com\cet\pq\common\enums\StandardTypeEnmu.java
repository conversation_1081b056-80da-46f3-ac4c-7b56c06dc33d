package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description: 电能质量标准库标准类型
 * @date 2023/9/14
 */
public enum StandardTypeEnmu {

    Indicator(1,"指标类"),
    Test(2,"测试类"),
    Evaluation(3,"评估类"),
    System(4,"系统类"),
    Terminal(5,"终端类"),
    Economic(6,"经济类"),
    TechnicalSupervision(7,"技术监督类"),
    Others(8,"其他类"),
    Pv(9,"分布式光伏"),
    ;


    private Integer id;
    private String text;

    StandardTypeEnmu(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (StandardTypeEnmu enmu : StandardTypeEnmu.values()) {
            if (enmu.id.equals(id)) {
                return enmu.text;
            }
        }
        return StringUtils.EMPTY;
    }
}

package com.cet.pq.common.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName LogAspect
 * @Description 记录日志和时间
 * @Date 2020/11/16
 */
@Component
@Aspect
public class LogAspect {
    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);

    @Pointcut("execution(* com.cet.pq.anlysis.service..*.*(..)) || execution(* com.cet.pq.pqservice.service..*.*(..))" +
            " || execution(* com.cet.pq.inventoryservice.service..*.*(..)) ||  execution(* com.cet.pq.common.feign..*.*(..))" )
    public void logPointcut(){
    }

    @Around("logPointcut()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable{
        long start = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long end = System.currentTimeMillis();
            logger.debug("+++++around " + joinPoint + "Use time : " + (end - start) + " ms!");
            return result;
        } catch (NullPointerException e) {
            long end = System.currentTimeMillis();
            // 验证测试环境问题临时修改，验证完毕后还原
            logger.error("+++++around " + joinPoint + "Use time :" + (end - start) + " ms with exception : NullPointerException" + e.getMessage());
            throw new RuntimeException("未知异常");
        } catch (Throwable e) {
            long end = System.currentTimeMillis();
            logger.error("+++++around " + joinPoint + "Use time :" + (end - start) + " ms with exception : " + e.getMessage());
            throw e;
        }
    }
}

# PQ业务特殊规则



1，接口参数校验，在service层中需要对接口传参对象中的参数做非空校验，对于传参中包含list集合数据的参数必须做校验

2，权限层级校验，需要校验传参中的companyId和companyModel是否在用户所属区域下，通过以下方法校验

```
AuthUtils.checkAuth(companyId, companyModel, Boolean.TRUE);
```

3，导出功能使用 ExcelUtil.commonExport，传参包含

```
ExcelUtil.commonExport(response, exportParameter, "templates/告警事件分类统计导出模板.xlsx");
```

字段类型：

- response：HttpServletResponse 对象

- fileName：导出文件模版

- exportParameter：导出列表的数据集
  

4，涉及到时间转换的操作，**必须** 使用DateUtils封装的方法，时间转换工具类，时间转换类包含时间戳，请学习 src/main/java/com/cet/pq/pqcommonservice/utils/DateUtils.java

5，涉及到数据转换的操作，**必须** 使用数据转换工具ParseDataUtil，将Object类型转换成基础数据类型

- Object转换成Integer使用 parseInteger方法
- Object转换成Double使用 parseDouble方法
- Object转换成String使用 parseString方法
- Object转换成Boolean使用 parseBoolean方法
- Object转换成List使用 parseList方法
- Object转换成Map使用 parseMap方法

​	

6，涉及到json数据转换的操作时，**必须** 使用JsonTransferUtils工具类，涉及到下列场景

- jsonString转换成list使用 transferJsonString方法
- jsonString转换成Object使用 parseObject方法
- list转换成jsonString使用toJsonString
- Object转换成jsonString使用toJsonString

7，Result结果校验，通过 ParamUtils.checkResultGeneric(result);



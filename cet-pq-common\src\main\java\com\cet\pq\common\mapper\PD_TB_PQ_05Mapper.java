package com.cet.pq.common.mapper;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.ConditionBlock;
import com.cet.pq.common.model.PD_TB_PQ_05;
import com.cet.pq.common.time.DateUtils;
import com.cet.pq.common.utils.ParseDataUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 3.8数据库查询mapper
 * @date 2021/3/23 8:55
 */
@Mapper
@Component("pD_TB_PQ_05Mapper")
@ConditionalOnBean(name = "dataQueryAspect")
public interface PD_TB_PQ_05Mapper {

    String tableNamePre = "PD_TB_PQ_05_";

    @SelectProvider(type = DynamicDataDaoProvider.class, method = "getAggData")
    List<PD_TB_PQ_05> getPqQuantityAggData(@Param("expressions")List<ConditionBlock> expressions);

    /**
     * 根据模型服务参数动态生成sql
     */
    class DynamicDataDaoProvider {
        public String getAggData(@Param("expressions")List<ConditionBlock> expressions) {
            Long startTime = ParseDataUtil.parseLong(expressions.stream()
                    .filter(e-> "logtime".equals(e.getProp()))
                    .flatMap(e->objectToList(e.getLimit()).stream())
                    .min((o1,o2)->ParseDataUtil.parseLong(o1)>ParseDataUtil.parseLong(o2)?1:-1)
                    .get());
            int year = DateUtils.getYearByTimeStamp(startTime);
            if (CollectionUtils.isEmpty(expressions)){
                SQL sql = new SQL();
                sql.SELECT(" * ");
                sql.FROM(tableNamePre + year);

                return sql.toString();
            }

            Iterator<ConditionBlock> iterator = expressions.iterator();
            SQL sql = new SQL();
            sql.SELECT(" * ");
            while(iterator.hasNext()){
                ConditionBlock con = iterator.next();
                sql.WHERE(propConverter(con.getProp())+operatorToSign(con.getOperator())+objectToString(con.getProp(),con.getLimit()));
                if (iterator.hasNext()) {
                    sql.AND();
                }
            }
            return sql.toString();
        }


        /**
         * 对象转成列表
         * @param object
         * @return
         */
        private List<Object> objectToList(Object object){
            if (object instanceof List){
                return new ArrayList<Object>((List)object);
            }
            if (object == null){
                return Collections.emptyList();
            }
            return Collections.singletonList(object);
        }

        /**
         * 对象转sql可识别的字符串
         * @param prop
         * @param object
         * @return
         */
        private String objectToString(String prop, Object object){
            if (object instanceof List){
                List<Object> list = ParseDataUtil.parseList(object);
                StringBuffer preString = new StringBuffer("(");
                list.forEach(l->preString.append(l.toString()).append(","));
                preString.deleteCharAt(preString.length()-1);
                preString.append(")");
                return aggToTimeType(prop,preString.toString());
            }
            return aggToTimeType(prop,object.toString());
        }

        /**
         * 聚合周期转timetype
         * @param prop
         * @param result
         * @return
         */
        private String aggToTimeType(String prop,String result){
            if (ColumnName.AGGREGATIONCYCLE.equals(prop)){
                return result.replace("12", "1")
                        .replace("13", "2")
                        .replace("14", "3")
                        .replace("14", "4")
                        .replace("17", "5");
            }
            if (ColumnName.LOGTIME.equals(prop)){
                return  "to_date('"+DateUtils.timeStampToString(Long.valueOf(result)/1000+60*60,DateUtils.DATE_TO_STRING_DETAIAL_PATTERN)+"','yyyy-MM-dd hh:mi:ss')";
            }
            return result;
        }


        /**
         * 操作符转sql可识别操作符
         * @param operator
         * @return
         */
        private String operatorToSign(String operator) {
            switch (operator) {
                case ConditionBlock.OPERATOR_BETWEEN:
                    return " BETWEEN ";
                case ConditionBlock.OPERATOR_EQ:
                    return " = ";
                case ConditionBlock.OPERATOR_GE:
                    return " >= ";
                case ConditionBlock.OPERATOR_GT:
                    return " > ";
                case ConditionBlock.OPERATOR_IN:
                    return " IN ";
                case ConditionBlock.OPERATOR_LE:
                    return " <= ";
                case ConditionBlock.OPERATOR_LIKE:
                    return " LIKE ";
                case ConditionBlock.OPERATOR_LT:
                    return " < ";
                case ConditionBlock.OPERATOR_NE:
                    return " != ";
                default:
                    throw new CommonManagerException("不支持的操作：" + operator);
            }
        }

        /**
         * 字段映射
         * recordtime-TO_DATE('1970-01-01 08:00:00','yyyy-mm-dd hh24:mi:ss'))*1000*24*60*60是将data类型转为时间戳，
         * round转换后会出现精度丢失，四舍五入即可消除该影响
         * -60*60*1000  3.8库记录的时间是凌晨1点 4.0库记录的是凌晨零点 所以减去60*60*1000 ms
         * @param prop
         * @return
         */
        private String propConverter(String prop){
            switch (prop) {
                case "logtime":
                    return "recordtime";
                case "line_id":
                    return "pqnodeid";
                case "aggregationcycle":
                    return "timetype";
                case "dataid":
                    return "paraid";
                case "quantitymaptemplate_id":
                    return "paraid";
                default:
                    throw new CommonManagerException("不支持的参数：" + prop);
            }
        }
    }
}

package com.cet.pq.common;

/**
 * <AUTHOR>
 */
public class ErrorCode{
	
	public static final ErrorDesc SUCCESS = new ErrorDesc(0, "成功");
	
	public static final ErrorDesc INTERNAL_ERROR = new ErrorDesc(-1, "内部错误");
	
	public static final ErrorDesc SERVICE_API_ERROR = new ErrorDesc(-2, "调用服务接口错误");

	public static final ErrorDesc NAME_DUPLICATION = new ErrorDesc(-3, "名称重复");
	
	public static final ErrorDesc ACCOUNT_NOT_NULL = new ErrorDesc(-4, "存在账号不能删除");
	
	public static final ErrorDesc OTHER_WARN = new ErrorDesc(-5, "警告信息");
	
	public static final ErrorDesc ACCOUNT_DEPENDENCY = new ErrorDesc(-6, "账号被设备关联不能删除");

	public static final ErrorDesc MODEL_LABEL_ERROR = new ErrorDesc(-7, "模型名称错误");

	public static final int SUCCESS_CODE = 0;

	public static final String FILE_UPLOAD_FAILED = "文件上传失败";
	
}

package com.cet.pq.common.model.offlinetest;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/12/11 15:06
 * @Description 新增、编辑普测对象入参
 */
@Data
public class SaveOffLineParams {
    @ApiModelProperty(required = true,value = "台账对象id,新增给0")
    private Long id;

    @ApiModelProperty(required = true, value = "变电站id")
    private Long substationId;

    @ApiModelProperty(required = true, value = "测试对象类型")
    @JsonProperty("testobjecttype")
    private Integer testObjectType;

    @ApiModelProperty(value = "测试对象名称")
    private String name;

    @ApiModelProperty(required = true, value = "电压等级")
    @JsonProperty("voltclass")
    private Integer voltClass;

    @ApiModelProperty(value = "上次普测时间")
    @JsonProperty("lasttesttime")
    private Long lastTestTime;

    @ApiModelProperty(value = "测点仪表型号")
    @JsonProperty("testdevicemodel")
    private String testDeviceModel;

    @ApiModelProperty(value = "接线方式;1:星形接线;2:角形接线;")
    @JsonProperty("wiredtype")
    private Integer wiredType;

    @ApiModelProperty(value = "PT变比")
    @JsonProperty("ptratio")
    private String ptRatio;

    @ApiModelProperty(value = "CT变比")
    @JsonProperty("ctratio")
    private String ctRatio;

    @ApiModelProperty(value = "最小短路容量")
    @JsonProperty("shortcircuitcapacity")
    private Double shortCircuitCapacity;

    @ApiModelProperty(value = "供电设备容量")
    @JsonProperty("supplyequipmentcapacity")
    private Double supplyEquipmentCapacity;

    @ApiModelProperty(value = "用户协议容量")
    @JsonProperty("userprotocolcapacity")
    private Double userProtocolCapacity;

    @ApiModelProperty(value = "普测周期")
    @JsonProperty("testcycle")
    private Integer testCycle;

    @ApiModelProperty(value = "电压上偏差")
    @JsonProperty("upvoltdeviation")
    private Double upVoltDeviation;

    @ApiModelProperty(value = "电压下偏差")
    @JsonProperty("lowvoltdeviation")
    private Double lowVoltDeviation;
}

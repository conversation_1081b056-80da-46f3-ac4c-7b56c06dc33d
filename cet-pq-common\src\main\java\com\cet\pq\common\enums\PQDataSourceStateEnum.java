package com.cet.pq.common.enums;

/**
 * @describe: TODO
 * @author: lian<PERSON><PERSON>
 * @date: 2023/6/9 10:07
 */
public enum PQDataSourceStateEnum {
    EXCEPTION(0, "异常"),
    NORMAL(1, "正常");

    private Integer state;

    private String desc;

    PQDataSourceStateEnum(Integer state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getState() {
        return state;
    }
}

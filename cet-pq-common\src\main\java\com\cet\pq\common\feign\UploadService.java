package com.cet.pq.common.feign;


import com.cet.pq.common.model.PagePushDataParams;
import com.cet.pq.common.model.Result;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * @Title: UploadFeign
 * @Package: com.cet.pq.pqwmuploadservice.feign
 * @Description:
 * @Author: zhangyifu
 * @Date: 2024/3/1 9:11
 * @Version:1.0
 */
@FeignClient("pq-wm-upload-service")
public interface UploadService {

    @ApiOperation(value = "向中台手动追捕推送数据")
    @PostMapping (value = "/pq/sd/pagePushData")
    Result<String> pagePushData(@RequestBody PagePushDataParams pagePushDataParams);

}

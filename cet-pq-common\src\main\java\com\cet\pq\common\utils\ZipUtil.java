package com.cet.pq.common.utils;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
 
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletResponse;
 
/**
 * 文件压缩和解压缩工具类
 * <AUTHOR>
 * @Date 2020-11-03
 */
public class ZipUtil {
	/**
	 * 压缩文件
	 * @param files 多个待压缩的文件
	 * @param outputStream 输出流
	 * @throws Exception 
	 * @throws IOException
	 */
	public static void compressFile(Map<String,InputStream> files, ZipOutputStream zipOutputStream) {
	    try {
	    	Set<Entry<String, InputStream>> entrySet = files.entrySet();
	        for (Entry<String, InputStream> file:entrySet) {
	            try {
	                zipFile(file.getValue(),file.getKey(), zipOutputStream);
	            } catch (Exception e) {
	                continue;
	            }
	        }
	    } catch (Exception e) {
	    } finally {
	    	try {
	    		zipOutputStream.close();
			} catch (IOException e) {
			}
	    }
	}
 
	/**
	 * 将流写入到zip文件中
	 * @param inputFile
	 * @param outputstream
	 * @throws Exception
	 */
	public static void zipFile(InputStream is, String fileName, ZipOutputStream outputstream) throws IOException, ServletException {
	    try {
	        if (is != null) {
                BufferedInputStream bInStream = new BufferedInputStream(is);
                ZipEntry entry = new ZipEntry(fileName);
                outputstream.putNextEntry(entry);
                int len = 0 ;
                byte[] buffer = new byte[10 * 1024];
                while ((len = is.read(buffer)) > 0) {
                	outputstream.write(buffer, 0, len);
                	outputstream.flush();
                }
                outputstream.closeEntry();
                bInStream.close();//关闭
                is.close();
	        } else {
	            throw new ServletException("文件不存在！");
	        } 
	    } catch (IOException e) {
	        throw e;
	    }
	}
	
	/**
	 * 下载打包的文件
	 *
	 * @param file
	 * @param response
	 */
	public static void downloadZip(File file, HttpServletResponse response) {
	    try {
	        // 以流的形式下载文件。
	        BufferedInputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
	        byte[] buffer = new byte[fis.available()];
	        fis.read(buffer);
	        fis.close();
	        // 清空response
	        response.reset();
	        OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
	        response.setCharacterEncoding("UTF-8");
	        response.setContentType("application/octet-stream");
	        response.setHeader("Content-Disposition", "attachment;filename=" + file.getName());
	        toClient.write(buffer);
	        toClient.flush();
	        toClient.close();
	        file.delete();
	    } catch (IOException ex) {
	        ex.printStackTrace();
	    }
	}
 
}
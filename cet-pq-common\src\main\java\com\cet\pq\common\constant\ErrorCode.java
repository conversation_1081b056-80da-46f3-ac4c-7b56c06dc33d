package com.cet.pq.common.constant;

/**
 * 错误信息
 * 
 * <AUTHOR>
 * @date 2019/6/2 9:26
 */
public class ErrorCode {

	public static final int SUCCESS_CODE = 0;

	public static final String SUCCESS_DESC = "成功";

	public static final int INTERVAL_CODE = -1;

	public static final String INTERVAL_DESC = "内部错误";

	public static final int ILLEGAL_ARGUMENT_CODE = -2;

	public static final String ILLEGAL_ARGUMENT_DESC = "非法参数";

	public static final int FORMAT_ERROR_CODE = -3;

	public static final String FORMAT_ERROR_DESC = "格式错误";

	public static final int DUPLICATE_NAME_CODE = -4;

	public static final String DUPLICATE_NAME_DESC = "重名错误";

	public static final int MODEL_DUPLICATE_NODE_CODE = -2000001;

	public static final String MODEL_DUPLICATE_NODE_DESC = "名称重复";

	public static final int PEC_DUPLICATE_NAME_CODE = -100003;

	public static final String PEC_DUPLICATE_NAME_DESC = "同一个通道下监测点名称重复";

	public static final String ERROR_FILE = "文件格式大小不符合,或含非法字符";

	public static final String MODEL_DUPLICATE_CODE_DESC = "编号重复";

	public static final String MODEL_DELETE_FAIL_DESC = "删除失败";
}

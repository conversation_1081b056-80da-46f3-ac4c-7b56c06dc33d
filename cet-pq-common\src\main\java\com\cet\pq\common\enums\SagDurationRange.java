package com.cet.pq.common.enums;

/**
 * <AUTHOR>
 * @ClassName SwellDurationRange
 * @Description 暂降评价持续时间定义
 * @Date 2020/9/3
 */
public enum SagDurationRange {
    //暂降评价持续时间定义
    SagDurationRange1(1, "d0_1", "(0.01,0.1]"),
    SagDurationRange2(2, "d0_25", "(0.1,0.25]"),
    SagDurationRange3(3, "d0_5", "(0.25,0.5]"),
    SagDurationRange4(4, "d1", "(0.5,1]"),
    SagDurationRange5(5, "d3", "(1,3]"),
    SagDurationRange6(6, "d5", "(3,5]"),
    SagDurationRange7(7, "d10", "(5,10]"),
    SagDurationRange8(8, "d20", "(10,20]"),
    SagDurationRange9(9, "d60", "(20,60]"),
    SagDurationRange10(10, "d180", "(60,180]"),
    //合并(3,5]，(5,10]用于展示
    SagDurationRange11(11, "d5", "(3,10]");

    private int id;
    private String name;
    private String duringTime;

    SagDurationRange(int id, String name, String duringTime) {
        this.id = id;
        this.name = name;
        this.duringTime = duringTime;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDuringTime() {
        return duringTime;
    }

    public static SagDurationRange getSagDurationRange(Integer id) {
        for (SagDurationRange type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return SagDurationRange1;
    }

    public static SagDurationRange getSagDurationRangeStat(Integer id) {

        if(id.equals(6)||id.equals(7)){
            id = 11;
        }
        for (SagDurationRange type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return SagDurationRange1;
    }
}

package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;

/**
 * <AUTHOR>
 * @Date 2024/7/29 18:01
 * @Description 列项类型枚举
 */
public enum AccountTypeEnum {
    SENSITIVE_USER("sensitiveuser",20),
    ELECTRIC_RAILWAY("electricrailway",21),
    WIND_POWER_STATION("windpowerstation",22),
    PHOTOVOLTAIC_STATION("photovoltaicstation",23),
    SMELT_LOAD("smeltload",24),
    ENERGY_STORAGE_STATION("energystoragestation",25),
    CHARGING_STATION("chargingstation",26),
    BUS_BAR_STATION("busbarstation",28),
    INTERFERENCE_SOURCE("interferencesource",27),
    ;

    private String text;
    private Integer id;

    AccountTypeEnum(String text, Integer id) {
        this.text = text;
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public static AccountTypeEnum getEnumById(Integer id) {
        for (AccountTypeEnum onEnum : AccountTypeEnum.values()) {
            if (id.equals(onEnum.getId())) {
                return onEnum;
            }
        }
        throw new CommonManagerException("模型信息有误");
    }


    /**
     * 根据模型名称获取列项id
     * @param name
     * @return
     */
    public static Integer getIdByText(String name) {
        for (AccountTypeEnum onEnum : AccountTypeEnum.values()) {
            if (name.equals(onEnum.getText())) {
                return onEnum.getId();
            }
        }
        throw new CommonManagerException("模型信息有误");
    }

}

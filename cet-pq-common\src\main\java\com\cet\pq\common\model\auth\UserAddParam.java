package com.cet.pq.common.model.auth;

import java.util.List;

import lombok.Data;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
public class UserAddParam {

	private String name;
	private String nicName;
	private String password;
	private String profile;
	private Integer state;
	private Long tenantId;
	private List<Long> auths;
	private String avater;
	private String customConfig;
	private String email;
	private Long expiredTime;
	private String mobilePhone;
	private List<Long> relativeUserGroup;
	private List<Role> roles;
	private List<GraphNode> graphNodes;
	private List<ModelNode> modelNodes;
	private List<PageNode> pageNodes;
	private List<PecstarNode> pecstarNodes;
	
}

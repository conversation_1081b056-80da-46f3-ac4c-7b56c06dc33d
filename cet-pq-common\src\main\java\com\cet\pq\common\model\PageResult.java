package com.cet.pq.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

import static com.cet.pq.common.utils.CommonUtils.getListPaging;

/**
 * <AUTHOR>
 */
@ApiModel(value = "PageResult", description = "服务接口返回分页结果")
@SuppressWarnings({"rawtypes", "unchecked"})
public class PageResult<T> implements Serializable {

    public static final int SUCCESS_CODE = 0;

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "错误码", name = "code", example = "1")
    private Integer code;

    @ApiModelProperty(value = "错误信息", name = "msg", example = "null")
    private String msg;

    @ApiModelProperty(value = "数据", name = "data", example = "xxx")
    private T data;

    /**
     * 默认显示10条
     */
    private static int DEFAULT_PAGE_SIZE = 10;
    /**
     * 每页条数
     */
    private Integer pageSize = DEFAULT_PAGE_SIZE;

    /**
     * 当前页码
     */
    private Integer pageNum = 1;

    /**
     * 总数
     */
    private long total = 0L;

    public PageResult() {
        code = 0;
        msg = "";
    }

    public PageResult(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public PageResult(Integer code, String msg, Integer pageNum, Integer pageSize, long total, T data) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.data = data;
        this.code = 0;
        this.msg = "";
    }

    public PageResult(Integer pageNum, Integer pageSize, long total, T data) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.data = data;
        this.code = 0;
        this.msg = "success";
    }

    public PageResult(Integer pageNum, Integer pageSize, T data) {
        this.code = 0;
        this.msg = "";
        this.data = data;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public PageResult(T data) {
        this.data = data;
        this.code = 0;
        this.msg = "";
    }

    public static PageResult success() {
        return new PageResult(0, "操作成功", null);
    }

    public static <T> PageResult success(Integer pageNum, Integer pageSize, long total, T data) {
        return new PageResult(0, "success", pageNum, pageSize, total, data);
    }

    public static <T> PageResult<T> success(T t) {
        return new PageResult<T>(0, "success", t);
    }

    public static PageResult error(String msg) {
        return new PageResult(500, msg, null);
    }

    public Integer getCode() {
        return this.code;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public long getTotal() {
        return total;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Long getTotalPageSize() {
        if (this.pageSize == null) {
            return 0L;
        }
        if (this.total % this.pageSize == 0L) {
            return this.total / this.pageSize;
        }
        return this.total / this.pageSize + 1L;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "PageResult{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                ", pageSize=" + pageSize +
                ", pageNum=" + pageNum +
                ", totalRows=" + total +
                '}';
    }


    /**
     * 返回分页结果
     *
     * @param pageSize 页面大小
     * @param pageNum  第几页
     * @param result   需要分页返回的数据
     * @return 分页后的对象
     */
    public static  <T> PageResult<List<T>> resultPaging(Integer pageSize, Integer pageNum, List<T> result) {
        List<T> pageData = getListPaging(result, pageNum, pageSize);
        PageResult pageResult = PageResult.success(pageData);
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setTotal(result.size());
        return pageResult;

    }
}

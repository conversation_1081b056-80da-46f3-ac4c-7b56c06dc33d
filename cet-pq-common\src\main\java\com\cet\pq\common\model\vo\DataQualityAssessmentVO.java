package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 14:46
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DataQualityAssessmentVO {

    /**
     * 监测点id
     */
    private Long monitorId;

    /**
     * 监测点编号
     */
    private String monitorCode;

    /**
     * 监测点名称
     */
    private String monitorName;

    /**
     * 装置接线状态评估: 0：异常，1：正常
     */
    private String deviceWiringStatusAssessment;

    /**
     * 装置一致性评估: 0：否，1：是
     */
    private String deviceConformityAssessment;

    /**
     * 数据完整性
     */
    private Double dataIntegrity;

    /**
     * 数据准确性
     */
    private Double dataAccuracy;

    /**
     * 数据状态: 0：优，1：良，2：差
     */
    private String dataStatus;

    /**
     * 装置整体状态评估: 0：异常，1：正常，2：故障
     */
    private String deviceStatusAssessment;

    /**
     * 校正状态：0：未校正，1：已校正，2：---
     */
    private String correctiveStatus;

}

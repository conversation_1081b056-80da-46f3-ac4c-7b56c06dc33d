package com.cet.pq.common.model.peccore;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/1/15 14:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PecsMeasureNodeView {
    private Long dataTypeId;
    private Long dataId;
    private String text;
    private Long stationId;
    private Long channelId;
    private Long deviceId;
    private String paraName;
    private Long nodeType;
    private Long nodeId;
    private int measType;
    private int funcType;
    private int logicalDeviceIndex;
    private int paraType;
    private List<PecsMeasureNodeView> children;

    public PecsMeasureNodeView(Long stationId, Long channelId, Long deviceId, Long nodeType, Long nodeId)
    {
        this.nodeType = nodeType;
        this.nodeId = nodeId;
        this.stationId = stationId;
        this.channelId = channelId;
        this.deviceId = deviceId;
        this.text = "";
        this.children = new ArrayList<>();
        this.measType = 0;
        this.funcType = 0;
        this.logicalDeviceIndex = 1;
        this.paraType = 1;
    }
}

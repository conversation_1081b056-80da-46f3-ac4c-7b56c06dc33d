package com.cet.pq.common.exception.handler;

import com.cet.pq.common.exception.ExcelException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.cet.pq.common.constant.ErrorCode;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.Result;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName ControllerExceptionHandler
 * @Description 统一异常处理
 * <AUTHOR>
 * @Date 2020/2/24 18:03
 */
@RestControllerAdvice
@SuppressWarnings("rawtypes")
public class ControllerExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(ControllerExceptionHandler.class);

    /**
     * 	捕获所有的异常
     * @param ex 异常
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Result handlerException(Exception ex) {
        logger.error("业务处理异常", ex);
        return new Result<String>(ErrorCode.INTERVAL_CODE, "业务处理异常", null);
    }

    @ExceptionHandler({BindException.class})
    @ResponseStatus(HttpStatus.OK)
    public Result handlerBindException(BindException ex) {
        List<ObjectError> allErrors = ex.getAllErrors();
        List<String> errorFieldList = allErrors.
                stream().
                map(objectError -> ((FieldError)objectError).getField()).collect(
                Collectors.toList());
        return new Result<String>(ErrorCode.INTERVAL_CODE, "参数类型异常", errorFieldList.get(0));
    }

    @ExceptionHandler(value = CommonManagerException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result handlerException(CommonManagerException ex) {
    	String msg = ex.getMessage();
        return new Result<String>(ErrorCode.INTERVAL_CODE,StringUtils.isBlank(ex.getMessage()) ? msg : ex.getMessage(), null);
    }

    @ExceptionHandler(value = ExcelException.class)
    @ResponseStatus(HttpStatus.OK)
    public Result handlerException(ExcelException ex) {
        return new Result<String>(ErrorCode.INTERVAL_CODE, ex.getMessage(), null);
    }

    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handlerException(HttpRequestMethodNotSupportedException ex) {
        return new Result<String>(ErrorCode.INTERVAL_CODE, ex.getMessage(), null);
    }
}

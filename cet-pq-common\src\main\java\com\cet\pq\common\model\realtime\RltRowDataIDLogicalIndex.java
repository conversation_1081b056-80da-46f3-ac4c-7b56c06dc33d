package com.cet.pq.common.model.realtime;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 实时数据查询参数
 *
 * <AUTHOR>
 * @date 2019/11/20 10:50
 */
@Data
@NoArgsConstructor
public class RltRowDataIDLogicalIndex {
    private Long dataId;
    private Integer logicalId;

    public RltRowDataIDLogicalIndex(Long dataId) {
        this.dataId = dataId;
        this.logicalId = 1;
    }
	public RltRowDataIDLogicalIndex(Long dataId,Integer logicalId) {
		this.dataId = dataId;
		this.logicalId = logicalId;
	}


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof RltRowDataIDLogicalIndex)) {
            return false;
        }
        RltRowDataIDLogicalIndex that = (RltRowDataIDLogicalIndex) o;
        return Objects.equals(getDataId(), that.getDataId()) &&
                Objects.equals(getLogicalId(), that.getLogicalId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getDataId(), getLogicalId());
    }
}

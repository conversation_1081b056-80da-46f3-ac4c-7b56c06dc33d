package com.cet.pq.common.model.offlinetest;

import com.cet.pq.common.constant.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
@Data
@NoArgsConstructor
public class OfflineGBLimit {

	private String modelLabel = TableName.OFFLINEGBLIMIT;

	private Long id;
	private Long dataid;
	private String dataname;
	private Double lowlimit;
	private Double uplimit;
	@JsonProperty("offlinetest_id")
	private Long offlinetestId;
	@JsonProperty("quantitymaptemplate_id")
	private Long quantitymaptemplateId;
	@JsonProperty("busbarsection_id")
	private Long busbarsectionId;

	public OfflineGBLimit(Long dataId,String dataname, Double lowlimit, Double uplimit,Long offlinetestId,Long quantitymaptemplateId) {
		this.dataid = dataId;
		this.dataname = dataname;
		this.lowlimit = lowlimit;
		this.offlinetestId = offlinetestId;
		this.quantitymaptemplateId = quantitymaptemplateId;
		this.uplimit = uplimit;
	}

	
	
}

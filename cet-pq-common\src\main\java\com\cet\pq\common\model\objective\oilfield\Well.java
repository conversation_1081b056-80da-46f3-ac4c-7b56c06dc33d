package com.cet.pq.common.model.objective.oilfield;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 油井
 */
@Data
public class Well {
	private long id;
	/**
	 * 井号编码，比如:G29-04
	 */
	private String code;
	/**
	 * 名称，井号，比如：高29-04
	 */
	private String name;
	/**
	 * 额定充次
	 */
	private Double ratedStrokeTimes;
	/**
	 * 额定冲程
	 */
	private Double ratedStroke;
	/**
	 * 电机额定功率
	 */
	private Double ratedMotorPower;
	/**
	 * 泵径
	 */
	private Double pupDiameter;
	/**
	 * 泵深
	 */
	private Double pupDepth;

	private Long ia;

	private Long ib;

	private Long ic;

	private Long kwh;

	private Long pf;

	private Long state;

	private Long vab;

	private Long van;

	private Long vbc;

	private Long vbn;

	private Double submergence;

	private Double balance;

	private Double liquidHeight;

	private Double liquidTemperature;

	private Double moistrue;

	private Double oilPressure;

	private Double oilViscosity;

	private Double sleevePressure;

	private Double stroke;

	private Double strokeTimes;
}

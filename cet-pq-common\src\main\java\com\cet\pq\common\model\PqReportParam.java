package com.cet.pq.common.model;

import com.cet.pq.common.utils.ParseDataUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/10 13:38
 */
@AllArgsConstructor
public class PqReportParam {
	@JsonProperty("dataid")
	private Long dataID;
	@JsonProperty("limithigh")
	private Double limitHigh;
	@JsonProperty("limitlow")
	private Double limitLow;
	@JsonProperty("dataname")
	private String dataName;
	
	public PqReportParam() {

	}

	public PqReportParam(Long dataID, String dataName) {
		this.dataID = dataID;
		this.dataName = dataName;
	}

	public Long getDataID() {
		return dataID;
	}

	public void setDataID(Long dataID) {
		this.dataID = dataID;
	}

	public Double getLimitHigh() {
		return Double.isNaN(ParseDataUtil.parseDouble(limitHigh)) ? null : limitHigh;
	}

	public void setLimitHigh(Double limitHigh) {
		this.limitHigh = limitHigh;
	}

	public Double getLimitLow() {
		return Double.isNaN(ParseDataUtil.parseDouble(limitLow)) ? null : limitLow;
	}

	public void setLimitLow(Double limitLow) {
		this.limitLow = limitLow;
	}

	public String getDataName() {
		return dataName;
	}

	public void setDataName(String dataName) {
		this.dataName = dataName;
	}
}

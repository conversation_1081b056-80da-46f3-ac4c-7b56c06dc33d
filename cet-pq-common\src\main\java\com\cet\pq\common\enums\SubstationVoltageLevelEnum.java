package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/10/9 11:29
 */
public enum SubstationVoltageLevelEnum {

    AC_KV_10("10kV", 1, 10D),

    AC_KV_35("35kV", 10, 35D),

    AC_KV_66("66kV", 20, 66D),

    AC_KV_72_5("72.5kV", 30, 72.5D),

    AC_KV_110("110kV", 40, 110D),

    AC_KV_220("220kV", 50, 220D),

    AC_KV_330("330kV", 60, 330D),

    AC_KV_500("500kV", 70, 500D),

    AC_KV_750("750kV", 80, 750D),

    AC_KV_1000("1000kV", 90, 1000D),

    DC_KV_400("±400kV", 100, 400D),

    DC_KV_500("±500kV", 110, 500D),

    DC_KV_660("±660kV", 120, 660D),

    DC_KV_800("±800kV", 130, 800D),

    DC_KV_1000("±1000kV", 140, 1000D),

    DC_KV_1100("±1100kV", 150, 1100D);


    private String name;
    private Integer value;
    private Double number;

    private SubstationVoltageLevelEnum(String name, Integer value, Double number) {
        this.name = name;
        this.value = value;
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public Double getNumber() {
        return number;
    }


    public static Integer getVoltageLevelValueByNameThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            throw new CommonManagerException("变电站电压等级不存在");
        }
        for (SubstationVoltageLevelEnum substationVoltageLevelEnum : SubstationVoltageLevelEnum.values()) {
            if (substationVoltageLevelEnum.getName().trim().equalsIgnoreCase(name)) {
                return substationVoltageLevelEnum.getValue();
            }
        }
        throw new CommonManagerException("变电站电压等级匹配失败");
    }

    public static Integer getVoltageLevelValueByNameNoThrows(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (SubstationVoltageLevelEnum substationVoltageLevelEnum : SubstationVoltageLevelEnum.values()) {
            if (substationVoltageLevelEnum.getName().trim().equalsIgnoreCase(name)) {
                return substationVoltageLevelEnum.getValue();
            }
        }
        return null;
    }

    public static String getVoltageNameByValueNoThrows(Integer value) {
        if (Objects.isNull(value)) {
            return StringUtils.EMPTY;
        }
        for (SubstationVoltageLevelEnum substationVoltageLevelEnum : SubstationVoltageLevelEnum.values()) {
            if (substationVoltageLevelEnum.getValue().equals(value)) {
                return substationVoltageLevelEnum.getName();
            }
        }
        return StringUtils.EMPTY;
    }


}

package com.cet.pq.common.encode.sm4;

/**
 * <AUTHOR>
 * @ClassName ByteLists
 * @Description TODO
 * @Date 2020/10/14
 */
public class ByteLists {
    public static byte[] toArray(java.util.List<Byte> list)
    {
        byte[] array = new byte[list.size()];
        for (int i = 0; i < list.size(); i++)
        {
            array[i] = list.get(i).byteValue();
        }
        return array;
    }

    public static void addPrimitiveArrayToList(byte[] array, java.util.List<Byte> list)
    {
        for (int i = 0; i < array.length; i++)
        {
            list.add(array[i]);
        }
    }

    public static void addPrimitiveArrayToList(int index, byte[] array, java.util.List<Byte> list)
    {
        for (int i = 0; i < array.length; i++)
        {
            list.add(index + i, array[i]);
        }
    }



}

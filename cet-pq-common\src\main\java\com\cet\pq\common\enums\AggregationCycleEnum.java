package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 聚合周期
 * <AUTHOR>
 * @Date 2020-8-21
 */
public enum AggregationCycleEnum {
	/**
	 * 1秒
	 */
	ONE_SEC("1s", 1),
	/**
	 * 3秒聚合
	 */
	THREE_SEC("3s", 2),
	/**
	 * 1分钟
	 */
	ONE_MIN("1min", 3),
	/**
	 * 5分钟
	 */
	FIVE_MIN("5min", 4),
	/**
	 * 10分钟
	 */
	TEN_MIN("10min", 5),
	/**
	 * 30分钟
	 */
	THIRT_MIN("30min", 6),
	/**
	 * 1小时
	 */
	ONE_HOUR("1h", 7),
	/**
	 * 2小时
	 */
	TWO_HOUR("2h", 8),
	/**
	 * 3小时
	 */
	THREE_HOUR("3h", 9),
	/**
	 * 6小时
	 */
	SIX_HOUR("6h", 10),
	/**
	 * 12小时
	 */
	TWELVE_HOUR("12h", 11),
	/**
	 * 一天
	 */
	ONE_DAY("1d", 12),
	/**
	 * 一周
	 */
	ONE_WEEK("1w", 13),
	/**
	 * 1个月
	 */
	ONE_MONTH("1m", 14),
	/**
	 * 3个月
	 */
	THREE_MONTH("3m", 15),
	/**
	 * 六个月
	 */
	SIX_MONTH("6m", 16),
	/**
	 * 一年
	 */
	ONE_YEAR("1y", 17),
	/**
	 * 15分钟
	 */
	FIFTEEN_MIN("15min", 18);
	
	private String key;
	private Integer value;

	private AggregationCycleEnum(String key, Integer value) {
		
	}

	public static Integer getAggregationCycle(String key) {
		if (StringUtils.isEmpty(key)) {
			return null;
		}
		for (AggregationCycleEnum aggregationCycleEnum : AggregationCycleEnum.values()) {
			if(key.equals(aggregationCycleEnum.key)) {
				return aggregationCycleEnum.value;
			}
		}
		throw new CommonManagerException("周期转换失败");
	}
}

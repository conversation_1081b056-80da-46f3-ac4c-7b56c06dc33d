package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2025/3/20 15:27
 * @Description svg容量枚举
 */
public enum SvgCapacityEnum {
    MVar8("8MVar", 1, 8.0),
    MVar10("10MVar", 2, 10.0),
    MVar12("12MVar", 3, 12.0),
    MVar20("20MVar", 4, 20.0),
    MVar30("30MVar", 5, 30.0),
    MVar40("40MVar", 6, 40.0),
    MVar50("50MVar", 7, 50.0),
    KVar30("30kVar", 8, 30.0),
    KVar50("50kVar", 8, 50.0),
    KVar100("100kVar", 9, 100.0),
    KVar200("200kVar", 10, 200.0),
    KVar300("300kVar", 11, 300.0),
    KVar500("500kVar", 12, 500.0),
    KVar150("150kVar", 13, 150.0),
    KVar1000("1000kVar", 14, 1000.0),
    ;
    private String name;
    private Integer value;
    private Double capacity;

    SvgCapacityEnum(String name, Integer value, Double capacity) {
        this.name = name;
        this.value = value;
        this.capacity = capacity;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public Double getCapacity() {
        return capacity;
    }

    public static Double getCapacityByValue(Integer value) {
        for (SvgCapacityEnum oneEnum : SvgCapacityEnum.values()) {
            if (value.equals(oneEnum.getValue())) {
                return oneEnum.getCapacity();
            }
        }
        return 0.0;
    }

}

package com.cet.pq.anlysis.common.enums.offlinetest;

/**
 * <AUTHOR>
 * @Date 2024/8/28
 * @Description 部分dataid对应趋势曲线物理量名称
 */
public enum DataIdName {

    DATA_ID_8094(8094L, "电压"),
    DATA_ID_8082(8082L, "闪变"),
    DATA_ID_8032(8032L, "总谐波畸变"),

    ;

    private Long dataId;
    private String name;

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    DataIdName(Long dataId, String name) {
        this.dataId = dataId;
        this.name = name;
    }

    public static String getText(Long dataId) {
        for (DataIdName e : values()) {
            if (dataId.equals(e.dataId)) {
                return e.name;
            }
        }
        return "";
    }
}

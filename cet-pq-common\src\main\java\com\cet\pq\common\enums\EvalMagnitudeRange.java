package com.cet.pq.common.enums;


import java.util.ArrayList;

/**
 * <AUTHOR>
 * @ClassName EvalMagnitudeRange
 * @Description 暂态评价幅值范围定义
 * @Date 2020/9/3
 */
public enum EvalMagnitudeRange {
    //暂态评价幅值范围定义
    EvalMagnitudeRange1(1, "U_0_10", "[0,10)"),
    EvalMagnitudeRange3(3, "U_10_20", "[10,20)"),
    EvalMagnitudeRange4(4, "U_20_30", "[20,30)"),
    EvalMagnitudeRange5(5, "U_30_40", "[30,40)"),
    EvalMagnitudeRange6(6, "U_40_50", "[40,50)"),
    EvalMagnitudeRange7(7, "U_50_60", "[50,60)"),
    EvalMagnitudeRange8(8, "U_60_70", "[60,70)"),
    EvalMagnitudeRange9(9, "U_70_80", "[70,80)"),
    EvalMagnitudeRange10(10, "U_80_90", "[80,90]"),
    EvalMagnitudeRange11(11, "U_110_120", "(110,120)"),
    EvalMagnitudeRange12(12, "U_120_180", "[120,180]");


    private int id;
    private String name;
    private String characteristicAmplitude;

    EvalMagnitudeRange(int id, String name, String characteristicAmplitude) {
        this.id = id;
        this.name = name;
        this.characteristicAmplitude = characteristicAmplitude;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getCharacteristicAmplitude() {
        return characteristicAmplitude;
    }

    public static EvalMagnitudeRange getEvalMagnitudeRange(Integer id) {
        for (EvalMagnitudeRange type : values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return EvalMagnitudeRange1;
    }

    public static ArrayList<String> getAllCharacteristicAmplitude() {
        ArrayList<String> list = new ArrayList<>();
        for (EvalMagnitudeRange evalMagnitudeRange : values()) {
            list.add(evalMagnitudeRange.characteristicAmplitude);
        }
        return list;
    }
}

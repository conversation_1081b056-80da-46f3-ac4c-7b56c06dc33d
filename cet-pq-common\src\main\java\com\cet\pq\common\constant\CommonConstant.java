package com.cet.pq.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public class CommonConstant {

	// 数字常量
	public static final Integer NUMBER_ZERO = 0;
	public static final Integer NUMBER_ONE = 1;
	public static final Integer NUMBER_TWO = 2;
	public static final Integer NUMBER_THREE = 3;
	public static final Integer NUMBER_FORE = 4;
	public static final Integer NUMBER_FIVE = 5;
	public static final Integer NUMBER_SIX = 6;
	public static final Integer NUMBER_SEVEN = 7;
	public static final Integer NUMBER_EIGHT = 8;
	public static final Integer NUMBER_NINE = 9;

	public static final Integer NUMBER_TEN = 10;

	public static final Integer NUMBER_ELEVEN = 11;

	public static final Integer NUMBER_TWELVE = 12;

	public static final Integer NUMBER_SIXTEEN = 16;

	public static final Integer NUMBER_TWENTY = 20;

	public static final Integer NUMBER_THIRTY_ONE = 31;

	public static final Integer NUMBER_TWENTY_SIX = 26;

	public static final Integer NUMBER_THIRTY_TWO = 32;

	public static final Integer NUMBER_THIRTY_THREE = 33;

	public static final Integer NUMBER_THIRTY_SEVEN = 37;

	public static final Integer NUMBER_FIFTY = 50;

	public static final Integer NUMBER_TWOHUNDRED = 200;

	public static final Integer NUMBER_FIVEHUNDRED = 500;

	public static final Integer NUMBER_TENTHOUSAND = 10000;

	public static final Integer NUMBER_TEN_THOUSAND_AND_ONE = 10001;

	public static final Integer NUMBER_TEN_THOUSAND_AND_SIXTY_FOUR = 10064;
	
	//特征幅值
	public static final Double TEN = 10.0;
	public static final Double FIFTY = 50.0;
	public static final Double SEVENTY = 70.0;
	public static final Double EIGHTY = 80.0;
	public static final Double NINETY = 90.0;
	public static final Double ONE_HUNDRED_AND_TEN = 110.0;
	public static final Double ONE_HUNDRED_AND_TWENTY = 120.0;
	public static final Double ONE_HUNDRED_AND_FORTY = 140.0;

	//持续时间
	public static final Long TWO_HUNDRED_MICROSECOND = 200000L;
	public static final Long FIVE_HUNDRED_MICROSECOND = 500000L;
	public static final Long THREE_THOUSAND_MICROSECOND = 3000000L;
	public static final Long SIXTY_THOUSAND_MICROSECOND = 60000000L;
	
	//聚合类型
	public static final Integer AGGREGATIONTYPE_MAX = 3;
	public static final Integer AGGREGATIONTYPE_MIN = 4;
	public static final Integer AGGREGATIONTYPE_AVG = 11;
	public static final Integer AGGREGATIONTYPE_CP = 6;
	

	// 常用统计周期-天
	public static final Integer AGGREGATIONCYCLE_ONE_DAY = 12;

	// 常用统计周期-月
	public static final Integer AGGREGATIONCYCLE_ONE_MONTH = 14;

	// 常用统计周期-天
	public static final Integer AGGREGATIONCYCLE_ONE_YEAR = 17;

	// 模型权限授权方式-禁止访问
	public static final String DISABLE = "disable";

	// 模型权限授权方式-允许访问
	public static final String ENABLE = "enable";

	// 内容
	public static final String CONTEXT = "context";

	public static final String USER_INFO = "userinfo:";

	// 下划线分隔符
	public static final String SEPARATOR_UNDER_LINE = "_";

	// 点
	public static final String POINT = ".";

	// 编译点

	public static final String POINT_COMPILE = "\\.";
	// 电压等级
	public static final String VOLTCLASS_DATANAME = "电压等级";
	// 最小短路容量
	public static final String SHORTCIRCUITCAPACITY_DATANAME = "最小短路容量";
	// 用户协议容量
	public static final String USERPROTOCOLCAPACITY_DATANAME = "用户协议容量";
	// 供电设备容量
	public static final String SUPPLYEQUIPMENTCAPACITY_DATANAME = "供电设备容量";
	// 监测时间
	public static final String DATATIME_DATANAM = "监测时间";

	public static final Integer IMPORT_SUCCESS = 1;
	public static final Integer IMPORT_FAIL = 2;
	public static final Integer IMPORT_PROCESS = 3;

	/**
	 * session key
	 */
	public static final String USER_SUB_UNIT_KEY = "userSubUnit";
	public static final String USER_TREE_MAP = "userTreeMap";
	public static final String USER_RELATION_TREE = "userRelationTree";
	public static final String COMMON_TREE_NODE = "commonTreeNode";
	public static final String TOP_TREE_NODE = "topTreeNode";
	/**
	 * null
	 */
	public static final String EMPTY = "null";

	public static final Long ID_3146 = 3146L;
	public static final Long ID_60200 = 60200L;

	//审计事件类型-成功
	public static final Integer EVENT_RESULT_SUCCESS = 1;
	//审计事件类型-失败
	public static final Integer EVENT_RESULT_FAIL = 2;

	public static final String ASC = "asc";

	public static final String DESC = "desc";

	public static final String PARENT = "parent";
	public static final String OTHER = "other";

	public static final String DOUBLE_HYPHEN = "--";

	public static final String BOOLEAN_FLASE = "否";

    public static final String STRING_EMPTY_AS_DEFAULT = "--";

	public static final String CONSTANT_STANDARD_COUNT_BY_COMPANY = "超标分析 - 按管理单位统计";

	public static final String CONSTANT_NUMBER = "序号";
	public static final String CONSTANT_NUMBER_NAME = "变电站名称";

	public static final String CONSTANT_LINE_NAME = "监测点名称";

	public static final String CONSTANT_SYSTEM_NAME = "所属系统";
	public static final String CONSTANT_DISPATCH_LINE_NAME = "调度系统";
	public static final String CONSTANT_POWER_LINE_NAME = "电能质量系统";
	public static final String CONSTANT_METER_LINE_NAME = "计量系统";
	public static final String CONSTANT_ZHIKAN_LINE_NAME = "智瞰系统";

	public static final String BLANK_DATA = "--";
	public static final String MVA_UNIT = "MVA";

	public static final List<Integer> SENSITIVE_USER_LIST = Arrays.asList(2401, 2402, 2403, 2404, 2405, 2406,
			2407, 2408, 2409, 2410, 2411, 2412, 2413, 2414, 2415, 2416, 2417, 2418, 2419, 2420, 2421);
}

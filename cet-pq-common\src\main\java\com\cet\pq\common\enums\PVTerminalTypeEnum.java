package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * 	装置类型枚举
 * <AUTHOR>
 *
 */
public enum PVTerminalTypeEnum {
	//装置类型枚举
	PQ(1,"电能质量监测装置"),
	SMART_ELECTRICITY_METER(2,"智能电能表"),
	PV_GRID_CONNECTED_CIRCUIT_BREAKER(3,"光伏并网断路器"),
	PV_INVERTER(4,"光伏逆变器"),
	POWER_QUALITY_MANAGEMENT_EQUIPMENT(5,"电能质量治理设备"),
	POWER_QUALITY_MONITORING_TERMINAL(6,"电能质量监测终端"),
	OTHER(7,"其他"),
	;

	private Integer key;
	private String value;

	private PVTerminalTypeEnum(Integer key, String value) {
		this.key = key;
		this.value = value;
	}

	public static Integer getType(String strType) {
		for (PVTerminalTypeEnum terminalTypeEnum : PVTerminalTypeEnum.values()) {
			if(terminalTypeEnum.value.equals(strType)) {
				return terminalTypeEnum.key;
			}
		}
		throw new CommonManagerException("装置类型枚举值转化异常");
	}
	
	public static Integer getTypeNoThrows(String strType) {
		if (StringUtils.isEmpty(strType)) {
			return null;
		}
		for (PVTerminalTypeEnum terminalTypeEnum : PVTerminalTypeEnum.values()) {
			if(terminalTypeEnum.value.equals(strType)) {
				return terminalTypeEnum.key;
			}
		}
		return 0;
	}

	public static String getValueByKeyNoThrows(Integer key) {
		for (PVTerminalTypeEnum terminalTypeEnum : PVTerminalTypeEnum.values()) {
		    if (terminalTypeEnum.key.equals(key)) {
		    	return terminalTypeEnum.value;
			}
		}
		return StringUtils.EMPTY;
	}

	public Integer getKey() {
		return key;
	}

	public void setKey(Integer key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
}

package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 监测类型
 */
public enum MonitorModeEnum {

    monitoringmaintransformer("主变",1),
    monitoringline("线路",2),
    monitoringpowerdistributionarea("台区",3),
    monitoringtransformeruser("专变用户",4),
    monitoringelectricityuser("低压普通用户",5);

    MonitorModeEnum(String text, Integer id) {
        this.text = text;
        this.id = id;
    }

    private String text;
    private Integer id;

    public String getText() {
        return text;
    }

    public Integer getId() {
        return id;
    }

    public static Integer getMonitorSortEnumId(String name) {
        for (MonitorModeEnum monitorSortEnum : values()) {
            if (name.equals(monitorSortEnum.getText())) {
                return monitorSortEnum.getId();
            }
        }
        return null;
    }

    public static Integer getMonitorSortEnumImport(String name) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (MonitorModeEnum monitorSortEnum : values()) {
            if (name.equals(monitorSortEnum.getText())) {
                return monitorSortEnum.getId();
            }
        }
        return 0;
    }

    public static String getMonitorSortEnumText(Integer id) {
        if(id == null){
            return "";
        }
        for (MonitorModeEnum monitorSortEnum : values()) {
            if (id.equals(monitorSortEnum.getId())) {
                return monitorSortEnum.getText();
            }
        }
        return "";
    }
    public static Integer getIdByNameImport(String name) {
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (MonitorModeEnum monitorSortEnum : values()) {
            if (name.equals(monitorSortEnum.getText())) {
                return monitorSortEnum.getId();
            }
        }
        return 0;
    }
}

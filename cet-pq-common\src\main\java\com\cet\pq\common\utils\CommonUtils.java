package com.cet.pq.common.utils;

import com.cet.pq.common.annotation.FieldAnnotation;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.EnumOperationType;
import com.cet.pq.common.enums.*;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.model.Result;
import com.cet.pq.common.model.vo.UpdateContent;
import com.cet.pq.common.time.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class CommonUtils {
    /**
     * Map集合元素个数
     */
    public static final int MAP_INIT_SIZE_16 = 16;
    /**
     * Map集合元素个数
     */
    public static final int MAP_INIT_SIZE_2 = 2;
    /**
     * Map集合元素个数
     */
    public static final int MAP_INIT_SIZE_1 = 1;
    /**
     * Map集合元素个数
     */
    public static final int MAP_INIT_SIZE_4 = 4;
    /**
     * 整形向浮点型装换系数
     */
    public static final Double DOUBLE_CONVERSION_COEFFICIENT = 1.0;
    /**
     * 百分数转换
     */
    public static final int PERCENT_COEFFICIENT = 100;
    /**
     * 钱币单位换算，元到丝级的转换
     */
    public static final int MONEY_YUAN_TO_SI = 10000;
    /**
     * 本机ip，ipv4格式
     */
    public static final String LOCAL_IP = "127.0.0.1";
    /**
     * 本机ip，ipv6格式
     */
    public static final String LOCAL_IP_V6 = "0:0:0:0:0:0:0:1";


    /**
     * 本机ip，ipv6格式
     */
    public static final String GET_ERROR_LOG = "获取详细日志error";


    /**
     * 下载格式：application/x-download
     */
    public static final String APPLICATION_X_DOWNLOAD = "application/x-download";
    public static final String CONTENT_DISPOSITION = "Content-disposition";
    public static final String ATTACHMENT_FILENAME = "attachment; filename=";

    /**
     * 下载格式：application/pdf
     */
    public static final String APPLICATION_PDF = "application/pdf";
    /**
     * 下载格式：application/msexcel
     */
    public static final String APPLICATION_MSEXCEL = "application/vnd.ms-excel";
    /**
     * 物理量数据查询时组合数据id的起始值
     */
    public static final int QUANTITY_SETTING_BEGIN = 0;
    /**
     * 数据精度
     */
    public static final int PRECISION = 2;
    /**
     * 数据为null时，展示的内容
     */
    public static final String BLANK_STR = "--";

    /**
     * 将一组数据分组，每组n个元素
     *
     * @param source 要分组的数据源
     * @param n      每组n个元素
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> getGroupsOfSameSize(List<T> source, int n) {
        if (CollectionUtils.isEmpty(source) || n <= 0) {
            return null;
        }
        List<List<T>> result = new ArrayList<List<T>>();
        int remainder = source.size() % n;
        int size = (source.size() / n);
        for (int i = 0; i < size; i++) {
            List<T> subset = null;
            subset = source.subList(i * n, (i + 1) * n);
            result.add(subset);
        }
        if (remainder > 0) {
            List<T> subset = null;
            subset = source.subList(size * n, size * n + remainder);
            result.add(subset);
        }
        return result;
    }

    /**
     * 将一组数据平均分成n组
     *
     * @param source 要分组的数据源
     * @param n      平均分成n组
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> getGroupsOfAverageSize(List<T> source, int n) {
        List<List<T>> result = new ArrayList<List<T>>();
        int remainder = source.size() % n;
        int number = source.size() / n;
        int offset = 0;
        for (int i = 0; i < n; i++) {
            List<T> value = null;
            if (remainder > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remainder--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }

        return result;
    }

    /**
     * 判断条件是否有效
     * 判断依据：当前集合不为空，并且集合只有一个值的时候不为0
     *
     * @param condition
     * @param <T>
     * @return
     */
    public static <T extends Number> boolean isConditionValid(List<T> condition) {
        if (CollectionUtils.isNotEmpty(condition)) {
            if (condition.size() == 1 && condition.get(0).equals(0)) {
                return false;
            }
            return true;
        }

        return false;
    }

    /**
     * 格式化Double类型的数值
     *
     * @param val
     * @param precision
     * @return
     */
    public static String formatDouble(Double val, Integer precision, String blankStr) {
        if (val == null || Double.isNaN(val)) {
            return blankStr;
        }

        if (precision == null) {
            return val.toString();
        }
        return String.format("%." + precision + "f", val);
    }

    /**
     * 计算两者的比率
     *
     * @param now
     * @param pre
     * @return
     */
    public static Double calcRate(Double now, Double pre) {
        if (now == null || pre == null) {
            return null;
        }

        return (now - pre) / pre;
    }

    /**
     * 计算两者的比率
     *
     * @param now
     * @param pre
     * @return
     */
    public static Double calcRate(Long now, Long pre) {
        if (now == null || pre == null) {
            return 0.0d;
        }
        if (pre == 0) {
            return 0.0d;
        }
        return now * DOUBLE_CONVERSION_COEFFICIENT / pre;
    }

    /**
     * 计算两个Double类型数值的运算
     *
     * @param v1            数值1
     * @param v2            数值2，该值为被除数、被减数或者计算比率的基准值
     * @param operationType 运算类型
     * @return 运算结果，如果两个计算值有任意为null或者计算方式不在给的枚举中，那么返回NaN
     */
    public static Double calcDouble(Double v1, Double v2, int operationType) {
        if (v1 == null || v2 == null) {
            return Double.NaN;
        }

        if (EnumOperationType.ADD.getId() == operationType) {
            return v1 + v2;
        } else if (EnumOperationType.SUBTRACT.getId() == operationType) {
            return v1 - v2;
        } else if (EnumOperationType.DIVISION.getId() == operationType) {
            if (v2 == 0) {
                return Double.NaN;
            }
            return v1 / v2;
        } else if (EnumOperationType.MULTIPLICATION.getId() == operationType) {
            return v1 * v2;
        } else if (EnumOperationType.RATE.getId() == operationType) {
            if (v2 == 0) {
                return Double.NaN;
            }
            // 当v2为0的时候会返回Infinity
            return (v1 - v2) / v2;
        } else {
            return Double.NaN;
        }
    }

    /**
     * 计算两个Double类型数值的运算
     *
     * @param v1            数值1
     * @param v2            数值2，该值为被除数、被减数或者计算比率的基准值
     * @param operationType 运算类型
     * @return 运算结果，如果两个计算值有任意为null或者计算方式不在给的枚举中，那么返回NaN
     */
    public static Double calcDoubleOrElse(Double v1, Double v2, int operationType, Double other) {
        if (v1 == null || v2 == null) {
            return other;
        }

        if (EnumOperationType.ADD.getId() == operationType) {
            return v1 + v2;
        } else if (EnumOperationType.SUBTRACT.getId() == operationType) {
            return v1 - v2;
        } else if (EnumOperationType.DIVISION.getId() == operationType) {
            if (v2 == 0) {
                return other;
            }
            return v1 / v2;
        } else if (EnumOperationType.MULTIPLICATION.getId() == operationType) {
            return v1 * v2;
        } else if (EnumOperationType.RATE.getId() == operationType) {
            if (v2 == 0) {
                return other;
            }
            // 当v2为0的时候会返回Infinity
            return (v1 - v2) / v2;
        } else {
            return other;
        }
    }

    /**
     * 计算两个Double类型数值的运算
     *
     * @param v1            数值1
     * @param v2            数值2，该值为被除数、被减数或者计算比率的基准值
     * @param operationType 运算类型
     * @return 运算结果，如果两个计算值有任意为null或者计算方式不在给的枚举中，那么返回NaN
     */
    public static Double calInt(Integer v1, Integer v2, int operationType) {
        if (v1 == null || v2 == null) {
            return null;
        }

        if (EnumOperationType.ADD.getId() == operationType) {
            return (double)v1 + v2;
        } else if (EnumOperationType.SUBTRACT.getId() == operationType) {
            return (double)v1 - v2;
        } else if (EnumOperationType.DIVISION.getId() == operationType) {
            if (v2 == 0) {
                return null;
            }
            return (double)v1 / v2;
        } else if (EnumOperationType.MULTIPLICATION.getId() == operationType) {
            return (double)v1 * v2;
        } else if (EnumOperationType.RATE.getId() == operationType) {
            if (v2 == 0) {
                return null;
            }
            // 当v2为0的时候会返回Infinity
            return v1 - (double)v2 / v2;
        } else {
            return null;
        }
    }

    /**
     * 计算两个Long类型数值的运算
     *
     * @param v1            数值1
     * @param v2            数值2，该值为被除数、被减数或者计算比率的基准值
     * @param operationType 运算类型
     * @return 运算结果
     */
    public static Long calcLong(Long v1, Long v2, int operationType) {
        if (v1 == null || v2 == null) {
            return null;
        }

        if (EnumOperationType.ADD.getId() == operationType) {
            return v1 + v2;
        } else if (EnumOperationType.SUBTRACT.getId() == operationType) {
            return v1 - v2;
        } else if (EnumOperationType.DIVISION.getId() == operationType) {
            return v1 / v2;
        } else if (EnumOperationType.MULTIPLICATION.getId() == operationType) {
            return v1 * v2;
        } else {
            return null;
        }
    }

    public static boolean isDoubleValid(Double val) {
        return val != null && !Double.isNaN(val);
    }

    public static Double formatNumber(Object data, Integer precision) {
        return data == null ? null : ParseDataUtil.parseDouble(String.format("%." + precision + "f", ParseDataUtil.parseDouble(data)));
    }

    public static String formatNumberString(Object data, Integer precision) {
        if (data == null){
            return null;
        }
        Double doubleValue = ParseDataUtil.parseDouble(String.format("%." + precision + "f", ParseDataUtil.parseDouble(data)));
        String formattedValue;
        // 判断是否为整数（小数部分为0）
        if (doubleValue % 1 == 0) {
            formattedValue = String.valueOf(doubleValue.intValue()); // 转换为整数格式
        } else {
            formattedValue = String.format("%.2f", doubleValue); // 保留两位小数
        }
        return formattedValue;
    }

    /**
     * @param list
     * @param pageNum
     * @param pageSize
     * @Description: list分页
     * @Author: gongtong
     **/
    public static <T> List<T> getListPaging(List<T> list, Integer pageNum, Integer pageSize) {
        if (list == null || list.size() <= 0 || pageNum == null || pageSize == null || pageSize>199999) {
            return new ArrayList<>(0);
        }
        //开始下标
        int startIndex = (pageNum - 1) * pageSize;
        //结束下标 subList()方法不包含结束下标的元素
        int endIndex = pageNum * pageSize;
        //list总条数
        int total = list.size();
        //总页数
        int pageCount = 0;
        //通过取余决定是否给总页数加1
        int num = total % pageSize;
        if (num == 0) {
            pageCount = total / pageSize;
        } else {
            pageCount = total / pageSize + 1;
        }
        //如果当前页是最后一页的话 要包含集合的最后一条数据，因为sublist方法本身结束的下标是不包含当前元素的
        if (pageNum == pageCount) {
            endIndex = total;
        }
        if (endIndex > list.size()) {
            return new ArrayList<>(0);
        }
        return list.subList(startIndex, endIndex);
    }

    /**
     * @param webVoltId
     * @Description: 获取基准短路容量
     * @Author: gongtong
     **/
    public static double getBaseCapacity(int webVoltId) {
        double baseCapacity = 100;//默认值100
        if (webVoltId <= CommonConstant.NUMBER_TWO  || webVoltId == CommonConstant.NUMBER_SEVEN) {
            baseCapacity = 10;
        } else if ((webVoltId >= CommonConstant.NUMBER_EIGHT && webVoltId <= CommonConstant.NUMBER_NINE) || webVoltId == CommonConstant.NUMBER_THREE) {
            baseCapacity = 100;
        } else if ((webVoltId >= CommonConstant.NUMBER_FORE  && webVoltId <= CommonConstant.NUMBER_FIVE) || webVoltId == CommonConstant.NUMBER_TEN) {
            baseCapacity = 250;
        } else if (webVoltId ==CommonConstant.NUMBER_ELEVEN) {
            baseCapacity = 500;
        } else if (webVoltId ==CommonConstant.NUMBER_SIX) {
            baseCapacity = 750;
        } else if (webVoltId >=CommonConstant.NUMBER_TWELVE) {
            baseCapacity = 2000;
        }
        return baseCapacity;
    }

    /**
     * 检查接口调用结果是否正确
     *
     * <AUTHOR>
     */
    public static <T> void checkResult(Result<T> res, String errorMsg) {
        if (res.getCode() != 0) {
            throw new CommonManagerException("接口调用错误，错误信息:" + errorMsg);
        }
    }

    /**
     * @Description: 获取修改日志
     * @return: java.lang.String
     **/
    public static String getUpdateLog(Object originalObject, Object object) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            Class originalClass = Class.forName(originalObject.getClass().getName());
            Field[] fields = originalClass.getDeclaredFields();
            extracted(originalObject, object, stringBuilder, fields);
        } catch (ClassNotFoundException | IllegalAccessException e) {
            log.error(GET_ERROR_LOG, e);
        }
        return stringBuilder.toString();
    }

    /**
     * @Description: 获取修改日志
     * @return: java.lang.String
     **/
    public static List<UpdateContent> getUpdateLog2(Object originalObject, Object object) {
        List<UpdateContent> result = new ArrayList<>();
        try {
            Class originalClass = Class.forName(originalObject.getClass().getName());
            Field[] fields = originalClass.getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(FieldAnnotation.class)) {
                    field.setAccessible(true);
                    if (!ParseDataUtil.parseString(field.get(originalObject)).equals(ParseDataUtil.parseString(field.get(object)))) {
                        Object originalValue = field.get(originalObject);
                        Object value = field.get(object);
                        //获取注解属性值
                        FieldAnnotation annotation = field.getAnnotation(FieldAnnotation.class);
                        result.add(setUpdatelog2(annotation.name(), annotation.type(), originalValue, value, annotation.method()));
                    }
                }
            }
        } catch (ClassNotFoundException | IllegalAccessException e) {
            log.error(GET_ERROR_LOG, e);
        }
        return result;
    }



    /**
     * @Description: 判断类型组装日志
     **/
    private static void setUpdatelog(StringBuilder stringBuilder, String name, Class type, Object originalValue, Object value, String method) {
        if (type == String.class) {
        } else if (type == Date.class) {
            originalValue = DateUtils.formatDate(ParseDataUtil.parseLong(originalValue), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
            value = DateUtils.formatDate(ParseDataUtil.parseLong(value), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
        } else if (type == Boolean.class) {
            originalValue = ParseDataUtil.transformBooleanToStr(originalValue);
            value = ParseDataUtil.transformBooleanToStr(value);
        } else if (type == MonitorTagEnums.class) {
            // MonitorTagEnums为字符串类型
            try {
                Method enumMethod = type.getDeclaredMethod(method, String.class);
                enumMethod.setAccessible(true);
                originalValue = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseString(originalValue)));
                value = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseString(value)));
            } catch (Exception e) {
                throw new CommonManagerException("MonitorTagEnums类型转换错误");
            }
        } else {
            //枚举类型
            try {
                Method enumMethod = type.getDeclaredMethod(method, Integer.class);
                enumMethod.setAccessible(true);
                originalValue = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseInteger(originalValue)));
                value = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseInteger(value)));
            } catch (Exception e) {
                throw new CommonManagerException("类型转换错误");
            }
        }
        stringBuilder.append(",").append(name).append(ParseDataUtil.parseString(originalValue)).append("修改为").append(ParseDataUtil.parseString(value));
    }

    // @AI-Generated-start

    /**
     * 当前类以及父类
     * @param originalObject
     * @param object
     * @return
     */
    public static String getUpdateLogFather(Object originalObject, Object object) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            Class<?> originalClass = originalObject.getClass();
            List<Field> allFields = new ArrayList<>();

            while (originalClass != null && !originalClass.equals(Object.class)) {
                Field[] declaredFields = originalClass.getDeclaredFields();
                allFields.addAll(Arrays.asList(declaredFields));
                originalClass = originalClass.getSuperclass();
            }

            Field[] fields = allFields.toArray(new Field[0]);

            extracted(originalObject, object, stringBuilder, fields);
        } catch (IllegalAccessException e) {
            log.error(GET_ERROR_LOG, e);
        }
        return stringBuilder.toString();
    } // @AI-Generated-end

    private static void extracted(Object originalObject, Object object, StringBuilder stringBuilder, Field[] fields) throws IllegalAccessException {
        for (Field field : fields) {
            if (field.isAnnotationPresent(FieldAnnotation.class)) {
                field.setAccessible(true);
                if (!ParseDataUtil.parseString(field.get(originalObject)).equals(ParseDataUtil.parseString(field.get(object)))) {
                    Object originalValue = field.get(originalObject);
                    Object value = field.get(object);
                    // 获取注解属性值
                    FieldAnnotation annotation = field.getAnnotation(FieldAnnotation.class);
                    setUpdatelog(stringBuilder, annotation.name(), annotation.type(), originalValue, value, annotation.method());
                }
            }
        }
    }

    /**
     * @Description: 判断类型组装日志
     **/
    private static UpdateContent setUpdatelog2(String name, Class type, Object originalValue, Object value, String method) {
        if (type == String.class) {
        } else if (type == Date.class) {
            originalValue = DateUtils.formatDate(ParseDataUtil.parseLong(originalValue), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
            value = DateUtils.formatDate(ParseDataUtil.parseLong(value), DateUtils.DATE_TO_STRING_DETAIAL_PATTERN);
        } else if (type == Boolean.class) {
            originalValue = ParseDataUtil.parseBoolean(originalValue) ? "是" : "否";
            value = ParseDataUtil.parseBoolean(value) ? "是" : "否";
        } else if (type == MonitorTagEnums.class) {
            // MonitorTagEnums为字符串类型
            try {
                Method enumMethod = type.getDeclaredMethod(method, String.class);
                enumMethod.setAccessible(true);
                originalValue = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseString(originalValue)));
                value = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseString(value)));
            } catch (Exception e) {
                throw new CommonManagerException("MonitorTagEnums类型转换错误");
            }
        } else if(type == Double.class){
            originalValue = getDoubleString(originalValue);
            value = getDoubleString(value);
        }
        else {
            //枚举类型
            try {
                Method enumMethod = type.getDeclaredMethod(method, Integer.class);
                enumMethod.setAccessible(true);
                originalValue = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseInteger(originalValue)));
                value = ParseDataUtil.parseString(enumMethod.invoke(null, ParseDataUtil.parseInteger(value)));
            } catch (Exception e) {
                throw new CommonManagerException("类型转换错误");
            }
        }
        return new UpdateContent(name, ParseDataUtil.parseString(originalValue), ParseDataUtil.parseString(value));
    }

    static Object getDoubleString(Object doubleInObject) {
        if(Objects.isNull(doubleInObject)) {
            return null;
        }
        double primitiveDouble = ParseDataUtil.parseDouble(doubleInObject);
        Integer integerInObject = (int) primitiveDouble;
        if(1d*integerInObject == primitiveDouble) {
            return integerInObject;
        }
        return doubleInObject;
    }

    public static Date delphiDateTimeToSecondDate(double dateTime) {
        Calendar calendar = Calendar.getInstance();
        int gmtOffset = calendar.get(Calendar.ZONE_OFFSET) + calendar.get(Calendar.DST_OFFSET);
        long timeLong = (long) (dateTime * 86400000L) - (25569 * 86400000L) - gmtOffset;
        long millis = timeLong % 1000;
        if (millis != 0) {
            // 进位值，大于等于补齐到1秒，小于则舍去毫秒
            int carryValue = 500;
            if (millis >= carryValue) {
                timeLong = timeLong - millis + 1000;
            } else {
                timeLong = timeLong - millis;
            }
        }
        return new Date(timeLong);
    }

    public static Boolean checkWord(String str) {
        Pattern pattern = compile("^[a-zA-Z0-9]*$");
        Matcher m = pattern.matcher(str);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    public static String getExportFormatString(Object obj) {
        if(obj instanceof String && StringUtils.isEmpty((String) obj)) {
            return CommonConstant.DOUBLE_HYPHEN;
        }
        if(Objects.isNull(obj)) {
            return CommonConstant.DOUBLE_HYPHEN;
        }
        return String.valueOf(obj);
    }
}


package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 9:24
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class EvtErrorCorrectionPart1VO {

    /**
     * 数值
     */
    private Object value;

    /**
     * 类型：0：HRU3电流换算值(A)，1：HRU3电流换算值(B)，2：HRU3电流换算值(c)，
     * 3：HRU3测量值(A)，4：HRU3测量值(B)，5：HRU3测量值(C)，
     * 6：I3测量值(A)，7：I3测量值(B)，8：I3测量值(C)，
     * 9：Urms_A，10：Urms_B，11：Urms_C，
     * 12：负序电压不平衡度，13：零序电压不平衡度
     */
    private Integer type;

    /**
     * 检测时间
     */
    private Long logtime;

}

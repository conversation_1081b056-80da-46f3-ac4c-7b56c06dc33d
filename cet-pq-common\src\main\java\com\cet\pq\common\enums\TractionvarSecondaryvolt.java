package com.cet.pq.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description: 牵引变二次电压
 * @date 2021/6/2 16:44
 */
public enum TractionvarSecondaryvolt {
    //牵引变二次电压
    VOLT_110(1, "27.5KV"),
    VOLT_220(2, "2*27.5KV"),
    VOLT_330(3, "55KV");

    private Integer id;
    private String text;

    TractionvarSecondaryvolt(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public static String getTextByIdNoThrows(Integer id) {
        for (TractionvarSecondaryvolt tractionvarSecondaryvolt : TractionvarSecondaryvolt.values()) {
            if (tractionvarSecondaryvolt.id.equals(id)) {
                return tractionvarSecondaryvolt.text;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getIdByTextNoThrows(String text) {
        for (TractionvarSecondaryvolt tractionvarPrimaryvolt : TractionvarSecondaryvolt.values()) {
            if (tractionvarPrimaryvolt.text.equals(text)) {
                return tractionvarPrimaryvolt.id;
            }
        }
        return null;
    }
}

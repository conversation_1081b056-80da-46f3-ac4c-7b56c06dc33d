package com.cet.pq.common.utils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import com.cet.pq.common.enums.MonitorSortEnum;
import com.cet.pq.common.enums.MonitorTypeEnum;
import com.cet.pq.common.model.ConditionBlock;
import com.cet.pq.common.model.ConditionBlockCompose;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.cet.pq.common.constant.ColumnName;
import com.cet.pq.common.constant.CommonConstant;
import com.cet.pq.common.constant.TableName;
import com.cet.pq.common.exception.CommonManagerException;
import com.cet.pq.common.handle.SessionHandler;
import com.cet.pq.common.model.SingleModelConditionDTO;
import com.cet.pq.common.model.auth.ModelNode;

/**
 * 统计树帮助类
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class CountTreeUtils {

	private static final String UNKNOW_VENDOR = "未知";

	public static String getAreaModelByCompanyModel(String rootNode) {
		String areaModel = null;
		if (TableName.NETCOMPANY.equals(rootNode)) {
			areaModel = TableName.COUNTRY;
		}else if(TableName.PROVINCECOMPANY.equals(rootNode)) {
			areaModel = TableName.PROVINCE;
		}else if(TableName.CITYCOMPANY.equals(rootNode)){
			areaModel = TableName.CITY;
		}else if(TableName.COUNTYCOMPANY.equals(rootNode)){
			areaModel = TableName.DISTRICT;
		}else {
			throw new CommonManagerException("请传入正确的单位节点");
		}
		return areaModel;
	}

	/**
	 * 根据用户的根节点获取直接下层节点
	 *
	 * <AUTHOR>
	 * @param rootNode
	 * @return
	 */
	public static String getFirstSubModelLabel(String rootNode) {
		String firstSubLayerModel = null;
		if (TableName.NETCOMPANY.equals(rootNode)) {
			firstSubLayerModel = TableName.PROVINCECOMPANY;
		} else if (TableName.COUNTRY.equals(rootNode)){
			firstSubLayerModel = TableName.PROVINCE;
		} else if (TableName.PROVINCECOMPANY.equals(rootNode)) {
			firstSubLayerModel = TableName.CITYCOMPANY;
		} else if  (TableName.PROVINCE.equals(rootNode)){
			firstSubLayerModel = TableName.CITY;
		} else if (TableName.CITYCOMPANY.equals(rootNode)) {
			firstSubLayerModel = TableName.COUNTYCOMPANY;
		} else if (TableName.CITY.equals(rootNode)){
			firstSubLayerModel = TableName.DISTRICT;
		} else {
			throw new CommonManagerException("请传入正确的顶层节点模型名称");
		}
		return firstSubLayerModel;
	}

	/**
	 * 根据统计口径获取统计的状态范围（国网：在线 + 故障，省公司：在线 + 故障 + 检修 + 调试）
	 *
	 * @param counttype
	 * @return
	 */
	public static List<Integer> getStatusRangByCountType(Integer counttype) {
		List<Integer> onRunStatusValueList = null;
		if (counttype.equals(CommonConstant.NUMBER_ONE)) {
			// 国网统计口径
			onRunStatusValueList = Arrays.asList(CommonConstant.NUMBER_ONE, CommonConstant.NUMBER_FIVE);
		} else {
			// 省公司统计口径
			onRunStatusValueList = Arrays.asList(CommonConstant.NUMBER_ONE, CommonConstant.NUMBER_TWO, CommonConstant.NUMBER_THREE, CommonConstant.NUMBER_FIVE);
		}
		return onRunStatusValueList;
	}

	/**
	 * 根据统计口径获取在线状态范围（国网：在线，省公司：在线 + 检修 + 调试）
	 * @param counttype
	 * @return
	 */
	public static List<Integer> getOnlineStatusRangeType(Integer counttype){
		List<Integer> onLineStatusValueList = null;
		if (counttype.equals(CommonConstant.NUMBER_ONE)) {
			// 国网统计口径
			onLineStatusValueList = Arrays.asList(CommonConstant.NUMBER_ONE);
		} else {
			// 省公司统计口径
			onLineStatusValueList = Arrays.asList(CommonConstant.NUMBER_ONE, CommonConstant.NUMBER_TWO, CommonConstant.NUMBER_THREE);
		}
		return onLineStatusValueList;
	}

	/**
	 * 根据用户权限获取统计树
	 *
	 * @param isupload
	 * @param isOfflineTest
	 * @return
	 */
	public static Map<String, Object> getCountTree(Boolean isupload, Boolean isOfflineTest) {
		ModelNode userModelNode = SessionHandler.getUserModel();
		Long modelId = userModelNode.getId();
		String modelLabel = userModelNode.getModelLabel();
		if (TableName.NETCOMPANY.equals(modelLabel)) {
			return getTreeForNet(modelId, isupload, isOfflineTest);
		} else if (TableName.PROVINCECOMPANY.equals(modelLabel)) {
			return getTreeForProvince(modelId, isupload, isOfflineTest);
		} else if (TableName.CITYCOMPANY.equals(modelLabel)) {
			return getTreeForCity(modelId, isupload, isOfflineTest);
		}
		throw new CommonManagerException("用户未分配用户区域，请联系管理员分配用户组");
	}

	/**
	 * 从统计树种获取所有的变电站树
	 *
	 * @param countTree
	 * @return
	 */
	public static List<Map<String, Object>> getSubstationTree(Map<String, Object> countTree) {
		ModelNode modelMode = SessionHandler.getUserModel();
		String rootLabel = modelMode.getModelLabel();
		String subLayerLabel = getFirstSubModelLabel(rootLabel);
		if (null == countTree) {
			countTree = getCountTree(null, Boolean.FALSE);
		}
		// 1、从单位树中获取变电站节点
		List<Map<String, Object>> substationTreeList = new ArrayList<>();
		List<Map<String, Object>> subLayerList =new ArrayList<>();
		if (countTree != null && countTree.get(subLayerLabel.concat(ColumnName.MODEL1)) != null) {
			subLayerList = ParseDataUtil.parseList(countTree.get(subLayerLabel.concat(ColumnName.MODEL1)));
		}

		if (CollectionUtils.isNotEmpty(subLayerList)) {
			subLayerList.forEach(subLayer -> {
				List<Map<String, Object>> substationList = ParseDataUtil.parseList(subLayer.get(ColumnName.SUBSTATION_MODEL));
				substationTreeList.addAll(substationList);
			});
		}
		List<Map<String, Object>> substationList = new ArrayList<>();
		if(countTree!=null && countTree.get(ColumnName.SUBSTATION_MODEL)!=null){
			substationList = ParseDataUtil.parseList(countTree.get(ColumnName.SUBSTATION_MODEL));
		}
		substationTreeList.addAll(substationList);
		return substationTreeList;
	}

	/**
	 * 从统计树种获取监测点树的集合，并记录下级单位个监测点的关联关系
	 *
	 * @param countTree                          统计树
	 * @param subLayerLabel                      写成模型名称
	 * @param subLayer2LineRelation              直接写成和监测点的对应关系
	 * @return
	 */
	public static List<Map<String, Object>> getLineTreeFromCountTree(Map<String, Object> countTree, String subLayerLabel, Map<String, List<Long>> subLayer2LineRelation) {
		List<Map<String, Object>> lineList = new ArrayList<>();
		List<Map<String, Object>> subLayerList = ParseDataUtil.parseList(countTree.get(subLayerLabel.concat("_model")));
		if (CollectionUtils.isNotEmpty(subLayerList)) {
			subLayerList.forEach(subLayer -> {
				List<Long> lineIdList = new ArrayList<>();
				Long subLayerId = ParseDataUtil.parseLong(subLayer.get(ColumnName.ID));
				String subLayerName = ParseDataUtil.parseString(subLayer.get(ColumnName.NAME));
				String key = subLayerId + "_" + subLayerName;
				List<Map<String, Object>> substationList = ParseDataUtil.parseList(subLayer.get("substation_model"));
				substationList.forEach(substation -> {
					List<Map<String, Object>> templineList = ParseDataUtil.parseList(substation.get("line_model"));
					List<Long> tempLineIdList = templineList.stream().map(line -> ParseDataUtil.parseLong(line.get(ColumnName.ID))).collect(Collectors.toList());
					lineIdList.addAll(tempLineIdList);
					lineList.addAll(templineList);
					if(substation.containsKey(TableName.POWERDISTRIBUTIONAREA + ColumnName.MODEL1)){
						List<Map<String, Object>> powerDistributionAreaList = ParseDataUtil.parseList(substation.get(TableName.POWERDISTRIBUTIONAREA + ColumnName.MODEL1));
						List<Map<String, Object>> powerDistributionAreaLineList = new ArrayList<>();
						powerDistributionAreaList.forEach(power->{
							if(power.containsKey(TableName.LINE + ColumnName.MODEL1)){
								powerDistributionAreaLineList.addAll(ParseDataUtil.parseList(power.get(TableName.LINE + ColumnName.MODEL1)));
							}
						});
						List<Long> powerDistributionAreaLineIdList = powerDistributionAreaLineList.stream().map(line -> ParseDataUtil.parseLong(line.get(ColumnName.ID))).collect(Collectors.toList());
						lineIdList.addAll(powerDistributionAreaLineIdList);
						lineList.addAll(powerDistributionAreaLineList);
					}
				});
				subLayer2LineRelation.put(key, lineIdList);
			});
		} else {
			List<Map<String, Object>> substationList = ParseDataUtil.parseList(countTree.get("substation_model"));
			substationList.forEach(substation -> {
				List<Map<String, Object>> templineList = ParseDataUtil.parseList(substation.get("line_model"));
				lineList.addAll(templineList);
			});
		}
		return lineList;
	}

	/**
	 * 统计树种获取所有终端，并记录下级节和终端的关系,适合按照单位统计
	 * @param countTree
	 * @param subLayerNode
	 * @param subLayer2PqterminalRelation
	 * @return
	 */
	public static List<Map<String, Object>> getTermialList(Map<String, Object> countTree,String subLayerNode,Map<String, List<Long>> subLayer2PqterminalRelation){
		List<Map<String, Object>> substationTreeList = ParseDataUtil.parseList(countTree.get(TableName.SUBSTATION + "_model"));
		List<Map<String,Object>> terminalList = getTerminalListFromSubstation(substationTreeList);
		List<Map<String, Object>> subLayerTreeList = ParseDataUtil.parseList(countTree.get(subLayerNode + "_model"));
		subLayerTreeList.forEach(subLayerTree->{
			Long id = ParseDataUtil.parseLong(subLayerTree.get(ColumnName.ID));
			String name = ParseDataUtil.parseString(subLayerTree.get(ColumnName.NAME));
			String key = id + "_" + name;
			List<Map<String,Object>> tempSubstationTreeList = ParseDataUtil.parseList(subLayerTree.get(TableName.SUBSTATION + "_model"));
			List<Map<String,Object>> tempTerminalList = getTerminalListFromSubstation(tempSubstationTreeList);
			List<Long> terminalIdList = tempTerminalList.stream().map(t->ParseDataUtil.parseLong(t.get(ColumnName.ID))).collect(Collectors.toList());
			subLayer2PqterminalRelation.put(key, terminalIdList);
			terminalList.addAll(tempTerminalList);
		});
		return terminalList;
	}


	/**
	 * 变电站下获取所有的终端
	 * @param substationTreeList
	 * @return
	 */
	private static List<Map<String, Object>> getTerminalListFromSubstation(List<Map<String, Object>> substationTreeList){
		List<Map<String,Object>> terminalList = new ArrayList<>();
		substationTreeList.forEach(substationTree->{
			List<Map<String,Object>> tempTerminalList = ParseDataUtil.parseList(substationTree.get(TableName.PQTERMINAL + "_model"));
			terminalList.addAll(tempTerminalList);
		});
		return terminalList;
	}


	/**
	 * 监测点根据厂家进行分组
	 *
	 * @return
	 */
	public static Map<String, List<Long>> getVendorLineMap(Map<String, Object> countTree) {
		if (null == countTree) {
			countTree = getCountTree(null, Boolean.FALSE);
		}
		log.info("time cursor 0-2 {}", new Date().getTime());
		ModelNode userNode = SessionHandler.getUserModel();
		String rootNode = userNode.getModelLabel();
		String subLayerLabel = getFirstSubModelLabel(rootNode);
		log.info("time cursor 0-3 {}", new Date().getTime());
		List<Map<String, Object>> lineList = getLineTreeFromCountTree(countTree, subLayerLabel, new ConcurrentHashMap<>());
		log.info("time cursor 0-4 {}", new Date().getTime());
		List<Map<String, Object>> pqterminalList =getTermialList(countTree, subLayerLabel, new ConcurrentHashMap<>());
		// 2、监测点根据终端厂商进行分组
		Map<String, List<Long>> vendorGroup = new ConcurrentHashMap<>();
		log.info("time cursor 0-5 {}", new Date().getTime());
		// 排除非电能质量测点
		List<Map<String, Object>> pqLineList = lineList.stream().filter(l -> ParseDataUtil.parseInteger(l.get(ColumnName.MONITORTYPE)) < 5).collect(Collectors.toList());
		// 终端只保留电能质量终端并预处理
		log.info("time cursor 0-6 {}", new Date().getTime());
		Map<Long, Map<String, Object>> pqTerminalMap = pqterminalList.stream()
				.collect(Collectors.toMap(t -> ParseDataUtil.parseLong(t.get(ColumnName.ID)), t -> t));
		log.info("time cursor 0-7 {}", new Date().getTime());
		for (Map<String, Object> line : pqLineList) {
			Long lineTerminalId = ParseDataUtil.parseLong(line.get(ColumnName.PQ_TERMINAL_ID));
			Map<String, Object> terminalMap = pqTerminalMap.get(lineTerminalId);
			String vendor = terminalMap == null ? UNKNOW_VENDOR : ParseDataUtil.parseString(terminalMap.get(ColumnName.VENDOR));
			vendor = StringUtils.isEmpty(vendor) ? UNKNOW_VENDOR : vendor;
			log.debug("time cursor 0-8 {}", new Date().getTime());
			setVendorGroup(vendor, ParseDataUtil.parseLong(line.get("id")), vendorGroup);
			log.debug("time cursor 0-9 {}", new Date().getTime());
		}
		log.info("time cursor 0-10 {}", new Date().getTime());
		return vendorGroup;
	}


	/**
	 * 单位树转行政区域树，网级行政区域开始
	 *
	 * @param countTree
	 * @return
	 */
	public static List<Map<String, Object>> transCompany2AreaTree(Map<String, Object> countTree) {
		String rootNode = SessionHandler.getUserModel().getModelLabel();
		// 1、从管理单位树中获取到所有的变电站节点
		List<Map<String, Object>> companySubstaionTree = getSubstationTree(countTree);
		// 2、按照用户组等级获取行政区域树
		List<SingleModelConditionDTO> subConditions = null;
		if (rootNode.equals(TableName.NETCOMPANY)) {
			// 行政区域树查询到变电站层级（行政网级-省级-市级-县级-变电站）
			subConditions = Arrays.asList(new SingleModelConditionDTO(TableName.PROVINCE), new SingleModelConditionDTO(TableName.CITY),
					new SingleModelConditionDTO(TableName.DISTRICT), new SingleModelConditionDTO(TableName.SUBSTATION));
			List<Map<String, Object>> netTreeList = ModelServiceUtils.getInterRelations(null, TableName.COUNTRY, null, null, subConditions, Boolean.FALSE);
			return netTreeList;
		} else if (rootNode.equals(TableName.PROVINCECOMPANY)) {
			// 行政区域树查询到变电站层级（省级-市级-县级-变电站）
			subConditions = Arrays.asList(new SingleModelConditionDTO(TableName.CITY), new SingleModelConditionDTO(TableName.DISTRICT),
					new SingleModelConditionDTO(TableName.SUBSTATION));
			List<Map<String, Object>> provinceTreeList = ModelServiceUtils.getInterRelations(null, TableName.PROVINCE, null, null, subConditions, Boolean.FALSE);
			replaceSubstationForProvince(provinceTreeList, companySubstaionTree);
			return provinceTreeList;
		} else {
			// 行政区域树查询到变电站层级（市级-县级-变电站）
			subConditions = Arrays.asList(new SingleModelConditionDTO(TableName.DISTRICT), new SingleModelConditionDTO(TableName.SUBSTATION));
			List<Map<String, Object>> cityTreeList = ModelServiceUtils.getInterRelations(null, TableName.CITY, null, null, subConditions, Boolean.FALSE);
			replaceSubstationForCity(cityTreeList, companySubstaionTree);
			return cityTreeList;
		}
	}

	private static void replaceSubstationForProvince(List<Map<String, Object>> provinceTreeList, List<Map<String, Object>> companySubstaionTree) {
		// 4、获取省级节点，省级阶段下不会有变电站
		Iterator<Map<String, Object>> provinceIt = provinceTreeList.iterator();
		while (provinceIt.hasNext()) {
			Map<String, Object> provinceTree = provinceIt.next();
			// 获取地市节点
			List<Map<String, Object>> cityTreeList = ParseDataUtil.parseList(provinceTree.get(TableName.CITY + "_model"));
			replaceSubstationForCity(cityTreeList, companySubstaionTree);
			// 省级行政区域下没有地级分支，移除省级分支
			if (CollectionUtils.isEmpty(cityTreeList)) {
				provinceIt.remove();
			} else {
				provinceTree.put("city_model", cityTreeList);
			}
		}
	}

	private static void replaceSubstationForCity(List<Map<String, Object>> cityTreeList, List<Map<String, Object>> companySubstaionTree) {
		Iterator<Map<String, Object>> cityIt = cityTreeList.iterator();
		// 替换地市下的变电站节点
		while (cityIt.hasNext()) {
			Map<String, Object> cityTree = cityIt.next();
			// 地市下有变电站-转化变电站树
			List<Map<String, Object>> citySubstationTree = ParseDataUtil.parseList(cityTree.get(TableName.SUBSTATION + "_model"));
			if (CollectionUtils.isNotEmpty(citySubstationTree)) {
				citySubstationTree = replaceSubstationTree(citySubstationTree, companySubstaionTree);
				cityTree.put("substation_model", citySubstationTree);
			}
			// 地市下是有县级行政-转化县级行政区域下的变电站树
			List<Map<String, Object>> countyTreeList = ParseDataUtil.parseList(cityTree.get(TableName.DISTRICT + "_model"));
			Iterator<Map<String, Object>> countyIt = countyTreeList.iterator();
			while (countyIt.hasNext()) {
				Map<String, Object> countyTree = countyIt.next();
				List<Map<String, Object>> countySubstationTree = ParseDataUtil.parseList(countyTree.get(TableName.SUBSTATION + "_model"));
				if (CollectionUtils.isNotEmpty(countySubstationTree)) {
					countySubstationTree = replaceSubstationTree(countySubstationTree, companySubstaionTree);
					countyTree.put("substation_model", countySubstationTree);
				} else {
					// 县级市下没有变电站，移除县级分支
					countyIt.remove();
				}
			}
			if (CollectionUtils.isNotEmpty(countyTreeList)) {
				cityTree.put("county_model", countyTreeList);
			}
			// 地市下没有变电站也没有县级分支，移除市级分支
			if (CollectionUtils.isEmpty(citySubstationTree) && CollectionUtils.isEmpty(countyTreeList)) {
				cityIt.remove();
			}
		}

	}

	/**
	 * 代替变电站树
	 *
	 * @param areaSubstationTree
	 * @param companySubstaionTree
	 * @return
	 */
	private static List<Map<String, Object>> replaceSubstationTree(List<Map<String, Object>> areaSubstationTree, List<Map<String, Object>> companySubstaionTree) {
		List<Map<String, Object>> tempSubstationTree = new ArrayList<>();
		Iterator<Map<String, Object>> it = areaSubstationTree.iterator();
		while (it.hasNext()) {
			Map<String, Object> substation = it.next();
			Long substationId = ParseDataUtil.parseLong(substation.get(ColumnName.ID));
			List<Map<String, Object>> filter = companySubstaionTree.stream().filter(s -> ParseDataUtil.parseLong(s.get(ColumnName.ID)).longValue() == substationId.longValue())
					.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(filter)) {
				it.remove();
			} else {
				it.remove();
				tempSubstationTree.addAll(filter);
			}
		}
		return tempSubstationTree;
	}

	/**
	 * 网级行政区下获取监测点集合
	 *
	 * @param netAreaTree
	 * @param subLayer2LineRelation
	 * @return
	 */
	public static List<Map<String, Object>> getLineListForNetTree(Map<String, Object> netAreaTree, Map<String, List<Long>> subLayer2LineRelation) {
		List<Map<String, Object>> provinceAreaTreeList = ParseDataUtil.parseList(netAreaTree.get(TableName.PROVINCE + "_model"));
		List<Map<String, Object>> lineList = new ArrayList<>();
		provinceAreaTreeList.forEach(provinceAreaTree -> {
			Long id = ParseDataUtil.parseLong(provinceAreaTree.get(ColumnName.ID));
			String name = ParseDataUtil.parseString(provinceAreaTree.get(ColumnName.NAME));
			String key = id + "_" + name;
			List<Map<String, Object>> tempLineList = getLineListForProvinceTree(Arrays.asList(provinceAreaTree), new ConcurrentHashMap<>(),null);
			List<Long> lineIdList = tempLineList.stream().map(s -> ParseDataUtil.parseLong(s.get(ColumnName.ID))).collect(Collectors.toList());
			lineList.addAll(tempLineList);
			subLayer2LineRelation.put(key, lineIdList);
		});
		return lineList;
	}

	/**
	 * 省级行政区下获取监测点集合
	 *
	 * @param
	 * @param subLayer2LineRelation
	 * @return
	 */
	public static List<Map<String, Object>> getLineListForSubstationTree(List<Map<String, Object>> cityAreaTreeList
			, Map<String, List<Long>> subLayer2LineRelation,Map<Long, Double[]> coordinateMap,Map<Long, String> voltclassMap ) {
		List<Map<String, Object>> districtAreaTreeList = new ArrayList<>();
		List<Map<String, Object>> substationTreeList = new ArrayList<>();
		cityAreaTreeList.forEach(cityAreaTree -> {
			List<Map<String, Object>> tempDistrictTreeList = ParseDataUtil.parseList(cityAreaTree.get(TableName.DISTRICT + "_model"));
			districtAreaTreeList.addAll(tempDistrictTreeList);
			List<Map<String, Object>> tempSubstationTreeList = ParseDataUtil.parseList(cityAreaTree.get(TableName.SUBSTATION + "_model"));
			substationTreeList.addAll(tempSubstationTreeList);
		});
		if (CollectionUtils.isNotEmpty(districtAreaTreeList)) {
			districtAreaTreeList.forEach(districtTree -> {
				List<Map<String, Object>> tempSubstationList = ParseDataUtil.parseList(districtTree.get(TableName.SUBSTATION + "_model"));
				for (Map<String, Object> substation : tempSubstationList) {
					String key = substation.get("id") + "_" + substation.get("name");
					List<Map<String, Object>> substationList = new ArrayList<>();
					substationList.add(substation);
					List<Map<String, Object>> lineList = getLineListFromSubstationTree(substationList);
					List<Long> lineIdList = lineList.stream().map(s -> ParseDataUtil.parseLong(s.get(ColumnName.ID))).collect(Collectors.toList());
					subLayer2LineRelation.put(key, lineIdList);


					Double[] coordinates = new Double[2];
					coordinates[0] = ParseDataUtil.parseDouble(substation.get("gislongitude"));
					coordinates[1] = ParseDataUtil.parseDouble(substation.get("gislatitude"));
					coordinateMap.put(ParseDataUtil.parseLong(substation.get("id")),coordinates);
					voltclassMap.put(ParseDataUtil.parseLong(substation.get("id")),ParseDataUtil.parseString(substation.get("voltclass$text")));
				}
				substationTreeList.addAll(tempSubstationList);
			});
		}
		return getLineListFromSubstationTree(substationTreeList);
	}

	/**
	 * 省级行政区下获取监测点集合
	 *
	 * @param provinceAreaTreeList
	 * @param subLayer2LineRelation
	 * @return
	 */
	public static List<Map<String, Object>> getLineListForProvinceTree(List<Map<String, Object>> provinceAreaTreeList, Map<String, List<Long>> subLayer2LineRelation
			,  Map<String, Map<String, List<Long>>> subLayer3LineRelation) {
		List<Map<String, Object>> cityAreaTreeList = new ArrayList<>();
		List<Map<String, Object>> lineList = new ArrayList<>();
		provinceAreaTreeList.forEach(provinceAreaTree -> {
			List<Map<String, Object>> tempCityTreeList = ParseDataUtil.parseList(provinceAreaTree.get(TableName.CITY + "_model"));
			cityAreaTreeList.addAll(tempCityTreeList);
		});
		cityAreaTreeList.forEach(cityAreaTree -> {
			Long id = ParseDataUtil.parseLong(cityAreaTree.get(ColumnName.ID));
			String name = ParseDataUtil.parseString(cityAreaTree.get(ColumnName.NAME));
			String key = id + "_" + name;
			List<Map<String, Object>> tempLineList = getLineListForCityTree(Arrays.asList(cityAreaTree), new ConcurrentHashMap<>());
			List<Long> lineIdList = tempLineList.stream().map(s -> ParseDataUtil.parseLong(s.get(ColumnName.ID))).collect(Collectors.toList());
			lineList.addAll(tempLineList);
			subLayer2LineRelation.put(key, lineIdList);
		});
		cityAreaTreeList.forEach(cityAreaTree -> {
			List<Map<String, Object>> districtList = ParseDataUtil.parseList(cityAreaTree.get(TableName.DISTRICT + "_model"));
			Map<String, List<Long>> map = new HashMap<>();
			districtList.forEach(district -> {
				Long id = ParseDataUtil.parseLong(district.get(ColumnName.ID));
				String name = ParseDataUtil.parseString(district.get(ColumnName.NAME));
				String key = id + "_" + name;
				List<Map<String, Object>> tempLineList = getLineListForCityTree(Arrays.asList(district), new ConcurrentHashMap<>());
				List<Long> lineIdList = tempLineList.stream().map(s -> ParseDataUtil.parseLong(s.get(ColumnName.ID))).collect(Collectors.toList());
				map.put(key, lineIdList);
			});
			subLayer3LineRelation.put(ParseDataUtil.parseString(cityAreaTree.get("id")),map);
		});

		return lineList;
	}

	/**
	 * 地级行政区区域获取监测点集合
	 *
	 * @param cityAreaTreeList
	 * @param subLayer2LineRelation
	 * @return
	 */
	public static List<Map<String, Object>> getLineListForCityTree(List<Map<String, Object>> cityAreaTreeList, Map<String, List<Long>> subLayer2LineRelation) {
		List<Map<String, Object>> districtAreaTreeList = new ArrayList<>();
		List<Map<String, Object>> substationTreeList = new ArrayList<>();
		cityAreaTreeList.forEach(cityAreaTree -> {
			List<Map<String, Object>> tempDistrictTreeList = ParseDataUtil.parseList(cityAreaTree.get(TableName.DISTRICT + "_model"));
			districtAreaTreeList.addAll(tempDistrictTreeList);
			List<Map<String, Object>> tempSubstationTreeList = ParseDataUtil.parseList(cityAreaTree.get(TableName.SUBSTATION + "_model"));
			substationTreeList.addAll(tempSubstationTreeList);
		});
		if (CollectionUtils.isNotEmpty(districtAreaTreeList)) {
			districtAreaTreeList.forEach(districtTree -> {
				Long id = ParseDataUtil.parseLong(districtTree.get(ColumnName.ID));
				String name = ParseDataUtil.parseString(districtTree.get(ColumnName.NAME));
				String key = id + "_" + name;
				List<Map<String, Object>> tempSubstationList = ParseDataUtil.parseList(districtTree.get(TableName.SUBSTATION + "_model"));
				List<Map<String, Object>> lineList = getLineListFromSubstationTree(tempSubstationList);
				List<Long> lineIdList = lineList.stream().map(s -> ParseDataUtil.parseLong(s.get(ColumnName.ID))).collect(Collectors.toList());
				subLayer2LineRelation.put(key, lineIdList);
				substationTreeList.addAll(tempSubstationList);
			});
		}
		return getLineListFromSubstationTree(substationTreeList);
	}

	/**
	 * 变电站树种获取所有监测点
	 *
	 * @param substationTreeList
	 * @return
	 */
	private static List<Map<String, Object>> getLineListFromSubstationTree(List<Map<String, Object>> substationTreeList) {
		List<Map<String, Object>> lineList = new ArrayList<>();
		substationTreeList.forEach(substationTree -> {
			List<Map<String, Object>> tempLineList = ParseDataUtil.parseList(substationTree.get(TableName.LINE + "_model"));
			lineList.addAll(tempLineList);
		});
		return lineList;
	}

	/**
	 * 根据厂家设置分组
	 *
	 * @param key
	 * @param lineId
	 * @param vendorGroup
	 */
	private static void setVendorGroup(String key, Long lineId, Map<String, List<Long>> vendorGroup) {
		if (vendorGroup.containsKey(key)) {
			List<Long> idList = ParseDataUtil.parseList(vendorGroup.get(key));
			idList.add(lineId);
			vendorGroup.put(key, idList);
		} else {
			List<Long> idList = new ArrayList<>();
			idList.add(lineId);
			vendorGroup.put(key, idList);
		}
	}

	/**
	 * 查询统计树： 网-省-变电站-监测点（终端 ）|| 测试点
	 *
	 * @param modelId
	 * @param isupload 对监测点和终端有效
	 * @param isOfflineTest
	 * @return
	 */
	static Map<String, Object> getTreeForNet(Long modelId, Boolean isupload, Boolean isOfflineTest) {
		List<ConditionBlock> lineCondition = new ArrayList<>();
		List<ConditionBlock> terminalCondition = new ArrayList<>();
		//设置监测点和终端查询条件
		//setLineAndTerminalCondition(lineCondition,terminalCondition,monitorType,monitorSort);
		SingleModelConditionDTO lineConditionDTO = new SingleModelConditionDTO(TableName.LINE);
		lineConditionDTO.setFilter(new ConditionBlockCompose(lineCondition));
		SingleModelConditionDTO terminalConditionDTO = new SingleModelConditionDTO(TableName.PQTERMINAL);
		terminalConditionDTO.setFilter(new ConditionBlockCompose(terminalCondition));
		List<SingleModelConditionDTO> subConditions = null;
		if (null != isOfflineTest && isOfflineTest) {
			subConditions = Arrays.asList(
					new SingleModelConditionDTO(TableName.PROVINCECOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
					new SingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
					new SingleModelConditionDTO(TableName.COUNTYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
					new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
					new SingleModelConditionDTO(TableName.OFFLINETEST, Arrays.asList(ColumnName.ID, ColumnName.NAME)));
		} else {
			subConditions = Arrays.asList(
					new SingleModelConditionDTO(TableName.PROVINCECOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
					new SingleModelConditionDTO(TableName.CITYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
					new SingleModelConditionDTO(TableName.COUNTYCOMPANY, Arrays.asList(ColumnName.ID, ColumnName.NAME)),
					new SingleModelConditionDTO(TableName.SUBSTATION, Arrays.asList(ColumnName.ID, ColumnName.NAME)	),
					lineConditionDTO, terminalConditionDTO);
		}
		List<Map<String, Object>> treeList = ModelServiceUtils.getInterRelations(Collections.singletonList(modelId), TableName.NETCOMPANY, null, null, subConditions, Boolean.FALSE);
		Map<String, Object> tree = CollectionUtils.isEmpty(treeList) ? null : treeList.get(0);
		// 把地市和县级的变电站节点树提取省级单位下
		if (null == tree) {
			throw new CommonManagerException("用户无权限查看该节点，请联系管理员分配权限");
		}
		List<Map<String, Object>> provinceCompanyTreeList = ParseDataUtil.parseList(tree.get(TableName.PROVINCECOMPANY + "_model"));
		provinceCompanyTreeList.forEach(provinceCompanyTree -> {
			List<Map<String, Object>> substationTree = getSubstationTreeFromProvinceCompanyTree(provinceCompanyTree);
			provinceCompanyTree.put(TableName.SUBSTATION + "_model", substationTree);
			provinceCompanyTree.remove(TableName.CITYCOMPANY + "_model");
		});
		tree.put(TableName.PROVINCECOMPANY + "_model", provinceCompanyTreeList);
		if ((null == isOfflineTest || !isOfflineTest) && (null != isupload && isupload)) {
			dealIsuploadForTree(tree, TableName.NETCOMPANY);
		}
		return tree;
	}

	/**
	 * 查询统计树：省-市-变电站-监测点 || 终端 || 测试点
	 *
	 * @param modelId
	 * @param isupload   对监测点和终端有效
	 * @param isOfflineTest
	 * @return
	 */
	static Map<String, Object> getTreeForProvince(Long modelId, Boolean isupload, Boolean isOfflineTest) {
		List<ConditionBlock> lineCondition = new ArrayList<>();
		List<ConditionBlock> terminalCondition = new ArrayList<>();
		//设置监测点和终端查询条件
		//setLineAndTerminalCondition(lineCondition,terminalCondition,monitorType,monitorSort);
		SingleModelConditionDTO lineConditionDTO = new SingleModelConditionDTO(TableName.LINE);
		lineConditionDTO.setFilter(new ConditionBlockCompose(lineCondition));
		SingleModelConditionDTO terminalConditionDTO = new SingleModelConditionDTO(TableName.PQTERMINAL);
		terminalConditionDTO.setFilter(new ConditionBlockCompose(terminalCondition));
		List<SingleModelConditionDTO> subConditions = null;
		if (null != isOfflineTest && isOfflineTest) {
			subConditions = new ArrayList<>(Arrays.asList(new SingleModelConditionDTO(TableName.CITYCOMPANY),
					new SingleModelConditionDTO(TableName.COUNTYCOMPANY),
					new SingleModelConditionDTO(TableName.SUBSTATION),
					new SingleModelConditionDTO(TableName.OFFLINETEST)));
		} else {
			subConditions = new ArrayList<>(Arrays.asList(new SingleModelConditionDTO(TableName.CITYCOMPANY),
					new SingleModelConditionDTO(TableName.COUNTYCOMPANY),
					new SingleModelConditionDTO(TableName.SUBSTATION),lineConditionDTO
					, terminalConditionDTO));
		}
		/*if(MonitorTypeEnum.DISTRIBUTION.getValue().equals(monitorType)){
			subConditions.add(new SingleModelConditionDTO(TableName.POWERDISTRIBUTIONAREA));
		}*/
		List<Map<String, Object>> treeList = ModelServiceUtils.getInterRelations(Collections.singletonList(modelId), TableName.PROVINCECOMPANY, null, null, subConditions, Boolean.FALSE);
		Map<String, Object> tree = CollectionUtils.isEmpty(treeList) ? new ConcurrentHashMap<>() : treeList.get(0);
		// 县级变电站树全部移动到地级
		List<Map<String, Object>> cityCompanyTreeList = ParseDataUtil.parseList(tree.get(TableName.CITYCOMPANY + "_model"));
		cityCompanyTreeList.forEach(cityCompanyTree -> {
			List<Map<String, Object>> citySubstationTreeList = getSubstationTreeFromCityCompanyTree(cityCompanyTree);
			cityCompanyTree.put(TableName.SUBSTATION + "_model", citySubstationTreeList);
			cityCompanyTree.remove(TableName.COUNTYCOMPANY + "_model");
		});
		tree.put(TableName.CITYCOMPANY + "_model", cityCompanyTreeList);
		if ((null == isOfflineTest || !isOfflineTest) && (null != isupload && isupload)) {
			dealIsuploadForTree(tree, TableName.PROVINCECOMPANY);
		}
		return tree;
	}

	public static void setLineAndTerminalCondition(List<ConditionBlock> lineCondition,List<ConditionBlock> terminalCondition,
													Integer monitorType, Integer monitorSort) {
		if(MonitorTypeEnum.MAIN.getValue().equals(monitorType)){
			//主网统计主配网类型为主网和主配网的监测点
			ConditionBlock typeCondition = new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_IN,
					Arrays.asList(MonitorTypeEnum.MAIN.getValue()));
			lineCondition.add(typeCondition);
			terminalCondition.add(typeCondition);
		}else if(MonitorTypeEnum.DISTRIBUTION.getValue().equals(monitorType)){
			//配网统计主配网类型配网和主配网的监测点
			ConditionBlock sortCondition = new ConditionBlock(ColumnName.MONITORSORT, ConditionBlock.OPERATOR_EQ, monitorSort);
			if(!MonitorSortEnum.ALL.getId().equals(monitorSort)){
				lineCondition.add(sortCondition);
			}
			List<Integer> typeList = new ArrayList<>(Arrays.asList(MonitorTypeEnum.DISTRIBUTION.getValue()));
			/*if(MonitorSortEnum.ALL.getId().equals(monitorSort) || MonitorSortEnum.FIRST_CLASS.getId().equals(monitorSort)){
				//如果为I类或者全部，加上主配网类型
				typeList.add(MonitorTypeEnum.MAIN_DISTRIBUTION.getValue());
			}*/
			ConditionBlock typeCondition = new ConditionBlock(ColumnName.MONITORTYPE, ConditionBlock.OPERATOR_IN, typeList);
			lineCondition.add(typeCondition);
			terminalCondition.add(typeCondition);
		}else if(monitorType.equals(0)){
			ConditionBlock sortCondition = new ConditionBlock(ColumnName.MONITORSORT, ConditionBlock.OPERATOR_EQ, monitorSort);
			if(Objects.nonNull(monitorSort) && !MonitorSortEnum.ALL.getId().equals(monitorSort)){
				lineCondition.add(sortCondition);
			}
		}
	}

	/**
	 * 查询统计树：市-（县）-变电站-监测点 || 终端 || 测试点
	 *
	 * @param modelId
	 * @param isupload   对监测点和终端有效
	 * @param isOfflineTest
	 * @return
	 */
	static Map<String, Object> getTreeForCity(Long modelId, Boolean isupload, Boolean isOfflineTest) {
		List<ConditionBlock> lineCondition = new ArrayList<>();
		List<ConditionBlock> terminalCondition = new ArrayList<>();
		//设置监测点和终端查询条件
		//setLineAndTerminalCondition(lineCondition,terminalCondition,monitorType,monitorSort);
		SingleModelConditionDTO lineConditionDTO = new SingleModelConditionDTO(TableName.LINE);
		lineConditionDTO.setFilter(new ConditionBlockCompose(lineCondition));
		SingleModelConditionDTO terminalConditionDTO = new SingleModelConditionDTO(TableName.PQTERMINAL);
		terminalConditionDTO.setFilter(new ConditionBlockCompose(terminalCondition));
		List<SingleModelConditionDTO> subConditions = null;
		if (null != isOfflineTest && isOfflineTest) {
			subConditions = Arrays.asList(new SingleModelConditionDTO(TableName.CITYCOMPANY),
					new SingleModelConditionDTO(TableName.COUNTYCOMPANY),
					new SingleModelConditionDTO(TableName.SUBSTATION),
					new SingleModelConditionDTO(TableName.OFFLINETEST));
		} else {
			subConditions = Arrays.asList(new SingleModelConditionDTO(TableName.CITYCOMPANY),
					new SingleModelConditionDTO(TableName.COUNTYCOMPANY),
					new SingleModelConditionDTO(TableName.SUBSTATION),
					lineConditionDTO, terminalConditionDTO);
		}
		List<Map<String, Object>> treeList = ModelServiceUtils.getInterRelations(Collections.singletonList(modelId), TableName.CITYCOMPANY, null, null, subConditions, Boolean.FALSE);
		Map<String, Object> tree = CollectionUtils.isEmpty(treeList) ? null : treeList.get(0);
		if ((null == isOfflineTest || !isOfflineTest) && (null != isupload && isupload)) {
			if(tree!=null){
				dealIsuploadForTree(tree, TableName.CITYCOMPANY);
			}
		}
		return tree;
	}

	/**
	 * 省级树中获取所有的变电站树
	 *
	 * @param provinceCompanyTree
	 * @return
	 */
	private static List<Map<String, Object>> getSubstationTreeFromProvinceCompanyTree(Map<String, Object> provinceCompanyTree) {
		List<Map<String, Object>> substationTreeList = new ArrayList<>();
		if (null == provinceCompanyTree) {
			return new ArrayList<>();
		}
		List<Map<String, Object>> cityCompanyTreeList = ParseDataUtil.parseList(provinceCompanyTree.get(TableName.CITYCOMPANY + "_model"));
		cityCompanyTreeList.forEach(cityCompanyTree -> {
			List<Map<String, Object>> citySubstationTreeList = getSubstationTreeFromCityCompanyTree(cityCompanyTree);
			substationTreeList.addAll(citySubstationTreeList);
		});
		return substationTreeList;
	}

	/**
	 * 地级树中获取所有的变电站树
	 *
	 * @param cityCompanyTree
	 * @return
	 */
	private static List<Map<String, Object>> getSubstationTreeFromCityCompanyTree(Map<String, Object> cityCompanyTree) {
		List<Map<String, Object>> substationTreeList = new ArrayList<>();
		if (null == cityCompanyTree) {
			return new ArrayList<>();
		}
		List<Map<String, Object>> countyCompanyTreeList = ParseDataUtil.parseList(cityCompanyTree.get(TableName.COUNTYCOMPANY + "_model"));
		List<Map<String, Object>> citySubstationTreeList = ParseDataUtil.parseList(cityCompanyTree.get(TableName.SUBSTATION + "_model"));
		countyCompanyTreeList.forEach(countyCompanyTree -> {
			List<Map<String, Object>> countySubstationTreeList = ParseDataUtil.parseList(countyCompanyTree.get(TableName.SUBSTATION + "_model"));
			substationTreeList.addAll(countySubstationTreeList);
		});
		substationTreeList.addAll(citySubstationTreeList);
		return substationTreeList;
	}

	/**
	 * 根据是否上送过滤树,树形状必须是（网-省-变电站-监测点||终端）或者（省-市-变电站 -监测点||终端）或者（市-（县）-变电站-监测点||终端）
	 *
	 * <AUTHOR>
	 * @param tree
	 * @param rootNode
	 * @return
	 */
	private static void dealIsuploadForTree(Map<String, Object> tree, String rootNode) {
		String firstSubLayerModel = getFirstSubModelLabel(rootNode);
		List<Map<String, Object>> firstSubLayerTree = ParseDataUtil.parseList(tree.get(firstSubLayerModel + "_model"));
		if (CollectionUtils.isNotEmpty(firstSubLayerTree)) {
			firstSubLayerTree.forEach(firstSubLayer -> {
				List<Map<String, Object>> tempSubstationTree = ParseDataUtil.parseList(firstSubLayer.get(TableName.SUBSTATION + "_model"));
				filterUploadSubstation(tempSubstationTree);
				firstSubLayer.put(TableName.SUBSTATION + "_model", tempSubstationTree);
			});
			tree.put(firstSubLayerModel + "_model", firstSubLayerTree);
		} else {
			List<Map<String, Object>> tempSubstationTree = ParseDataUtil.parseList(tree.get(TableName.SUBSTATION + "_model"));
			tree.put(TableName.SUBSTATION + "_model", tempSubstationTree);
		}

	}

	/**
	 * 在变电站层级上移除掉非上送的分支
	 *
	 * <AUTHOR>
	 * @param substationTree
	 */
	private static void filterUploadSubstation(List<Map<String, Object>> substationTree) {
		// 对substationTree进行迭代
		Iterator<Map<String, Object>> substationIt = substationTree.iterator();
		while (substationIt.hasNext()) {
			Map<String, Object> substation = substationIt.next();
			// 获取监测点列表
			List<Map<String, Object>> lineList = ParseDataUtil.parseList(substation.get("line_model"));
			List<Map<String, Object>> lineFilter = lineList.stream().filter(l -> ParseDataUtil.parseBoolean(l.get("isupload"))).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(lineFilter)) {
				substationIt.remove();
			}else {
				substation.put("line_model", lineFilter);
			}
		}
	}

}

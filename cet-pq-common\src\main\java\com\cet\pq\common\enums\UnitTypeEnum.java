package com.cet.pq.common.enums;


import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/6/5 15:51
 * @Description
 */
public enum UnitTypeEnum {
    //管理单位
    COMPANY_UNIT("管理单位台账",1),
    //行政区域
    AREA_UNIT("行政区域台账",2),
    //变电站
    SUBSTATION_UNIT("变电站台账",3),
    //台区台账
    POWERDISTRIBUTION_UNIT("台区台账",4),
    //配网终端台账
    DISTRIBUTION_TERMINAL_UNIT("配网终端台账",5),
    //终端监测点台账
    TERMINA_LINE_UNIT("终端监测点台账",6),
    //主网终端台账
    TERMINAL_UNIT("主网终端台账",7),
    //监测点台账
    LINE_UNIT("主网监测点台账",8),
    //监测点台账
    BUSBARSECTION_UNIT("母线台账",9),
    //敏感用户台账
    SENSITIVEUSER_UNIT("敏感用户台账",10),
    //敏感用户台账
    PHOTOVOLTAICUSER_UNIT("光伏用户台账",11),
    //无功设备台账
    REACTIVEDEVICE_UNIT("无功设备台账",12),
    //普测对象台账
    OFFLINETEST_UNIT("普测对象台账",13),
    //铁路牵引站台账
    ELECTRIC_RAILWAY_UNIT("铁路牵引站台账",14),
    //风电场台账
    WIND_POWER_UNIT("风电场台账",15),
    //光伏电站台账
    PHOTOVOLTAIC_STATION_UNIT("光伏电站台账",16),
    //冶炼负荷台账
    SMELT_LOAD_UNIT("冶炼负荷台账",17),
    //其他干扰源台账
    INTERFERENCE_SOURCE_UNIT("其他干扰源台账",18),
    //普测计划编制
    TEST_PLAN_UNIT("普测计划编制",19),

    // 储能站台账
    ENERGY_STORAGE_STATION_UNIT("储能站台账", 20),

    // 储能站台账
    CHARGING_STATION_UNIT("充电站台账", 21),

    DISTRIBUTION_TRANSFORMER_UNIT("变压器配变台账", 22),

    MAIN_TRANSFORMER_UNIT("变压器主变台账", 23)
    ;

    UnitTypeEnum(String name, Integer id) {
        this.name = name;
        this.id = id;
    }

    private String name;
    private Integer id;

    public String getName() {
        return name;
    }

    public Integer getId() {
        return id;
    }

    public static Integer getIdByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (UnitTypeEnum unitTypeEnum : UnitTypeEnum.values()) {
            if (unitTypeEnum.name.equals(name)) {
                return unitTypeEnum.id;
            }
        }
        return 0;
    }
}

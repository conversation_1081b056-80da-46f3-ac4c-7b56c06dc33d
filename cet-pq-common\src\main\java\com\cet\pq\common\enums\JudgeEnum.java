package com.cet.pq.common.enums;

/**
 * <AUTHOR>
 * @date 2023/03/03 9:47
 */
public enum JudgeEnum {

    /**
     * 否
     */
    NO("否", 0),
    /**
     * 是
     */
    YES("是", 1);

    private String key;
    private Integer value;

    JudgeEnum(String key, Integer value) {
        this.key = key;
        this.value = value;
    }

    public static String getJudgeEnumKeyByValue(Integer value){
        for(JudgeEnum judgeEnum : values()){
            if(judgeEnum.getValue().equals(value)){
                return judgeEnum.getKey();
            }
        }
        return "";
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}

package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * @Title: MonitorTagEnums
 * @Package: com.cet.pq.pqwmuploadservice.enums
 * @Description:
 * @Author: zhangyifu
 * @Date: 2024/1/2 16:36
 * @Version:1.0
 */
public enum MonitorTagEnums {
    acrossprovince("01", "跨省联络线"),
    publicconnectionline("02", "公用联络线"),
    maintransformerhighvoltageside("03", "主变高压侧"),
    maintransformermediumvoltageside("04", "主变中压侧"),
    maintransformerlowvoltageside("05", "主变低压侧"),
    governanceequipment("06", "治理设备"),
    converterstation("07", "受换流站影响"),
    transformerside("08", "换流变"),
    converterstationfilter("09", "换流站滤波器"),
    acconverter("10", "换流站出线"),
    electrifiedrailway("11", "电气化铁路"),
    smeltingload("12", "冶炼负荷"),
    commermuniccivilelec("13", "城市商业"),
    sensitiveusers("14", "敏感用户"),
    importantusers("15", "重要用户"),
    onshorewindpower("16", "陆上风电"),
    offshorewindpower("17", "海上风电"),
    powerstation("18", "光伏电站"),
    distributedphotovoltaic("19", "分布式光伏"),
    otherpowersources("98", "其他电源");

    private String code;
    private String mean;

    MonitorTagEnums(String code, String mean) {
        this.code = code;
        this.mean = mean;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMean() {
        return mean;
    }

    public void setMean(String mean) {
        this.mean = mean;
    }

    public static String getCodeByMeanNoThrows(String mean) {
        if (StringUtils.isEmpty(mean)) {
            return null;
        }
        for (MonitorTagEnums monitorTagEnums : MonitorTagEnums.values()) {
            if (mean.equals(monitorTagEnums.getMean())) {
                return monitorTagEnums.getCode();
            }
        }
        return "0";
    }

    public static String getMeanByCodeNoThrows(String code) {
        for (MonitorTagEnums monitorTagEnums : MonitorTagEnums.values()) {
            if (code.equals(monitorTagEnums.getCode())) {
                return monitorTagEnums.getMean();
            }
        }
        return StringUtils.EMPTY;
    }

}

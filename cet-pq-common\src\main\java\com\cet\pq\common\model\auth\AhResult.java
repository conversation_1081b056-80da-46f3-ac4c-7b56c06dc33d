package com.cet.pq.common.model.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value="Result",description="服务接口返回结果")
@SuppressWarnings({ "rawtypes", "unchecked" })
public class AhResult<T> implements Serializable {

	public static final int SUCCESS_resultCode = 0;

	private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="错误码",name="resultCode",example="1")
    private Integer resultCode;

    @ApiModelProperty(value="错误信息",name="resultInfo",example="null")
    private String resultInfo;

    @ApiModelProperty(value="数据",name="data",example="xxx")
    private T data;

    public AhResult() {
        resultCode = 0;
        resultInfo = "";
    }

    public AhResult(Integer resultCode, String resultInfo, T data) {
        this.resultCode = resultCode;
        this.resultInfo = resultInfo;
        this.data = data;
    }

	public static AhResult success() {
		return new AhResult(0,"操作成功",null);
    }
    
    public static <T> AhResult<T> success(T t){
    	return new AhResult<T>(0, "success", t);
    }
    
    public static AhResult error(String resultInfo) {
		return new AhResult(500,resultInfo,null);
    }
    public static <T> AhResult<T> error(T t) {
		return new AhResult(500,"error",t);
    }
    public Integer getResultCode() {
        return this.resultCode;
    }

    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(String resultInfo) {
        this.resultInfo = resultInfo;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Result{" +
                "resultCode=" + resultCode +
                ", resultInfo='" + resultInfo + '\'' +
                ", data=" + data +
                '}';
    }
}

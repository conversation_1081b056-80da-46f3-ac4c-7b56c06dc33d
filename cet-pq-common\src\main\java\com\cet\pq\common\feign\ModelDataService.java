package com.cet.pq.common.feign;

import com.cet.pq.common.annotation.SessionModel;
import com.cet.pq.common.model.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 模型服务的数据接口
 * 
 * <AUTHOR>
 */
@FeignClient(value = "model-service")
public interface ModelDataService {
	/**
	 * 表计
	 */
	static final String METER = "meter";
	/**
	 * 模型实例和Peccore节点的映射
	 */
	static final String MODEL_INSTANCE_AND_PECNODE_MAP = "modelinstanceandpecnodemap";
	/**
	 * 测点
	 */
	static final String MEASURE_ITEM = "measureitem";

	/**
	 * 通用查询接口，支持单模型列表、层次结构及树型结构返回 接口对查询数据的条数有做限制，每一层级返回条数不超过1万条，最多不超过5层
	 * 
	 * @param condition
	 * @return
	 */
	@PostMapping("/model/v1/query")
		//@ValidModelAuth
	ResultWithTotal<List<Map<String, Object>>> query(@RequestBody QueryCondition condition);

	@PostMapping("model/v1/query")
	ResultWithTotal<List<Map<String, Object>>> queryQuantityAggData(@RequestBody QueryCondition condition);

	/**
	 * 通用查询接口，支持单模型列表、层次结构及树型结构返回 接口对查询数据的条数有做限制，每一层级返回条数不超过1万条，最多不超过5层
	 * 
	 * @param condition
	 * @return
	 */
	@PostMapping("/model/v1/query")
	//@ValidModelAuth
	<T> ResultWithTotal<List<T>> queryObj(@RequestBody QueryCondition condition);

	
	/**
	 * 根据Id删除数据
	 * 
	 * @param modelLabel
	 * @param idRange
	 * @return
	 */
	@DeleteMapping("/model/v1/{modelLabel}")
	//@ValidModelAuth
	@SessionModel
	Result<Object> deleteById(@PathVariable("modelLabel") String modelLabel, @RequestBody List<Integer> idRange);

	/**
	 * 批量写入层次模型数据
	 * 
	 * @param data
	 * @return
	 */
	@PostMapping("/model/v1/write/hierachy")
	//@ValidModelAuth
	@SessionModel
	Result<Object> write(@RequestBody Object data, @RequestParam("ignoreDuplicate") Boolean ignoreDuplicate, @RequestParam("returnData") Boolean returnData);
	
	/**
	 * 批量写入层次模型数据
	 * 
	 * @param data
	 * @return
	 */
	@PostMapping("/model/v1/write/hierachy")
	//@ValidModelAuth
	@SessionModel
	Result<Object> write(@RequestBody Object data);

	/**
	 * 批量写入层次模型数据
	 *
	 * @param flatWriteData
	 * @return
	 */
	@PostMapping("/model/v1/write/flat")
	//@ValidModelAuth
	@SessionModel
	Result<Object> flatWrite(@RequestBody FlatWriteData flatWriteData);
	
	
	/**
	 * 查询所有枚举类型
	 * 
	 * @return
	 */
	@GetMapping("/model/v1/enumerations")
	public Result<List<IdTextPairWithModel>> getEnumerations();

	/**
	 * 查询指定枚举类型
	 * 
	 * @param modelLabel
	 * @return
	 */
	@GetMapping("/model/v1/enumerations/{modelLabel}")
	public Result<List<IdTextPair>> getEnumrationByModel(@PathVariable(value = "modelLabel") String modelLabel);

	/**
	 * 根据记录的id和模型数据删除记录
	 * 
	 * @param modelLabel 需要删除的模型的名称
	 * @param ids        需要删除的id列表
	 * @return
	 */
	@DeleteMapping("/model/v1/{modelLabel}")
	@SessionModel
	public Result<Object> delete(@PathVariable(value = "modelLabel") String modelLabel, List<Long> ids);

	/**
	 * 将实例从一个层次下面移动到另一个层次节点下面 移动受模型关系的约束，如果两个模型之间没有关系，此时存储时应该出错.
	 * 
	 * @param movetoOtherParams 待被转移的记录
	 * @return void
	 */
	@PutMapping("/model/v1/moveto")
	public Result<Object> moveToOther(@RequestBody List<MoveToOtherDTO> movetoOtherParams);

	/**
	 * 将实例从某个层次结构移出，模型数据本身不被删除
	 *
	 * @param moveOutParams 待被移出的数据
	 * @return void
	 */
	@DeleteMapping("/model/v1/moveout")
	public Result<Object> moveOut(@RequestBody List<MoveOutDTO> moveOutParams);
}

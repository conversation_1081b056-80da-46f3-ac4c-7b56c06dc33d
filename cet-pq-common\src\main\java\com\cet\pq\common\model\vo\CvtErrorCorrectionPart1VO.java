package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 10:59
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CvtErrorCorrectionPart1VO {

    /**
     * CVT影响超标谐波次数
     */
    private Integer harmonicCount;

    /**
     * 选定时长2~25次谐波电压含有率
     */
    private Double value;

    /**
     * 幅值误差
     */
    private Double amplitudeError;

    /**
     * 相别：0：A，1：B，2：C
     */
    private Integer phase;

}

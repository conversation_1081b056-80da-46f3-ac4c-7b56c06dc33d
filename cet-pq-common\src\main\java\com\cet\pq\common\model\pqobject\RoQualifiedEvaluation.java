package com.cet.pq.common.model.pqobject;

import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/19 16:44
 */
@Data
public class RoQualifiedEvaluation implements Comparable<RoQualifiedEvaluation> {
	private String modelLabel = "ro_qualifiedevaluation";
	private Integer voltagegroup;
	private Long updatetime;
	private Double sumqualifiedvalue;
	private Long id;
	private Double qualifiedvalue;
	private Long paraset_id;
	private Integer onlinedaycnt;
	private String monitored_label;
	private Long monitored_id;
	private Long logtime;
	private Integer aggregationcycle;

	@Override
	public int compareTo(RoQualifiedEvaluation o) {
		return (int) (o.getQualifiedvalue() * 100 - this.getQualifiedvalue() * 100);
	}
}

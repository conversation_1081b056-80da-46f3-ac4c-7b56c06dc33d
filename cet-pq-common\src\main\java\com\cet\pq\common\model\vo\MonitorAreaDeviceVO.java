package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/05/18 13:51
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MonitorAreaDeviceVO {

    /**
     * A相电压幅值
     */
    private Double uA;

    /**
     * B相电压幅值
     */
    private Double uB;

    /**
     * C相电压幅值
     */
    private Double uC;

    /**
     * A相电压相位
     */
    private Double uaDeg;

    /**
     * B相电压相位
     */
    private Double ubDeg;

    /**
     * C相电压相位
     */
    private Double ucDeg;

    /**
     * A相电流幅值
     */
    private Double iA;

    /**
     * B相电流幅值
     */
    private Double iB;

    /**
     * C相电流幅值
     */
    private Double iC;

    /**
     * A相电流相位
     */
    private Double iaDeg;

    /**
     * B相电流相位
     */
    private Double ibDeg;

    /**
     * C相电流相位
     */
    private Double icDeg;

    /**
     * 电压相序错误：ABC，BAC，ACB，CBA
     */
    private String uOut;

    /**
     * 电流相序错误：ABC，BAC，ACB，CBA
     */
    private String iOut1;

    /**
     * 电流极性错误：A，B，C，AB，AC，BC，ABC
     */
    private String iOut2;

    /**
     * 断线：0，1，2，3
     */
    private Integer p;


}

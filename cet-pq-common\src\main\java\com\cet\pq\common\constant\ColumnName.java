package com.cet.pq.common.constant;

/**
 * <AUTHOR>
 * @description 数据库名称列定义
 */
public class ColumnName {

    /**
     * 列
     */
    public static final String CONTROLTIME = "controltime";
    public static final String OBJECT_INSNAME = "object_insname";
    public static final String STARTTIME ="starttime";
    public static final String ENDTIME ="endtime";
    public static final String EVALUATIONOBJECT_ID ="evaluationobject_id";
    public static final String EVALUATIONSTATUS ="evaluationstatus";
    public static final String OBJECT_LABEL = "object_label";
    public static final String OBJECT_ID = "object_id";
    public static final String TYPE = "type";
    public static final String REMARK = "remark";
    public static final String CITYCOMPANY_ID = "citycompanyid";
    public static final String AUDITFLOWDETAIL_MODEL = "auditflowdetail_model";
    public static final String BIZTYPE = "biztype";
    public static final String ID = "id";
    public static final String NAME = "name";

    public static final String ZHIKANID = "zhikanid";
    public static final String ZHI_KAN_NAME = "zhikanname";
    public static final String DATATYPE = "datatype";
    public static final String CONTROLID = "controlid";
    public static final String PARENTID = "parentid";
    public static final String PARENT_TYPE = "parenttype";
    public static final String AGGREGATIONCYCLE = "aggregationcycle";
    public static final String AGGREGATIONTYPE = "aggregationtype";
    public static final String LOGTIME = "logtime";

    public static final String URL = "url";
    public static final String COUNT = "count";
    public static final String LINE_ID = "line_id";
    public static final String LINE_NAME = "line_name";
    public static final String QUANTITYPARASET_NAME = "quantityparaset_name";
    public static final String LINEID = "lineid";

    public static final String RELATEDLINEID = "relatedlineid";
    public static final String DISTUREBANCESOURCEID = "disturebancesourceid";
    public static final String VOLTAGECLASS = "voltageclass";
    public static final String COMPANYNAME = "companyName";
    public static final String SERVERITYDEGREE = "serverityDegree";
    public static final String EVENTID = "eventid";
    public static final String MONITORED_LABEL = "monitored_label";
    public static final String MONITOREDLABEL = "monitoredlabel";
    public static final String MONITORED_ID = "monitored_id";
    public static final String MONITOREDID = "monitoredid";
    public static final String VOLTAGE_GROUP = "voltagegroup";
    public static final String VOLTAGELEVEL = "voltagelevel";
    public static final String LATESTDATETIME = "latestDatetime";
    public static final String RULEID = "ruleid";
    public static final String ELECTRICRAILWAY_MODEL = "electricrailway_model";
    public static final String PHOTOVOLTAICSTATION_MODEL = "photovoltaicstation_model";
    public static final String WINDPOWERSTATION_MODEL = "windpowerstation_model";
    public static final String INTERFERENCESOURCE_MODEL = "interferencesource_model";
    public static final String SMELTLOAD_MODEL = "smeltload_model";
    public static final String ENERGYSTORAGESTATION_MODEL = "energystoragestation_model";
    public static final String CHARGINGSTATION_MODEL = "chargingstation_model";
    public static final String PQ_VARIATION_EVENT_TYPE = "pqvariationeventtype";
    public static final String QUANTITYPARASET_ID = "quantityparaset_id";
    public static final String PQMONITORTYPE = "pqmonitortype";
    public static final String DATA_ID = "dataid";
    public static final String STATUS = "status";
    public static final String STATUS_TEXT = "status$text";
    public static final String FILENAME = "filename";
    public static final String LOGINPIC = "loginpic";
    public static final String PLANNEDUSER = "planneduser";
    public static final String PLANNEDGENERATIONTIME = "plannedgenerationtime";
    public static final String TESTOBJECTTYPE = "testobjecttype";
    public static final String AUDITFLOW_MODEL = "auditflow_model";
    public static final String AUDITLOG_MODEL = "auditlog_model";
    public static final String FACTOR = "factor";
    public static final String OFFLINETEST_MODEL = "offlinetest_model";
    public static final String PLANNEDSTARTTIME = "plannedstarttime";
    public static final String GENERAL_PLAN_WARN_MONTHS = "generalplanwarnmonths";
    public static final String LAST_TEST_TIME = "lasttesttime";
    public static final String IS_WRAN = "iswran";
    public static final String IS_OVER_LIMIT = "isoverlimit";
    public static final String TESTCYCLE = "testcycle";
    public static final String TESTOBJECTTYPE_TEXT = "testobjecttype$text";
    public static final String RAILWAYLINENAME = "railwaylinename";
    public static final String RAILWAYTYPE = "railwaytype";
    public static final String TRACTIONTYPENAME = "tractionTypeName";
    public static final String TRACTION_TYPE = "tractiontype";
    public static final String TRACTION_VAR_PRIMARY_VOLT = "tractionvarprimaryvolt";
    public static final String TRACTION_VAR_SECONDARY_VOLT = "tractionvarsecondaryvolt";
    public static final String IMPORTTIME = "importtime";
    public static final String RANGED_MODEL_ID = "rangedmodelid";
    public static final String RANGED_MODEL_LABEL = "rangedmodellabel";
    public static final String UNITTYPE = "unittype";
    public static final String USERNAME = "username";
    public static final String PAGEURL = "pageurl";
    public static final String PATH = "path";
    public static final String CONTEXT = "context";
    public static final String SUCCESS = "success";
    public static final String SUBSTATION_ID = "substation_id";
    public static final String EVALUATE = "evaluate";
    public static final String UPLOADONLY = "uploadonly";
    public static final String ISUPLOAD = "isupload";
    public static final String RANGE_ID = "rangeid";
    public static final String RANGE_LABEL = "rangelabel";
    public static final String RMSSTATTYPE = "rmsstattype";
    public static final String RMS_OBJECT_TYPE = "rmsobjecttype";
    public static final String REALTIMESTATUS = "realtimestatus";
    public static final String MONITORSTATUS = "monitorstatus";
    public static final String OBJECTTYPE = "objecttype";
    public static final String EVENT_TIME = "eventtime";
    public static final String SEND_TIME = "sendtime";
    public static final String IS_MINUTE_EXTREME = "isminuteextreme";

    public static final String IS_MANUAL = "ismanual";
    public static final String UPDATE_TIME = "updatetime";
    public static final String HIERARCHY = "hierarchy";
    public static final String PQMONITOR_ID = "pqmonitor_id";
    public static final String OPERATION_TIME = "operationtime";
    public static final String UNIT = "unit";
    public static final String SUBSTATION_MODEL = "substation_model";

    public static final String COMPLETECHECKTIME = "completechecktime";

    public static final String  CITY_MODEL ="city_model";
    public static final String  LINE_MODEL ="line_model";

    public static final String  FULLNAME ="fullname";
    public static final String  CODE ="code";

    public static final String  FLAG ="flag";

    public static final String MODEL_SINGLE1 ="_model_single";

    public static final String  MODELLABEL = "modelLabel";
    public static final String  DEPARTMENT = "department";
    public static final String MODEL1 ="_model";
    public static final String  CHILDREN ="children";
    public static final String  PLATFORMAREANAME ="platformareaname";
    public static final String  LINENAME ="linename";
    public static final String  LINECODE ="linecode";
    public static final String  ISPHOTOVOLTAIC ="isphotovoltaic";
    public static final String  IFRURALPOWERGRID ="ifruralpowergrid";
    public static final String  NATUREOFUSE ="natureofuse";
    public static final String  POWERSUPPLYRADIUS ="powersupplyradius";
    public static final String  LINELENGTH ="linelength";
    public static final String  DISTRIBUTEDPHOTOVOLTAICNUM ="distributedphotovoltaicnum";
    public static final String  PHOTOVOLTAICCAPACITY ="photovoltaiccapacity";
    public static final String  IFBEVAP ="ifbevap";
    public static final String  APLOADTYPE ="aploadtype";
    public static final String  TRANSFORMERMODEL = "transformermodel";
    public static final String  TRANSFORMERCAPACITY = "transformercapacity";
    public static final String  PERMEABILITY = "permeability";
    public static final String  IFSHOULDPOINT = "ifshouldpoint";
    public static final String  IFMONITOR = "ifmonitor";


    public static final String  CITYCOMPANY_MODEL ="citycompany_model";

    public static final String  LINEONLINERATE ="lineOnlineRate";

    public static final String  DATACOMPLEXRATE ="dataComplexRate";

    public static final String  CHECKSTATUS ="checkstatus";

    public static final String  REGULARENABLE ="regularenable";

    public static final String  TERMINALCODE ="terminalcode";

    public static final String  PLANCHECKTIME ="planchecktime";

    public static final String  CHECKCYCLE ="checkcycle";

    public static final String  PORT ="port";

    public static final String  IP ="ip";

    public static final String KV = "kV";

    public static final String COMMISSIONINGDATE = "commissioningdate";

    public static final String VOLTCLASS = "voltclass";

    public static final String GISLONGITUDE = "gislongitude";

    public static final String GISLATITUDE = "gislatitude";

    public static final String STATIONNAME = "stationname";

    public static final String CHANNELNAME = "channelname";

    public static final String CHANNELID = "channelid";

    public static final String DRIVERTYPENAME = "drivertypename";

    public static final String NEUTRALPOINTGROUNDTYPE = "neutralpointgroundtype";

    public static final String VOLTAGETRANSFORMERTYPE = "voltagetransformertype";

    public static final String WIREDTYPE = "wiredtype";

    public static final String GENERALPLAN_MODEL = "generalplan_model";
    public static final String OFFLINETEST_ID = "offlinetest_id";
    public static final String IMPORT_FILE_PATH = "importfilepath";
    public static final String REPORT_PATH = "reportPath";

    public static final String FIRST_TIME = "firsttime";
    public static final String LAST_TIME = "lasttime";

    public static final String GENERALPLAN_ID = "generalplan_id";

    public static final String UPVOLTDEVIATION = "upvoltdeviation";

    public static final String LOWVOLTDEVIATION = "lowvoltdeviation";

    public static final String SHORTCIRCUITCAPACITY = "shortcircuitcapacity";

    public static final String USERPROTOCOLCAPACITY = "userprotocolcapacity";

    public static final String SUPPLYEQUIPMENTCAPACITY = "supplyequipmentcapacity";

    public static final String DEVICETYPENAME = "devicetypename";

    public static final String BRANCHINDEX = "branchindex";

    public static final String DATALOGMAPNAME = "datalogmapname";

    public static final String ACCESSDATE = "accessdate";

    public static final String CTRATIO = "ctratio";

    public static final String DATALOGINTERVAL = "dataloginterval";

    public static final String PTRATIO = "ptratio";

    public static final String INPUTCURRENT = "inputcurrent";

    public static final String INPUTVOLT = "inputvolt";

    public static final String PROVINCECOMPANYNAME = "provincecompanyname";

    public static final String CITYCOMPANYNAME = "citycompanyname";

    public static final String COUNTYCOMPANYNAME = "countycompanyname";

    public static final String SUBSTATIONNAME = "substationname";

    public static final String SUBMASK = "submask";

    public static final String MAC = "mac";

    public static final String GATE = "gate";

    public static final String VENDOR = "vendor";

    public static final String TERMINALTYPE = "terminaltype";

    public static final String TERMINALLEVEL = "terminallevel";

    public static final String EQUIPMENTMODEL = "equipmentmodel";
    public static final String INPUTTYPE = "inputtype";

    public static final String RATEDVOLTAGE = "ratedvoltage";

    public static final String VOLTAGEMEASURECOUNT = "voltagemeasurecount";

    public static final String CURRENTMEASURECOUNT = "currentmeasurecount";

    public static final String TIMINGMODE = "timingmode";

    public static final String STORAGECAPACITY = "storagecapacity";

    public static final String MAINTENANCECOMPANYNAME = "maintenancecompanyname";

    public static final String MAINTENANCETEAM = "maintenanceteam";

    public static final String MASTER = "master";

    public static final String CABINETTYPE = "cabinettype";

    public static final String CABINETNAME = "cabinetname";

    public static final String COMPANY = "company";

    public static final String PROPERTY = "property";

    public static final String ASSETCODE = "assetcode";

    public static final String ADDWAY = "addway";

    public static final String LEVEL = "level";

    public static final String SUPERNAME = "supername";

    public static final String AREALEVEL = "arealevel";

    public static final String PARENTAREANAME = "parentareaname";

    public static final String AREA = "area";

    public static final String MAINTENANCETEAMNAME = "maintenanceteamname";

    public static final String POWERSTATIONNAME = "powerstationname";

    public static final String POWERSTATIONTYPE = "powerstationtype";

    public static final String ISINTELLIGENTSTATION = "isintelligentstation";

    public static final String ISCENTERSTATION = "iscenterstation";

    public static final String REGIONTYPE = "regiontype";

    public static final String REGIONALCODE = "regionalcode";

    public static final String STATIONCODE = "stationcode";

    public static final String ISLOCKED = "islocked";

    public static final String IMPORTANCETYPE = "importancetype";

    public static final String CONVERTSTATIONTYPE = "convertstationtype";

    public static final String TESTINSTRUMENTTYPE = "testinstrumenttype";

    public static final String MODEL = "model";

    public static final String VENDER = "vender";

    public static final String RANGE = "range";

    public static final String TIMELIMIT = "timelimit";

    public static final String PRODUCTIONDATE = "productiondate";

    public static final String DEVICENAME = "devicename";

    public static final String GOVERNANCEDEVICETYPE = "governancedevicetype";

    public static final String CAPACITIVECAPACITY = "capacitivecapacity";

    public static final String INDUCTIVECAPACITY = "inductivecapacity";

    public static final String POWER_CAPACITY = "powercapacity";

    public static final String VOLTAGEGRADE = "voltagegrade";

    public static final String IDENTIFY = "identify";

    public static final String MY = "my";

    public static final String STATIONID = "stationid";

    public static final String DATA = "data";

    public static final String COUNTYCOMPANY_MODEL = "countycompany_model";

    public static final String NETCOMPANY_MODEL = "netcompany_model";

    public static final String PROVINCECOMPANY_MODEL = "provincecompany_model";

    public static final String SUBSTATIONID = "substationid";

    public static final String RELATEDSUBSTATIONIDS = "relatedSubstationIds";

    public static final String DISTRICT_MODEL = "district_model";

    public static final String PQTERMINAL_MODEL = "pqterminal_model";

    public static final String MONITORTYPE = "monitortype";

    public static final String MONITORSORT = "monitorsort";

    public static final String MONITORMODE = "monitormode";

    public static final String RELATEDMODELLABEL = "relatedmodellabel";

    public static final String RELATEDMODELID = "relatedmodelid";

    public static final String SORT = "sort";

    public static final String POWERDISTRIBUTIONAREA = "powerdistributionarea";

    public static final String POWERDISTRIBUTIONAREA_MODEL = "powerdistributionarea_model";

    public static final String PECDEVICEEXTEND_MODEL = "pecdeviceextend_model";

    public static final String DEVICEID = "deviceid";

    public static final String POWERDISTRIBUTIONAREANAME = "powerdistributionareaname";

    public static final String INSTALLATIONDATE = "installationdate";

    public static final String VERIFICATIONDATE = "verificationdate";

    public static final String VALIDPERIOD = "validperiod";

    public static final String PVLINENAME = "pvLineName";

    public static final String VOLTCLASS_TEXT = "voltclass$text";

    public static final String OBJECTTYPE_TEXT = "objecttype$text";

    public static final String SUBSTATION_NAME = "substationName";

    public static final String SUBSTATIONVOLTCLASS = "substationVoltclass";

    public static final String SUBSTATIONVOLTCLASS_TEXT = "substationVoltClass$text";

    public static final String POWER_DISTRIBUTION_AREA_NAME = "powerDistributionAreaName";

    public static final String WIRETYPE = "wiretype";

    public static final String REPORTTYPE = "reporttype";

    public static final String ROWID = "rowid";

    public static final String ISSPECIALLINEPOWER = "isspeciallinepower";

    public static final String IFSHOULDMONITOR = "ifshouldmonitor";

    public static final String OBJECTSTATTYPE = "objectstattype";

    public static final String PLANNEDSTATUS = "plannedstatus";

    public static final String CREATE_TIME = "createtime";
    public static final String RESULT_CREATE_TIME = "resultcreatetime";
    public static final String EVALUATION_TYPE = "evaluationtype";
    public static final String BCEID = "bceid";
    public static final String PROJECTBASICINFOID = "projectbasicinfoid";
    public static final String BCE_LABEL = "bcelabel";
    public static final String BECLINEID = "lineid";
    public static final String INSPECTIONREMARK = "inspectionremark";

    public static final String HIG_BUS_ID = "higbusid";
    public static final String MEDIUM_BUS_ID = "mediumbusid";
    public static final String LOW_BUS_ID = "lowbusid";
    public static final String START_BUS_ID = "startbusid";
    public static final String END_BUS_ID = "endbusid";
    public static final String ISID = "isid";

    public static final String ALARM_TIMES = "alarmTimes";

    public static final String ALARM_TYPE = "alarmType";

    public static final String OVERDAYCNT = "overdaycnt";

    public static final String OVERNODECNT = "overnodecnt";

    //add by wisdom
    public static final String INTERFERENCESOURCECODE = "interferencesourcecode";
    public static final String NETCHECKRESULT = "netcheckresult";
    public static final String FAULTCAUSE = "faultcause";
    //

    public static final String OBJID = "objid";
    public static final String QUANTITYPARASETID = "quantityparasetId";

    //@AI-Generated-start
    public static final String COL = "col";
    public static final String COL1 = "col1";

    public static final String COL2 = "col2";

    public static final String COL3 = "col3";

    public static final String COL4 = "col4";

    public static final String COL5 = "col5";

    public static final String COL6 = "col6";

    public static final String COL7 = "col7";

    public static final String COL8 = "col8";

    public static final String COL9 = "col9";

    public static final String COL10 = "col10";

    public static final String COL11 = "col11";

    public static final String COL12 = "col12";

    public static final String COL13 = "col13";

    public static final String COL14 = "col14";

    public static final String COL15 = "col15";

    public static final String COL16 = "col16";

    public static final String COL17 = "col17";

    public static final String COL18 = "col18";

    public static final String COL19 = "col19";

    public static final String COL20 = "col20";

    public static final String COL21 = "col21";

    public static final String COL22 = "col22";

    public static final String COL23 = "col23";

    public static final String COL24 = "col24";

    public static final String COL25 = "col25";

    public static final String COL26 = "col26";

    public static final String COL27 = "col27";

    public static final String COL28 = "col28";

    public static final String COL29 = "col29";

    public static final String COL30 = "col30";

    public static final String COL31 = "col31";

    public static final String COL32 = "col32";

    public static final String COL33 = "col33";

    public static final String COL34 = "col34";

    public static final String COL35 = "col35";

    public static final String COL36 = "col36";

    public static final String COL37 = "col37";

    public static final String COL38 = "col38";

    public static final String COL39 = "col39";

    public static final String COL40 = "col40";

    public static final String COL41 = "col41";

    public static final String COL42 = "col42";

    public static final String COL43 = "col43";
    public static final String COL44 = "col44";
    public static final String COL45 = "col45";
    public static final String COL46 = "col46";
    public static final String COL47 = "col47";
    public static final String COL48 = "col48";
    public static final String COL49 = "col49";
    public static final String COL50 = "col50";
    public static final String COL51 = "col51";
    public static final String COL52 = "col52";
    public static final String COL53 = "col53";
    public static final String COL54 = "col54";
    public static final String COL55 = "col55";
    public static final String COL56 = "col56";
    public static final String COL57 = "col57";
    public static final String COL58 = "col58";
    public static final String COL59 = "col59";
    public static final String COL60 = "col60";
    public static final String COL61 = "col61";
    public static final String COL62 = "col62";
    public static final String COL63 = "col63";
    public static final String COL64 = "col64";
    public static final String COL65 = "col65";

    //@AI-Generated-end
    public static final String TEMPLATES = "templates/";

    public static final String EXPORT = "export/";

    public static final String XLSX = ".xlsx";

    public static final String IS_SIMPLE = "issimple";

    public static final String ACCOUNT_TYPE = "accounttype";

    public static final String SIMPLE_ORDER = "simpleorder";

    public static final String HIGH_SIDE_VOLTAGE = "highsidevoltage";

    public static final String LOW_SIDE_VOLTAGE = "lowsidevoltage";

    public static final String BALANCE_TYPE = "balancetype";

    public static final String RATED_CAPACITY = "ratedcapacity";

    public static final String TOTAL_CAPACITY = "totalcapacity";

    public static final String RATED_CURRENT = "ratedcurrent";

    public static final String PHASE_NUMBER = "phasenumber";

    public static final String COIL_WINDING_CONFIGURATION = "coilwindingconfiguration";

    public static final String IMPEDANCE_VOLTAGE_RATIO = "impedancevoltageratio";

    public static final String NO_LOAD_LOSS = "noloadloss";

    public static final String LOAD_LOSS = "loadloss";

    public static final String NO_LOAD_CURRENT = "noloadcurrent";

    public static final String HIGH_VOLTAGE_SIDE_LINE = "highvoltagesideline";

    public static final String LOW_VOLTAGE_SIDE_AREA = "lowvoltagesidearea";

    public static final String STATION_TYPE = "stationtype";

    public static final String TRANSFORMER_NUMBER = "transformernumber";

    public static final String OPPOSITE_SIDE_SUBSTATION = "oppositesidesubstation";
    public static final String WINDING_NUMBER = "windingnumber";
    public static final String HIGH_VOLTAGE_SIDE_CAPACITY = "highvoltagesidecapacity";
    public static final String HIGH_VOLTAGE_SIDE_VOLT_CLASS = "highvoltagesidevoltclass";
    public static final String HIGH_VOLTAGE_SIDE_BUSBAR_ID = "highvoltagesidebusbarid";
    public static final String HIGH_VOLTAGE_SIDE_BUSBAR_NAME = "highvoltagesidebusbarname";

    public static final String MIDDLE_VOLTAGE_SIDE_CAPACITY = "middlevoltagesidecapacity";
    public static final String MIDDLE_VOLTAGE_SIDE_VOLT_CLASS = "middlevoltagesidevoltclass";
    public static final String MIDDLE_VOLTAGE_SIDE_BUSBAR_ID = "middlevoltagesidebusbarid";
    public static final String MIDDLE_VOLTAGE_SIDE_BUSBAR_NAME = "middlevoltagesidebusbarname";

    public static final String LOW_VOLTAGE_SIDE_CAPACITY = "lowvoltagesidecapacity";
    public static final String LOW_VOLTAGE_SIDE_VOLT_CLASS = "lowvoltagesidevoltclass";

    public static final String LOW_VOLTAGE_SIDE_BUSBAR_ID = "lowvoltagesidebusbarid";
    public static final String LOW_VOLTAGE_SIDE_BUSBAR_NAME = "lowvoltagesidebusbarname";


    public static final String HIGH_VSIDE_TAP_CHANGER_VOLT = "highvsidetapchangervolt";
    public static final String HIGH_VSIDE_TAP_CHANGER_MAX_POS = "highvsidetapchangermaxpos";
    public static final String HIGH_VSIDE_TAP_CHANGER_MIN_POS = "highvsidetapchangerminpos";


    public static final String MIDDLE_VSIDE_TAP_CHANGER_VOLT = "middlevsidetapchangervolt";
    public static final String MIDDLE_VSIDE_TAP_CHANGER_MAX_POS = "middlevsidetapchangermaxpos";
    public static final String MIDDLE_VSIDE_TAP_CHANGER_MIN_POS = "middlevsidetapchangerminpos";


    public static final String LOW_VSIDE_TAP_CHANGER_VOLT = "lowvsidetapchangervolt";
    public static final String LOW_VSIDE_TAP_CHANGER_MAX_POS = "lowvsidetapchangermaxpos";
    public static final String LOW_VSIDE_TAP_CHANGER_MIN_POS = "lowvsidetapchangerminpos";


    public static final String HIGH_SHORT_CIRCUIT_IMPEDANCE = "highshortcircuitimpedance";
    public static final String MIDDLE_SHORT_CIRCUIT_IMPEDANCE = "middleshortcircuitimpedance";
    public static final String LOW_SHORT_CIRCUIT_IMPEDANCE = "lowshortcircuitimpedance";

    public static final String HIGH_NO_LOAD_LOSS = "highnoloadloss";
    public static final String MIDDLE_NO_LOAD_LOSS = "middlenoloadloss";
    public static final String LOW_NO_LOAD_LOSS = "lownoloadloss";

    public static final String HIGH_ONLOAD_LOSS = "highonloadloss";
    public static final String MIDDLE_ONLOAD_LOSS = "middleonloadloss";
    public static final String LOW_ONLOAD_LOSS = "lowonloadloss";

    public static final String HIGH_NO_LOAD_CURRENT = "highnoloadcurrent";
    public static final String MIDDLE_NO_LOAD_CURRENT = "middlenoloadcurrent";
    public static final String LOW_NOLOAD_CURRENT = "lownoloadcurrent";

    public static final String DEVICE_TYPE = "devicetype";

    public static final String ORDER_ID = "orderid";


    public static final String LOAD_TYPE = "loadtype";

    public static final String FAN_TYPE = "fantype";

    public static final String RUN_STATE = "runstate";

    public static final String INVERTER_NETWORK_WAY = "inverternetworkway";

    public static final String STORE_ENERGY_TYPE = "storeenergytype";
    public static final String DEPTH = "depth";

    // 母线相关
    public static final String BUS_BAR_SECTION_MODEL = "busbarsection_model";
    public static final String IF_ONLINE_MONITOR = "ifonlinemonitor";
    public static final String IF_ACCESS = "ifaccess";

    // 干扰源相关
    public static final String IS_COMPLAINT = "iscomplaint";
    public static final String IS_LOCAL_TEST = "islocaltest";
    public static final String IS_LONG_OVER_LIMIT = "islongoverlimit";
    public static final String IS_SENT_NOTICE = "issentnotice";
    public static final String MAIN_OVER_ANAYLISYS = "mainoveranaylisys";
    public static final String OVER_REASON = "overreason";
    public static final String RECTIFY = "rectify";
    public static final String COMPLAINT_CONTENT = "complaintcontent";
    public static final String OTHER_INFO = "otherinfo";

    public static final String ITICSCHEME_ID = "iticscheme_id";

    public static final String PQVARIATIONEVENT_ID = "pqvariationevent_id";
    public static final String PQVARIATIONEVENT_UUID = "pqvariationevent_uuid";
    public static final String SEMISCHEME_ID = "semischeme_id";
    public static final String DURATION = "duration";
    public static final String MAGNITUDE = "magnitude";
    public static final String VOLTAGETYPE = "voltagetype";

    public static final String COMPANYSUBSTATIONCOUNT_MODEL = "companysubstationcount_model";
    public static final String SUBSTATIONVOLTAGELEVEL = "substationvoltagelevel";

    public static final String REQUIRED = "required";

    public static final String GOVERNANCE_DEVICE_MODEL = "governancedevice_model";
    public static final String MODEL_TYPE = "modeltype";

    public static final String COLUMN_ENUM_TEXT = "$text";
    public static final String OVERRATEBYNOW = "overRateByNow";

    public static final String COMMUNICATIONMODE = "communicationmode";
    public static final String PRODUCTIONCODE = "productioncode";
    public static final String HARDWAREVERSION = "hardwareversion";
    public static final String SOFTWAREVERSION = "softwareversion";
    public static final String NUM = "num";
    public static final String REPORT_TYPE = "reporttype";

    public static final String ACCESSLOCATION ="accesslocation";
    public static final String RUNSTATUS ="runstatus";
    public static final String FILTERTYPE ="filtertype";
    public static final String MANUFACTURETIME ="manufacturetime";
    public static final String RELATEDSUBSTATION = "relatedsubstation";

    public static final String DATA_TIME = "datatime";
    public static final String PREDICTION_DATA_TYPE = "predictiondatatype";
    public static final String PREDICTION_TIME_TYPE = "predictiontimetype";
    public static final String DEGREE = "degree";
    public static final String DATANAME ="dataname";
    public static final String UPLIMIT ="uplimit";
    public static final String LOWLIMIT ="lowlimit";
    public static final String  CONVERTER_TYPE = "convertertype";
    public static final String  CABINETCODE = "cabinetcode";
    public static final String  TERMINALCHANNELNUM = "terminalchannelnum";
    public static final String PROJECTNAME ="projectname";
    public static final String  PROJECTCODE = "projectcode";
    public static final String  DEFECTCAUSE = "defectcause";
    public static final String  OVERCNT = "overcnt";

    public static final String TEMPLATE_ID = "templateid";
    public static final String JLDBH = "jldbh";
    public static final String LINETYPE = "linetype";
    public static final String RELATED_MODEL_LABEL = "relatedmodellabel";
    public static final String RELATED_MODEL_ID = "relatedmodelid";
    public static final String PARENT_NAME = "parentname";
    public static final String DISTRIBUTION_TRANSFORMER = "distributiontransformer";

    public static final String DISTRIBUTION_TRANSFORMER_MODEL = "distributiontransformer_model";
    public static final String ELECTRICITY_CATEGORY = "electricitycategory";

    public static final String PQ_TERMINAL_ID = "pqterminal_id";
    public static final String COUNT_ID = "count_id";
    public static final String MEASUREDBY = "measuredby";

    public static final String QUANTITY = "quantity";
    public static final String CAPACITY = "capacity";

    public static final String DEVICECODE = "devicecode";

    public static final String SUBSTATIONCOUNT = "substationcount";
    public static final String EQUIPMENTTYPE = "equipmenttype";
    public static final String LIMIT_OVER = "limitover";
    public static final String ALARMNUM = "alarmnum";
    public static final String IFGENERATIONUSER = "ifgenerationuser";

    //@AI-Generated-start
    public static final String SUBSTATIONLEVEL = "substationlevel";

    public static final String SUBSTATIONLEVEL_TEXT = "substationlevel$text";

    public static final String ONLINESTATUS = "onlinestatus";

    public static final String ONLINESTATUS_TEXT = "onlinestatus$text";

    //@AI-Generated-end

    public static final String EVENT_TYPE = "eventtype";
    public static final String INUSE_DATE = "inusedate";
    public static final String INDUSTRY_TYPE = "industrytype";
    public static final String TOGRID_TYPE = "togridtype";

    public static final String GET_NAME = "getName";

}

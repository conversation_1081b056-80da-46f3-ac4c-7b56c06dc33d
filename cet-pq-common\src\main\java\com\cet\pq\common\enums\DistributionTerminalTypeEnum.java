package com.cet.pq.common.enums;

import com.cet.pq.common.exception.CommonManagerException;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/6/9 10:39
 * @Description
 */
public enum  DistributionTerminalTypeEnum {
    //装置类型枚举
    PQ(1,"电能质量监测终端"),
    INTELLIGENT_FUSION_TERMINAL(2,"台区智能融合终端"),
    SMART_ENERGY_METER(3,"智能电能表"),
    PHOTOVOLTAIC_CIRCUIT_BREAKER(4,"光伏并网断路器"),
    PHOTOVOLTAIC_INVERTER(5,"光伏逆变器"),
    PQEQUIPMENT(6,"电能质量治理设备"),
    DEMARCATION_SWITCH(7,"分界开关"),
    OTHER(8,"其他")


    ;


    private Integer key;
    private String value;

    private DistributionTerminalTypeEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public static Integer getType(String strType) {
        for (DistributionTerminalTypeEnum terminalTypeEnum : DistributionTerminalTypeEnum.values()) {
            if(terminalTypeEnum.value.equals(strType)) {
                return terminalTypeEnum.key;
            }
        }
        throw new CommonManagerException("装置类型枚举值转化异常");
    }

    public static Integer getTypeNoThrows(String strType) {
        if (StringUtils.isEmpty(strType)) {
            return null;
        }
        for (DistributionTerminalTypeEnum terminalTypeEnum : DistributionTerminalTypeEnum.values()) {
            if(terminalTypeEnum.value.equals(strType)) {
                return terminalTypeEnum.key;
            }
        }
        return 0;
    }

    public static String getValueByKeyNoThrows(Integer key) {
        for (DistributionTerminalTypeEnum terminalTypeEnum : DistributionTerminalTypeEnum.values()) {
            if (terminalTypeEnum.key.equals(key)) {
                return terminalTypeEnum.value;
            }
        }
        return StringUtils.EMPTY;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

package com.cet.pq.common.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/06/06 17:26
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LongTermFlickerVO {

    /**
     * 监测点编号
     */
    private Long monitorId;

    /**
     * 上部分
     */
    private List<LongTermFlickerPart1VO> part1;

    /**
     * 中间部分
     */
    private List<LongTermFlickerPart2VO> part2;

    /**
     * A相长时闪变值越限状态: 0代表未越限，对应到显示界面“max(Plt,A)<Plimit”
     * ，1代表越限，对应到显示界面“max(Plt,A)>Plimit”
     */
    private String overstA;

    /**
     * B相长时闪变值越限状态: 0代表未越限，对应到显示界面“max(Plt,B)<Plimit”
     * ，1代表越限，对应到显示界面“max(Plt,B)>Plimit”
     */
    private String overstB;

    /**
     * C相长时闪变值越限状态: 0代表未越限，对应到显示界面“max(Plt,C)<Plimit”
     * ，1代表越限，对应到显示界面“max(Plt,C)>Plimit”
     */
    private String overstC;

    /**
     * A相长时闪变值偏离值状态: 0代表小于限值，对应到显示界面“Sa<Plimit”,
     * 1代表大于等于限值，对应到显示界面“Sa>Plimit”
     */
    private String devstA;

    /**
     * B相长时闪变值偏离值状态: 0代表小于限值，对应到显示界面“Sb<Plimit”,
     * 1代表大于等于限值，对应到显示界面“Sb>Plimit”
     */
    private String devstB;

    /**
     * C相长时闪变值偏离值状态: 0代表小于限值，对应到显示界面“Sc<Plimit”,
     * 1代表大于等于限值，对应到显示界面“Sc>Plimit”
     */
    private String devstC;

    /**
     * A相长时闪变辨识结果指示灯: -1:绿色，0：橙色，1：红色
     */
    private String aColor;

    /**
     * B相长时闪变辨识结果指示灯: -1:绿色，0：橙色，1：红色
     */
    private String bColor;

    /**
     * C相长时闪变辨识结果指示灯: -1:绿色，0：橙色，1：红色
     */
    private String cColor;

    /**
     * 判断结果输出: 0：冲击或波动负荷扰动，1：暂态电能质量干扰影响，2：闪变未超限
     */
    private String per;

    /**
     * 监测区域
     */
    private String cityCompany;


}

package com.cet.pq.anlysis.common.enums.offlinetest.xinjiang;

import com.cet.pq.anlysis.common.constants.OfflineTestSheetName;
/**
 * <AUTHOR>
 * @date: 2022/11/8 11:30
 */
public enum SumData {

    // A相电流有效值
    DATAID_1000001_MAX(1000001L, 3, "A相电流最大值", OfflineTestSheetName.ELECTRICTY, "C", "13"),
    DATAID_1000001_MIN(1000001L, 4, "A相电流最小值", OfflineTestSheetName.ELECTRICTY, "E", "13"),
    DATAID_1000001_AVG(1000001L, 11, "A相电流平均值", OfflineTestSheetName.ELECTRICTY, "D", "13"),
    DATAID_1000001_CP95(1000001L, 6, "A相电流95概率值", OfflineTestSheetName.ELECTRICTY, "F", "13"),

    // B相电流有效值
    DATAID_1000002_MAX(1000002L, 3, "B相电流最大值", OfflineTestSheetName.ELECTRICTY, "H", "13"),
    DATAID_1000002_MIN(1000002L, 4, "B相电流最小值", OfflineTestSheetName.ELECTRICTY, "J", "13"),
    DATAID_1000002_AVG(1000002L, 11, "B相电流平均值", OfflineTestSheetName.ELECTRICTY, "I", "13"),
    DATAID_1000002_CP95(1000002L, 6, "B相电流95概率值", OfflineTestSheetName.ELECTRICTY, "K", "13"),

    // B相电流有效值
    DATAID_1000003_MAX(1000003L, 3, "C相电流最大值", OfflineTestSheetName.ELECTRICTY, "M", "13"),
    DATAID_1000003_MIN(1000003L, 4, "C相电流最小值", OfflineTestSheetName.ELECTRICTY, "O", "13"),
    DATAID_1000003_AVG(1000003L, 11, "C相电流平均值", OfflineTestSheetName.ELECTRICTY, "N", "13"),
    DATAID_1000003_CP95(1000003L, 6, "C相电流95概率值", OfflineTestSheetName.ELECTRICTY, "P", "13"),

    // A相电压长时闪变
    DATAID_82_MAX(82L, 3, "A相电压长时闪变最大值", OfflineTestSheetName.VOLTAGE, "C", "120"),
    DATAID_82_MIN(82L, 4, "A相电压长时闪变最小值", OfflineTestSheetName.VOLTAGE, "E", "120"),
    DATAID_82_AVG(82L, 11, "A相电压长时闪变平均值", OfflineTestSheetName.VOLTAGE, "D", "120"),
    DATAID_82_CP95(82L, 6, "A相电压长时闪变95概率值", OfflineTestSheetName.VOLTAGE, "F", "120"),

    // B相电压长时闪变
    DATAID_83_MAX(83L, 3, "B相电压长时闪变最大值", OfflineTestSheetName.VOLTAGE, "H", "120"),
    DATAID_83_MIN(83L, 4, "B相电压长时闪变最小值", OfflineTestSheetName.VOLTAGE, "J", "120"),
    DATAID_83_AVG(83L, 11, "B相电压长时闪变平均值", OfflineTestSheetName.VOLTAGE, "I", "120"),
    DATAID_83_CP95(83L, 6, "B相电压长时闪变95概率值", OfflineTestSheetName.VOLTAGE, "K", "120"),

    // C相电压长时闪变
    DATAID_84_MAX(84L, 3, "C相电压长时闪变最大值", OfflineTestSheetName.VOLTAGE, "M", "120"),
    DATAID_84_MIN(84L, 4, "C相电压长时闪变最小值", OfflineTestSheetName.VOLTAGE, "O", "120"),
    DATAID_84_AVG(84L, 11, "C相电压长时闪变平均值", OfflineTestSheetName.VOLTAGE, "N", "120"),
    DATAID_84_CP95(84L, 6, "C相电压长时闪变95概率值", OfflineTestSheetName.VOLTAGE, "P", "120"),

    // A相电压短时闪变
    DATAID_76_MAX(76L, 3, "A相电压短时闪变最大值", OfflineTestSheetName.VOLTAGE, "C", "119"),
    DATAID_76_MIN(76L, 4, "A相电压短时闪变最小值", OfflineTestSheetName.VOLTAGE, "E", "119"),
    DATAID_76_AVG(76L, 11, "A相电压短时闪变平均值", OfflineTestSheetName.VOLTAGE, "D", "119"),
    DATAID_76_CP95(76L, 6, "A相电压短时闪变95概率值", OfflineTestSheetName.VOLTAGE, "F", "119"),

    // B相电压短时闪变(77)
    DATAID_77_MAX(77L, 3, "B相电压短时闪变最大值", OfflineTestSheetName.VOLTAGE, "H", "119"),
    DATAID_77_MIN(77L, 4, "B相电压短时闪变最小值", OfflineTestSheetName.VOLTAGE, "J", "119"),
    DATAID_77_AVG(77L, 11, "B相电压短时闪变平均值", OfflineTestSheetName.VOLTAGE, "I", "119"),
    DATAID_77_CP95(77L, 6, "B相电压短时闪变95概率值", OfflineTestSheetName.VOLTAGE, "K", "119"),

    // C相电压短时闪变(78)
    DATAID_78_MAX(78L, 3, "C相电压短时闪变最大值", OfflineTestSheetName.VOLTAGE, "M", "119"),
    DATAID_78_MIN(78L, 4, "C相电压短时闪变最小值", OfflineTestSheetName.VOLTAGE, "O", "119"),
    DATAID_78_AVG(78L, 11, "C相电压短时闪变平均值", OfflineTestSheetName.VOLTAGE, "N", "119"),
    DATAID_78_CP95(78L, 6, "C相电压短时闪变95概率值", OfflineTestSheetName.VOLTAGE, "P", "119"),

    // 频率(1)
    DATAID_1_MAX(1L, 3, "频率最大值", OfflineTestSheetName.FREQUENCY, "C", "12"),
    DATAID_1_MIN(1L, 4, "频率最小值", OfflineTestSheetName.FREQUENCY, "G", "12"),
    DATAID_1_AVG(1L, 11, "频率平均值", OfflineTestSheetName.FREQUENCY, "E", "12"),
    DATAID_1_CP95(1L, 6, "频率95概率值", OfflineTestSheetName.FREQUENCY, "I", "12"),

    // 频率偏差表(97)
    DATAID_97_MAX(97L, 3, "频率偏差最大值", OfflineTestSheetName.FREQUENCY, "C", "13"),
    DATAID_97_MIN(97L, 4, "频率偏差最小值", OfflineTestSheetName.FREQUENCY, "G", "13"),
    DATAID_97_AVG(97L, 11, "频率偏差平均值", OfflineTestSheetName.FREQUENCY, "E", "13"),
    DATAID_97_CP95(97L, 6, "频率偏差95概率值", OfflineTestSheetName.FREQUENCY, "I", "13"),

    // 功率因数(2000016)
    DATAID_2000016_MAX(2000016L, 3, "功率因数最大值", OfflineTestSheetName.FREQUENCY, "C", "17"),
    DATAID_2000016_MIN(2000016L, 4, "功率因数最小值", OfflineTestSheetName.FREQUENCY, "G", "17"),
    DATAID_2000016_AVG(2000016L, 11, "功率因数平均值", OfflineTestSheetName.FREQUENCY, "E", "17"),
    DATAID_2000016_CP95(2000016L, 6, "功率因数95概率值", OfflineTestSheetName.FREQUENCY, "I", "17"),

    // 总有功功率（2000004）
    DATAID_2000004_MAX(2000004L, 3, "总有功功率最大值", OfflineTestSheetName.FREQUENCY, "C", "14"),
    DATAID_2000004_MIN(2000004L, 4, "总有功功率最小值", OfflineTestSheetName.FREQUENCY, "G", "14"),
    DATAID_2000004_AVG(2000004L, 11, "总有功功率平均值", OfflineTestSheetName.FREQUENCY, "E", "14"),
    DATAID_2000004_CP95(2000004L, 6, "总有功功率95概率值", OfflineTestSheetName.FREQUENCY, "I", "14"),

    // 总无功功率（2000008）
    DATAID_2000008_MAX(2000008L, 3, "总有功功率最大值", OfflineTestSheetName.FREQUENCY, "C", "15"),
    DATAID_2000008_MIN(2000008L, 4, "总有功功率最小值", OfflineTestSheetName.FREQUENCY, "G", "15"),
    DATAID_2000008_AVG(2000008L, 11, "总有功功率平均值", OfflineTestSheetName.FREQUENCY, "E", "15"),
    DATAID_2000008_CP95(2000008L, 6, "总有功功率95概率值", OfflineTestSheetName.FREQUENCY, "I", "15"),

    // 总视在功率（2000012）
    DATAID_2000012_MAX(2000012L, 3, "总视在功率最大值", OfflineTestSheetName.FREQUENCY, "C", "16"),
    DATAID_2000012_MIN(2000012L, 4, "总视在功率最小值", OfflineTestSheetName.FREQUENCY, "G", "16"),
    DATAID_2000012_AVG(2000012L, 11, "总视在功率平均值", OfflineTestSheetName.FREQUENCY, "E", "16"),
    DATAID_2000012_CP95(2000012L, 6, "总视在功率95概率值", OfflineTestSheetName.FREQUENCY, "I", "16"),

    // 电压不平衡度-正序分量
    DATAID_70_MAX(70L, 3, "正序电压幅值最大值", OfflineTestSheetName.VOLTAGE, "D", "123"),
    DATAID_70_MIN(70L, 4, "正序电压幅值最小值", OfflineTestSheetName.VOLTAGE, "I", "123"),
    DATAID_70_AVG(70L, 11, "正序电压幅值平均值", OfflineTestSheetName.VOLTAGE, "F", "123"),
    DATAID_70_CP95(70L, 6, "正序电压幅值95概率值", OfflineTestSheetName.VOLTAGE, "K", "123"),

    // 电压不平衡度-负序分量
    DATAID_71_MAX(71L, 3, "负序电压幅值最大值", OfflineTestSheetName.VOLTAGE, "D", "124"),
    DATAID_71_MIN(71L, 4, "负序电压幅值最小值", OfflineTestSheetName.VOLTAGE, "I", "124"),
    DATAID_71_AVG(71L, 11, "负序电压幅值平均值", OfflineTestSheetName.VOLTAGE, "F", "124"),
    DATAID_71_CP95(71L, 6, "负序电压幅值95概率值", OfflineTestSheetName.VOLTAGE, "K", "124"),

    // 电压不平衡度
    DATAID_103_MAX(103L, 3, "电压不平衡度最大值", OfflineTestSheetName.VOLTAGE, "D", "126"),
    DATAID_103_MIN(103L, 4, "电压不平衡度最小值", OfflineTestSheetName.VOLTAGE, "I", "126"),
    DATAID_103_AVG(103L, 11, "电压不平衡度平均值", OfflineTestSheetName.VOLTAGE, "F", "126"),
    DATAID_103_CP95(103L, 6, "电压不平衡度95概率值", OfflineTestSheetName.VOLTAGE, "K", "126"),

    // 电流不平衡度-正序分量
    DATAID_1000036_MAX(1000036L, 3, "正序电压幅值最大值", OfflineTestSheetName.ELECTRICTY, "D", "120"),
    DATAID_1000036_MIN(1000036L, 4, "正序电压幅值最小值", OfflineTestSheetName.ELECTRICTY, "I", "120"),
    DATAID_1000036_AVG(1000036L, 11, "正序电压幅值平均值", OfflineTestSheetName.ELECTRICTY, "F", "120"),
    DATAID_1000036_CP95(1000036L, 6, "正序电压幅值95概率值", OfflineTestSheetName.ELECTRICTY, "K", "120"),

    // 电流不平衡度-负序分量
    DATAID_1000037_MAX(1000037L, 3, "负序电压幅值最大值", OfflineTestSheetName.ELECTRICTY, "D", "121"),
    DATAID_1000037_MIN(1000037L, 4, "负序电压幅值最小值", OfflineTestSheetName.ELECTRICTY, "I", "121"),
    DATAID_1000037_AVG(1000037L, 11, "负序电压幅值平均值", OfflineTestSheetName.ELECTRICTY, "F", "121"),
    DATAID_1000037_CP95(1000037L, 6, "负序电压幅值95概率值", OfflineTestSheetName.ELECTRICTY, "K", "121"),

    // 电流不平衡度
    DATAID_1000049_MAX(1000049L, 3, "电压不平衡度最大值", OfflineTestSheetName.ELECTRICTY, "D", "123"),
    DATAID_1000049_MIN(1000049L, 4, "电压不平衡度最小值", OfflineTestSheetName.ELECTRICTY, "I", "123"),
    DATAID_1000049_AVG(1000049L, 11, "电压不平衡度平均值", OfflineTestSheetName.ELECTRICTY, "F", "123"),
    DATAID_1000049_CP95(1000049L, 6, "电压不平衡度95概率值", OfflineTestSheetName.ELECTRICTY, "K", "123");

    private Long dataId;

    private Integer aggregationType;

    private String dataName;

    private String sheetName;

    private String col;

    private String row;

    private SumData(Long dataId, Integer aggregationType, String dataName, String sheetName, String col, String row) {
        this.dataId = dataId;
        this.aggregationType = aggregationType;
        this.dataName = dataName;
        this.sheetName = sheetName;
        this.col = col;
        this.row = row;
    }

    public Long getDataId() {
        return dataId;
    }

    public Integer getAggregationType() {
        return aggregationType;
    }

    public String getDataName() {
        return dataName;
    }

    public String getSheetName() {
        return sheetName;
    }

    public String getCol() {
        return col;
    }

    public String getRow() {
        return row;
    }


}

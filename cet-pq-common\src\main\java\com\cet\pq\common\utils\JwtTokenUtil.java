package com.cet.pq.common.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/6/28 14:56
 * @description
 */
@Slf4j
@Component
public class JwtTokenUtil implements Serializable {

    private static final String SECRET = "cet-soft:matterhorn";

    private String generateToken(Map<String, Object> claims) {
        Date expirationDate = new Date(System.currentTimeMillis() + 2592000000L);
        return Jwts.builder().setClaims(claims).setExpiration(expirationDate).signWith(SignatureAlgorithm.HS512, "cet-soft:matterhorn").compact();
    }

    public static Claims getClaimsFromToken(String token) {
        Claims claims;
        try {
            claims = (Claims) Jwts.parser().setSigningKey("cet-soft:matterhorn").parseClaimsJws(token).getBody();
        } catch (Exception e) {
            log.error(e.getMessage());
            claims = null;
        }
        return claims;
    }


    public String getUsernameFromToken(String token) {
        String username = null;
        try {
            Claims claims = getClaimsFromToken(token);
            if(Objects.nonNull(claims)){
                username = claims.getSubject();
            }
        } catch (Exception e) {
            username = null;
        }
        return username;
    }

    public Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            if(Objects.nonNull(claims)){
                Date expiration = claims.getExpiration();
                return Boolean.valueOf(expiration.before(new Date()));
            }
            return Boolean.valueOf(false);
        } catch (Exception e) {
            return Boolean.valueOf(false);
        }
    }

    public String refreshToken(String token) {
        String refreshedToken = null;
        try {
            Claims claims = getClaimsFromToken(token);
            if(Objects.nonNull(claims)){
                claims.put("created", new Date());
                refreshedToken = generateToken((Map<String, Object>) claims);
            }
        } catch (Exception e) {
            refreshedToken = null;
        }
        return refreshedToken;
    }


    public static String getToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token == null) {
            token = request.getHeader("x-token");
        }
        if (token == null) {
            token = request.getHeader("UserToken");
        }
        return token;
    }

    public static HashMap parseToken(String token) {
        HashMap<String, String> tokenMap = new HashMap<>(3);
        if (StringUtils.isEmpty(token) || !token.startsWith("Bearer")) {
            return tokenMap;
        }
        token = token.replaceFirst("Bearer", "").trim();
        Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            String name = claims.get("sub").toString();
            tokenMap.put("name", name);
            String userId = claims.get("id").toString();
            tokenMap.put("userId", userId);
            String client = claims.get("client").toString();
            tokenMap.put("client", client);
        }
        return tokenMap;
    }

    public static void requestBundleToken(HttpServletRequest request) {
        String token = getToken(request);
        if (token == null) {
            return;
        }
        HashMap parseToken = parseToken(token);
        request.setAttribute("User-ID", parseToken.get("userId"));
        request.setAttribute("User-Name", parseToken.get("name"));
    }

    public static String getUserId(HttpServletRequest request) {
        String token = getToken(request);
        if (token == null) {
            return null;
        }
        HashMap parseToken = parseToken(token);
        return ParseDataUtil.parseString(parseToken.get("userId"));
    }

}
